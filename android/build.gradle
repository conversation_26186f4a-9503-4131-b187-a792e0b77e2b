// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        maven { url 'https://plugins.gradle.org/m2/' } // Gradle Plugin Portal
//        mavenCentral()
        jcenter()

//        flatDir{
//            dirs 'libs'
//        }
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.3'

        // use for apt 'com.jakewharton:butterknife-compiler:8.1.0'
        classpath 'com.neenbedankt.gradle.plugins:android-apt:1.8'

        classpath 'com.google.gms:google-services:4.3.10' // google-services plugin
//        classpath 'com.google.gms:google-services:3.2.0'
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url "$rootDir/../node_modules/react-native/android"
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        google()
//        mavenCentral()
        jcenter()
        maven { url 'https://jitpack.io' }

        exclusiveContent {
            filter {
                includeGroup "com.facebook.react"
            }
            forRepository {
                maven {
                    url "$rootDir/../node_modules/react-native/android"
                }
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    // dependency versions
    googlePlayServicesVersion = "11.8.0"
    compileSdkVersion = 28
    buildToolsVersion = "28.0.3"
    targetSdkVersion = 26
    supportLibVersion = "27.1.1"
    kotlinVersion = "1.6.10"
}
