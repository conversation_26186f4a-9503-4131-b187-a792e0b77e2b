package com.mpos.utils;

import android.text.TextUtils;

import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.util.Utils;

import junit.framework.TestCase;

import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;

/**
 * Create by anhnguyen on 11/30/20
 */
public class MposUtilTest extends TestCase {

    @Test
    public void test() {
//        String content = "\"{\\\"capk\\\":[\\\"df0701019f220107df060101df0504203112179f0605A000000003df0403000003df0314B4BC56CC4E88324932CBC643D6898F6FE593B172df028190A89F25A56FA6DA258C8CA8B40427D927B4A1EB4D7EA326BBB12F97DED70AE5E4480FC9C5E8A972177110A1CC318D06D2F8F5C4844AC5FA79A4DC470BB11ED635699C17081B90F1B984F12E92C1C529276D8AF8EC7F28492097D8CD5BECEA16FE4088F6CFAB4A1B42328A1B996F9278B0B7E3311CA5EF856C2F888474B83612A82E4E00D0CD4069A6783140433D50725F\\\",\\\"df0701019f220108df060101df0504203112239f0605A000000003df0403000003df031420D213126955DE205ADC2FD2822BD22DE21CF9A8df0281b0D9FD6ED75D51D0E30664BD157023EAA1FFA871E4DA65672B863D255E81E137A51DE4F72BCC9E44ACE12127F87E263D3AF9DD9CF35CA4A7B01E907000BA85D24954C2FCA3074825DDD4C0C8F186CB020F683E02F2DEAD3969133F06F7845166ACEB57CA0FC2603445469811D293BFEFBAFAB57631B3DD91E796BF850A25012F1AE38F05AA5C4D6D03B1DC2E568612785938BBC9B3CD3A910C1DA55A5A9218ACE0F7A21287752682F15832A678D6E1ED0B\\\",\\\"df0701019f220109df060101df0504203112229f0605A000000003df0403000003df03141FF80A40173F52D7D27E0F26A146A1C8CCB29046df0281f89D912248DE0A4E39C1A7DDE3F6D2588992C1A4095AFBD1824D1BA74847F2BC4926D2EFD904B4B54954CD189A54C5D1179654F8F9B0D2AB5F0357EB642FEDA95D3912C6576945FAB897E7062CAA44A4AA06B8FE6E3DBA18AF6AE3738E30429EE9BE03427C9D64F695FA8CAB4BFE376853EA34AD1D76BFCAD15908C077FFE6DC5521ECEF5D278A96E26F57359FFAEDA19434B937F1AD999DC5C41EB11935B44C18100E857F431A4A5A6BB65114F174C2D7B59FDF237D6BB1DD0916E644D709DED56481477C75D95CDD68254615F7740EC07F330AC5D67BCD75BF23D28A140826C026DBDE971A37CD3EF9B8DF644AC385010501EFC6509D7A41\\\",\\\"df0701019f220192df060101df0504203112229f0605A000000003df0403000003df0314429C954A3859CEF91295F663C963E582ED6EB253df0281b0996AF56F569187D09293C14810450ED8EE3357397B18A2458EFAA92DA3B6DF6514EC060195318FD43BE9B8F0CC669E3F844057CBDDF8BDA191BB64473BC8DC9A730DB8F6B4EDE3924186FFD9B8C7735789C23A36BA0B8AF65372EB57EA5D89E7D14E9C7B6B557460F10885DA16AC923F15AF3758F0F03EBD3C5C2C949CBA306DB44E6A2C076C5F67E281D7EF56785DC4D75945E491F01918800A9E2DC66F60080566CE0DAF8D17EAD46AD8E30A247C9F\\\",\\\"df0701019f220194df060101df0504203112229f0605A000000003df0403000003df0314C4A3C43CCF87327D136B804160E47D43B60E6E0Fdf0281f8ACD2B12302EE644F3F835ABD1FC7A6F62CCE48FFEC622AA8EF062BEF6FB8BA8BC68BBF6AB5870EED579BC3973E121303D34841A796D6DCBC41DBF9E52C4609795C0CCF7EE86FA1D5CB041071ED2C51D2202F63F1156C58A92D38BC60BDF424E1776E2BC9648078A03B36FB554375FC53D57C73F5160EA59F3AFC5398EC7B67758D65C9BFF7828B6B82D4BE124A416AB7301914311EA462C19F771F31B3B57336000DFF732D3B83DE07052D730354D297BEC72871DCCF0E193F171ABA27EE464C6A97690943D59BDABB2A27EB71CEEBDAFA1176046478FD62FEC452D5CA393296530AA3F41927ADFE434A2DF2AE3054F8840657A26E0FC617\\\",\\\"df0701019f220195df060101df0504203112229f0605A000000003df0403000003df0314EE1511CEC71020A9B90443B37B1D5F6E703030F6df028190BE9E1FA5E9A803852999C4AB432DB28600DCD9DAB76DFAAA47355A0FE37B1508AC6BF38860D3C6C2E5B12A3CAAF2A7005A7241EBAA7771112C74CF9A0634652FBCA0E5980C54A64761EA101A114E0F0B5572ADD57D010B7C9C887E104CA4EE1272DA66D997B9A90B5A6D624AB6C57E73C8F919000EB5F684898EF8C3DBEFB330C62660BED88EA78E909AFF05F6DA627B\\\",\\\"df0701019f220199df060101df0504203112229f0605A000000003df0403000003df03144ABFFD6B1C51212D05552E431C5B17007D2F5E6Ddf028180AB79FCC9520896967E776E64444E5DCDD6E13611874F3985722520425295EEA4BD0C2781DE7F31CD3D041F565F747306EED62954B17EDABA3A6C5B85A1DE1BEB9A34141AF38FCF8279C9DEA0D5A6710D08DB4124F041945587E20359BAB47B7575AD94262D4B25F264AF33DEDCF28E09615E937DE32EDC03C54445FE7E382777\\\",\\\"df0701019f220104df060101df0504203112179f0605A000000004df0403000003df0314381A035DA58B482EE2AF75F4C3F2CA469BA4AA6Cdf028190A6DA428387A502D7DDFB7A74D3F412BE762627197B25435B7A81716A700157DDD06F7CC99D6CA28C2470527E2C03616B9C59217357C2674F583B3BA5C7DCF2838692D023E3562420B4615C439CA97C44DC9A249CFCE7B3BFB22F68228C3AF13329AA4A613CF8DD853502373D62E49AB256D2BC17120E54AEDCED6D96A4287ACC5C04677D4A5A320DB8BEE2F775E5FEC5\\\",\\\"df0701019f220105df060101df0504203112239f0605A000000004df0403000003df0314EBFA0D5D06D8CE702DA3EAE890701D45E274C845df0281b0B8048ABC30C90D976336543E3FD7091C8FE4800DF820ED55E7E94813ED00555B573FECA3D84AF6131A651D66CFF4284FB13B635EDD0EE40176D8BF04B7FD1C7BACF9AC7327DFAA8AA72D10DB3B8E70B2DDD811CB4196525EA386ACC33C0D9D4575916469C4E4F53E8E1C912CC618CB22DDE7C3568E90022E6BBA770202E4522A2DD623D180E215BD1D1507FE3DC90CA310D27B3EFCCD8F83DE3052CAD1E48938C68D095AAC91B5F37E28BB49EC7ED597\\\",\\\"df0701019f220106df060101df0504203112239f0605A000000004df0403000003df0314F910A1504D5FFB793D94F3B500765E1ABCAD72D9df0281f8CB26FC830B43785B2BCE37C81ED334622F9622F4C89AAE641046B2353433883F307FB7C974162DA72F7A4EC75D9D657336865B8D3023D3D645667625C9A07A6B7A137CF0C64198AE38FC238006FB2603F41F4F3BB9DA1347270F2F5D8C606E420958C5F7D50A71DE30142F70DE468889B5E3A08695B938A50FC980393A9CBCE44AD2D64F630BB33AD3F5F5FD495D31F37818C1D94071342E07F1BEC2194F6035BA5DED3936500EB82DFDA6E8AFB655B1EF3D0D7EBF86B66DD9F29F6B1D324FE8B26CE38AB2013DD13F611E7A594D675C4432350EA244CC34F3873CBA06592987A1D7E852ADC22EF5A2EE28132031E48F74037E3B34AB747F\\\",\\\"df0701019f2201EFdf060101df0504203112239f0605A000000004df0403000003df031421766EBB0EE122AFB65D7845B73DB46BAB65427Adf0281f8A191CB87473F29349B5D60A88B3EAEE0973AA6F1A082F358D849FDDFF9C091F899EDA9792CAF09EF28F5D22404B88A2293EEBBC1949C43BEA4D60CFD879A1539544E09E0F09F60F065B2BF2A13ECC705F3D468B9D33AE77AD9D3F19CA40F23DCF5EB7C04DC8F69EBA565B1EBCB4686CD274785530FF6F6E9EE43AA43FDB02CE00DAEC15C7B8FD6A9B394BABA419D3F6DC85E16569BE8E76989688EFEA2DF22FF7D35C043338DEAA982A02B866DE5328519EBBCD6F03CDD686673847F84DB651AB86C28CF1462562C577B853564A290C8556D818531268D25CC98A4CC6A0BDFFFDA2DCCA3A94C998559E307FDDF915006D9A987B07DDAEB3B\\\",\\\"df0701019f2201F1df060101df0504203112239f0605A000000004df0403000003df0314D8E68DA167AB5A85D8C3D55ECB9B0517A1A5B4BBdf0281b0A0DCF4BDE19C3546B4B6F0414D174DDE294AABBB828C5A834D73AAE27C99B0B053A90278007239B6459FF0BBCD7B4B9C6C50AC02CE91368DA1BD21AAEADBC65347337D89B68F5C99A09D05BE02DD1F8C5BA20E2F13FB2A27C41D3F85CAD5CF6668E75851EC66EDBF98851FD4E42C44C1D59F5984703B27D5B9F21B8FA0D93279FBBF69E090642909C9EA27F898959541AA6757F5F624104F6E1D3A9532F2A6E51515AEAD1B43B3D7835088A2FAFA7BE7\\\",\\\"df0701019f2201F3df060101df0504203112239f0605A000000004df0403000003df0314A69AC7603DAF566E972DEDC2CB433E07E8B01A9Adf02819098F0C770F23864C2E766DF02D1E833DFF4FFE92D696E1642F0A88C5694C6479D16DB1537BFE29E4FDC6E6E8AFD1B0EB7EA0124723C333179BF19E93F10658B2F776E829E87DAEDA9C94A8B3382199A350C077977C97AFF08FD11310AC950A72C3CA5002EF513FCCC286E646E3C5387535D509514B3B326E1234F9CB48C36DDD44B416D23654034A66F403BA511C5EFA3\\\",\\\"df0701019f2201F8df060101df0504203112239f0605A000000004df0403000003df0314F06ECC6D2AAEBF259B7E755A38D9A9B24E2FF3DDdf028180A1F5E1C9BD8650BD43AB6EE56B891EF7459C0A24FA84F9127D1A6C79D4930F6DB1852E2510F18B61CD354DB83A356BD190B88AB8DF04284D02A4204A7B6CB7C5551977A9B36379CA3DE1A08E69F301C95CC1C20506959275F41723DD5D2925290579E5A95B0DF6323FC8E9273D6F849198C4996209166D9BFC973C361CC826E1\\\",\\\"df0701019f2201FAdf060101df0504203112239f0605A000000004df0403000003df03145BED4068D96EA16D2D77E03D6036FC7A160EA99Cdf028190A90FCD55AA2D5D9963E35ED0F440177699832F49C6BAB15CDAE5794BE93F934D4462D5D12762E48C38BA83D8445DEAA74195A301A102B2F114EADA0D180EE5E7A5C73E0C4E11F67A43DDAB5D55683B1474CC0627F44B8D3088A492FFAADAD4F42422D0E7013536C3C49AD3D0FAE96459B0F6B1B6056538A3D6D44640F94467B108867DEC40FAAECD740C00E2B7A8852D\\\",\\\"df0701019f2201FEdf060101df0504203112169f0605A000000004df0403000003df03149A295B05FB390EF7923F57618A9FDA2941FC34E0df028180A653EAC1C0F786C8724F737F172997D63D1C3251C44402049B865BAE877D0F398CBFBE8A6035E24AFA086BEFDE9351E54B95708EE672F0968BCD50DCE40F783322B2ABA04EF137EF18ABF03C7DBC5813AEAEF3AA7797BA15DF7D5BA1CBAF7FD520B5A482D8D3FEE105077871113E23A49AF3926554A70FE10ED728CF793B62A1\\\",\\\"df0701019f2201FFdf060101df0504203112169f0605A000000004df0403000003df0270B855CC64313AF99C453D181642EE7DD21A67D0FF50C61FE213BCDC18AFBCD07722EFDD2594EFDC227DA3DA23ADCC90E3FA907453ACC954C47323BEDCF8D4862C457D25F47B16D7C3502BE081913E5B0482D838484065DA5F6659E00A9E5D570ADA1EC6AF8C57960075119581FC81468D\\\",\\\"df0701019f220102df060101df0504203112169f0605A000000025df0403000003df031433F5B0344943048237EC89B275A95569718AEE20df0270AF4B8D230FDFCB1538E975795A1DB40C396A5359FAA31AE095CB522A5C82E7FFFB252860EC2833EC3D4A665F133DD934EE1148D81E2B7E03F92995DDF7EB7C90A75AB98E69C92EC91A533B21E1C4918B43AFED5780DE13A32BBD37EBC384FA3DD1A453E327C56024DACAEA74AA052C4D\\\",\\\"df0701019f220103df060101df0504203112169f0605A000000025df0403000003df03148708A3E3BBC1BB0BE73EBD8D19D4E5D20166BF6Cdf028180B0C2C6E2A6386933CD17C239496BF48C57E389164F2A96BFF133439AE8A77B20498BD4DC6959AB0C2D05D0723AF3668901937B674E5A2FA92DDD5E78EA9D75D79620173CC269B35F463B3D4AAFF2794F92E6C7A3FB95325D8AB95960C3066BE548087BCB6CE12688144A8B4A66228AE4659C634C99E36011584C095082A3A3E3\\\",\\\"df0701019f220104df060101df0504203112179f0605A000000025df0403000003df0314FDD7139EC7E0C33167FD61AD3CADBD68D66E91C5df0260D0F543F03F2517133EF2BA4A1104486758630DCFE3A883C77B4E4844E39A9BD6360D23E6644E1E071F196DDF2E4A68B4A3D93D14268D7240F6A14F0D714C17827D279D192E88931AF7300727AE9DA80A3F0E366AEBA61778171737989E1EE309\\\",\\\"df0701019f22010Edf060101df0504203112179f0605A000000025df0403000003df0314A7266ABAE64B42A3668851191D49856E17F8FBCDdf028190AA94A8C6DAD24F9BA56A27C09B01020819568B81A026BE9FD0A3416CA9A71166ED5084ED91CED47DD457DB7E6CBCD53E560BC5DF48ABC380993B6D549F5196CFA77DFB20A0296188E969A2772E8C4141665F8BB2516BA2C7B5FC91F8DA04E8D512EB0F6411516FB86FC021CE7E969DA94D33937909A53A57F907C40C22009DA7532CB3BE509AE173B39AD6A01BA5BB85\\\",\\\"df0701019f22010Fdf060101df0504203112179f0605A000000025df0403000003df0314A73472B3AB557493A9BC2179CC8014053B12BAB4df0281b0C8D5AC27A5E1FB89978C7C6479AF993AB3800EB243996FBB2AE26B67B23AC482C4B746005A51AFA7D2D83E894F591A2357B30F85B85627FF15DA12290F70F05766552BA11AD34B7109FA49DE29DCB0109670875A17EA95549E92347B948AA1F045756DE56B707E3863E59A6CBE99C1272EF65FB66CBB4CFF070F36029DD76218B21242645B51CA752AF37E70BE1A84FF31079DC0048E928883EC4FADD497A719385C2BBBEBC5A66AA5E5655D18034EC5\\\",\\\"df0701019f220110df060101df0504203112189f0605A000000025df0403000003df0314C729CF2FD262394ABC4CC173506502446AA9B9FDdf0281f8CF98DFEDB3D3727965EE7797723355E0751C81D2D3DF4D18EBAB9FB9D49F38C8C4A826B99DC9DEA3F01043D4BF22AC3550E2962A59639B1332156422F788B9C16D40135EFD1BA94147750575E636B6EBC618734C91C1D1BF3EDC2A46A43901668E0FFC136774080E888044F6A1E65DC9AAA8928DACBEB0DB55EA3514686C6A732CEF55EE27CF877F110652694A0E3484C855D882AE191674E25C296205BBB599455176FDD7BBC549F27BA5FE35336F7E29E68D783973199436633C67EE5A680F05160ED12D1665EC83D1997F10FD05BBDBF9433E8F797AEE3E9F02A34228ACE927ABE62B8B9281AD08D3DF5C7379685045D7BA5FCDE58637\\\",\\\"df0701019f220165df060101df0504203112169f0605A000000025df0403000003df0314894C5D08D4EA28BB79DC46CEAD998B877322F416df028190E53EB41F839DDFB474F272CD0CBE373D5468EB3F50F39C95BDF4D39FA82B98DABC9476B6EA350C0DCE1CD92075D8C44D1E57283190F96B3537D9E632C461815EBD2BAF36891DF6BFB1D30FA0B752C43DCA0257D35DFF4CCFC98F84198D5152EC61D7B5F74BD09383BD0E2AA42298FFB02F0D79ADB70D72243EE537F75536A8A8DF962582E9E6812F3A0BE02A4365400D\\\",\\\"df0701019f2201C9df060101df0504203112169f0605A000000025df0403000003df03148E8DFF443D78CD91DE88821D70C98F0638E51E49df0281b0B362DB5733C15B8797B8ECEE55CB1A371F760E0BEDD3715BB270424FD4EA26062C38C3F4AAA3732A83D36EA8E9602F6683EECC6BAFF63DD2D49014BDE4D6D603CD744206B05B4BAD0C64C63AB3976B5C8CAAF8539549F5921C0B700D5B0F83C4E7E946068BAAAB5463544DB18C63801118F2182EFCC8A1E85E53C2A7AE839A5C6A3CABE73762B70D170AB64AFC6CA482944902611FB0061E09A67ACB77E493D998A0CCF93D81A4F6C0DC6B7DF22E62DB\\\",\\\"df0701019f2201CAdf060101df0504203112169f0605A000000025df0403000003df03146BDA32B1AA171444C7E8F88075A74FBFE845765Fdf0281f8C23ECBD7119F479C2EE546C123A585D697A7D10B55C2D28BEF0D299C01DC65420A03FE5227ECDECB8025FBC86EEBC1935298C1753AB849936749719591758C315FA150400789BB14FADD6EAE2AD617DA38163199D1BAD5D3F8F6A7A20AEF420ADFE2404D30B219359C6A4952565CCCA6F11EC5BE564B49B0EA5BF5B3DC8C5C6401208D0029C3957A8C5922CBDE39D3A564C6DEBB6BD2AEF91FC27BB3D3892BEB9646DCE2E1EF8581EFFA712158AAEC541C0BBB4B3E279D7DA54E45A0ACC3570E712C9F7CDF985CFAFD382AE13A3B214A9E8E1E71AB1EA707895112ABC3A97D0FCB0AE2EE5C85492B6CFD54885CDD6337E895CC70FB3255E3\\\",\\\"df0701019f220101df060101df0504203112229f0605A000000152df0403000003df0314E0C2C1EA411DB24EC3E76A9403F0B7B6F406F398df0281808D1727AB9DC852453193EA0810B110F2A3FD304BE258338AC2650FA2A040FA10301EA53DF18FD9F40F55C44FE0EE7C7223BC649B8F9328925707776CB86F3AC37D1B22300D0083B49350E09ABB4B62A96363B01E4180E158EADDD6878E85A6C9D56509BF68F0400AFFBC441DDCCDAF9163C4AACEB2C3E1EC13699D23CDA9D3AD\\\",\\\"df0701019f220103df060101df0504203112229f0605A000000152df0403000003df0314CA1E9099327F0B786D8583EC2F27E57189503A57df028190BF321241BDBF3585FFF2ACB89772EBD18F2C872159EAA4BC179FB03A1B850A1A758FA2C6849F48D4C4FF47E02A575FC13E8EB77AC37135030C5600369B5567D3A7AAF02015115E987E6BE566B4B4CC03A4E2B16CD9051667C2CD0EEF4D76D27A6F745E8BBEB45498ED8C30E2616DB4DBDA4BAF8D71990CDC22A8A387ACB21DD88E2CC27962B31FBD786BBB55F9E0B041\\\",\\\"df0701019f220104df060101df0504203112229f0605A000000152df0403000003df031417F971CAF6B708E5B9165331FBA91593D0C0BF66df0281b08EEEC0D6D3857FD558285E49B623B109E6774E06E9476FE1B2FB273685B5A235E955810ADDB5CDCC2CB6E1A97A07089D7FDE0A548BDC622145CA2DE3C73D6B14F284B3DC1FA056FC0FB2818BCD7C852F0C97963169F01483CE1A63F0BF899D412AB67C5BBDC8B4F6FB9ABB57E95125363DBD8F5EBAA9B74ADB93202050341833DEE8E38D28BD175C83A6EA720C262682BEABEA8E955FE67BD9C2EFF7CB9A9F45DD5BDA4A1EEFB148BC44FFF68D9329FD\\\",\\\"df0701019f220105df060101df0504203112229f0605A000000152df0403000003df031412BCD407B6E627A750FDF629EE8C2C9CC7BA636Adf0281f8E1200E9F4428EB71A526D6BB44C957F18F27B20BACE978061CCEF23532DBEBFAF654A149701C14E6A2A7C2ECAC4C92135BE3E9258331DDB0967C3D1D375B996F25B77811CCCC06A153B4CE6990A51A0258EA8437EDBEB701CB1F335993E3F48458BC1194BAD29BF683D5F3ECB984E31B7B9D2F6D947B39DEDE0279EE45B47F2F3D4EEEF93F9261F8F5A571AFBFB569C150370A78F6683D687CB677777B2E7ABEFCFC8F5F93501736997E8310EE0FD87AFAC5DA772BA277F88B44459FCA563555017CD0D66771437F8B6608AA1A665F88D846403E4C41AFEEDB9729C2B2511CFE228B50C1B152B2A60BBF61D8913E086210023A3AA499E423\\\",\\\"df0701019f22015Adf060101df0504203112229f0605A000000152df0403000003df028180EDD8252468A705614B4D07DE3211B30031AEDB6D33A4315F2CFF7C97DB918993C2DC02E79E2FF8A2683D5BBD0F614BC9AB360A448283EF8B9CF6731D71D6BE939B7C5D0B0452D660CF24C21C47CAC8E26948C8EED8E3D00C016828D642816E658DC2CFC61E7E7D7740633BEFE34107C1FB55DEA7FAAEA2B25E85BED948893D07\\\",\\\"df0701019f22015Bdf060101df0504203112229f0605A000000152df0403000003df028190D3F45D065D4D900F68B2129AFA38F549AB9AE4619E5545814E468F382049A0B9776620DA60D62537F0705A2C926DBEAD4CA7CB43F0F0DD809584E9F7EFBDA3778747BC9E25C5606526FAB5E491646D4DD28278691C25956C8FED5E452F2442E25EDC6B0C1AA4B2E9EC4AD9B25A1B836295B823EDDC5EB6E1E0A3F41B28DB8C3B7E3E9B5979CD7E079EF024095A1D19DD\\\",\\\"df0701019f22015Cdf060101df0504203112229f0605A000000152df0403000003df0281b0833F275FCF5CA4CB6F1BF880E54DCFEB721A316692CAFEB28B698CAECAFA2B2D2AD8517B1EFB59DDEFC39F9C3B33DDEE40E7A63C03E90A4DD261BC0F28B42EA6E7A1F307178E2D63FA1649155C3A5F926B4C7D7C258BCA98EF90C7F4117C205E8E32C45D10E3D494059D2F2933891B979CE4A831B301B0550CDAE9B67064B31D8B481B85A5B046BE8FFA7BDB58DC0D7032525297F26FF619AF7F15BCEC0C92BCDCBC4FB207D115AA65CD04C1CF982191\\\",\\\"df0701019f220110df060101df0504203112179f0605A000000065df0403000003df0314C75E5210CBE6E8F0594A0F1911B07418CADB5BABdf02819099B63464EE0B4957E4FD23BF923D12B61469B8FFF8814346B2ED6A780F8988EA9CF0433BC1E655F05EFA66D0C98098F25B659D7A25B8478A36E489760D071F54CDF7416948ED733D816349DA2AADDA227EE45936203CBF628CD033AABA5E5A6E4AE37FBACB4611B4113ED427529C636F6C3304F8ABDD6D9AD660516AE87F7F2DDF1D2FA44C164727E56BBC9BA23C0285\\\",\\\"df0701019f220112df060101df0504203112229f0605A000000065df0403000003df0314874B379B7F607DC1CAF87A19E400B6A9E25163E8df0281b0ADF05CD4C5B490B087C3467B0F3043750438848461288BFEFD6198DD576DC3AD7A7CFA07DBA128C247A8EAB30DC3A30B02FCD7F1C8167965463626FEFF8AB1AA61A4B9AEF09EE12B009842A1ABA01ADB4A2B170668781EC92B60F605FD12B2B2A6F1FE734BE510F60DC5D189E401451B62B4E06851EC20EBFF4522AACC2E9CDC89BC5D8CDE5D633CFD77220FF6BBD4A9B441473CC3C6FEFC8D13E57C3DE97E1269FA19F655215B23563ED1D1860D8681\\\",\\\"df0701019f220113df060101df0504203112229f0605A000000065df0403000003df031454CFAE617150DFA09D3F901C9123524523EBEDF3df0281f8A3270868367E6E29349FC2743EE545AC53BD3029782488997650108524FD051E3B6EACA6A9A6C1441D28889A5F46413C8F62F3645AAEB30A1521EEF41FD4F3445BFA1AB29F9AC1A74D9A16B93293296CB09162B149BAC22F88AD8F322D684D6B49A12413FC1B6AC70EDEDB18EC1585519A89B50B3D03E14063C2CA58B7C2BA7FB22799A33BCDE6AFCBEB4A7D64911D08D18C47F9BD14A9FAD8805A15DE5A38945A97919B7AB88EFA11A88C0CD92C6EE7DC352AB0746ABF13585913C8A4E04464B77909C6BD94341A8976C4769EA6C0D30A60F4EE8FA19E767B170DF4FA80312DBA61DB645D5D1560873E2674E1F620083F30180BD96CA589\\\",\\\"df0701019f220114df060101df0504203112229f0605A000000065df0403000003df0314C0D15F6CD957E491DB56DCDD1CA87A03EBE06B7Bdf0281f8AEED55B9EE00E1ECEB045F61D2DA9A66AB637B43FB5CDBDB22A2FBB25BE061E937E38244EE5132F530144A3F268907D8FD648863F5A96FED7E42089E93457ADC0E1BC89C58A0DB72675FBC47FEE9FF33C16ADE6D341936B06B6A6F5EF6F66A4EDD981DF75DA8399C3053F430ECA342437C23AF423A211AC9F58EAF09B0F837DE9D86C7109DB1646561AA5AF0289AF5514AC64BC2D9D36A179BB8A7971E2BFA03A9E4B847FD3D63524D43A0E8003547B94A8A75E519DF3177D0A60BC0B4BAB1EA59A2CBB4D2D62354E926E9C7D3BE4181E81BA60F8285A896D17DA8C3242481B6C405769A39D547C74ED9FF95A70A796046B5EFF36682DC29\\\",\\\"df0701019f220109df060101df0504203112229f0605A000000065df0403000003df03144410C6D51C2F83ADFD92528FA6E38A32DF048D0Adf028180B72A8FEF5B27F2B550398FDCC256F714BAD497FF56094B7408328CB626AA6F0E6A9DF8388EB9887BC930170BCC1213E90FC070D52C8DCD0FF9E10FAD36801FE93FC998A721705091F18BC7C98241CADC15A2B9DA7FB963142C0AB640D5D0135E77EBAE95AF1B4FEFADCF9C012366BDDA0455C1564A68810D7127676D493890BD\\\",\\\"df0701019f220111df060101df0504203112229f0605A000000065df0403000003df0314D9FD62C9DD4E6DE7741E9A17FB1FF2C5DB948BCBdf0281b0A2583AA40746E3A63C22478F576D1EFC5FB046135A6FC739E82B55035F71B09BEB566EDB9968DD649B94B6DEDC033899884E908C27BE1CD291E5436F762553297763DAA3B890D778C0F01E3344CECDFB3BA70D7E055B8C760D0179A403D6B55F2B3B083912B183ADB7927441BED3395A199EEFE0DEBD1F5FC3264033DA856F4A8B93916885BD42F9C1F456AAB8CFA83AC574833EB5E87BB9D4C006A4B5346BD9E17E139AB6552D9C58BC041195336485\\\",\\\"df0701019f22010Fdf060101df0504203112229f0605A000000065df0403000003df03142A1B82DE00F5F0C401760ADF528228D3EDE0F403df0281909EFBADDE4071D4EF98C969EB32AF854864602E515D6501FDE576B310964A4F7C2CE842ABEFAFC5DC9E26A619BCF2614FE07375B9249BEFA09CFEE70232E75FFD647571280C76FFCA87511AD255B98A6B577591AF01D003BD6BF7E1FCE4DFD20D0D0297ED5ECA25DE261F37EFE9E175FB5F12D2503D8CFB060A63138511FE0E125CF3A643AFD7D66DCF9682BD246DDEA1\\\",\\\"df0701019f220108df060101df0504203112229f0605A000000065df0403000003df0314DD36D5896228C8C4900742F107E2F91FE50BC7EEdf028180B74670DAD1DC8983652000E5A7F2F8B35DFD083EE593E5BA895C95729F2BADE9C8ABF3DD9CE240C451C6CEFFC768D83CBAC76ABB8FEA58F013C647007CFF7617BAC2AE3981816F25CC7E5238EF34C4F02D0B01C24F80C2C65E7E7743A4FA8E23206A23ECE290C26EA56DB085C5C5EAE26292451FC8292F9957BE8FF20FAD53E5\\\",\\\"df0701019f22010Bdf060101df0504203112229f0605A000000333df0403000003df0314602A4CAB6084C493F01B29AB41F3140B85EABBEFdf0281f8CF9FDF46B356378E9AF311B0F981B21A1F22F250FB11F55C958709E3C7241918293483289EAE688A094C02C344E2999F315A72841F489E24B1BA0056CFAB3B479D0E826452375DCDBB67E97EC2AA66F4601D774FEAEF775ACCC621BFEB65FB0053FC5F392AA5E1D4C41A4DE9FFDFDF1327C4BB874F1F63A599EE3902FE95E729FD78D4234DC7E6CF1ABABAA3F6DB29B7F05D1D901D2E76A606A8CBFFFFECBD918FA2D278BDB43B0434F5D45134BE1C2781D157D501FF43E5F1C470967CD57CE53B64D82974C8275937C5D8502A1252A8A5D6088A259B694F98648D9AF2CB0EFD9D943C69F896D49FA39702162ACB5AF29B90BADE005BC157\\\",\\\"df0701019f220101df060101df0504203112229f0605A000000724df0403000003df0314BD331F9996A490B33C13441066A09AD3FEB5F66Cdf0281b0AE0B89755F0509F111FDF7CDBABE0491A2E3A6A778A0FCB1744C5445749FE9407E5BDE86D402DC63BAE999BD6698132181BE2AAD0B96C9BEBA11A521B165165AA40057292F79F7329724D178AF18FC342BB8B58D1DD84FF44847056BF17F66307500228558D847678F9FA462E290F3DFD898F11381BA1710B94D42F160780D0F60A909516653978AE750568B3960071092633530C053FFB7097EFBF140AFCB196861A0DA94ECDAC8D336BE97B8E9AFB7\\\",\\\"df0701019f220102df060101df0504203112229f0605A000000724df0403000003df031415C48DAD19DB502AB397647A38A0755E0FD0CB39df0281f8AABB504E3B2CC61F243A6BE3BFB18B649E17ED625F1F8EE04704873AD60564B22A1B0258EA1D25BA7A19F53D59E5FC60E85CE1535F99774ACA94A636F430F0A0E7E98BD5738475C66EBFED3FF4220EF8347D203BF3640714D47132AAC8276145DBA29A055CFE1476CF8CB0AD90A4FFA76F58A8CD2433B802829CD7E49AB10123B83E8BA1E90D556607DF128F7342467230E30B8C347137CD106E212D30E9BD5867D7232B425B3F644F02D71FED7A3C3CCA67BE4C04BC711C4B32DF8F7194765C65566B2E27CB86DEB49EA0E2F65B80D54DBE923C421027413BAA3B93517EE08631D58D00098BA22372921321C40CC5115346587ED436A181\\\",\\\"df0701019f220101df060101df0504203112229f0605A000000727df0403000003df0314EFB8FD0155EE274F85D0832BAA7E5EFBDD8AEA51df0281f88B4DD21AB688BE8B0B369E7D7D9F747D6A2148E1CA2D27B8372F6089F49C678B4DF6E8A12EE6793BBAA5508984230296C3BBB9BA3370F98B1C646F95B00D095B01AE895562EDB308CC3629C73E55AB2E49CC8DEF8A0D5D0317767BADD6C55DF8AA27317FF9E5BF11DB107491D6CD805394F12879424DA475EE5A7F82750FF6AC6FE314B4C0E622E0F017AD6A094A0F323FD1D94F42B75A565478F4971C0632DAF5D557433E28F0393B2A19B6DB8938530C69BD765E9734AAB220A115DA49395C8BD6C16F87421FBCAB8BC5AF765C85040E40674B649E61D3506AFEB2BAF9C9723BB66A34BC0E44726B1755D3D4935B451A0C4A87CDDEBD51\\\"],\\\"aid\\\":[\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c0800000000000000009f1a0207049f3c0207049f160f0000000000000000000000000000009f3901009f150200005f3601009f73030000007f10059F830301019f3501227f114f7F154C7F16239F821801069F8208060000000000009F8209060000000000009F820A060000000000007F16239F8218010B9F8208060000000000009F8209060000000000009F820A060000000000009f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e009f0610000000000000000000000000000000009f090200969f4005F000F0A001df7a05F000F0A0019f0106000000000000df19060000000000009f660436C04000df170100df160100df150400000000df14009f1e080000000000000000df13050000000000df7903E0F8C8df12050000000000df7806000000000000df11050000000000df7600df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f33036068C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000072710109f09020002df7f01309f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F49f1d08243A000000000000df14039F37049f1e083833323031494343df13050010000000df79036068C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFEE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000000310109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000000320109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000000330109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000007f100aDF81180160DF811901089f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000000410109f09020002df7f01309f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F49f1d08243A000000000000df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000007f101f9F6D01C09F820A060000000000009F6E04D8A000009F824001019F821801E09f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0606A000000025019f090200019f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DE00FC9800df7806000000000000df1105DC50FC9800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000052410109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000006510109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000015230109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000033301019f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000072410109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\",\\\"df74010Fdf7301019f7b06000000001388df7208F4F0F0FAAFFE80009f1b04000000009f3d01009f1c084E4C2D47503733309f1a0207049f3c0207049f160e42435445535431323334353637389f3901059f150212345f3601009f73030000009f3501229f3303E0B8C85f2a020704df010100df2106000000000000df20069999999999999f4e04616263649f0607A00000072420109f090200969f4005F000F0A001df7a05F000F0A0019f0106001234567890df19060000000000009f660436C04000df170100df160100df1504000001F4df14039F37049f1e083833323031494343df13050010000000df7903E0F8C8df1205DC4004F800df7806000000000000df1105DC4000A800df76099F1A0295059A039C01df750101\\\"]}\"";
//
//        content = validateContent(content);
//        System.out.println(content);
        String value = LanguageCode.LANGUAGE_VI.getLanguageCode();
        System.out.println("->"+value);
    }
    @Test
    public void testConvertTime() {
        // Thursday, July 6, 2017 4:07:17 PM ICT
        String createdDateBank = "Friday, June 30, 2017 10:58:13 AM ICT".replace(",","");
//        String createdDateBank = "Thursday, September 14, 2017 1:51:46 PM ICT";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("EEE MMM dd yyyy hh:mm:ss a z", Locale.US);
        try {
            Date date = simpleDateFormat.parse(createdDateBank);
            long createdDate = date.getTime();
            System.out.println(createdDate);
            System.out.println(Utils.convertTimestamp(createdDate, 3));
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public String validateContent(String content) {
        if (content.startsWith("\"")) {
            content = content.substring(1);
        }
        if (content.endsWith("\"")) {
            content = content.substring(0, content.length() - 1);
        }
        if (content.contains("\\\"")) {
            content = content.replace("\\\"", "\"");
        }
        return content;
    }

    @Test
    public void testSaveAmounts() {
        ArrayList<String> arr = new ArrayList<>();
        arr.add("123");
        arr.add("456");
        arr.add("789");
        arr.add("abc");
        arr.add("abcasdf");
        arr.add("12");
        arr.add("34");
        String cache = "";
        for (String item : arr) {
            cache = saveAmount(item, cache);
            System.out.println("->" + cache);
        }
    }

    public String saveAmount(String curr, String cache) {
        curr += (cache.length()==0?"":Constants.CHAR_SPLIT_PHONE + cache);
        String[] arr = curr.split(Constants.CHAR_SPLIT_PHONE);
        if (arr.length > 3) {
            System.out.println(">3:" + curr);
            curr = curr.substring(0, curr.lastIndexOf(Constants.CHAR_SPLIT_PHONE));
        }
        return curr;
    }

    @Test
    public void testSplitString() {
        String text = "LibPrinterMpos-LibPrinterMpos-LibPrinterMpos-nextpayLibPrinterMpos-LibPrinterMpos-";

        System.out.println("----->"+text.split("nextpay")[0]);

//        showImageBase64(text);

    }

    private void showImageBase64(String imageBase64) {
//        if (!TextUtils.isEmpty(imageBase64)) {
            int numCharShow = 4;
            System.out.println( "showImageBase64: =====>"+imageBase64.length());
            int page = 0;
            for (int i = 0; i <= imageBase64.length(); i+=numCharShow) {
                System.out.println( "showImageBase64: i=" + i);
                System.out.println( imageBase64.substring(i,
                        Math.min( (page+1) * numCharShow, imageBase64.length())
                ));
                page++;
            }
            System.out.println( "showImageBase64: <=====");
//        }
    }

}