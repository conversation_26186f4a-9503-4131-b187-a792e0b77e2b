package com.mpos.screen;

import android.util.Base64;

import com.mpos.common.DataStoreApp;
import com.mpos.models.VetcObj;
//import com.ps.mpos.lib.core.control.EncodeDecode;

import org.junit.Test;

/**
 * Created by anhnguyen on 1/31/18.
 */
public class FragmentVetcTest{


    /*@Test
    public void upperCaseFirstLetterOfString() throws Exception {
        String test = "%s + %s%%/total amount";

        System.out.println( String.format(test, 8800, 1.25));
    }*/

    /*@Test
    public void calculatorFee() {
        VetcObj.Vetc vetcFee = new VetcObj().new Vetc();
        vetcFee.setFlatFee(8800);
        vetcFee.setAtmFee(0.15f);
        vetcFee.setVisaMasterJcbDomesticFee(0.7f);
        vetcFee.setVisaMasterJcbInternationalFee(2.5f);
        int amount = 1000;
        int fee = (int) (vetcFee.getFlatFee() + amount * vetcFee.getVisaMasterJcbDomesticFee()/100);
        System.out.println("result: "+fee);
    }*/


    @Test
    public void testDecyptDataFromPartner() {
        String content = "eyJhbW91bnQiOjUxODAwMCwib3JkZXJJZCI6Ijk3ODc5MDI2NiIsImRlc2NyaXB0aW9uIjoiW1Rpa2ldIFRoYW5oIHRvYW4gZG9uIGhhbmcgIzk3ODc5MDI2NiIsInVybENhbGxCYWNrIjoidGlraS1kcml2ZXI6Ly9vcmRlci91cGRhdGU/Y29uZmlybU1vZGU9MCZvcmRlcklkPVZISmhibk53YjNKMFQzSmtaWEk2TldRMk56STFPR1l5TnpGbFkyTTJZbU5qWldRM01UaGwmb3JkZXJOdW1iZXI9OTc4NzkwMjY2Jm9yZGVyVHlwZT1ERUxJVkVSWV9PUkRFUiZyZXR1cm5lZERhdGE9IiwiZXh0UGFyYW0iOnsidGltZXN0YW1wIjoxNTY3MDQ2NzY5OTgyLCJzdGF0ZSI6ImNlNmY2MjA2MGM4NWI3MmFkNzBlMGQ2NTkxZjY0Yjc1Iiwib3JkZXJJZCI6IlZISmhibk53YjNKMFQzSmtaWEk2TldRMk56STFPR1l5TnpGbFkyTTJZbU5qWldRM01UaGwifX0=";

        byte[] decodedString = Base64.decode(content, Base64.DEFAULT);

        String clearData = new String(decodedString);
        System.out.println(clearData);

    }
    /*@Test
    public void testAes() {
        String content = "Av89PHORm+2ZL6q/IGJPPTyKetF3UjSR+JzxvxYifaQgNQFOTdOz7gzbfz3NDJVsCUklz3qN3pRv7TXMoKjI+jPMLlkwMqXUJaHExbOKouaseCtdg5aO7/nF3KHhUAEbPiOK50gt7F1t2BJF6G4jFAFISSmGh9lP+mzcVx4QKQwCVIu+nHraeDM7KcyWUWeOsdRNdVawXgzYPxcNmswPOm5vx0hdQcDhEQCsICBvx2bLaiI2LmHcG9FVek1vDczWNOwUAXRSh8owafawi/UoedWQhp4zLhUfbda2jkVjVnwRtB6GVfSixrjszXKT8ISj+FvUFgMbyhGgLuWnQX9N9a/ZSpOKWna1vDWlKN2c9NYWUl8eM6g7KLE3AnWb5n7wcpMPRBEzr8QPKgtByOSytaS9BDW2pMjYGy468GAALzlGc/rOKdog5oR27Ym971Iw";
        String key = "srzn91LDYQ4hU6mV";
        try {
            String clearData = EncodeDecode.doAESDecrypt(content, key);
            System.out.println("-->"+clearData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}