<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="vn.mpos">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" /> <!-- android 12 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
    <uses-permission android:name="com.pax.permission.MAGCARD" />
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.ICC" />
    <uses-permission android:name="com.pax.permission.PICC" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- use for emv config pax -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="https" />
        </intent>
    </queries>

    <application
        android:name="com.mpos.common.MyApplication"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/APPLICATION_NAME"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:theme="@style/MyTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:label">
        <activity
            android:name="com.mpos.screen.mart.ActivitySettingSP04"
            android:exported="false"></activity>
        <activity
            android:name="com.mpos.screen.mart.ActivityBtConnectTcp"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@style/ThemeBtPrinterS85" />
        <activity
            android:name="com.mpos.screen.mart.ActivityHomeMart"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.mpos.screen.mart.ActivityQrEmart"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.mpos.screen.login.LoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateUnchanged|adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="mpos-vn" />
            </intent-filter>
        </activity>
        <activity android:name="com.mpos.screen.ActivityLoginActivation" />
        <activity
            android:name="com.mpos.screen.ActivitySubLogin"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name="com.mpos.screen.ActivityRegisterMerchant"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.mpos.screen.ActivityHomeEnter"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" />
        <activity
            android:name="com.mpos.screen.ActivityPrePayment"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleInstance" />
        <activity
            android:name="com.mpos.screen.ActivityScanQRCode"
            android:configChanges="orientation|screenSize|keyboardHidden" />
        <activity android:name="com.mpos.screen.ActivityPaymentInfo" />
        <activity android:name="com.mpos.screen.ActivitySendEmail" />
        <activity android:name="com.mpos.screen.ActivityNewsDetail" />
        <activity android:name="com.mpos.screen.ActivityEnterCardWebView" />
        <activity
            android:name="com.mpos.screen.ActivitySendLog"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>

                <!-- note: change it in call start activity send log -->
                <action android:name="vn.mpos.SEND_LOG" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.mpos.screen.ActivityPaymentHistory"
            android:screenOrientation="portrait"
            android:theme="@style/MyTheme" />
        <activity
            android:name="com.mpos.screen.ActivityMainNew"
            android:configChanges="orientation|keyboardHidden|screenSize" /> <!-- log error -->
        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
        <activity
            android:name="com.mpos.rnmodules.MyReactActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:label="@string/app_name"
            android:theme="@style/RNTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name="com.mpos.screen.ActivityResultPending" /> <!-- SERVER_KEY: AIzaSyAxk5JzFo2LjCMT2eZhMKMuetSpf12cGTU -->
        <!-- Client Registration ID: APA91bEEP3TZf4WXlqFBAXP65pRfBXG3vwtDaWEWYfxE-prUIZnPzCgFKINItmSTGJLPT11m8zOH1RMzmYSsXjlM6qlTN1Bxj4kzCl6_pwjS9oAwnkRS5M4 -->
        <!-- link test gcm below -->
        <!-- http://techzog.com/development/gcm-notification-test-tool-android/ -->
        <activity
            android:name="com.mpos.rnmodules.ActivityChangePass"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/MyTheme" /> <!-- React Native -->
        <activity android:name="com.mpos.screen.FlutterTransferActivity"
            android:windowSoftInputMode="adjustResize"/>

        <meta-data
            android:name="com.google.android.actions"
            android:resource="@xml/settings_preference" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDu4QJmJT3SCDWpd1xRK0vnpadiAigHOmE" />

        <service
            android:name="com.mpos.firebase.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service> <!-- service of XPrinter-SP-P201 -->
        <service android:name="net.posprinter.service.PosprinterService" />
<!--        <service android:name="com.mpos.screen.mart.SocketService" />-->
        <service android:name="com.mpos.screen.mart.socket.TakashimayaSocketService" />
        <service android:name="com.mpos.screen.mart.socket.EmartSocketService" />

        <receiver
            android:name="com.vnt.vntstore.sdk.StoreRegisterListenerReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.vntstore.REGISTER_LISTENER" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>
    </application>

</manifest>