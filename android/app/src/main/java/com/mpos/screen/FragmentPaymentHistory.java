package com.mpos.screen;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.emilsjolander.components.stickylistheaders.StickyListHeadersListView;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.mpos.adapters.HistoryEmartAdapter;
import com.mpos.adapters.HistoryMultiAcquirerAdapter;
import com.mpos.adapters.HistoryPaidAdapter;
import com.mpos.adapters.HistoryQrNlAdapter;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogSettlement;
import com.mpos.listeners.ItemListeners;
import com.mpos.models.DataFailTransaction;
import com.mpos.models.DataHistoryQrNl;
import com.mpos.models.ItemFailHistory;
import com.mpos.models.PaymentItem;
import com.mpos.models.PaymentItemEmart;
import com.mpos.screen.login.LoginActivity;
import com.mpos.screen.mart.ActivityQrEmart;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.LibLoginMacq;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.HistoryRes;
import com.mpos.sdk.core.modelma.HistorySend;
import com.mpos.sdk.core.modelma.LoginRes;
import com.mpos.sdk.core.modelma.Paging;
import com.mpos.sdk.core.modelma.SettleSend;
import com.mpos.sdk.core.modelma.TransItemMacq;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.ButterKnifeUtils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MyDialogShow;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.ViewCollections;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class FragmentPaymentHistory extends BaseFragment {

    String TAG = "FragmentPaymentHistory";

    final int REQUEST_CODE_DETAIL_MA = 13;

    @BindView(R.id.empty)           View vEmpty;
    @BindView(R.id.settle)          Button btnSettle;
    @BindView(R.id.total_amount)    TextView tvAmount;
    @BindView(R.id.name)            TextView tvGuideSettle;
    @BindView(R.id.tv_status)       TextView tvError;
    @BindView(R.id.listView1)       StickyListHeadersListView lv;
    @BindView(R.id.progressBar1)    ProgressBar progressBar;
    @BindView(R.id.layout_search_his_with_date)
    LinearLayout container_search_QrNl;
    @BindView(R.id.spn_type_time_qrnl)
    Spinner spinnerTypeQrNl;

    @BindViews({R.id.progressBar_more, R.id.tv_loading_more})
    List<View> vLoadMore;

    private static final String ARG_TOKEN_L2 = "tokenL2";
    private static final String ARG_TYPE = "type_fragment";

    public static final String TYPE_BANK       = "BANK";
    public static final String TYPE_BANK_FAIL  = "BANK_FAIL";
    public static final String TYPE_MPOS       = "MPOS";
    public static final String TYPE_EMART       = "EMART";
    public static final String TYPE_QR          = "QR";
    private static final String TYPE_MULTI_ACQUIRER = "MA";
    private static final String TYPE_MA_FAIL = "MA_FAIL";

    private String typeFragment = "";

//    private OnFragmentInteractionListener mListener;

    private ToastUtil mToast;
    private MyProgressDialog mPrgdl;
    private View view;
    private Context context;

    private HistoryPaidAdapter adapterPayment;
    private HistoryMultiAcquirerAdapter adapterPaymentMA;
    private HistoryEmartAdapter adapterEmart;
    private HistoryQrNlAdapter adapterQrNl;

    private ArrayList<PaymentItem> data = new ArrayList<>();
    private ArrayList<PaymentItem> dataSearch = new ArrayList<>();


    private List<TransItemMacq> dataMacq = new ArrayList<>();

    private String tokenL2;
    private int mCount;
    private int currPage = 0;
    private int totalPage = 0;
    private boolean isLoadingMore = false;
    private boolean canLoadMore = false;
    private boolean canSettle = false;
    private boolean lastIsSearch;
    private String lastKeywork;
    private String userId;

    private SaveLogController logUtil;

    private boolean isUseReader = false;

    private boolean isReCallLogin = true;

    public FragmentPaymentHistory() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param tokenL2 token level 2
     * @return A new instance of fragment FragmentPaymentHistory.
     */
    public static FragmentPaymentHistory newInstanceHistoryBank(String tokenL2) {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TOKEN_L2, tokenL2);
        args.putString(ARG_TYPE, TYPE_BANK);
        fragment.setArguments(args);
        return fragment;
    }
    public static FragmentPaymentHistory newInstanceFailHistoryBank(String tokenL2) {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TOKEN_L2, tokenL2);
        args.putString(ARG_TYPE, TYPE_BANK_FAIL);
        fragment.setArguments(args);
        return fragment;
    }

    public static FragmentPaymentHistory newInstanceHistoryMpos(String tokenL2) {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TYPE, TYPE_MPOS);
        args.putString(ARG_TOKEN_L2, tokenL2);
        fragment.setArguments(args);
        return fragment;
    }
    public static FragmentPaymentHistory newInstanceHistoryMultiAcquirer() {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TYPE, TYPE_MULTI_ACQUIRER);
        fragment.setArguments(args);
        return fragment;
    }
    public static FragmentPaymentHistory newInstanceHistoryFailMA() {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TYPE, TYPE_MA_FAIL);
        fragment.setArguments(args);
        return fragment;
    }

    public static FragmentPaymentHistory newInstanceHistoryGiftCardEmart(String tokenL2) {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TYPE, TYPE_EMART);
        args.putString(ARG_TOKEN_L2, tokenL2);
        fragment.setArguments(args);
        return fragment;
    }

    public static Fragment newInstanceHistoryQR(String tokenL2) {
        FragmentPaymentHistory fragment = new FragmentPaymentHistory();
        Bundle args = new Bundle();
        args.putString(ARG_TYPE, TYPE_QR);
        args.putString(ARG_TOKEN_L2, tokenL2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            tokenL2 = getArguments().getString(ARG_TOKEN_L2);
            typeFragment = getArguments().getString(ARG_TYPE);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.fragment_payment_history, container, false);

        ButterKnife.bind(this, view);

        isUseReader = DataStoreApp.getInstance().isUseReader();
        userId = PrefLibTV.getInstance(context).getUserId();
        logUtil = MyApplication.self().getSaveLogController();
        mToast = new ToastUtil(context);

        canSettle = PrefLibTV.getInstance(context).getPermitSettlement();

        loadSalesHistory();

        return view;
    }

    @Override
    public void onAttach(@NotNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Override
    public void onDetach() {
        super.onDetach();
        this.context = null;
    }

    private void showLoading() {
        showProgressBar(true);
    }

    private void hideLoading() {
        showProgressBar(false);
    }

    private void showLoadingMore(boolean show) {
        ViewCollections.set(vLoadMore, ButterKnifeUtils.SHOW, show);
    }

    private void showProgressBar(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    private void clearTokenLv2() {
        PrefLibTV.getInstance(context).put(PrefLibTV.TKL2, "");
    }

    private void loadSalesHistory() {
        vEmpty.setVisibility(View.GONE);
        Utils.LOGD(TAG, "getSalesHistory: typeFragment=" + typeFragment + " isUseReader=" + isUseReader);
        if (typeFragment.equals(TYPE_MULTI_ACQUIRER)) {
            loadSaleHistoryMultiAcquirer();
        } else if (typeFragment.equals(TYPE_BANK)) {
            loadSalesHistoryBank();
        }
        else {
            // fix bug session timeout in vietinbank: not auto request
//            getSalesHistoryMpos();

            if (!isUseReader) {
                if (typeFragment.equals(TYPE_BANK_FAIL)) {
                    loadFailHistoryBank();
                }
                else if (typeFragment.equals(TYPE_MA_FAIL)) {
                    loadFailHistoryMA();
                }
                else {
                    loadSalesHistoryMpos();
                }
            }
        }
    }

    private void showViewSettle(boolean show) {
        tvGuideSettle.setVisibility(show? View.VISIBLE:View.GONE);
        tvAmount.setVisibility(show? View.VISIBLE:View.GONE);
        btnSettle.setVisibility(show && canSettle? View.VISIBLE:View.GONE);
    }

    public void loadSalesHistoryMpos() {
        searchSalesHistoryMpos(false, null, 0);
    }

    public void searchSalesHistoryMpos(final boolean isSearch, final String keywork, final int page) {
        logUtil.appendLogRequestApi(Config.TRANSACTION_LIST_V2);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.TRANSACTION_LIST_V2);
            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
            jo.put("pageIndex", page);
            jo.put("merchantEmail", PrefLibTV.getInstance(context).getEmailMerchant());

            if (isUseReader) {
                jo.put("muid", userId);
            } else {
                jo.put("muid", "MVISA");
            }

            if (isSearch && !TextUtils.isEmpty(keywork)) {
                jo.put("query", keywork);
            }

            lastIsSearch = isSearch;
            lastKeywork = keywork;

            Utils.LOGD(TAG, "history-mpos: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "getSalesHistory: " + e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY_API, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        if (page > 0) {
                            showLoadingMore(true);
                        } else {
                            showLoading();
                        }
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(TAG, "onFailure: " + arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.TRANSACTION_LIST_V2 + " onFailure", arg2);
                        isLoadingMore = false;
                        if (page > 0) {
                            showLoadingMore(false);
                        } else {
                            hideLoading();
                        }
                        showViewError(getMyString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT));
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        if (page > 0) {
                            showLoadingMore(false);
                        } else {
                            hideLoading();
                        }
                        try {
                            isLoadingMore = false;
                            JSONObject response = new JSONObject(new String(arg2));
                            Utils.LOGD(TAG, "-getSalesHistoryMpos: " + response);
                            handlerDataHistory(response, isSearch);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    public void loadFailHistoryBank() {
        logUtil.appendLogRequestApi(ConstantsPay.DECLINED_HISTORY);
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.DECLINED_HISTORY);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("sessionKey", ssk);
            jo.put("pagingNo", 1);
            jo.put("itemsPerPage", 30);
            jo.put("tokenL2", tokenL2);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            Utils.LOGD(TAG, jo.toString());
            Utils.LOGD(TAG, "FAIL BANK: send=>" + ssk);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "getSalesHistory: " + e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showLoading();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            hideLoading();
                            String response = EncodeDecode.doAESDecrypt(new String(arg2), ssk);
                            handleDataFailHistory(response);
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(ConstantsPay.DECLINED_HISTORY + " error(session timeout)");
                            Utils.LOGE(TAG, "Exception", e);
                            if (context != null) {
                                MyDialogShow.showDialogErrorReLogin(getMyString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), context);
                            }
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        logUtil.appendLogRequestApiFail(ConstantsPay.DECLINED_HISTORY + " onfailure", arg2);
                        Utils.LOGE("Fail Sales history Error: ", arg3.getMessage());
                        hideLoading();
                        clearTokenLv2();
                        showViewError(getMyString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT));
                    }
                });
    }

    public void loadSalesHistoryEmart(int page) {
        logUtil.appendLogRequestApi("HistoryEmart");
        currPage = page;
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("pageIndex", String.valueOf(page));

            Utils.LOGD(TAG, "history-emart: " + jo);
            logUtil.appendLogAction("history-emart: " + jo.toString());

            entity = ApiMultiAcquirerInterface.getInstance()
                    .buildStringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "getSalesHistory: " + e1.getMessage());
        }

        showLoading();
        Utils.LOGD(TAG, "url= " + ApiMultiAcquirerInterface.URL_HISTORY_EMART_CARD);
        MposRestClient.getInstance(context).post(context, ApiMultiAcquirerInterface.URL_HISTORY_EMART_CARD, entity,
                Config.CONTENT_TYPE, new MyTextHttpResponseHandler(context) {
                    @Override
                    public void onFailApi(int i, Header[] headers, String s, Throwable throwable) {
                        isLoadingMore = false;
                        if (page > 0) {
                            showLoadingMore(false);
                        } else {
                            hideLoading();
                        }

                        Utils.LOGD(TAG, "getSalesHistoryEmart onFailure() called with: statusCode = [" + i + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + s + "], throwable = [" + throwable + "]");
                        String clearData = MacqUtil.getInstance().parseAndDecryptData(s);
                        logUtil.appendLogAction("onFail  sale Emart: " + clearData);
                        Utils.LOGD(TAG, "getSalesHistoryEmart onFailure: " + clearData);
                        showViewError(getMyString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT));
                    }

                    @Override
                    public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                        if (page > 0) {
                            showLoadingMore(false);
                        } else {
                            hideLoading();
                        }
                        hideLoading();
                        try {
                            String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                            Utils.LOGD(TAG, "-getSalesHistoryEmart: " + clearData);
                            logUtil.appendLogAction("-getSalesHistoryEmart: ");
                            isLoadingMore = false;
                            handlerDataHistoryEmart(clearData);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    public void initViewSearchQr(int page) {
        if (typeFragment.equals(TYPE_QR)) {
            initSearchHistoryView();
        }
        preLoadSalesHistoryQrNl(page);
    }

    public void preLoadSalesHistoryQrNl(int page) {
        String timeFrom, timeTo;
        int typeTime = spinnerTypeQrNl.getSelectedItemPosition();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        Date dateTo = Calendar.getInstance().getTime();
        timeTo = dateFormat.format(dateTo);
        Date dateFrom = new Date();
        switch (typeTime) {
            // 0 TODAY
            case 1:     // DAY_AGO
                dateFrom.setDate(dateTo.getDate() - 1);
                timeFrom =dateFormat.format(dateFrom);
                break;
            case 2:     // 3 DAY_AGO
                dateFrom.setDate(dateTo.getDate() - 3);
                timeFrom =dateFormat.format(dateFrom);
                break;
            case 3:     // 7 DAY_AGO
                dateFrom.setDate(dateTo.getDate() - 7);
                timeFrom =dateFormat.format(dateFrom);
                break;
            case 4:     // 2 WEEKS_AGO
                dateFrom.setDate(dateTo.getDay() - 14);
                timeFrom =dateFormat.format(dateFrom);
                break;
            case 5:     // 1 MONTHS_AGO
                dateFrom.setDate(dateTo.getMonth() - 1);
                timeFrom =dateFormat.format(dateFrom);
                break;
            default:
                timeFrom = "";
                timeFrom = "";
                break;
        }

        loadSalesHistoryQrNl(page, timeFrom, timeTo);
    }

    private void loadSalesHistoryQrNl(int page, String timeFrom, String timeTo) {
        Utils.LOGD(TAG, Config.TRANSACTION_LIST_V2 + " HistoryQrNl");
        logUtil.appendLogRequestApi(Config.TRANSACTION_LIST_V2 + "HistoryQrNl");
        currPage = page;

        String checkSum = buildCheckSumGetListOrder(timeFrom, timeTo, String.valueOf(page));
        Utils.LOGD(TAG, "checkSum= " + checkSum);
        RequestParams params = new RequestParams();
        params.put("function", Constants.FUNCTION_GET_LIST_ORDER_QR);
        params.put("merchant_site_code", DataStoreApp.getInstance().getQrNlSiteCode());
        params.put("time_created_from", timeFrom);
        params.put("time_created_to", timeTo);
        params.put("payment_method_code", Constants.METHOD_CODE_QR);
        params.put("page", String.valueOf(page));
        params.put("order_description", PrefLibTV.getInstance(context).getUserId());
        params.put("checksum", checkSum);

        Utils.LOGD("fetchBankSupportQrNl Data: ", params.toString());
        logUtil.appendLogAction("GetBanks merchant_site_code= " + DataStoreApp.getInstance().getQrNlSiteCode());

        // get list bank support
        showLoading();
        MposRestClient.getInstance(context).post(context, BuildConfig.URL_SERVER_QR_NL, params, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    if (page > 0) {
                        showLoadingMore(false);
                    }
                    isLoadingMore = false;
                    hideLoading();
                    lv.setVisibility(View.VISIBLE);
                    String contentResponse = new String(arg2);
                    JSONObject jRoot = new JSONObject(contentResponse);
                    Utils.LOGD(TAG, "onSuccess loadSalesHistoryQrNl: " + contentResponse);
                    logUtil.appendLogAction("onSuccess loadSalesHistoryQrNl: ");
                    Type listType = new TypeToken<DataHistoryQrNl>() {}.getType();
                    DataHistoryQrNl dataHistoryQrNl = new Gson().fromJson(jRoot.toString(), listType);
                    handlerDataHistoryQrNl(dataHistoryQrNl);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(TAG, "onFailure loadSalesHistoryQrNl: " + arg3.getMessage());
                logUtil.appendLogAction("loadSalesHistoryQrNl onFailure: "+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                isLoadingMore = false;
                hideLoading();
                lv.setVisibility(View.VISIBLE);
                if(ScreenUtils.canShowDialog(context)){
                    MyDialogShow.showDialogError(context.getString(R.string.msg_err_load_qr), context, false);

                }
            }
        });
    }

    private void initSearchHistoryView() {
        container_search_QrNl.setVisibility(View.VISIBLE);
        List<String> listLocation = new ArrayList<>();
        listLocation.add(context.getString(R.string.tv_today));
        listLocation.add(context.getString(R.string.tv_day_ago));
        listLocation.add(context.getString(R.string.tv_3_days_ago));
        listLocation.add(context.getString(R.string.tv_week_ago));
        listLocation.add(context.getString(R.string.tv_2_week_ago));
        listLocation.add(context.getString(R.string.tv_months_ago));
        ArrayAdapter<String> adapterLocation = new ArrayAdapter<String>(context,
                android.R.layout.simple_spinner_item, listLocation);
        adapterLocation.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTypeQrNl.setAdapter(adapterLocation);

    }

    private int calSumPageQrNl(String totalRecord) {
        int quotient = Integer.parseInt(totalRecord) / 15;
        int remainder = Integer.parseInt(totalRecord) % 15;

        if (remainder != 0) {
            quotient++;
        }
        Utils.LOGD(TAG, "sumPage= " + quotient);
        return quotient;
    }

    private String buildCheckSumGetListOrder(String timeFrom, String timeTo, String page) {
        Utils.LOGD(TAG, "buildCheckSumGetListOrder checkSum ===== " +
                          DataStoreApp.getInstance().getQrNlSiteCode() + '|' + "" + '|' +
                "" + '|' + timeFrom + '|' + timeTo + '|' +
                "QRCODE" + '|' + "" + '|' +
                page + '|' + DataStoreApp.getInstance().getQrNlPassCode());

        String checkSum = DataStoreApp.getInstance().getQrNlSiteCode() + '|' + "" + '|' +
                "" + '|' + timeFrom + '|' + timeTo + '|' +
                "QRCODE" + '|' + "" + '|' +
                page + '|' + DataStoreApp.getInstance().getQrNlPassCode();
        return Utils.sha256(checkSum);
    }

    private void handlerDataHistoryEmart(String content) {
        PaymentItemEmart paymentItemEmart = MyGson.parseJson(content, PaymentItemEmart.class);

        if (paymentItemEmart != null) {
            totalPage = Integer.parseInt(paymentItemEmart.getPaging().getPageCount());

            if (currPage > 1) {     // with emart start page = 1
                putDataLoadMoreToViewEmart(paymentItemEmart.getData());
            } else {
                if (context != null) {
                    putDataToListViewEmart(paymentItemEmart.getData());
                }
            }

            checkCanLoadMore(true);
        }
    }

    private void handlerDataHistoryQrNl(DataHistoryQrNl dataHistoryQrNl) {
        if (dataHistoryQrNl != null) {
            logUtil.appendLogAction("dataHistoryQrNl resultCode= " + dataHistoryQrNl.getResult_code());
            if (dataHistoryQrNl.getResult_code().equals(Constants.SUCCESS_QR)) {
                String totalRecord = dataHistoryQrNl.getResultData().getTotalRecord();
                Utils.LOGD(TAG, "dataHistoryQrNl= " + totalRecord);

                totalPage = calSumPageQrNl(totalRecord);

                if (currPage > 1) {     // start page = 1
                    putDataLoadMoreToViewQrNl(dataHistoryQrNl.getResultData().getData());
                } else {
                    if (context != null) {
                        putDataToListViewQrNl(dataHistoryQrNl.getResultData().getData());
                    }
                }

                checkCanLoadMore(true);
            } else {
                showDialogErrQr(dataHistoryQrNl.getResult_code());
            }
        }
    }

    private void showDialogErrQr(String result_code) {
        switch (result_code) {
            case "0101":
                MyDialogShow.showDialogError(context.getString(R.string.dialog_error_title_default), context.getString(R.string.error_qrCode_empty), context, false);
                break;
            default:
                MyDialogShow.showDialogError(context.getString(R.string.dialog_error_title_default), context.getString(R.string.msg_err_fetch_his_qr, result_code), context, false);
                break;
        }
    }

    private void putDataLoadMoreToViewEmart(ArrayList<PaymentItemEmart.TransItem> data) {
        adapterEmart.addMore(data);
    }

    private void putDataLoadMoreToViewQrNl(ArrayList<DataHistoryQrNl.ResultData.Data> data) {
        adapterQrNl.addMore(data);
    }

    private void handleDataFailHistory(String content) {

        DataFailTransaction data = MyGson.parseJson(content, DataFailTransaction.class);

        if (data != null) {
            PrefLibTV.getInstance(context).setSessionKey( data.getSessionKey());
//            Utils.LOGD(TAG, "FAIL BANK: recei=>" + PrefLibTV.getInstance(context).getSessionKey() + " - " + data.getSessionKey());
            convertDataFailHistory(data.getListDeclined());

            putDataFailToListView(data.getListDeclined());
        }
    }

    private void putDataFailToListView(List<ItemFailHistory> listDeclined) {
        adapterPayment = new HistoryPaidAdapter<>(context, typeFragment, listDeclined);
        adapterPayment.setTypeAdapter(HistoryPaidAdapter.TYPE_ADAPTER_FAIL_HIS);

        lv.setAdapter(adapterPayment);
    }

    private void convertDataFailHistory(List<ItemFailHistory> arr) {
        if (arr != null && arr.size() > 0 && arr.get(0).getCreatedDate() == 0) {
            for (ItemFailHistory itemFailHistory : arr) {
                itemFailHistory.convertTime();
            }
        }
    }

    private void convertDataFailHistoryMA(List<ItemFailHistory> arr) {
        if (arr != null && arr.size() > 0 && arr.get(0).getCreatedDate() == 0) {
            for (ItemFailHistory itemFailHistory : arr) {
                itemFailHistory.copyTime();
            }
        }
    }

    public void loadSalesHistoryBank() {
        logUtil.appendLogRequestApi(Config.GET_HISTORY);
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.GET_HISTORY);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("sessionKey", ssk);
            jo.put("pagingNo", 1);
            jo.put("itemsPerPage", 500);
            jo.put("tokenL2", tokenL2);
            jo.put("userID", userId);
            Utils.LOGD(TAG, jo.toString());
            Utils.LOGD(TAG, "sale bank: send=>" + ssk);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "getSalesHistory: " + e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showLoading();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        String content = new String(arg2);
                        try {
                            hideLoading();
//                            Utils.LOGD(TAG, "onSuccess: "+content);
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(content, ssk));
                            Utils.LOGD(TAG, "getSalesHistoryBank: " + response);
                            Utils.LOGD(TAG, "Sales Bank: recei=" + response.getString("sessionKey"));
                            PrefLibTV.getInstance(context).setSessionKey( response.getString("sessionKey"));
                            handlerDataHistory(response);
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.GET_HISTORY + " error(session timeout): " + content);
                            Utils.LOGE(TAG, "Exception", e);
                            if (context != null) {
                                MyDialogShow.showDialogErrorReLogin(getMyString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), context);
                            }
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        logUtil.appendLogRequestApiFail(Config.GET_HISTORY + " onfailure " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3), arg2);
                        Utils.LOGE(TAG, "Sales history Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        hideLoading();
                        clearTokenLv2();
                        showViewError(getMyString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT));
                    }
                });
    }

    private void loadSaleHistoryMultiAcquirer() {
        loadSaleHistoryMultiAcquirer(0);
    }

    private void loadSaleHistoryMultiAcquirer(int pageLoad) {
        if (pageLoad > 0) {
            showLoadingMore(true);
        }
        else {
            showLoading();
        }
        Paging paging = new Paging();
//        paging.setPageIndex(currPage);
        paging.setPageIndex(pageLoad);
//        paging.setPageSize(4);
        paging.setPageSize(20);
        // todo fake test
//        paging.setPageSize(10);

        HistorySend historySend = new HistorySend();
        historySend.setMuid(userId);
        historySend.setMposMid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID, String.class));
//        historySend.setSerialNumber(PrefLibTV.getInstance(context).getSerialNumber());
        historySend.setPaging(paging);
        logUtil.appendLogRequestApi("HistoryMACQ");
//        Utils.LOGD(TAG, "getSaleHistoryMultiAcquirer: " + MyGson.getGson().toJson(historySend));
//        Utils.LOGD(TAG, "getSaleHistoryMultiAcquirer: " + ApiMultiAcquirerInterface.URL_GET_HISTORY_V2);

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(historySend);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_HISTORY_V2, historySend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "GET_HISTORY onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                if (pageLoad > 0) {
                    showLoadingMore(false);
                }
                else {
                    hideLoading();
                }
                isLoadingMore = false;

                DataError dataError = new DataError();
                if (statusCode == HttpStatus.SC_UNAUTHORIZED || statusCode == HttpStatus.SC_BAD_REQUEST || statusCode == HttpStatus.SC_PAYMENT_REQUIRED || statusCode == HttpStatus.SC_PRECONDITION_FAILED) {
                    if (isReCallLogin) {
                        dataError.build(statusCode, rawJsonData, getMyString(R.string.msg_warning_401_history, String.valueOf(statusCode)));
                        MyDialogShow.showDialogRetryCancel(dataError.getMsg(), context, view -> handlerFailApiFetchHistory(), true);
                    }
                    else {
                        dataError.build(statusCode, rawJsonData, getMyString(R.string.msg_warning_logout_unauthorized, String.valueOf(statusCode)));
                        MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true, view -> gotoLogin());
                    }
                }
                else {
                    dataError.build(statusCode, rawJsonData, getMyString(R.string.error_ma_default, String.valueOf(statusCode)));
                    MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
                }

                logUtil.appendLogResponseFail("HistoryMACQ OnFail: " + dataError.getMsg() + "statusCode= " + statusCode);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "GET_HISTORY onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                if (pageLoad > 0) {
                    showLoadingMore(false);
                }
                else {
                    hideLoading();
                }
                isReCallLogin = true;
                isLoadingMore = false;
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, "GET_HISTORY onSuccess: " + clearData);

                HistoryRes historyRes = MyGson.parseJson(clearData, HistoryRes.class);
                handlerDataHistoryMa(historyRes);
            }
        });
    }

    protected void gotoLogin(){
        logUtil.appendLogAction("gotoLogin");
        String dockAddress = PrefLibTV.getInstance(context).getBluetoothAddressPAX();
        String ssIdbase = PrefLibTV.getInstance(context).getSSIDPAX();
        String userName = PrefLibTV.getInstance(context).getUserId();
        PrefLibTV.getInstance(context).clearDataAuto();
        DataStoreApp.getInstance().clearData();
        MyApplication.self().clearMposSkd();
        PrefLibTV.getInstance(context).createBluetoothAddressPAX(dockAddress);
        PrefLibTV.getInstance(context).createSSIDPAX(ssIdbase);
        PrefLibTV.getInstance(context).setUserId(userName);
        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        context.startActivity(intent);
        finish();
    }

    void handlerFailApiFetchHistory() {
        logUtil.appendLogAction("handlerFailApiFetchHistory reInitLogin");
        isReCallLogin = false;
        LibLoginMacq libLoginMacq = new LibLoginMacq(this.context, PrefLibTV.getInstance(context).getUserId(),
                PrefLibTV.getInstance(context).getPW(), PrefLibTV.getInstance(context).getSerialNumber(),
                new LibLoginMacq.ItfHandlerResultLoginMacq() {
            public void appendLogMacq(String log) {
                logUtil.appendLogAction(log);
            }

                    @Override
                    public void onFailLoginMacq(int typeFail, int statusCode, String rawJsonData) {
                        logUtil.appendLogResponseFail("fail MA: code=" + statusCode + " --- typeFail=" + typeFail +  "->" + rawJsonData);
                        logUtil.appendLogResponseFail("fail MA: code="+statusCode+" ->"+rawJsonData);
                        DataError dataError = new DataError();
                        if (statusCode == 0) {
                            dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
                        }
                        else {
                            dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                        }
                        MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
                    }

            public void onSuccessLoginMacq(LoginRes loginRes) {
                logUtil.appendLogAction("onSuccessLoginMacq -> loadSaleHistoryMultiAcquirer");
                loadSaleHistoryMultiAcquirer();
            }
        });
        libLoginMacq.initAuthenMA();
    }

    void loadFailHistoryMA() {
        showLoading();
        Paging paging = new Paging();
        paging.setPageIndex(currPage);
        paging.setPageSize(30);

        HistorySend historySend = new HistorySend();
        historySend.setMuid(userId);
        historySend.setPaging(paging);
        historySend.setMposMid(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID, String.class));

        Utils.LOGD(TAG, "getFailHistoryMA: " + MyGson.getGson().toJson(historySend));

//        StringEntity entity = ApiMultiAcquirerInterface.getInstance().buildStringEntity(historySend);
        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_GET_DECLINED_HISTORY_V2, historySend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "GET_DECLINED_HISTORY onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getMyString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, ApiMultiAcquirerInterface.URL_GET_DECLINED_HISTORY_V2 + " onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, ApiMultiAcquirerInterface.URL_GET_DECLINED_HISTORY_V2 + " onSuccess: " + clearData);

                DataFailTransaction failTransaction = MyGson.parseJson(clearData, DataFailTransaction.class);
                handlerDataHistoryDeclineMa(failTransaction);
            }
        });
    }

    private void handlerDataHistoryMa(HistoryRes historyRes) {
        if (historyRes == null || historyRes.getData() == null || historyRes.getData().size() == 0) {
            showViewEmpty();
        }
        else {
            if (historyRes.getSummary() == null || historyRes.getSummary().getTotalAmount() == 0) {
                showViewSettle(false);
            }
            else {
                showViewSettle(true);
                mCount = historyRes.getSummary().getTotalTrans();
                tvAmount.setText(String.format("%s %s", Utils.zenMoney(historyRes.getSummary().getTotalAmount()), ConstantsPay.CURRENCY));
            }
            if (historyRes.getPaging() != null) {
                currPage = historyRes.getPaging().getPageIndex();
                totalPage = historyRes.getPaging().getPageCount();
            }
            if (currPage == 0) {
                dataMacq = historyRes.getData();
            }
            else {
                dataMacq.addAll(historyRes.getData());
            }
            if (context != null) {
                Utils.LOGD(TAG, "handlerDataHistoryMa: pageIndex=" + currPage);
                if (currPage > 0) {
                    putDataMacqLoadMoreToView(historyRes.getData());
                }
                else {
                    putDataToListViewMA(historyRes.getData());
                }
            }
            checkCanLoadMore(false);
            if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.hasWaitSignatureMacq, Boolean.class, false)) {
                processCheckHasWaitSignatureMacq();
            }
        }
    }

    private void processCheckHasWaitSignatureMacq() {
        boolean hasTranWaitSignature = false;
        for (TransItemMacq transItem : dataMacq) {
            if (Constants.TRANS_STATUS_PENDING_SIGNATURE.equals(transItem.getStatus())) {
                hasTranWaitSignature = true;
                break;
            }
        }
        Utils.LOGD(TAG, "processCheckHasWaitSignatureMacq: hasTranWaitSignature=" + hasTranWaitSignature);
        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasWaitSignatureMacq, hasTranWaitSignature);
    }

    private void handlerDataHistoryDeclineMa(DataFailTransaction historyRes) {
        if (historyRes == null || historyRes.getListDeclinedMA() == null || historyRes.getListDeclinedMA().size() == 0) {
            showViewEmpty();
        }
        else {
//            if (historyRes.getPaging() != null) {
//                currPage = historyRes.getPaging().getPageIndex();
//                totalPage = historyRes.getPaging().getPageCount();
////                int pageCount = Integer.parseInt(historyRes.getPaging().getPageCount());
//                Utils.LOGD(TAG, "handlerDataHistoryMa: currPage=" + currPage + " pageCount=" + totalPage);
//                checkCanLoadMore();
//                if (currPage == 0 && canLoadMore) {
//                    initLoadMore();
//                }
//            }

            showViewSettle(false);
            convertDataFailHistoryMA(historyRes.getListDeclinedMA());
            putDataFailToListView(historyRes.getListDeclinedMA());

        }
    }

    private void handlerDataHistory(JSONObject response) {
        handlerDataHistory(response, false);
    }

    private boolean handlerDataHistory(JSONObject response, boolean isSearch) {
        if (response.has("error")) {
            try {
                final JSONObject jo = response.getJSONObject("error");
                String msg = getMyString(R.string.error) + " " + String.format("%02d", jo.getInt("code"))
                        + ": " + LibError.getErrorMsg(jo.getInt("code"), context);
                logUtil.appendLogRequestApi(Config.GET_HISTORY + " error server: " + msg);
                if (LibError.isSessionExpired(jo.getInt("code"))) {
                    MyDialogShow.showDialogErrorReLogin(msg, context);
                } else {
                    MyDialogShow.showDialogErrorFinish(msg, context);
                }
            } catch (JSONException e) {
                Utils.LOGE(TAG, "Exception", e);
                MyDialogShow.showDialogErrorFinish("", context);
            }
        } else {
            try {
                logUtil.appendLogRequestApi("handlerDataHistory success: " + typeFragment);
                String keyTransListServer = typeFragment.equals(TYPE_BANK) ? "transactionList" : "data";
                JSONArray jsonArray = response.getJSONArray(keyTransListServer);
                if (jsonArray.length() == 0) {
                    if (typeFragment.equals(TYPE_BANK)) {
                        showViewEmpty();
                    } else {
                        showViewError(getMyString(R.string.txt_no_data));
                    }
                    return true;
                }

                vEmpty.setVisibility(View.GONE);

                JSONObject jItem;
                data = new ArrayList<>();

                if (typeFragment.equals(TYPE_BANK)) {
                    tokenL2 = response.getString("tokenL2");

                    String amount = response.getString("totalAmount");
                    if (!TextUtils.isEmpty(amount) && !"0".equals(amount)) {
                        showViewSettle(true);
                        String builderAmount = response.getString("totalAmount") +
                                " " +
                                ConstantsPay.CURRENCY_SPACE_POST;
                        tvAmount.setText(builderAmount);
                        mCount = response.getInt("totalTransactionCount");
                    } else {
                        showViewSettle(false);
                    }

                    for (int j = 0, length = jsonArray.length(); j < length; j++) {
                        jItem = jsonArray.getJSONObject(j);
                        PaymentItem item = new PaymentItem(Utils.returnDate(jItem.getLong("transactionDate")),
                                Utils.convertTimestamp(jItem.getLong("transactionDate"), 2),
                                Utils.convertTimestamp(jItem.getLong("transactionDate"), 1),
                                jItem.getString("transactionID"), "",
                                ConstantsPay.CURRENCY,
                                jItem.getString("amountAuthorized"),
                                jItem.getString("applicationLabel"),
                                jItem.getString("maskedPAN"),
                                jItem.has("approvalCode") ? jItem.getString("approvalCode") : "",
                                jItem.getString("invoiceNumber"),
                                jItem.getInt("transactionStatus"),
                                jItem.has("trxType") ? jItem.getString("trxType") : "",
                                ""
                        );
                        item.setTransactionRequestID(JsonParser.getDataJson(jItem, "transactionRequestID"));
                        item.setUdid(JsonParser.getDataJson(jItem, "udid"));
                        data.add(item);
                    }
                    if (context != null) {
                        putDataToListView(data);
                    }
                }
                // mpos
                else {
                    currPage = response.getInt("pageIndex");
                    totalPage = response.getInt("pageCount");

//                    String amountMinCashierReward = DataStoreApp.getInstance().getFeedbackAmountMin();
                    for (int j = 0, length = jsonArray.length(); j < length; j++) {
                        jItem = jsonArray.getJSONObject(j);


                        PaymentItem item = new PaymentItem(Utils.returnDate(jItem.getLong("createdDate")),
                                Utils.convertTimestamp(jItem.getLong("createdDate"), 2),
                                Utils.convertTimestamp(jItem.getLong("createdDate"), 1),
                                jItem.getString("txid"), "",
                                ConstantsPay.CURRENCY,
                                Utils.zenMoney(JsonParser.getDataJson(jItem, "amount")),
                                JsonParser.getDataJson(jItem, "issuerCode"),
                                JsonParser.getDataJson(jItem, "pan"),
                                JsonParser.getDataJson(jItem, "authCode"),
                                JsonParser.getDataJson(jItem, "rrn"),
                                jItem.getInt("status"),
                                jItem.has("applicationUsageControl") ? jItem.getString("applicationUsageControl") : "",  // trxType
                                JsonParser.getDataJson(jItem, "accquirer")
                        );
                        item.setTransactionRequestID(JsonParser.getDataJson(jItem, "transactionRequestID"));
                        item.setTransactionPushType(JsonParser.getDataJson(jItem, "transactionPushType"));
                        item.setTransactionType(JsonParser.getDataJson(jItem, "transactionType"));

                        item.setFeedbackStatus(JsonParser.getDataJson(jItem, "feedbackStatus"));
                        item.setMobileUserPhone(JsonParser.getDataJson(jItem, "mobileUserPhone"));
                        item.setUdid(JsonParser.getDataJson(jItem, "udid"));
                        item.setQrStaticType(JsonParser.getDataJson(jItem, "qrStaticType", ""));
                        item.setIssuerCode(JsonParser.getDataJson(jItem, "issuerCode", ""));
                        item.setIssuerName(JsonParser.getDataJson(jItem, "issuerName", ""));
                        item.setIssuerNameEn(JsonParser.getDataJson(jItem, "issuerNameEn", ""));
                        String wfId = JsonParser.getDataJson(jItem, "idWF");
                        if (!TextUtils.isEmpty(wfId)) {
                            item.setWfId(wfId);
                        }

                        data.add(item);
                    }
                    if (currPage > 0) {
                        putDataLoadMoreToView(data);
                    } else {
                        if (context != null) {
                            putDataToListView(data);
                        }
                    }

                    checkCanLoadMore(false);
                }

            } catch (JSONException e) {
                logUtil.appendLogRequestApi(Config.GET_HISTORY + " error(parse json list data) " + typeFragment);
                Utils.LOGE(TAG, "Exception", e);
                MyDialogShow.showDialogRetryCancelFinish(null, getMyString(R.string.error_try_again),
                        context, v -> {
                            if (typeFragment.equals(TYPE_BANK)) {
                                loadSalesHistoryBank();
                            } else{
                                loadSalesHistoryMpos();
                            }
                }, true);
            }
        }
        return false;
    }

    private void clearDataListView() {
        if (adapterPayment != null) {
            adapterPayment.getData().clear();
            adapterPayment.notifyDataSetChanged();
        }
    }

    private void putDataToListViewEmart(ArrayList<PaymentItemEmart.TransItem> data) {
        adapterEmart = new HistoryEmartAdapter(context, data, ((position, obj) -> {}));

        lv.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case SCROLL_STATE_IDLE:
                    case SCROLL_STATE_TOUCH_SCROLL:
                        Glide.with(context).resumeRequests();
                        break;
                    case SCROLL_STATE_FLING:
                        Glide.with(context).pauseRequests();
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {

            }
        });

        lv.setOnItemClickListener((adapterView, view1, i, l) -> {
            PaymentItemEmart.TransItem item = (PaymentItemEmart.TransItem) adapterView.getItemAtPosition(i);
            gotoTransDetailEmart(item, 98);
        });

        lv.setAdapter(adapterEmart);

        initLoadMoreEmart();
    }

    private void putDataToListViewQrNl(ArrayList<DataHistoryQrNl.ResultData.Data> data) {
        adapterQrNl = new HistoryQrNlAdapter(data, context);

        lv.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case SCROLL_STATE_IDLE:
                    case SCROLL_STATE_TOUCH_SCROLL:
                        Glide.with(context).resumeRequests();
                        break;
                    case SCROLL_STATE_FLING:
                        Glide.with(context).pauseRequests();
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {

            }
        });

        lv.setOnItemClickListener((adapterView, view1, i, l) -> {
            Utils.LOGD(TAG, "setOnItemClickListener");
            DataHistoryQrNl.ResultData.Data itemData = (DataHistoryQrNl.ResultData.Data) adapterView.getItemAtPosition(i);
            gotoTransDetailQrNl(itemData, 1110);
        });

        lv.setAdapter(adapterQrNl);

        initLoadMoreQrNl();
    }

    private void initLoadMoreEmart() {
        lv.setOnScrollListener(new AbsListView.OnScrollListener() {

            public void onScrollStateChanged(AbsListView view, int scrollState) { }

            // Check if we reached the end of the list
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {

                if (canLoadMore && firstVisibleItem + visibleItemCount == totalItemCount && totalItemCount > 0) {
                    Utils.LOGD(TAG, "onScroll: --> bottom + flag=" + isLoadingMore);
                    if (!isLoadingMore) {
                        isLoadingMore = true;
                        loadSalesHistoryEmart(currPage + 1);
                    }
                }
            }
        });
    }

    private void initLoadMoreQrNl() {
        lv.setOnScrollListener(new AbsListView.OnScrollListener() {

            public void onScrollStateChanged(AbsListView view, int scrollState) { }

            // Check if we reached the end of the list
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {

                if (canLoadMore && firstVisibleItem + visibleItemCount == totalItemCount && totalItemCount > 0) {
                    Utils.LOGD(TAG, "onScroll: --> bottom + flag=" + isLoadingMore);
                    if (!isLoadingMore) {
                        isLoadingMore = true;
                        preLoadSalesHistoryQrNl(currPage + 1);
                    }
                }
            }
        });
    }

    private void putDataToListView(ArrayList<PaymentItem> data) {
        adapterPayment = new HistoryPaidAdapter<>(context, typeFragment, data);
//        lv.setOnScrollListener(new PauseOnScrollListener(ImageLoader.getInstance(), true, true));
        lv.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case SCROLL_STATE_IDLE:
                    case SCROLL_STATE_TOUCH_SCROLL:
                        Glide.with(context).resumeRequests();
                        break;
                    case SCROLL_STATE_FLING:
                        Glide.with(context).pauseRequests();
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {

            }
        });
        lv.setAdapter(adapterPayment);
        lv.setOnItemClickListener((arg0, arg1, arg2, arg3) -> {

            PaymentItem paymentItemTemp = (PaymentItem) arg0.getItemAtPosition(arg2);

            if (!TextUtils.isEmpty(paymentItemTemp.getTransactionPushType())) {
                gotoTransDetail(paymentItemTemp, 3, Boolean.TRUE, Boolean.FALSE);

            } else {
                if (TYPE_MPOS.equals(typeFragment)) {
                    if (TextUtils.isEmpty(paymentItemTemp.getWfId()) || !isUseReader) {
                        gotoTransDetail(paymentItemTemp, 3, Boolean.TRUE, Boolean.TRUE);
                    } else {
                        TransItemMacq transItemMacq = new TransItemMacq();
                        transItemMacq.setWfId(paymentItemTemp.getWfId());
                        transItemMacq.setTxid(paymentItemTemp.getId());
                        gotoTransDetailMA(transItemMacq, REQUEST_CODE_DETAIL_MA);
                    }
                } else {
                    gotoTransDetail(paymentItemTemp, 2, Boolean.FALSE, Boolean.FALSE);
                }
            }
        });

        if (typeFragment.equals(TYPE_MPOS)) {
            initLoadMore();
        }
    }

    private void putDataToListViewMA(List<TransItemMacq> data) {
        adapterPaymentMA = new HistoryMultiAcquirerAdapter(context, data, (position, obj) -> {
//            gotoTransDetailMA((TransItemMacq) obj, REQUEST_CODE_DETAIL_MA);
        });

        lv.setOnItemClickListener((adapterView, view1, i, l) -> {
            TransItemMacq itemMacq = (TransItemMacq) adapterView.getItemAtPosition(i);
            gotoTransDetailMA(itemMacq, REQUEST_CODE_DETAIL_MA);
        });

        lv.setAdapter(adapterPaymentMA);
        initLoadMore();
    }

    private void putDataLoadMoreToView(ArrayList<PaymentItem> data) {
        adapterPayment.addMore(data);
    }

    private void putDataMacqLoadMoreToView(List<TransItemMacq> data) {
        adapterPaymentMA.addMore(data);
    }

    private void initLoadMore() {
        lv.setOnScrollListener(new AbsListView.OnScrollListener() {

            public void onScrollStateChanged(AbsListView view, int scrollState) { }

            // Check if we reached the end of the list
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {

                if (canLoadMore && firstVisibleItem + visibleItemCount == totalItemCount && totalItemCount > 0) {
//                    searchDistance = sharedPrefs.getDistance(getActivity());
                    Utils.LOGD(TAG, "onScroll: --> bottom + flag=" + isLoadingMore);
                    if (!isLoadingMore) {
                        isLoadingMore = true;
                        Utils.LOGD(TAG, "onScroll: do load more");
                        if (typeFragment.equals(TYPE_MULTI_ACQUIRER)) {
                            loadSaleHistoryMultiAcquirer(currPage + 1);
                        }
                        else {
                            searchSalesHistoryMpos(lastIsSearch, lastKeywork, currPage + 1);
                        }
                    }
                }
            }
        });
    }

    private void checkCanLoadMore(boolean currPageStart1) {
        Utils.LOGD(TAG, "checkCanLoadMore: curr=" + currPage + " total=" + totalPage);
        if (currPageStart1) {
            canLoadMore = currPage < totalPage;
        } else {
            canLoadMore = currPage < totalPage - 1;
        }
    }

    private void showViewEmpty() {
        vEmpty.setVisibility(View.VISIBLE);
        tvError.setText(getMyString(R.string.history_settle_empty));
        btnSettle.setVisibility(View.GONE);
    }

    private void showViewError(String msgError) {
        vEmpty.setVisibility(View.VISIBLE);
        tvError.setText(msgError);
    }

    public void searchPaymentByKey(String keyword) {
        if (typeFragment.equals(TYPE_BANK)) {
            searchBank(keyword);
        }
        else if (typeFragment.equals(TYPE_MPOS)) {
            searchMpos(keyword);
        }
        else if (typeFragment.equals(TYPE_MULTI_ACQUIRER)) {
            searchOfflineMacq(keyword);
        }
//        else {
//            mToast.showToast(getMyString(R.string.alert_coming_soon));
//        }
    }

    public void searchBank(String keyword) {
        if (data != null && data.size() > 0) {
            dataSearch = new ArrayList<>();
            for (int i = 0; i < data.size(); i++) {
                PaymentItem item = data.get(i);
                if (item != null
                        && (item.getAmount().replaceAll("\\D", "").contains(keyword)
                        || item.getNumber().contains(keyword)
                        || item.getInvoiceNo().contains(keyword)
                )) {
                    dataSearch.add(item);
                }
            }
            if (context != null) {
                putDataToListView(dataSearch);
            }
        }
    }

    public void searchOfflineMacq(String keyword) {
//        dataMacq = adapterPaymentMA.getData();
        if (dataMacq != null && dataMacq.size() > 0) {
            ArrayList<TransItemMacq> dataSearch = new ArrayList<>();
            for (int i = 0; i < dataMacq.size(); i++) {
                TransItemMacq item = dataMacq.get(i);
                if (item != null
                        && (item.getAmount().replaceAll("\\D", "").contains(keyword)
                        || item.getPan().contains(keyword)
//                        || item.getInvoiceNo().contains(keyword)
                )) {
                    dataSearch.add(item);
                }
            }
            if (context != null) {
                putDataToListViewMA(dataSearch);
            }
        }
    }

    public void searchMpos(String keyword) {
        searchSalesHistoryMpos(true, keyword, 0);
    }

    private void gotoTransDetail(PaymentItem pi, int requestCode, boolean hasTransMVisa, boolean isDomesticInMVisa) {
        Utils.LOGD(TAG, "gotoTransDetail() called with: pi = [" + pi + "], requestCode = [" + requestCode + "], hasTransMVisa = [" + hasTransMVisa + "], isDomesticInMVisa = [" + isDomesticInMVisa + "]");
        Intent i = new Intent(context, ActivityPaymentInfo.class);
//        if (TYPE_MPOS.equals(typeFragment)) {
        i.putExtra(IntentsMP.EXTRA_PAYMENT_INFO, pi);
        i.putExtra(IntentsMP.EXTRA_IS_DOMESTIC_IN_MVISA, isDomesticInMVisa);
        i.putExtra("type", TypePaymentInfor.DETAIL_BANK);
//        }

        if (hasTransMVisa) {
            /*
             * note: VIEW DETAIL PAYMENT BASE ON mVISA
             */
//            Intent intentMVISA = new Intent(context, ActivityPaymentInfo.class);
            i.putExtra(ActivityPaymentInfo.KEY_TXID, pi.getId());
            //startActivityForResult(i, requestCode);
        }
        else {
            /*
             * note: VIEW DETAIL PAYMENT OTHERS
             */
//            Intent i = new Intent(context, ActivityPaymentInfo.class);
            i.putExtra("tid", pi.getId());
            i.putExtra("tokenL2", tokenL2);
            i.putExtra("transactionRequestId", pi.getTransactionRequestID());
        }
        startActivityForResult(i, requestCode);
    }

    private void gotoTransDetailMA(TransItemMacq transItem, int requestCode) {
        Utils.LOGD(TAG, "gotoTransDetailMA() called with: transItem = [" + transItem + "], requestCode = [" + requestCode + "]");
        Intent i = new Intent(context, ActivityPaymentInfo.class);
        i.putExtra("type", TypePaymentInfor.DETAIL_MA);
        i.putExtra(IntentsMP.EXTRA_TRANS_INFO, transItem);
        i.putExtra("tid", transItem.getTid());
        startActivityForResult(i, requestCode);
    }

    private void gotoTransDetailEmart(PaymentItemEmart.TransItem itemEmart, int requestCode) {
        Intent i = new Intent(context, ActivityPaymentInfo.class);
        i.putExtra(IntentsMP.EXTRA_TYPE_HISTORY_EMART, itemEmart);
        i.putExtra("type", TypePaymentInfor.GIFT_CARD_EMART);
        startActivityForResult(i, requestCode);
    }

    private void gotoTransDetailQrNl(DataHistoryQrNl.ResultData.Data data, int requestCode) {
        Intent i = new Intent(context, ActivityPaymentInfo.class);
        i.putExtra("type", TypePaymentInfor.QR_NL);
        i.putExtra(IntentsMP.EXTRA_TYPE_HISTORY_QR_NL, data);
        startActivityForResult(i, requestCode);
    }

    @OnClick({R.id.settle, R.id.btn_search_qr_nl})
    protected void onClickView(View v) {
        if (v.getId() == R.id.settle) {
            if (canSettle) {
                showDialogConfirmSettle();
            } else {
                mToast.showToast("Can't settle");
            }
        } else if (v.getId() == R.id.btn_search_qr_nl) {
            lv.setVisibility(View.GONE);
            preLoadSalesHistoryQrNl(1);
        }
    }

    Dialog dialogSettle;

    private void showDialogConfirmSettle() {
        Utils.LOGD(TAG, "showDialogConfirmSettle");
        if (dialogSettle != null) {
            dialogSettle = null;
        }

        final DialogSettlement dialogSettlement = new DialogSettlement();
        dialogSettlement.initVariable(tvAmount.getText().toString(), mCount);
        dialogSettlement.setClickListener( () -> {
            if (typeFragment.equals(TYPE_MULTI_ACQUIRER)) {
                sendSettleMA();
            }
            else {
                creditSettlement();
            }
        }
        );
        dialogSettlement.show(getChildFragmentManager(), "DialogSettlement");
    }

    public void creditSettlement() {
        if (mPrgdl == null) {
            mPrgdl = new MyProgressDialog(context);
        }
        logUtil.appendLogRequestApi(Config.CREDIT_SETTLEMENT);
        StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.CREDIT_SETTLEMENT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", userId);
            jo.put("sessionKey", ssk);
            jo.put("tokenL2", tokenL2);
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "creditSetttlement: " + e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPrgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
                            PrefLibTV.getInstance(context).setSessionKey( response.getString("sessionKey"));
                            mPrgdl.hideLoading();
                            Utils.LOGD("settlement: ", response.toString());
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    String msg = getMyString(R.string.error) + " " + String.format("%02d", jo.getInt("code"))
                                            + ": " + LibError.getErrorMsg(jo.getInt("code"), context);
                                    logUtil.appendLogRequestApi(Config.CREDIT_SETTLEMENT + " error server:" + msg);
                                    if (LibError.isSessionExpired(jo.getInt("code"))) {
                                        MyDialogShow.showDialogErrorReLogin(msg, context);
                                    } else {
                                        MyDialogShow.showDialogError(msg, context);
                                    }
                                } catch (JSONException e) {
                                    Utils.LOGE("Exception", e.getMessage());
                                    MyDialogShow.showDialogError("", context);
                                }
                            } else {
                                logUtil.appendLogRequestApi(Config.CREDIT_SETTLEMENT + " success");
                                showDialogSuccessSettle();
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.CREDIT_SETTLEMENT + " exception(timeout)");
                            Utils.LOGE("Exception", e.getMessage());
                            MyDialogShow.showDialogErrorReLogin(getMyString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), context);
                        }
                    }

                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE("settlement Error: ", arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.CREDIT_SETTLEMENT + " onFailure", arg2);
                        mPrgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancel("", getMyString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                                context, v -> creditSettlement(), true);
                    }
                });
    }

    private void sendSettleMA() {
        if (mPrgdl == null) {
            mPrgdl = new MyProgressDialog(context);
        }
        mPrgdl.showLoading();
        SettleSend workFlow = new SettleSend(PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_TID, String.class));
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(workFlow));
        MposRestClient.getInstance(context).post(context, ApiMultiAcquirerInterface.URL_DO_SETTLEMENT, entity, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "doSettle onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPrgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getMyString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "doSettle onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());
                Utils.LOGD(TAG, "doSettle onSuccess: " + clearData);
                mPrgdl.hideLoading();
                if (statusCode == HttpStatus.SC_OK) {
                    showDialogSuccessSettle();
                }
                else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getMyString(R.string.error_ma_default, String.valueOf(statusCode)));
                    MyDialogShow.showDialogError(getMyString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
                }
            }
        });
    }

    private void showDialogSuccessSettle() {
        MyDialogShow.showDialogInfo(context, getMyString(R.string.settle_success), true);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        Utils.LOGD(TAG, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        super.onActivityResult(requestCode, resultCode, data);
        switch (resultCode) {
            //backToMainAfterQuickWithdrawal
            case 2:
                finish();
                break;
            // refresh data
            case 3:
                if (typeFragment.equals(TYPE_BANK)) {
                    clearDataListView();
                    showViewSettle(false);
                    loadSalesHistoryBank();
                }
                else if (typeFragment.equals(TYPE_MULTI_ACQUIRER)) {
                    clearDataListView();
                    showViewSettle(false);
                    loadSaleHistoryMultiAcquirer();
                }
                else {
                    loadSalesHistoryMpos();
                }
                break;
            default:
                break;
        }
    }

    private void finish() {
        Utils.LOGD(TAG, "finish() called");
        if (context != null) {
            ((Activity) context).finish();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (logUtil != null) {
            logUtil.saveLog();
        }
        if (dialogSettle != null && dialogSettle.isShowing()) {
            dialogSettle.dismiss();
        }
    }

    enum TypePaymentInfor {
        DETAIL_MA,
        DETAIL_BANK,
        GIFT_CARD_EMART,
        QR_NL,
    }
}
