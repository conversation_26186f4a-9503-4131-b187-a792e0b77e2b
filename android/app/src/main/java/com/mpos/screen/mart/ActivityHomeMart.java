package com.mpos.screen.mart;

import static com.mpos.screen.ActivityPrePayment.RC_PAY;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.LocationManagerMp;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyUtils;
import com.pps.core.ToastUtil;

import java.util.Arrays;

import vn.mpos.R;

public class ActivityHomeMart extends AppCompatActivity {
    private static final String TAG = ActivityHomeMart.class.getSimpleName();

    final int RQ_PERMISSION_LOCATION = 15;
    final int RQ_PERMISSION_STORAGE = 16;
//    final int RQ_PERMISSION_PAX = 17;
    final int RQ_PERMISSION_P8 = 18;
    final int RQ_PERMISSION_FOREGROUND = 19;
    private int countRequestPermissionLocation = 0;
    private int countRequestPermissionForeground = 0;
    private int countRequestPermissionStorage = 0;
    private long mStartPressBack = 0;

    private EMartPresenter presenter;
    private ToastUtil mToast;

    protected SaveLogController logUtil;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home_mart);
        logUtil = MyApplication.self().getSaveLogController();

        initView();

        checkPermissions();
        presenter.checkUpgradeEmvConfigPax();

        if (DevicesUtil.isSP02() || DevicesUtil.isPax()) {
            disableButtonHomeRecent();
        }
    }

    private void checkPermissions() {
        boolean isDisableCheckGps = DataStoreApp.getInstance().getIsDisableCheckGps();
        Utils.LOGD(TAG, "isDisableCheckGps = " + isDisableCheckGps);
        if (!isDisableCheckGps) {
            processLocationPermissions();
        } else if (DevicesUtil.isPax()) {
            processStoragePermission();
        }
    }

    private void processStoragePermission() {
        if (MyUtils.checkHaveStoragePermission(getApplicationContext())) {      //auto run with pax
            Utils.LOGD(TAG, "Have Storage Permission");
            logUtil.appendLogAction("Have Storage Permission");
//            presenter.checkUpgradeEmvConfigPax();
        } else {
            Utils.LOGD(TAG, "Have not Storage Permission");
            logUtil.appendLogAction("Have not Storage Permission count= " + countRequestPermissionStorage);
//            Toast.makeText(this, getString(R.string.need_allow_access_storage), Toast.LENGTH_LONG).show();
            if (countRequestPermissionStorage < 3) {
                countRequestPermissionStorage++;
                MyUtils.requestStoragePermission(this, RQ_PERMISSION_STORAGE);
            }
        }
    }

    private void processLocationPermissions() {
        if (MyUtils.checkHaveLocationPermission(ActivityHomeMart.this)) {
            Utils.LOGD(TAG, "Have Location Permission");
            logUtil.appendLogAction("Have Location Permission");

            // fetch location
            String lat = PrefLibTV.getInstance(ActivityHomeMart.this).getLatitude();
            String lng = PrefLibTV.getInstance(ActivityHomeMart.this).getLongtitude();
            if (TextUtils.isEmpty(lat) || TextUtils.isEmpty(lng) || lat.equals(Constants.SVALUE_0) || lng.equals(Constants.SVALUE_0)) {
                processGetLocation();
            }

            if (DevicesUtil.isPax()) {
                processStoragePermission();
            }
        } else {
            Utils.LOGD(TAG, "Have not Location Permission");
            logUtil.appendLogAction("Have not Location Permission count= " + countRequestPermissionLocation);
            Toast.makeText(this, getString(R.string.need_allow_access_location), Toast.LENGTH_SHORT).show();
            if (countRequestPermissionLocation >= 3) {
                finish();
            }
            else {
                countRequestPermissionLocation++;
                MyUtils.requestLocationPermission(ActivityHomeMart.this, RQ_PERMISSION_LOCATION);
            }
        }
    }

    private void initView() {
        mToast = new ToastUtil(this);

        EMartView eMartView = new EMartView(ActivityHomeMart.this);
        presenter = new EMartPresenter(ActivityHomeMart.this, eMartView, this);
        presenter.start();
    }

    private void processGetLocation() {
        LocationManagerMp locationManagerMp = new LocationManagerMp(ActivityHomeMart.this);
        locationManagerMp.setCbLocation(location -> locationManagerMp.stopGetLocation());
        locationManagerMp.startGetLocation();
    }

    @Override
    public void onBackPressed() {
        Utils.LOGD(TAG, "onbackpress: getFragmentManager().getBackStackEntryCount()="+getFragmentManager().getBackStackEntryCount());
        if (System.currentTimeMillis() - mStartPressBack < 1500) {
            super.onBackPressed();
        }
        else {
            mStartPressBack = System.currentTimeMillis();
            mToast.showToast(getString(R.string.alert_confirm_exit_app));
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        logUtil.appendLogAction("onActivityResult ActivityHomeMart");
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Utils.LOGD(TAG, "onRequestPermissionsResult() called with: requestCode = [" + requestCode + "], permissions = [" + Arrays.toString(permissions) + "], grantResults = [" + Arrays.toString(grantResults) + "]");
        switch (requestCode) {
            case RQ_PERMISSION_STORAGE:
                processStoragePermission();
                break;
            case RQ_PERMISSION_LOCATION:
                processLocationPermissions();
                break;
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.LOGD(TAG, "onResume: ===>");
        logUtil.appendLogAction("onResume: ===>"+ TAG);
        presenter.handleOnResume();
    }

    private void disableButtonHomeRecent() {
        try {
            boolean isDisableStatusBar = DataStoreApp.getInstance().getIsDisableStatusBar();
            boolean isDisableNavbar = DataStoreApp.getInstance().getIsDisableNavbar();
            if (isDisableStatusBar) {
                MposUtil.getInstance().handlerActionDisableStatusBar(ActivityHomeMart.this, true);
            }
            if (isDisableNavbar) {
                MposUtil.getInstance().handlerActionDisplayNavbar(ActivityHomeMart.this, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        Utils.LOGD(TAG, "onStop: ===>");
        logUtil.appendLogAction("onStop: ===>"  + TAG);
        presenter.cancelTimer();
        presenter.dismissDialogResult();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Utils.LOGD(TAG, "onPause: --->");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Utils.LOGD(TAG, "onDestroy");
        logUtil.appendLogAction("onDestroy: ===>"+ TAG);
        presenter.closeSocketServer();
        presenter.unregisterReceiver();
        presenter.cancelTimer();
    }

    @Override
    protected void onStart() {
        super.onStart();
        Utils.LOGD(TAG, "onStart Home");
    }
}