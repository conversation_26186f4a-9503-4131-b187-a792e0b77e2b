package com.mpos.screen.login;

import android.content.Intent;

import com.mpos.BasePresenter;
import com.mpos.BaseView;

class LoginContract {

    interface View extends BaseView<Presenter> {

        void showProgressBar(boolean show);
        void showToast(String msg);

        void showUserId(String userId);
        String getUserIdEnter();
        void showUserPin(String userPin);
        void showPhoneSupport(String phone);


        void showSerialNumber(int typeDevice, String serialNumber);
        void showViewConnectDevice(int typeDevice);
        void showViewSplash(boolean show);

        void showError(int errorCodeLogin, String msg, boolean resetInputPin);
        void showErrorLoadMpos(int errorCode, String msg);
        void showErrorLoadOnFailureMpos(String msg);
        void showAlertCountDown(String msgCountDown, String timeShow);

        void hideImvBack();
        void requestFocusView(int id);
    }


    interface Presenter extends BasePresenter {

        void attemptLogin(String user, String pass);
//        void loadMerchantInfo();
        void fetchMerchantInfo();

        String getPhoneByTime();

        void selectConnectReader(int type);

        void handlerLoadFailMpos();
        void handlerActivityResult(int requestCode, int resultCode, Intent data);

        void initConfigServer();
        void gotoConfigBasePax();
    }


}
