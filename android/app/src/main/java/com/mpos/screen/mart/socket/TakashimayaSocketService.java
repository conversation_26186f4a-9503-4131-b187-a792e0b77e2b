package com.mpos.screen.mart.socket;

import android.text.TextUtils;
import com.mpos.models.DataOrderTakashimaya;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.sdk.util.Utils;

import java.lang.ref.WeakReference;

public class TakashimayaSocketService extends BaseSocketService implements ISocketServiceWithCallback {

    ItfCallback itfCallback;

    @Override
    void initSocketServer() {
        boolean hasOldInstance = SocketServerTakashimaya.checkExitAnCloseOldInstance();
        Utils.LOGD(TAG, "initSocketServer: hasOldInstance=" + hasOldInstance);
        appendLog(hasOldInstance ? "has oldInstance -> close it" : "has not oldInstance");

        SocketServerTakashimaya.getInstance().initParams(new WeakReference<>(this), logUtil, new EMartPresenter.ItfProcessMsgSocketTakashimaya() {
            @Override
            public void addOrder(DataOrderTakashimaya data, EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onAddOrder(data, in);
                }
            }

            @Override
            public void doCancelOrder(EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onCancelOrder(in);
                }
            }

            @Override
            public void doErrSocket(String msg) {
                if (itfCallback != null) {
                    itfCallback.onErrSocket(msg);
                }
            }
        }, (isConnect, ipClient) -> {
            if (itfCallback != null) {
                if (!TextUtils.isEmpty(ipClient)) {
                    itfCallback.isClientConnected(isConnect, ipClient);
                } else {
                    itfCallback.isClientConnected(isConnect, "");
                }
            }
        });

        SocketServerTakashimaya.getInstance().openConnect();
    }

    @Override
    public void closeTaskSocket() {
        if (SocketServerTakashimaya.getInstance() != null) {
            SocketServerTakashimaya.getInstance().closeTaskSocket();
            itfCallback = null;
        }
    }

    public interface ItfCallback {
        void onAddOrder(DataOrderTakashimaya data, EMartPresenter.ItfSendResultToClient in);

        void onCancelOrder(EMartPresenter.ItfSendResultToClient in);

        void onErrSocket(String msg);

        void isClientConnected(boolean isConnect, String ipClient);
    }


    @Override
    public void setItfCallBack(Object callback) {
        this.itfCallback = (TakashimayaSocketService.ItfCallback) callback;
    }
}
