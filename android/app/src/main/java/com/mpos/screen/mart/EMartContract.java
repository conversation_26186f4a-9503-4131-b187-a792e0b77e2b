package com.mpos.screen.mart;

import com.mpos.sdk.BasePresenter;
import com.mpos.sdk.BaseView;
import com.mpos.sdk.core.model.GiftCardInfor;

public class EMartContract {
    interface View extends BaseView<EMartContract.Presenter> {
        void showLoading(String msg);
        void hideLoading();
        void updateIpAddress(String ipLAn);
        void updateStateSocket(EMartPresenter.SocketState state, String ipLAn);
        void updateStatus(EMartPresenter.TransStatus status);
//        void updateIPGateWay(String ipGateWay);
        void updateClientConnect(boolean isConnect, String ipAdressClient);

        void hideViewGiftCard();
        void errGetInforCard();
        void updateViewInfoCard(GiftCardInfor data);

        void onStateCardInfo(int state);

        void showFullUI();
        void onlyShowUIWaitOrder();
        void onClickGotoPushPayment();
    }

    interface Presenter extends BasePresenter {
        String loadIpSocketServer();
        void closeSocketServer();
        void payment(String orderId, long amount, String description);
        void callbackTrans(String msg);
        void callbackCancelOrder(String msg);
        void logOut();
        void handlerActionSetting();
        void handleOnResume();

//        void resetSocket();

        void gotoHistory();
        void gotoDeposit();
        void gotoPaymentEnterAmount();
//        void printerVoucherEmart();
        void startProcessReadGiftCard();
        void closeSearchGiftCard();

        void checkUpgradeEmvConfigPax();
    }
}
