package com.mpos.screen;

import android.os.Bundle;
import android.os.Handler;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.mpos.common.DataStoreApp;
import com.mpos.customview.SearchViewLayout;
import com.mpos.customview.ViewToolBar;
import com.mpos.screen.mart.ReceiverManagerFinish;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;

import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

public class ActivityPaymentHistory extends AppCompatActivity{

    String TAG = "ActivityPaymentHistoryNew";

    @BindView(R.id.main_content)        View vRoot;
    @BindView(R.id.search_view)         View searchViewLayout;
    @BindView(R.id.viewpager)           ViewPager mViewPager;
    @BindView(R.id.tabs)                TabLayout tabLayout;
    /**
     * The {@link PagerAdapter} that will provide
     * fragments for each of the sections. We use a
     * {@link FragmentPagerAdapter} derivative, which will keep every
     * loaded fragment in memory. If this becomes too memory intensive, it
     * may be best to switch to a
     * {@link FragmentStatePagerAdapter}.
     */
    private SectionsPagerAdapter mSectionsPagerAdapter;
    private boolean isUseReader = false;
//    private boolean isShowFailHistory = true;
    private boolean isMultiAcquirer = false;
    private ViewToolBar vToolBar;
    private SearchViewLayout mSearchView;

    boolean loadedHistoryMpos = false;
    boolean loadedHistoryFail = false;
    boolean loadedHistoryEmart = false;
    boolean loadedHistoryQR = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment_history);

        ButterKnife.bind(this);
        isUseReader = DataStoreApp.getInstance().isUseReader();
        isMultiAcquirer = ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(this).getBankName());

        vToolBar = new ViewToolBar(this, vRoot);
        vToolBar.showTextTitle(getString(R.string.NAV_BAR_TITLE_SALES_HISTORY));
        vToolBar.showButtonBack(true);

        vToolBar.setOnclickSearch(v -> {
            vToolBar.showHideToolbar(false);
            searchViewLayout.setVisibility(View.VISIBLE);
            mSearchView.focusEditTextSearch();
            UIUtil.showKeyboard(ActivityPaymentHistory.this, mSearchView.getEdtSearch());
        });

        // Create the adapter that will return a fragment for each of the three
        // primary sections of the activity.
        mSectionsPagerAdapter = new SectionsPagerAdapter(getSupportFragmentManager());

//        if (isUseReader && PrefLibTV.getInstance(context).getFlagServer() == ConstantsPay.SERVER_SCB) {
//            isShowFailHistory = true;
            mViewPager.setOffscreenPageLimit(5);
//        }
        // Set up the ViewPager with the sections adapter.
        mViewPager.setAdapter(mSectionsPagerAdapter);

        tabLayout.setupWithViewPager(mViewPager);
        if (isUseReader) {
            tabLayout.setVisibility(View.VISIBLE);
//            tabLayout.setTabMode(TabLayout.MODE_FIXED);
//            tabLayout.setTabMode(TabLayout.MODE_AUTO);
//            tabLayout.setTabGravity(TabLayout.GRAVITY_START);
        } else {
            tabLayout.setVisibility(View.GONE);
        }
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
//            boolean isStartChange = false;
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
//                Utils.LOGD(TAG, "onPageScrolled: ");
            }

            @Override
            public void onPageSelected(int position) {
                Utils.LOGD(TAG, "onPageSelected: "+position);
                if (position == 1 && !loadedHistoryMpos) {
                    doLoadHisMpos();
                }
                else if (position == 2 && !loadedHistoryFail) {
                    doLoadHisFail();
                } else if (position == 3 && !loadedHistoryEmart) {
                    doloadHisEmart();
                } else if (position == 3 && !loadedHistoryQR) {
                    doloadHisQR();
                } else if (position == 4 && !loadedHistoryQR) {
                    doloadHisQR();
                }

                if (mSearchView.getVisibility()==View.VISIBLE) {
                    mSearchView.cancelSearch();
                }

                // don't show icon_search in tab: fail
                vToolBar.showIconSearch(position != 2);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                Utils.LOGD(TAG, "onPageScrollStateChanged() called with: state = [" + state + "]");
//                if (state == 1 && mSearchView.getVisibility()==View.VISIBLE) {
//                    mSearchView.cancelSearch();
//                }
            }
        });


        mSearchView = new SearchViewLayout(this, searchViewLayout);
        mSearchView.setItfViewSearchListener(new SearchViewLayout.ItfViewSearchListener() {
            @Override
            public void onQuerySearch(String querySearch) {
                searchData(querySearch);
            }

            @Override
            public void onDelStringSearch() {
            }

            @Override
            public void onDismissSearch() {
                mSearchView.setVisibility(View.GONE);
                vToolBar.showHideToolbar(true);
                dismissKeyBroad();
            }
        });

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            ReceiverManagerFinish.getInstance().pushActivityNeedFinish(this);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        dismissKeyBroad();
    }

    private void dismissKeyBroad() {
        UIUtil.hideKeyboard(this, vRoot);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {

        return super.onOptionsItemSelected(item);
    }

    private void doLoadHisMpos() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryMpos = true;
            ((FragmentPaymentHistory) fragment).loadSalesHistoryMpos();
        }
    }

    private void doLoadHisFail() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryFail = true;
            if (isMultiAcquirer) {
                ((FragmentPaymentHistory) fragment).loadFailHistoryMA();
            }
            else {
                ((FragmentPaymentHistory) fragment).loadFailHistoryBank();
            }
        }
    }

    private void doloadHisEmart() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryEmart = true;
            ((FragmentPaymentHistory) fragment).loadSalesHistoryEmart(1);
        }
    }

    private void doloadHisQR() {
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {
            loadedHistoryQR = true;
            ((FragmentPaymentHistory) fragment).initViewSearchQr(1);
        }
    }

    Handler handlerSearch = new Handler();

    private void searchData(final String text) {
        handlerSearch.removeCallbacksAndMessages(null);
        handlerSearch.postDelayed(() -> {
            Utils.LOGD(TAG, "run: search="+text);
            doSearchData(text);
        }, 800);
    }

    private void doSearchData(String text) {
        Utils.LOGD(TAG, "doSearchData: currtag="+mViewPager.getCurrentItem());
        Fragment fragment = mSectionsPagerAdapter.getActiveFragment(mViewPager, mViewPager.getCurrentItem());
        if (fragment instanceof FragmentPaymentHistory) {

            ((FragmentPaymentHistory) fragment).searchPaymentByKey(text);
        }
    }

    /**
     * A {@link FragmentPagerAdapter} that returns a fragment corresponding to
     * one of the sections/tabs/pages.
     */
    public class SectionsPagerAdapter extends FragmentPagerAdapter {

        ArrayList<Fragment> listFm = new ArrayList<>();
        ArrayList<String> titles = new ArrayList<>();

        public SectionsPagerAdapter(FragmentManager fm) {
            super(fm,FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);

            getFragmentHistory();
        }

        private void getFragmentHistory() {
            if (isUseReader) {
                if (isMultiAcquirer) {
                    listFm.add(FragmentPaymentHistory.newInstanceHistoryMultiAcquirer());
                } else {
                    listFm.add(FragmentPaymentHistory.newInstanceHistoryBank(getIntent().getStringExtra("tokenL2")));
                }
                titles.add(getString(R.string.history_macq));

                listFm.add(FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2")));
                titles.add(getString(R.string.history_other));

                if (isMultiAcquirer) {
                    listFm.add(FragmentPaymentHistory.newInstanceHistoryFailMA());
                } else {
                    listFm.add(FragmentPaymentHistory.newInstanceFailHistoryBank(getIntent().getStringExtra("tokenL2")));
                }
                titles.add(getString(R.string.history_fail));

                if (PrefLibTV.getInstance(ActivityPaymentHistory.this).getPermitSocket() && PrefLibTV.getInstance(ActivityPaymentHistory.this).getPermitPayGiftCard().equals(Constants.SVALUE_1)) {
                    listFm.add(FragmentPaymentHistory.newInstanceHistoryGiftCardEmart(getIntent().getStringExtra("tokenL2")));
                    titles.add(getString(R.string.history_voucher_card));
                }else {
                    loadedHistoryEmart = true;
                }

                if (DataStoreApp.getInstance().getPermitQrNl().equals(Constants.SVALUE_1)) {
                    listFm.add(FragmentPaymentHistory.newInstanceHistoryQR(getIntent().getStringExtra("tokenL2")));
                    titles.add(getString(R.string.history_qr));
                }else {
                    loadedHistoryQR = true;
                }
            }else {
                listFm.add(FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2")));
                if (isMultiAcquirer) {
                    titles.add(getString(R.string.history));
                }
                else {
                    titles.add(getString(R.string.history_other));
                }
            }
        }

        @NotNull
        @Override
        public Fragment getItem(int position) {
            Utils.LOGD(TAG, "getItem: " + position + " isUseReader=" + isUseReader);
            return listFm.get(position);

//            if (isUseReader) {
//                if (position == 0) {
//                    if (isMultiAcquirer) {
//                        return FragmentPaymentHistory.newInstanceHistoryMultiAcquirer();
//                    }
//                    else {
//                        return FragmentPaymentHistory.newInstanceHistoryBank(getIntent().getStringExtra("tokenL2"));
//                    }
//                }
//                else if (position == 1) {
//                    return FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2"));
//                } else if (position == 2) {
//                    if (isMultiAcquirer) {
//                        return FragmentPaymentHistory.newInstanceHistoryFailMA();
//                    } else {
//                        return FragmentPaymentHistory.newInstanceFailHistoryBank(getIntent().getStringExtra("tokenL2"));
//                    }
//                } else if (position == 3 && PrefLibTV.getInstance(ActivityPaymentHistory.this).getPermitSocket()) {
//                    Utils.LOGD(TAG, "title: page: gift card");
//                    return FragmentPaymentHistory.newInstanceHistoryGiftCardEmart(getIntent().getStringExtra("tokenL2"));
//                } else if ((position == 4) && DataStoreApp.getInstance().getPermitQrNl().equals(Constants.SVALUE_1)) {
//                    Utils.LOGD(TAG, "title: page: QR");
//                    return FragmentPaymentHistory.newInstanceHistoryQR(getIntent().getStringExtra("tokenL2"));
//                } else {
//                    return null;
//                }
//            }
//            else {
//                return FragmentPaymentHistory.newInstanceHistoryMpos(getIntent().getStringExtra("tokenL2"));
//            }
        }

        @Override
        public int getCount() {
//            int size;
////            if (isMultiAcquirer) {
////                size = 1;
////            }
////            else
//            if (isUseReader) {
////                if (PrefLibTV.getInstance(ActivityPaymentHistory.this).getPermitSocket()) {
////                    size = 4; // todo merchant tich hop
////                } else {
////                    size = 3;
////                }
//
//                size = 2;
//                if (PrefLibTV.getInstance(ActivityPaymentHistory.this).getPermitSocket()) {
//                    size++; // todo merchant tich hop
//                }
//
//                if (DataStoreApp.getInstance().getPermitQrNl().equals(Constants.SVALUE_1)) {
//                    size++; // permit QR
//                }
//
//            }
//            else {
//                size = 1;
//            }
////            Utils.LOGD(TAG, "getCount: " + size);
//
//            Utils.LOGD(TAG, "title: sizee= " + size);
            return listFm.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
//            if (isUseReader){
//                switch (position) {
//                    case 0:
//                        return getString(R.string.history_in_day);
//                    case 1:
//                        return getString(R.string.history_other);
//                    case 2:
//                        return getString(R.string.history_fail);
//                    case 3:
//                        Utils.LOGD(TAG, "title: history_voucher_card");
//                        return getString(R.string.history_voucher_card);
//                    case 4:
//                        Utils.LOGD(TAG, "title: history_qr");
//                        return getString(R.string.history_qr);
//                    default:
//                        break;
//                }
//            } else {
////                if (isMultiAcquirer) {
////                    return getString(R.string.history);
////                }
////                else {
//                    return getString(R.string.history_other);
////                }
//            }

//            return null;
            return titles.get(position);
        }

        public Fragment getActiveFragment(ViewPager container, int position) {
            String name = makeFragmentName(container.getId(), position);
            return  getSupportFragmentManager().findFragmentByTag(name);
        }

        private String makeFragmentName(int viewId, int index) {
            return "android:switcher:" + viewId + ":" + index;
        }
    }
}
