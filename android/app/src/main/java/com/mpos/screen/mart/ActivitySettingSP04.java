package com.mpos.screen.mart;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.customview.ViewToolBar;
import com.mpos.models.BluetoothDeviceTcp;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.mpos.utils.Constants;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;
import com.vnt.vntstore.sdk.ResourceFile;
import com.vnt.vntstore.sdk.StoreCheckUpdateListener;
import com.vnt.vntstore.sdk.StoreDownloadManager;

import net.cachapa.expandablelayout.ExpandableLayout;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.BuildConfig;
import vn.mpos.R;

public class ActivitySettingSP04 extends AppCompatActivity {

    public static final String TAG = ActivitySettingSP04.class.getSimpleName();

    @BindView(R.id.btn_connect_base_sp04)
    protected Button btnConnectBase;
    @BindView(R.id.tv_base_connect)
    protected TextView tvBaseConnected;
    @BindView(R.id.tv_ssid_base)
    protected TextView tvSsidBase;
    @BindView(R.id.btn_infor_base_sp04)
    protected Button btnBaseInfor;

    @BindView(R.id.ll_config_base_pax)
    ConstraintLayout llConfigBase;
    @BindView(R.id.ll_update_sp04)
    ConstraintLayout llUpdateSp04;

    @BindView(R.id.tv_version_app)
    TextView tvVersion;
    @BindView(R.id.btn_check_update)
    AppCompatTextView btnCheckUpdate;
    @BindView(R.id.tv_percent_update_app)
    TextView tvPercentUpdate;
    @BindView(R.id.pgb_process_update_app)
    ProgressBar pgbUpdateApp;
    @BindView(R.id.ll_progress_update)
    LinearLayout llProgressUpdate;
    @BindView(R.id.switch_auto_print)
    SwitchCompat switchAutoPrint;

    @BindView(R.id.ll_hide_statusbbar)
    LinearLayout llHideStatusbbar;
    @BindView(R.id.switch_disable_statusbar)
    SwitchCompat switchDisableStatusbar;

    @BindView(R.id.ll_hide_navbar)
    LinearLayout llHideNavbar;
    @BindView(R.id.switch_disable_navbar)
    SwitchCompat switchDisableNavbar;

    @BindView(R.id.ll_auto_hide_dlg)
    LinearLayout llAutoHideDlg;

    @BindView(R.id.ll_set_show_full_ui)
    LinearLayout llShowFullUi;
    @BindView(R.id.switch_auto_dissmis_dlg)
    SwitchCompat switchAutoDissmisDlg;

    @BindView(R.id.ll_confirmVoid_tcp)
    LinearLayout llConfirmVoidTcp;
    @BindView(R.id.switch_confirmVoid_tcp)
    SwitchCompat swtConfirmVoid;

    @BindView(R.id.expandable_layout)
    ExpandableLayout expandableLayout;

    @BindView(R.id.ll_set_auto_print)
    LinearLayout llSetAutoPrint;
    @BindView(R.id.radio_gr_print_receipt)
    RadioGroup radioGrPrintReceipt;

    @BindView(R.id.switch_only_show_ui_wait_order)
    SwitchCompat swOnlyShowUiWaitOrder;


    ViewToolBar vToolBar;
    SaveLogController logUtil;

    private MyProgressDialog mPgdl;
    final int requestCodePermissionBluetooth = 6;
    public static final int REQUEST_DEVICE_EMART = 7;
    public static final int REQUEST_ENABLE_BT_MART	        = 8;
    private ToastUtil toastUtil;
    boolean isConfigBase = false;
    ExecutorService executor;

    @SuppressLint("StringFormatMatches")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting_sp04);
        ButterKnife.bind(this, ActivitySettingSP04.this);

        logUtil = MyApplication.self().getSaveLogController();

        vToolBar = new ViewToolBar(this, findViewById(R.id.container));
        vToolBar.showButtonCancel(true, v -> onBackPressed());
        toastUtil = new ToastUtil(ActivitySettingSP04.this);
        mPgdl   = new MyProgressDialog(ActivitySettingSP04.this);

        if (getIntent() != null) {
            String type = getIntent().getStringExtra(Constants.TYPE_SETTING);
            if (type.contains(Constants.CONFIG_BASE)) {
                llConfigBase.setVisibility(View.VISIBLE);
                llUpdateSp04.setVisibility(View.GONE);
                vToolBar.showTextTitle(getString(R.string.txt_config_base));
                isConfigBase = true;
            } else {
                llConfigBase.setVisibility(View.GONE);
                llUpdateSp04.setVisibility(View.VISIBLE);
                vToolBar.showTextTitle(getString(R.string.title_settings));

                try {
                    Utils.LOGD(TAG,"Version: " + getPackageManager().getPackageInfo(getPackageName(), 0).versionCode);
                    tvVersion.setText(getString(R.string.txt_version, getPackageManager().getPackageInfo(getPackageName(), 0).versionCode));
                } catch (PackageManager.NameNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }

        initView();
    }

    private void initView() {
        btnConnectBase.setOnClickListener((v) -> {
            logUtil.appendLogAction("goto connect to base");
            checkBluetoothTcp();
        });

        btnBaseInfor.setOnClickListener((v) -> {
            logUtil.appendLogAction("getInfor base");
            getInforDock();
        });

        if (DevicesUtil.isPax()) {
            btnCheckUpdate.setVisibility(View.VISIBLE);
        }
        btnCheckUpdate.setOnClickListener((v) -> {
            if ((executor != null) && !executor.isShutdown() && !executor.isTerminated()) {
                return;
            }

            if (DevicesUtil.isPax()) {
                MyDialogShow.showDialogCancelAndClick("", getString(R.string.msg_warning_check_update_app), getString(R.string.yes), ActivitySettingSP04.this, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        checkUpdateApp();
                    }
                }, true, false);
            }
        });

        if (MyUtils.isTypeAppEmart()) {
            llConfirmVoidTcp.setVisibility(View.VISIBLE);
            boolean isShow = DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowConfirmVoidTcp, Boolean.class);
            swtConfirmVoid.setChecked(isShow);
            swtConfirmVoid.setOnCheckedChangeListener((compoundButton, b) -> {
                Utils.LOGD(TAG, "swtConfirmVoid = " + b);
                logUtil.appendLogAction("swtConfirmVoid = " + b);
                DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isShowConfirmVoidTcp, b);
            });
        }
        setupPrintMoreReceipt();

        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isConfirmPassword, Boolean.class, Boolean.FALSE)) {
            if (DevicesUtil.isSP02() || DevicesUtil.isPax()) {
                llHideNavbar.setVisibility(View.VISIBLE);
                llHideStatusbbar.setVisibility(View.VISIBLE);
            }

            llAutoHideDlg.setVisibility(View.VISIBLE);
            llShowFullUi.setVisibility(View.VISIBLE);

            boolean isDisableStatusBar = DataStoreApp.getInstance().getIsDisableStatusBar();
            switchDisableStatusbar.setChecked(isDisableStatusBar);
            switchDisableStatusbar.setOnCheckedChangeListener((compoundButton, b) -> {
                Utils.LOGD(TAG, "switchDisableStatusbar = " + b);
                logUtil.appendLogAction("switchDisableStatusbar = " + b);
                DataStoreApp.getInstance().setIsDisableStatusBar(b);
                MposUtil.getInstance().handlerActionDisableStatusBar(ActivitySettingSP04.this, b);
            });

            boolean isDisableNavbar = DataStoreApp.getInstance().getIsDisableNavbar();
            switchDisableNavbar.setChecked(isDisableNavbar);
            switchDisableNavbar.setOnCheckedChangeListener((compoundButton, b) -> {
                logUtil.appendLogAction("switchDisableNavbar = " + !b);
                DataStoreApp.getInstance().setIsDisableNavbar(b); // save !enabled
                MposUtil.getInstance().handlerActionDisplayNavbar(ActivitySettingSP04.this, !b);
            });

            boolean isAutoCloseDlg = PrefLibTV.getInstance(ActivitySettingSP04.this).get(PrefLibTV.isAutoCloseDialog, Boolean.class);
            switchAutoDissmisDlg.setChecked(isAutoCloseDlg);
            switchAutoDissmisDlg.setOnCheckedChangeListener((compoundButton, b) -> {
                Utils.LOGD(TAG, "switchAutoDissmisDlg = " + b);
                logUtil.appendLogAction("switchAutoDissmisDlg = " + b);
                PrefLibTV.getInstance(ActivitySettingSP04.this).put(PrefLibTV.isAutoCloseDialog, b);
            });
        }
    }

    private void setupPrintMoreReceipt() {
        boolean isAutoPrintReceipt = DataStoreApp.getInstance().getIsAutoPrintReceipt();
        switchAutoPrint.setChecked(isAutoPrintReceipt);
        if (isAutoPrintReceipt) {
            expandableLayout.expand();
        }
        switchAutoPrint.setOnCheckedChangeListener((compoundButton, b) -> {
            Utils.LOGD(TAG, "switchAutoPrint = " + b);
            logUtil.appendLogAction("switchAutoPrint = " + b);
            DataStoreApp.getInstance().setIsAutoPrintReceipt(b);

            if (b) {
                expandableLayout.expand();
            } else {
                expandableLayout.collapse();
            }
        });

        int numPrintReceipt = DataStoreApp.getInstance().getPrintMoreReceipt();
        switch (numPrintReceipt) {
            case 1:
                radioGrPrintReceipt.check(R.id.radio_1_receipt);
                break;
            case 2:
                radioGrPrintReceipt.check(R.id.radio_2_receipt);
                break;
            case 3:
                radioGrPrintReceipt.check(R.id.radio_3_receipt);
                break;
            default:
                radioGrPrintReceipt.check(R.id.radio_1_receipt);
                break;
        }

        radioGrPrintReceipt.setOnCheckedChangeListener((group, i) -> {
            int checkedRadioId = group.getCheckedRadioButtonId();
            if (checkedRadioId == R.id.radio_1_receipt) {
                DataStoreApp.getInstance().setPrintMoreReceipt(1);
            } else if (checkedRadioId == R.id.radio_2_receipt) {
                DataStoreApp.getInstance().setPrintMoreReceipt(2);
            } else if (checkedRadioId == R.id.radio_3_receipt) {
                DataStoreApp.getInstance().setPrintMoreReceipt(3);
            }
        });

        boolean isOnlyShowUIWaitOrder = DataStoreApp.getInstance().getIsOnlyShowUIWaitOrderTCP();
        swOnlyShowUiWaitOrder.setChecked(isOnlyShowUIWaitOrder);
        swOnlyShowUiWaitOrder.setOnCheckedChangeListener((compoundButton, b) -> {
            logUtil.appendLogAction("swOnlyShowUiWaitOrder = " + b);
            DataStoreApp.getInstance().setIsOnlyShowUIWaitOrderTCP(b); // save !enabled
        });
    }

    private void checkUpdateApp() {
        logUtil.appendLogAction("checkUpdateApp");
        mPgdl.showLoading(getString(R.string.tv_progress_update_app));
//        tvVersion.setText(getString(R.string.tv_progress_update_app));

        SharedPreferences sp = getSharedPreferences(BuildConfig.APPLICATION_ID, Context.MODE_PRIVATE);
        executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            StoreDownloadManager.getInstance().init(ActivitySettingSP04.this);
            StoreDownloadManager.getInstance().checkUpdate(new StoreCheckUpdateListener() {
                @Override
                public boolean onCheckResVersion(String s, String s1) {
                    Utils.LOGD(TAG, "onCheckResVersion " + s);
                    Utils.LOGD(TAG, "onCheckResVersion ver= " + s1);
                    return !sp.getString(s, "").equals(s1);
                }

                @Override
                public void onProgress(int i) {
                    Utils.LOGD(TAG, "onProgress i: " + i);
                    runOnUiThread(() -> {
                        mPgdl.hideLoading();
                        llProgressUpdate.setVisibility(View.VISIBLE);
                        tvPercentUpdate.setText(i + "%");
                        pgbUpdateApp.setProgress(i);
                    });
                }

                @Override
                public boolean onPermitInstall() {
                    Utils.LOGD(TAG, "onPermitInstall: ");
                    logUtil.appendLogAction("onPermitInstall");
                    return true;
                }

                @SuppressLint("StringFormatMatches")
                @Override
                public void onComplete(boolean b, List<ResourceFile> list) {
                    Utils.LOGD(TAG, "onComplete bool= " + b);
                    logUtil.appendLogAction("onComplete update = " + b);
                    if (!b) {
                        runOnUiThread(() -> {
                            Utils.LOGD(TAG, "onComplete runOnUiThread= ");
                            llProgressUpdate.setVisibility(View.GONE);
                            try {
                                tvVersion.setText(getString(R.string.txt_version, getPackageManager().getPackageInfo(getPackageName(), 0).versionCode));
                            } catch (PackageManager.NameNotFoundException e) {
                                e.printStackTrace();
                            }
                            MyDialogShow.showDialogError(getString(R.string.msg_err_complete_update), ActivitySettingSP04.this, false);
                            if (!executor.isShutdown()) {
                                executor.shutdownNow();
                            }
                        });
                    }
                }

                @SuppressLint("StringFormatMatches")
                @Override
                public void onError(int i, String s) {
                    Utils.LOGE(TAG, "onError errCode= " + i);
                    runOnUiThread(() -> {
                        mPgdl.hideLoading();
                        llProgressUpdate.setVisibility(View.GONE);
                        try {
                            tvVersion.setText(getString(R.string.txt_version, getPackageManager().getPackageInfo(getPackageName(), 0).versionCode));
                        } catch (PackageManager.NameNotFoundException e) {
                            e.printStackTrace();
                        }
                        MyDialogShow.showDialogError(getString(R.string.msg_err_update_app, String.valueOf(i)), ActivitySettingSP04.this, false);
                        if (!executor.isShutdown()) {
                            executor.shutdownNow();
                        }
                    });
                }
            });
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isConfigBase) {
            initViewBaseConnected();
        }
    }

    public void checkBluetoothTcp() {
        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        if (btAdapter != null) {
            boolean havePermissionBluetooth = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !UtilsSystem.checkHaveBluetoothPermission(ActivitySettingSP04.this)) {

                havePermissionBluetooth = false;
                requestBluetoothPermission();
            }
            if (havePermissionBluetooth) {
                if (!btAdapter.isEnabled()) {
                    UtilsSystem.enableBluetooth(ActivitySettingSP04.this, REQUEST_ENABLE_BT_MART);
                } else {
                    showDeviceList();
                }
            }
        } else {
//            Toast.makeText(this, getString(R.string.msg_bluetooth_is_not_supported), Toast.LENGTH_SHORT).show();
            showToast(getString(R.string.msg_bluetooth_is_not_supported));
        }
    }

    private void getInforDock() {
        String ssid = PrefLibTV.getInstance(ActivitySettingSP04.this).getSSIDPAX();
        if (!TextUtils.isEmpty(ssid)) {
            tvSsidBase.setText(getString(R.string.tv_ssid_base, ssid));
        } else {
            tvSsidBase.setText(getString(R.string.tv_base_not_connected));
        }
        tvSsidBase.setVisibility(View.VISIBLE);
    }

    private void initViewBaseConnected() {
        String dockAddress = PrefLibTV.getInstance(ActivitySettingSP04.this).getBluetoothAddressPAX();
        if (TextUtils.isEmpty(dockAddress)) {
            logUtil.appendLogAction("dockAddress= null");
            tvBaseConnected.setText(getString(R.string.tv_base_not_connected));
        } else {
            Utils.LOGD(TAG, "dockAddress= " + dockAddress);
            logUtil.appendLogAction("dockAddress= " + dockAddress);
            BluetoothDeviceTcp device = MyGson.parseJson(dockAddress, BluetoothDeviceTcp.class);
            tvBaseConnected.setText(getString(R.string.tv_base_connected, device.getName()));
        }
    }

    private void showDeviceList() {
        Intent intent = new Intent(ActivitySettingSP04.this, ActivityBtConnectTcp.class);
        startActivityForResult(intent, REQUEST_DEVICE_EMART);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, "onActivityResult: requestCode=" + requestCode + " resultCode=" + resultCode);
        switch (requestCode) {
            case REQUEST_DEVICE_EMART: {
                // todo update view info
                break;
            }
            case REQUEST_ENABLE_BT_MART: {
                if (resultCode == RESULT_OK) {
                    showDeviceList();
                }
                break;
            }
            default:
                break;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        handleRequestPermission(requestCode, permissions, grantResults);
    }

    public void handleRequestPermission(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == requestCodePermissionBluetooth &&
                ((grantResults.length == 1 && grantResults[0] != PackageManager.PERMISSION_GRANTED)
                        || grantResults.length == 2 && (grantResults[0] != PackageManager.PERMISSION_GRANTED || grantResults[1] != PackageManager.PERMISSION_GRANTED))
        ) {
            MyDialogShow.showDialogWarning(ActivitySettingSP04.this, getString(R.string.msg_warning_denial_permission_bluetooth), false);
        } else {
            checkBluetoothTcp();
        }
    }

    private void requestBluetoothPermission() {
//        Toast.makeText(ActivitySettingSP04.this, getString(R.string.msg_request_permission_bluetooth), Toast.LENGTH_LONG).show();
        showToast(getString(R.string.msg_request_permission_bluetooth));
        UtilsSystem.requestBluetoothPermission(ActivitySettingSP04.this, requestCodePermissionBluetooth);
    }

    private void showToast(String msg) {
        toastUtil.showToast(msg);
    }

    @Override
    public void onBackPressed() {
        if ((executor != null) && !executor.isShutdown() && !executor.isTerminated()) {
            MyDialogShow.showDialogWarning(ActivitySettingSP04.this, getString(R.string.msg_warning_update_app), false);
        } else {
            super.onBackPressed();
            finish();
        }
    }
}