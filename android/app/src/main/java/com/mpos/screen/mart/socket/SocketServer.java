package com.mpos.screen.mart.socket;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.mpos.models.DataOrder;
import com.mpos.models.DataPrePayEMart;
import com.mpos.models.DataVoid;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.Utils;

import java.io.BufferedInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import vn.mpos.R;

public class SocketServer {
    private static final String TAG = SocketServer.class.getSimpleName();

    //Service
    public static final String ADD_ORDER        = "ADD_ORDER_INFOR";
    public static final String CANCEL_ORDER     = "CANCEL_ORDER";
    public static final String UPDATE_TRANS     = "UPDATE_TRANSACTION";
    public static final String VOID_TRANSACTION = "VOID_TRANSACTION";
    public static final String NOTIFICATION     = "NOTIFICATION";

    private static final int PORT = 1110;

    private static final int maxLengthDataInputStream = 10000;

    private static final String endString = "nextpay";
    private final String tagSocketServer = "SocSV: "; // use for push log

    private static SocketServer socketServerManager;

    ServerSocket serverSocket;
    Socket socket;
    DataInputStream dataInputStream;
    DataOutputStream dataOutputStream;
    EMartPresenter.ItfProcessMsgSocket itfProcessMsgSocket;
    EMartPresenter.ItfClientConnect itfClientConnect;

    WeakReference<BaseSocketService> weakReference;

    ExecutorService executor = Executors.newSingleThreadExecutor();
    Handler handler = new Handler(Looper.getMainLooper());

    boolean accept = true;

    SaveLogController logUtil;

    String msg_callback_data = "Data received";

    public static synchronized boolean checkExitAnCloseOldInstance() {
        Utils.LOGD(TAG, "checkExitAnCloseOldInstance: ");
        if (socketServerManager != null) {
            socketServerManager.closeTaskSocket();
            return true;
        }
        return false;
    }

    public static synchronized SocketServer getInstance() {
        if (socketServerManager == null) {
            Utils.LOGD(TAG, "-> create new instance");
            socketServerManager = new SocketServer();
            socketServerManager.appendLog("-> create new instance");
        }
        else {
            Utils.LOGD(TAG, "->use curr instance");
            socketServerManager.appendLog("->use curr instance");
        }

        return socketServerManager;
    }

    private SocketServer() {
    }

//    public SocketServer(WeakReference<SocketService> weakReference, SaveLogController logUtil,
//                        EMartPresenter.ItfProcessMsgSocket itfProcessMsgSocket, EMartPresenter.ItfClientConnect clientConnect) {
//        this.weakReference = weakReference;
//        this.itfProcessMsgSocket = itfProcessMsgSocket;
//        this.itfClientConnect = clientConnect;
//        this.logUtil = logUtil;
//    }

    public void initParams(WeakReference<BaseSocketService> weakReference, SaveLogController logUtil,
                           EMartPresenter.ItfProcessMsgSocket itfProcessMsgSocket, EMartPresenter.ItfClientConnect clientConnect) {
        Utils.LOGD(TAG, "initParams: -->");
        this.weakReference = weakReference;
        this.itfProcessMsgSocket = itfProcessMsgSocket;
        this.itfClientConnect = clientConnect;
        this.logUtil = logUtil;
    }

    public void openConnect() {
        executor.execute(() -> {
            //Background work here
            try {
                appendLogException("step1: start socketServer");
                serverSocket = new ServerSocket(PORT);
                Utils.LOGD(TAG, "openConnect: timeoutSK=" + serverSocket.getSoTimeout());
            } catch (IOException e) {
                Utils.LOGE(TAG, "openServer socket err= " + e.getMessage());
                appendLog("openServer socket err= " + e.getMessage());
                e.printStackTrace();
            }

            Utils.LOGD(TAG, "doInBackground: is Open Server");
            appendLog("openConnect socket");

            handlerObverseSocket(true);
        });
    }

    private void handlerObverseSocket(boolean isReset) {
        if (accept) {
            try {
                if (serverSocket != null) {
                    handlerServerOpen();
                }
            } catch (IOException e) {
                Utils.LOGE(TAG, "SocketServer: err " + e.getMessage());
                e.printStackTrace();
                runOnUIThread(() -> {
                    appendLogException("SocketServer err: " + e.getMessage());
                    logUtil.pushLog();
                });
                Utils.LOGD(TAG, "isClose reset= " + isReset);

                if (isReset) {
                    Utils.LOGD(TAG, "run isClose reset= ");
                    resetAcceptSocket(); // catch IOException serverSocket
                }
            }
        }
    }

    private void handlerServerOpen() throws IOException {
        while (true) {
            Utils.LOGD(TAG, "server waiting....");
//            updateClientConnect(false, "");
            appendLog("server waiting....");
            socket = serverSocket.accept();
            Utils.LOGD(TAG, "doInBackground: serverSocket accept success");
            if ((socket != null) && (socket.isConnected())) {
                appendLog("step2 socket server acppent sucess");
                try {
                    updateClientConnect(true, socket.getInetAddress().getHostAddress());
                } catch (Exception e) {
                    e.printStackTrace();
                }

                StringBuilder data = new StringBuilder();

                dataInputStream = new DataInputStream(new BufferedInputStream(socket.getInputStream()));
//            Utils.LOGD(TAG, "----#1----available=" + socket.getInputStream().available());
//            while (true) {
//                try {
//                    data.append((char) dataInputStream.readByte());
//                } catch (IOException e) {
//                    // has error when client connect but don't send anything => need reset server to listener after msg
//                    e.printStackTrace();
//                    resetAcceptSocket();
//                    return;
//                }
//                if (data.length() > maxLengthDataInputStream) {
//                    appendLog("dataInput > maxLengthDataInputStream");
//                    break;
//                }
//                if (data.indexOf(endString) > 0) {
////                    Utils.LOGD(TAG, " end string");
//                    break;
//                }
//            }

                //
                /*
                 * change to dataInputStream.read() => don't need catch exception when client close immediately
                 * but when client don't close (1) or haven't string "nextpay" at last (2)
                 *  (1) dataOutputStream.write(dataInBytes, 0, dataInBytes.length);
                 *      dataOutputStream.close();
                 *  (2) abc...zyx-nextpay
                 * => server still waiting
                 * => need add timeout in read
                 */
                handler.postDelayed(() -> {
                    Utils.LOGD(TAG, " timeout read msg from client " + data);
                    appendLog(" timeout read msg from client");
                    Thread thread = new Thread(() -> processDataFromClient(data));
                    thread.start();
                }, 10000);
                boolean noneError = true;
                int charRead;
                try {
                    while ((charRead = dataInputStream.read()) != -1) {
                        //                Utils.LOGD(TAG, "handlerServerOpen: ----#2----available=" + data + " charRead=" + charRead);
                        data.append((char) charRead);
                        if (data.length() > maxLengthDataInputStream) {
                            appendLog("dataInput > maxLengthDataInputStream");
                            break;
                        }
                        if (data.indexOf(endString) > 0) {
                            break;
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    noneError = false;
                    Utils.LOGD(TAG, "handlerServerOpen: don't handle Exception from read()");
                }

                Utils.LOGD(TAG, "----#end----");
                handler.removeCallbacksAndMessages(null);

                if (noneError) {
                    processDataFromClient(data);
                }
            } else {
                appendLogException("socketServer accept close");
            }
        }
    }

    private void processDataFromClient(StringBuilder data) {
        String clearData;
        clearData = data.toString().split(endString)[0];

        wakeLockScreen();

        Utils.LOGD(TAG, "doInBackground: clearData= " + clearData);
        appendLog("received data from client success --> " + clearData);

        if(clearData.contains(CANCEL_ORDER) && !accept) {           // case chưa add order đã cancel
            Utils.LOGD(TAG, "processDataFromClient CANCEL_ORDER");
//            setAccept(false);
            handlerMsgCancelOrder(clearData);
        }

        if (!accept) {
            return;
        }

        if (clearData.contains(ADD_ORDER)) {
            setAccept(false);
            handlerMsgAddOrder(clearData);
        } else if (clearData.contains(VOID_TRANSACTION)) {
            setAccept(false);
            handlerMsgVoidTrans(clearData);
        } else {
            Utils.LOGD(TAG, "doInBackground: warning_invalid_data ");
            appendLog("warning_invalid_data: " + clearData);

            processMsgError();
        }
    }

    private void wakeLockScreen() {
        PowerManager pm = (PowerManager) weakReference.get().getSystemService(Context.POWER_SERVICE);
        PowerManager.WakeLock wl = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK | PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE, "mpos:socket");
        wl.acquire(2000);
        wl.release();
    }

    private void updateClientConnect(boolean isConnect, String ipAdressClient) {
        Utils.LOGD(TAG, "connect: " + isConnect + "--- Ip Client= " + ipAdressClient);
        appendLog("connect: " + isConnect + "--- Ip Client= " + ipAdressClient);
        runOnUIThread(() -> {
            if (!TextUtils.isEmpty(ipAdressClient)) {
                itfClientConnect.clientConnected(isConnect, ipAdressClient);
            } else {
                itfClientConnect.clientConnected(isConnect, "");
            }
        });
    }

    private void handlerMsgVoidTrans(String data) {
        DataVoid dataVoid = buildDataVoid(data);
        if (dataVoid != null) {
            String content = createDataPrePayVoid(dataVoid);
            appendLog("step 3 reviced msg: void= " + content);
            sendMsgAckToClient(content);

            runOnUIThread(() -> itfProcessMsgSocket.doVoid(dataVoid, msg -> {
                Utils.LOGD(TAG, "handlerMsgVoidTrans");
                Thread thread = new Thread(() -> sendMsgToClient(true, msg));
                thread.start();
            }));
        } else {

            Utils.LOGD(TAG, "invalid_data data void");
            appendLog("invalid_data data void");
            processMsgError();
        }
    }

    private void handlerMsgAddOrder(String data) {
        DataOrder dataOrder = buildDataOrder(data);
        if ((dataOrder != null) && (!TextUtils.isEmpty(dataOrder.getOrderId()))) {
            String content = createDataPrePayAddOrder(dataOrder);
            appendLog("step 3 reviced msg: billinfor= " + content);
            sendMsgAckToClient(content);

            runOnUIThread(() -> itfProcessMsgSocket.addOrder(dataOrder, msg -> {
                Utils.LOGD(TAG, "itfCallbackSuccess != null");
                appendLog("step end: callback msg to client= " + msg);
                Thread thread = new Thread(() -> sendMsgToClient(true, msg));
                thread.start();
            }));

        } else {
            Utils.LOGD(TAG, "invalid_data data order");
            appendLog("invalid_data data order");
            processMsgError();
        }
    }

    private void handlerMsgCancelOrder(String data) {
        DataOrder dataOrder = buildDataOrder(data);
        if ((dataOrder != null) && (!TextUtils.isEmpty(dataOrder.getOrderId()))) {
            String content = createDataPrePayAddOrder(dataOrder);
            appendLog("step 3 reviced msg: cancel trans= " + content);
            sendMsgAckToClient(content);

            runOnUIThread(() -> {
                itfProcessMsgSocket.doCancelOrder(msg -> {
                    Utils.LOGD(TAG, "doCancelOrder send result");
                    appendLog("doCancelOrder send result " + msg);
                    Thread thread = new Thread(() -> sendMsgAckToClient(msg));
                    thread.start();
                });
            });
        } else {
            Utils.LOGD(TAG, "invalid_data data order");
            appendLog("invalid_data data order");
            processMsgError();
        }
    }

    private void processMsgError() {

        runOnUIThread(() -> itfProcessMsgSocket.doErrSocket(weakReference.get().getString(R.string.warning_invalid_data)));
        String msgError = buildDataErr(weakReference.get().getString(R.string.warning_invalid_data));
        sendMsgToClient(true, msgError);
//        resetAcceptSocket(); //
    }

    private String createDataPrePayAddOrder(DataOrder dataOrder) {
        DataPrePayEMart dataPrePayEMart = new DataPrePayEMart();
        dataPrePayEMart.setServiceName(NOTIFICATION);
        dataPrePayEMart.setOrderId(dataOrder.getOrderId());
        dataPrePayEMart.setResponseMess(msg_callback_data);
        return new Gson().toJson(dataPrePayEMart);
    }

    private String createDataPrePayVoid(DataVoid dataVoid) {
        DataPrePayEMart dataPrePayEMart = new DataPrePayEMart();
        dataPrePayEMart.setServiceName(NOTIFICATION);
        dataPrePayEMart.setOrderId(dataVoid.getOrderId());
        dataPrePayEMart.setResponseMess(msg_callback_data);
        return new Gson().toJson(dataPrePayEMart);
    }

    private DataOrder buildDataOrder(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return null;
        }
        DataOrder dataOrder = null;
        Utils.LOGD(TAG, "buildDataOrder: clear Msg= " + msg);
        try {
            dataOrder = MyGson.parseJson(msg, DataOrder.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dataOrder;
    }

    private DataVoid buildDataVoid(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return null;
        }
        DataVoid dataVoid = null;
        Utils.LOGD(TAG, "buildDataOrder: clear Msg= " + msg);
        try {
            dataVoid = MyGson.parseJson(msg, DataVoid.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return dataVoid;
    }

    private String buildDataErr(String errorMsg) {
        DataPrePayEMart dataPrePayEMart = new DataPrePayEMart();
        dataPrePayEMart.setServiceName(UPDATE_TRANS);
        dataPrePayEMart.setStatus(EMartPresenter.FAIL);
        dataPrePayEMart.setResponseCode(EMartPresenter.ORDER_CODE_CANCEL);
        dataPrePayEMart.setResponseMess(errorMsg);
        return MyGson.getGson().toJson(dataPrePayEMart);
    }

    /* push msg to client */

    private void sendMsgAckToClient(String msg) {
        sendMsgToClient(false, msg);
    }

    private void sendMsgToClient(boolean isResetSocket, String msg) {
        Utils.LOGD(TAG, "sendMsgToClient() called with: isResetSocket = [" + isResetSocket + "], msg = [" + msg + "]");
        try {
            appendLog("action: sendMsgToClient: " + ((socket != null) ? socket.isConnected() : "socket is null"));
            if (socket != null && socket.isConnected()) {
                pushMsgToClient(msg);
                Utils.LOGD(TAG, "sendMsgToClient: success");
                appendLog("sendMsgToClient: success");
            }
        } catch (IOException e) {
            appendLogException("sendMsgToClient: push result err: " + e.getMessage());
            Utils.LOGE(TAG, "sendMsgToClient: ", e);
            e.printStackTrace();
        } finally {
            if (isResetSocket) {
                updateClientConnect(false, "");
                resetAcceptSocket();
            }
        }
    }

    private void pushMsgToClient(String data) throws IOException {
        appendLog("func pushMsgToClient> ");
        if (socket != null && socket.isConnected()) {
            dataOutputStream = new DataOutputStream(socket.getOutputStream());
            byte[] dataSend = data.getBytes(StandardCharsets.UTF_8);
            dataOutputStream.write(dataSend, 0, dataSend.length);
            Utils.LOGD(TAG, "pushMsgToClient successs data= " + data);
            appendLog("pushMsgToClient success");
        } else {
            Utils.LOGD(TAG, "pushMsgToClient socket = null/notConnected data=" + data);
            appendLogException("func pushMsgToClient socket = null/notConnected");
        }
    }

    public void closeTaskSocket() {
        Utils.LOGD(TAG, "closeTaskSocket");
        try {
            appendLog("closeTaskSocket-->start");
            setAccept(false);
            if (dataInputStream != null) {
                dataInputStream.close();
                dataInputStream = null;
            }
            if (dataOutputStream != null) {
                dataOutputStream.close();
                dataOutputStream = null;
            }

            if (socket != null) {
                socket.close();
                socket = null;
                Utils.LOGD(TAG, "1--socket close");
                appendLog("socket close");
            }

            if (serverSocket != null) {
                serverSocket.close();
                serverSocket = null;
                Utils.LOGD(TAG, "server Socket close");
                appendLog("server Socket close");
            }

            if (executor != null) {
                executor.shutdownNow();
            }
            appendLog("closeTaskSocket-->end");
        } catch (IOException e) {
            appendLogException("closeTaskSocket err= " + e.getMessage());
            Utils.LOGD(TAG, "closeTaskSocket err= " + e.getMessage());
            e.printStackTrace();
        } finally {
            socketServerManager = null;
        }
    }

    private void resetAcceptSocket() {
       Utils.LOGD(TAG, "resetAcceptSocket: ");
        appendLog("reset Socket");
        setAccept(true);
        try {
            if (socket != null) {
                socket.close();
                appendLog("resetAcceptSocket socket close");
            }
            if (dataInputStream != null) {
                dataInputStream.close();
                dataInputStream = null;
            }
            if (dataOutputStream != null) {
                dataOutputStream.close();
                dataOutputStream = null;
            }
        } catch (IOException e) {
            appendLog("resetAcceptSocket: err" + e.getMessage());
            e.printStackTrace();
        }

//        if (socket != null) {
//            try {
//                socket.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            Utils.LOGD(TAG, "2--socket close");
//        }

        handlerObverseSocket(false);
    }

    private void setAccept(boolean accept) {
        this.accept = accept;
    }

    private void appendLog(String log) {
        if (logUtil != null) {
            logUtil.appendLogAction(tagSocketServer + log);
        }
    }
    
    private void appendLogException(String log) {
        if (logUtil != null) {
            logUtil.appendLogException(tagSocketServer + log);
        }
    }

    private void runOnUIThread(Runnable runnable) {
        AppExecutors.getInstance().mainThread().execute(runnable);
    }
}
