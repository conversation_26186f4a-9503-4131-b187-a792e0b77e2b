package com.mpos.screen;

import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.Glide;
import com.google.zxing.EncodeHintType;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogMVisaSuccess;
import com.mpos.customview.DialogQRCodeScale;
import com.mpos.customview.DialogResult;
import com.mpos.customview.MposDialog;
import com.mpos.delegates.Singleton;
import com.mpos.models.DataQrPay;
import com.mpos.models.QrPayResponse;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.mpos.utils.QrCodeUtils;
import com.pps.core.MyProgressDialog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

//import com.nostra13.universalimageloader.core.ImageLoader;
//import com.visa.mvisa.generator.InputInvalidException;
//import com.visa.mvisa.generator.MerchantQrDataRequest;

//import static com.visa.mvisa.generator.QrCodeDataGenerator.generateQrCodeData;

/**
 * Created by noe on 10/12/17
 */

public class ActivityScanQRCode extends AppCompatActivity {
    public static final String TAG = ActivityScanQRCode.class.getSimpleName();
    @BindView(R.id.imgQRCode)
    protected ImageView imgQRCode;
    @BindView(R.id.tv_mvisa_amount)
    protected AppCompatTextView tvAmount;
    @BindView(R.id.btn_check_status_trans)
    protected Button btnCheckStatus;
    @BindView(R.id.imv_type_qr)
    ImageView imgQrType;
    @BindView(R.id.tvGuideUseQR)
    AppCompatTextView tvGuide;
    @BindView(R.id.toolbar_title)
    AppCompatTextView toolbarTitle;
    @BindView(R.id.imv_back)
    AppCompatImageView imvBack;
    @BindView(R.id.scan_qr_tv_count_down)
    AppCompatTextView tvCountDown;
    @BindView(R.id.imgQRCode_expired)
    View viewExpired;
    @BindView(R.id.scan_qr_tv_title_count_down)
    AppCompatTextView tvTitleCountDown;
    private String amount;
    private String note;

    private String qrid;
    private String qrCode;
    private String merchantId;
    private String mvisaMidMaster;
    private String mvisaMid;
    private String emailCustomer;
    private String merchantNameShortcut;
    private String merchantCategoryCode;
    private String terminalId;
    private String orderCode;
    private String txId;
    private String typeQR;
    private String udid;

    private SaveLogController logUtil;
    private MyProgressDialog mPgdl;

    public static int WIDTH;
    private String tagLengthValue = "";
    private int widthYourPhone = 0;
    private DialogQRCodeScale dialogQRCodeScale;
    private QrPayResponse qrPayResponse = null;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan_qrcode);
        logUtil = MyApplication.self().getSaveLogController();
        logUtil.pushLog();
        mPgdl = new MyProgressDialog(this);
        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        ButterKnife.bind(this);

        logUtil.appendLogAction("---screen Scan QR---");

        merchantId = DataStoreApp.getInstance().getmerchantId();
        mvisaMid = DataStoreApp.getInstance().getmvisaMid();
        mvisaMidMaster = DataStoreApp.getInstance().getmvisaMidMaster();
        merchantNameShortcut = DataStoreApp.getInstance().getMerchantNameShortcut(); //old code: "QR"+dataStoreApp.getTerminalId();
        merchantCategoryCode = DataStoreApp.getInstance().getMerchantCategoryCode();
        terminalId = DataStoreApp.getInstance().getTerminalId();

//        tvNameCompanyFull.setText(merchantNameShortcut);

        Utils.LOGD(TAG, ">>> merchantId: " + merchantId);
        Utils.LOGD(TAG, ">>> mvisaMid: " + mvisaMid);
        Utils.LOGD(TAG, ">>> merchantNameShortcut: " + merchantNameShortcut);
        Utils.LOGD(TAG, ">>> terminalId: " + terminalId);

//        ViewToolBar vToolBar = new ViewToolBar(this, findViewById(R.id.container));
//        vToolBar.showTextTitle(getString(R.string.txt_title_payment_pr));
//        vToolBar.showButtonBack(true);

//      getDataWhenClickNotification();
        getYourSceneSize();
        getDataFromIntent();
//        setPoweredMpos();

        initView();
        onBack();
//        countDown();
        LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter(Constants.IntentFilterNotificationMVISA));
    }

    private void initView() {
        toolbarTitle.setText(getString(R.string.txt_title_payment_pr));
        if (qrPayResponse != null) {
//            ImageLoader.getInstance().displayImage(qrPayResponse.getQrIcon(), imgQrType);
//            Glide.with(this).load(qrPayResponse.getImgPartner()).into(imgLogoAppBankSupport);
//            ImageLoader.getInstance().displayImage(qrPayResponse.getImgPartner(), imgLogoAppBankSupport);
            if (qrPayResponse.getImgLogo() != null) {
                Glide.with(this).load(qrPayResponse.getImgLogo()).into(imgQrType);
            } else {
                imgQrType.setImageResource(qrPayResponse.getResourceDefault());
            }

            if ("en".equals(Locale.getDefault().getLanguage())) {
                tvGuide.setText(qrPayResponse.getDescriptionEn());
            } else {
                tvGuide.setText(qrPayResponse.getDescription());
            }
            btnCheckStatus.setVisibility(qrPayResponse.getCheckStatus() == 1 ? View.VISIBLE : View.GONE);
        } else {
            btnCheckStatus.setVisibility(View.GONE);
        }

//        if (typeQR.equals(Constants.TYPE_QR_MVISA)) {
//            ImageLoader.getInstance().displayImage("https://mpos.vn/assets/qr/mvisa_bank.png", imgLogoAppBankSupport);
//            //tvTypeQr.setText(getString(R.string.dialog_mvisa_payment_base_on_qr_international_only_line));
//        } else {
//            imgLogoAppBankSupport.setBackgroundResource(R.drawable.img_napas);
//            //tvTypeQr.setText(getString(R.string.dialog_mvisa_payment_base_on_qr_domestic_only_line));
//        }
    }

    private void onBack() {
        imvBack.setOnClickListener(view -> {
            finish();
        });
    }

    private void countDown() {
        new CountDownTimer(120000, 1000) {
            @Override
            public void onTick(long l) {
                updateCountDownTimer(l);
            }

            @Override
            public void onFinish() {
//                Ẩn tạm thời. khi nào có dữ liệu databse trả về thì re commented là được.
//                viewExpired.setVisibility(View.VISIBLE);
//                tvTitleCountDown.setText(getString(R.string.expir_countdown));
//                tvTitleCountDown.setTextColor(getResources().getColor(R.color.red_1));
            }
        }.start();
    }

    private void updateCountDownTimer(long mTimeLeftInMillis) {
        int hour = (int) (((mTimeLeftInMillis / 1000) / 60)) / 24;
        int minutes = (int) (((mTimeLeftInMillis / 1000) / 60)) % 24;
        int seconds = (int) ((mTimeLeftInMillis / 1000) % 60);
        String timeLeftFormat = String.format(Locale.getDefault(), "%02d:%02d:%02d", hour, minutes, seconds);
        tvCountDown.setText(timeLeftFormat);
    }

    //note: new
    private void getYourSceneSize() {
//        int heightScene = getWindowManager().getDefaultDisplay().getHeight();
        int widthScene = Singleton.getInstance().getWidthScreen();
//        int widthScene = getWindowManager().getDefaultDisplay().getWidth();
//        Utils.LOGD(TAG, "widthScene: "+widthScene);
//        Utils.LOGD(TAG, "heightScene: "+heightScene);

        widthYourPhone = widthScene;
        WIDTH = widthScene / 2;

//        ViewTreeObserver vto = imgQRCode.getViewTreeObserver();
//        vto.addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
//            public boolean onPreDraw() {
//                imgQRCode.getViewTreeObserver().removeOnPreDrawListener(this);
//                int imgQRCodeHeight = imgQRCode.getMeasuredHeight();
//                int imgQRCodeWidth  = imgQRCode.getMeasuredWidth();
//
//                Utils.LOGD(TAG, "imgQRCodeWidth: "+imgQRCodeWidth+"px"+" *** "+pxToDp(imgQRCodeWidth)+"dp");
//                Utils.LOGD(TAG, "imgQRCodeHeight: "+imgQRCodeHeight+"px"+" *** "+pxToDp(imgQRCodeHeight)+"dp");
//
//                int widthimgQRCode  = imgQRCode.getDrawable().getIntrinsicWidth();
//                int heightimgQRCode = imgQRCode.getDrawable().getIntrinsicHeight();
//
//                Utils.LOGD(TAG, "widthimgQRCode: "+widthimgQRCode+"px"+" *** "+pxToDp(widthimgQRCode)+"dp");
//                Utils.LOGD(TAG, "heightimgQRCode: "+heightimgQRCode+"px"+" *** "+pxToDp(heightimgQRCode)+"dp");
//                return true;
//            }
//        });
    }


    @OnClick({R.id.btn_go_home, R.id.btn_check_status_trans})
    protected void onClickView(View v) {
        if (v.getId() == R.id.btn_go_home) {
            setResult(Constants.RESULT_NOT_HANDLER);
            finish();
        } else if (v.getId() == R.id.btn_check_status_trans) {
            checkStatus(udid, qrPayResponse.getQrType());
        }
    }

//    public int pxToDp(int px) {
//        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
//        int dp = Math.round(px / (displayMetrics.xdpi / DisplayMetrics.DENSITY_DEFAULT));
//        return dp;
//    }

    private final BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            String message = intent.getStringExtra("message");
            final String category = intent.getStringExtra("category");
            final String udid = intent.getStringExtra("udid");

            Utils.LOGD(TAG, "----------------------------------");
            Utils.LOGD(TAG, "message: " + message);
            Utils.LOGD(TAG, "category: " + category);
            Utils.LOGD(TAG, "udid: " + udid);

            if (logUtil != null) {
                logUtil.appendLogAction("recei notify: cate=" + category + " msg=" + message + " udid=" + udid);
            }

            if (!DataStoreApp.getInstance().getCategoryGCM().equals(udid)) {
                String type = "";
                if (!DataStoreApp.getInstance().getCategoryGCM().equals(category)) {
                    type = category.split(Pattern.quote("|"))[0];            //MVISA or VIMO
                }

                dismissDialogQRScale();
                showDialogReceiverNotification(message, udid, type);
                DataStoreApp.getInstance().saveCategoryGCM(udid);
            }
//            if (!dataStoreApp.getCategoryGCM().equals(category)) {
//                dismissDialogQRScale();
//                showDialogReceiverNotification(message, category);
//                dataStoreApp.saveCategoryGCM(category);
//            }
        }
    };

    private void dismissDialogQRScale() {
        try {
            if (dialogQRCodeScale != null && dialogQRCodeScale.getDialog() != null
                    && dialogQRCodeScale.getDialog().isShowing()) {
                dialogQRCodeScale.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showDialogReceiverNotification(final String msg, final String udid, final String typeQR) {
//    private void showDialogReceiverNotification(final String msg, final String category) {
        final DialogMVisaSuccess dialogMVisaSuccess = new DialogMVisaSuccess(ActivityScanQRCode.this);
        dialogMVisaSuccess.setCanceledOnTouchOutside(false);
        dialogMVisaSuccess.setTitleDialog(getString(R.string.dialog_mvisa_title_success));
        dialogMVisaSuccess.setContentDialog(msg);
        dialogMVisaSuccess.setOnClickListenerDialogClose(v -> dialogMVisaSuccess.dismiss());
        dialogMVisaSuccess.setOnClickListenerDialogDetail(v -> {
            dialogMVisaSuccess.dismiss();
            checkStatus(udid, typeQR);
//                checkStatus(category);
        });
        dialogMVisaSuccess.show();
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (logUtil != null) {
            logUtil.saveLog();
        }
    }

    @Override
    protected void onDestroy() {
        // Unregister since the activity is about to be closed.
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver);

        // Dismiss dialogScaleQRCode if Activity Destroy
        dismissDialogQRScale();
        super.onDestroy();
    }

    private void getDataFromIntent() {
        Bundle bundle = getIntent().getBundleExtra(Constants.KEY_EXTRA_MVISA);
        if (bundle != null) {
            // from installment
            if (bundle.containsKey(Constants.QR_NAME)) {
                String qrName = bundle.getString(Constants.QR_NAME);
                if (DataStoreApp.getInstance().getListQrPay() != null && DataStoreApp.getInstance().getListQrPay().size() > 0
                        && qrName != null && !qrName.isEmpty()) {
                    for (QrPayResponse qrPayRes : DataStoreApp.getInstance().getListQrPay()) {
                        if (qrName.equals(qrPayRes.getQrName())) {
                            qrPayResponse = qrPayRes;
                            break;
                        }
                    }
                    if (qrPayResponse == null) {
                        qrPayResponse = new QrPayResponse();
                        qrPayResponse.setResourceDefault(R.drawable.ic_qr_scan_international);
                        qrPayResponse.setQrName(getString(R.string.dialog_mvisa_payment_base_on_qr_international_only_line));
                        qrPayResponse.setDescription(getString(R.string.txt_guide_use_qrcode));
                        qrPayResponse.setDescriptionEn(getString(R.string.txt_guide_use_qrcode));
                    }
                }
            }
            if (bundle.containsKey(Constants.KEY_MVISA_AMOUNT)) {
                amount = bundle.getString(Constants.KEY_MVISA_AMOUNT);
                Utils.LOGD(TAG, ">>> TransactionAmount: " + amount);
                tvAmount.setText(Utils.zenMoney(amount) + ConstantsPay.CURRENCY_SPACE_PRE);
            }

            if (bundle.containsKey(Constants.KEY_MVISA_EMAIL_CUSTOMER)) {
                emailCustomer = bundle.getString(Constants.KEY_MVISA_EMAIL_CUSTOMER);
                Utils.LOGD(TAG, ">>> EmailCustomer: " + emailCustomer);

//                if (!TextUtils.isEmpty(emailCustomer)) {
//                    tvEmail.setText(emailCustomer);
//                } else {
//                    tbrEmail.setVisibility(View.GONE);
//                    viewAboveEmail.setVisibility(View.GONE);
//                }
            }

            if (bundle.containsKey(Constants.KEY_MVISA_NOTE)) {
                note = bundle.getString(Constants.KEY_MVISA_NOTE);
                if (!TextUtils.isEmpty(note)) {
                    setHtmlNote(note);
                }
//                else {
//                    tvGuide.setVisibility(View.GONE);
//                    viewAboveNote.setVisibility(View.GONE);
//                }
            }

            if (bundle.containsKey(Constants.KEY_MVISA_QRID)) {
                qrid = bundle.getString(Constants.KEY_MVISA_QRID);
                Utils.LOGD("ActivityScanQRCode", "qrid: " + qrid);
            }

            qrCode = bundle.getString(Constants.KEY_MVISA_QRCODE);
            typeQR = bundle.getString(Constants.KEY_MVISA_TYPE, Constants.TYPE_QR_MVISA);
            udid = bundle.getString(Constants.KEY_MVISA_UDID);
            if (bundle.containsKey(Constants.EXTRA_INTENT)) {
                qrPayResponse = (QrPayResponse) bundle.getSerializable(Constants.EXTRA_INTENT);
            }
            if (!TextUtils.isEmpty(qrCode)) {
                tagLengthValue = qrCode;
                putContentQrToImage();
            }
//            else if (!TextUtils.isEmpty(amount)) {
//                generatedQRCODE();
//            }
        }
    }

//    private void generatedQRCODE() {
////        tvTimeCreated.setText(setTimeCreatedQRCode());
//
//        MerchantQrDataRequest merchantQrDataRequest = createMerchantDataRequest();
//        try {
//            tagLengthValue = generateQrCodeData(merchantQrDataRequest);
//        } catch (InputInvalidException e) {
//            e.printStackTrace();
//        }
//        putContentQrToImage();
//    }

    private void putContentQrToImage() {
        GenerateQRWithLogoCompany(tagLengthValue, imgQRCode);
        logUtil.appendLogAction("tlv=" + tagLengthValue);

        //note: new
        imgQRCode.setOnClickListener(v -> {
            dialogQRCodeScale = DialogQRCodeScale.newInstance(tagLengthValue, widthYourPhone);
            dialogQRCodeScale.show(getSupportFragmentManager(), DialogQRCodeScale.class.getName());
        });
    }

    public void GenerateQRWithLogoCompany(String tlv, ImageView imgQR) {
        try {
            //setting size of qr code
            int width = WIDTH;
            int height = WIDTH;
            int smallestDimension = Math.min(width, height);
            //setting parameters for qr code
            String charset = "UTF-8";
            Map<EncodeHintType, ErrorCorrectionLevel> hintMap = new HashMap<>();
            hintMap.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);

            QrCodeUtils qrCodeUtils = new QrCodeUtils();
            qrCodeUtils.createQRCode(this, imgQR, tlv, charset, hintMap, smallestDimension, smallestDimension);

        } catch (Exception ex) {
            Log.e("QrGenerate", ex.getMessage());
            logUtil.appendLogException("GenerateQRWithLogoCompany", ex.getMessage());
        }
    }



    /*private String setTimeCreatedQRCode() {
     */

    /**
     * date create format is
     * 09:50, 12-10-2017
     *//*
        long currentTime = new Date().getTime();
        String str_1 = MyUtils.convertTimestamp(currentTime, "HH:mm");
        String str_2 = MyUtils.convertTimestamp(currentTime, "dd-MM-yyyy");

        return str_1 + ", " + str_2;
    }*/
    private void setHtmlNote(String contentNote) {
        Spanned note;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            note = Html.fromHtml(getApplicationContext().getString(R.string.txt_note, contentNote), Html.FROM_HTML_MODE_LEGACY);
        } else {
            note = Html.fromHtml(getApplicationContext().getString(R.string.txt_note, contentNote));
        }
        tvGuide.setText(note);
    }

//    private void setPoweredMpos() {
//        Spanned poweredMpos;
//        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
//            poweredMpos = Html.fromHtml(getApplicationContext().getString(R.string.power_by_mpos), Html.FROM_HTML_MODE_LEGACY);
//        } else {
//            poweredMpos = Html.fromHtml(getApplicationContext().getString(R.string.power_by_mpos));
//        }
//        tvPoweredMpos.setText(poweredMpos);
//    }


    /*private MerchantQrDataRequest createMerchantDataRequest() {
        MerchantQrDataRequest merchantQrDataRequest = new MerchantQrDataRequest();

//      merchantQrDataRequest.setAdditionalConsumerDataRequest("");
        merchantQrDataRequest.setPayloadFormatIndicator("01");
        merchantQrDataRequest.setPointOfInitiation("12");

        merchantQrDataRequest.setMasterCardPan1(mvisaMidMaster);
        merchantQrDataRequest.setmVisaMerchantId(mvisaMid);
        merchantQrDataRequest.setStoreId(merchantId);
        merchantQrDataRequest.setMerchantName(merchantNameShortcut);
        merchantQrDataRequest.setMerchantCategoryCode(merchantCategoryCode);

        merchantQrDataRequest.setTerminalId(terminalId);
//        merchantQrDataRequest.setMerchantCategoryCode("1234");

        merchantQrDataRequest.setCityName("HANOI");
        merchantQrDataRequest.setCountryCode("VN");
        merchantQrDataRequest.setCurrencyCode("704");
        merchantQrDataRequest.setTransactionAmount(amount);

        merchantQrDataRequest.setTag62Present(true);
//        merchantQrDataRequest.setTerminalId(terminalId);

        if (!TextUtils.isEmpty(qrid)) {
            Utils.LOGD(TAG, "qrid: " + qrid);
            merchantQrDataRequest.setBillId(qrid);
        }

//        Gson gson = new Gson();
//        String json = gson.toJson(merchantQrDataRequest);
//        Utils.LOGD(TAG, "merchantQrDataRequest: "+json);

        return merchantQrDataRequest;
    }*/


    int errorCode;

    private void checkStatus(final String udid, final String type) {   //final String category,
//      category : "MVISA|731307994214"
//        String type = category.split(Pattern.quote("|"))[0];            //MVISA
//        final String orderCode = category.split(Pattern.quote("|"))[1]; //731307994214
//
//        Utils.LOGD(TAG, "------- checkStatus ------");
//        Utils.LOGD(TAG, ">>> category: " + category);
//        Utils.LOGD(TAG, ">>> type: " + type);
//        Utils.LOGD(TAG, ">>> orderCode: " + orderCode);

        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(null, getString(R.string.check_internet), this, v -> {
                checkStatus(udid, type);
//                    checkStatus(category);
            });
            return;
        }

        logUtil.appendLogRequestApi(Config.URL_CHECK_STATUS_MVISA);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, "TRANSACTION_PUSH_CHECK_STATUS");
            jo.put("udid", udid);
            jo.put("type", type);
//            jo.put("orderCode", orderCode);

            Utils.LOGD(TAG, "REQUEST: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(ActivityScanQRCode.this).post(ActivityScanQRCode.this, Config.URL_CHECK_STATUS_MVISA, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(TAG, "onFailure: " + arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.URL_CHECK_STATUS_MVISA + " onFailure", arg2);
                        mPgdl.hideLoading();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        String msg = null;
                        try {
//                            JsonParser jsonParser = new JsonParser();
                            JSONObject jRoot = new JSONObject(new String(arg2));

                            Utils.LOGD(TAG, "RESPONSE: " + jRoot);

                            if (jRoot.has("error")) {
                                final JSONObject jError = jRoot.getJSONObject("error");
                                errorCode = jError.getInt("code");
                                msg = jError.getString("message");
                                logUtil.appendLogAction("logIn: error=" + errorCode);
                                logUtil.saveLog();

                                //  SUCCESSFULLY!!!
//                                    {
//                                        "amount":"279.0",
//                                        "orderCode":"731307994214",
//                                        "pan":"422151******8039",
//                                        "cardHolderName":"DINH HOANG SON",
//                                        "createdDate":"1510211446000",
//                                        "transactionStatus":"APPROVED",
//                                        "integratedInfo":
//                                            {
//                                                "orderID":"279",
//                                                "orderType":1
//                                            },
//                                        "error":
//                                            {
//                                                "code":1000,
//                                                "message":"DO_SERVICE_SUCCESS",
//                                                "messageEn":""
//                                            }
//                                    }

                                if (errorCode == 1000) {
                                    String trangThaiThanhToan = JsonParser.getDataJson(jRoot, "transactionStatus");

                                    if (Constants.STATUS_TRANS_APPROVED.equals(trangThaiThanhToan)) {
                                        handleSuccessTransaction(jRoot, trangThaiThanhToan);
                                    } else {
                                        handleNotSuccessTransaction();
                                    }
                                } else {
                                    //  ERROR!!!
                                    final MposDialog mposDialogError = MyUtils.initDialogGeneralError(ActivityScanQRCode.this, errorCode, msg, ActivityScanQRCode.class.getName());
                                    mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
                                    mposDialogError.show();
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.URL_CHECK_STATUS_MVISA + " Exception:" + e.getMessage());
                            logUtil.saveLog();
                            Utils.LOGE(TAG, "Exception", e);
                        }

                        if (!TextUtils.isEmpty(msg)) {
                            logUtil.appendLogException(msg);
                            logUtil.saveLog();
                        }

                        logUtil.pushLog();
                    }
                });
    }

    private void handleSuccessTransaction(JSONObject jRoot, String trangThaiThanhToan) throws JSONException {
        String soTienThanhToan = JsonParser.getDataJson(jRoot, "amount");
        String soThe = JsonParser.getDataJson(jRoot, "pan");
        String tenKhachHang = JsonParser.getDataJson(jRoot, "cardHolderName");
        String email = DataStoreApp.getInstance().getemailMerchant();
        String createdDate = JsonParser.getDataJson(jRoot, "createdDate");
        String orderCode = JsonParser.getDataJson(jRoot, "orderCode");
        txId = JsonParser.getDataJson(jRoot, "txid");
        this.orderCode = orderCode;

        if (soTienThanhToan.endsWith(".0")) {
            soTienThanhToan = soTienThanhToan.replace(".0", "").trim();
        }

        String orderIDIntegrated = null;
        if (jRoot.has("integratedInfo")) {
            JSONObject jRootIntegratedInfo = jRoot.getJSONObject("integratedInfo");
            orderIDIntegrated = JsonParser.getDataJson(jRootIntegratedInfo, "orderID");
//                                        String orderTypeIntegrated = jsonParser.getDataJson(jRootIntegratedInfo, "orderType");
        }

        Utils.LOGD(TAG, "dataStoreApp.getOrderIdFromPartner(): " + DataStoreApp.getInstance().getOrderIdFromPartner());
        if (!TextUtils.isEmpty(orderIDIntegrated) && DataStoreApp.getInstance().getOrderIdFromPartner().equals(orderIDIntegrated)) {
//                                        send data to other apps
            Utils.LOGD(TAG, "------------------------------------------------------------------");
            Utils.LOGD(TAG, "orderIDIntegrated: " + orderIDIntegrated);
            callbackToPartner(orderIDIntegrated, createdDate);
        } else {
            DataQrPay dataQrPay = new DataQrPay(trangThaiThanhToan, soTienThanhToan
                    , orderCode, MyUtils.formatTimeTransaction(createdDate), getString(R.string.dialog_mvisa_payment_base_on_qr_international), soThe
                    , email, udid);
            dataQrPay.setTxId(txId);
            setResult(RESULT_OK);
            DialogResult dialogResult = DialogResult.newInstanceForMvisa(DialogResult.RESULT_SUCCESS, "", null, dataQrPay);
            dialogResult.setCancelable(false);
            dialogResult.show(getSupportFragmentManager(), DialogResult.class.getName());
        }
    }

    private void handleNotSuccessTransaction() {
        MyDialogShow.showDialogInfo(this, getString(R.string.error_not_pending_qr_transaction), false, view -> {
        });
    }

    private void callbackToPartner(String orderIdPartner, String transDate) {
        String urlCallback = DataStoreApp.getInstance().getUrlCallbackPartner();
        Utils.LOGD(TAG, "callbackToPartner: urlCallback = " + urlCallback);
        if (TextUtils.isEmpty(urlCallback)) {
            showDialogErrorCallbackPartner(getString(R.string.txt_no_urlcallback_copy_tid_to_clipboard));
        } else {
            try {
                //dataFromPartner.getContent()
                JSONObject jRoot = new JSONObject();
                jRoot.put("transType", 1);
                jRoot.put("transDesc", note);
                jRoot.put("transDate", transDate);
                jRoot.put("transAmount", Long.parseLong(amount));
                jRoot.put("muid", PrefLibTV.getInstance(this).getUserId());

                jRoot.put("orderId", orderIdPartner);

                jRoot.put("transCode", orderCode);
                jRoot.put("issuerCode", "QRPAYMENT");

                if (!TextUtils.isEmpty(DataStoreApp.getInstance().getExtParamPartner())) {
                    jRoot.put("extParam", DataStoreApp.getInstance().getExtParamPartner());
                }

                Utils.LOGD(TAG, "callbackToPartner: " + jRoot);

                StringBuilder secrectKey = new StringBuilder(DataStoreApp.getInstance().getIntergratSecrectKey());
                // if secrect_key = "" -> default: 16 character F
                for (int i = secrectKey.length(); i < 16; i++) {
                    secrectKey.append("F");
                }
                Utils.LOGD(TAG, "key: " + secrectKey);
                String content = EncodeDecode.doAESEncrypt(jRoot.toString(), DataStoreApp.getInstance().getIntergratSecrectKey());
                Utils.LOGD(TAG, "callbackToPartner: contentEncryptAES = " + content);
                content = Uri.encode(content);
                Utils.LOGD(TAG, "callbackToPartner: content encode=" + content);

                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(urlCallback + content)));
                finish();
            } catch (Exception e) {
                Utils.LOGE(TAG, "callBackToPartner: ", e);
                showDialogErrorCallbackPartner(String.format(getString(R.string.txt_copy_tid_to_clipboard), urlCallback));
            }
        }
    }

    private void showDialogErrorCallbackPartner(String msg) {
        MyDialogShow.showDialogCancelAndClick("", msg, getString(R.string.ALERT_BTN_OK), this, v -> {
            pushTextToClipboard(orderCode);
            finish();
        }, false, true);
    }

    private void pushTextToClipboard(String text) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("TransactionId", text);
        clipboard.setPrimaryClip(clip);
    }

    public static boolean checkIsTablet(Context context) {
        return (context.getResources().getConfiguration().screenLayout & Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }

}

