package com.mpos.screen.mart;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.mpos.common.MyApplication;
import com.mpos.models.DataOrder;
import com.mpos.models.DataVoid;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.screen.mart.socket.SocketServer;
import com.pax.dal.entity.EBeepMode;
import com.pax.emvdemo.ITYPaxApi;

import java.lang.ref.WeakReference;

import vn.mpos.R;


public class SocketService extends Service {

    private static final String TAG = SocketService.class.getSimpleName();
    //    SocketServer socketServer;
    SaveLogController logUtil = MyApplication.self().getSaveLogController();
    Context context;
    ITYPaxApi paxApi;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        appendLog("onStartCommand");
        int NOTIFICATION_ID = (int) (System.currentTimeMillis() % 10000);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Utils.LOGD(TAG, "onStartCommand channel1");
            NotificationChannel channel1 = new NotificationChannel(
                    getPackageName(),
                    context.getString(R.string.app_name),
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel1);
//            PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, 0);

            PendingIntent pendingIntent = PendingIntent.getActivity(context,
                0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_CANCEL_CURRENT);

            Notification notification = new NotificationCompat.Builder(this, getPackageName())
                    .setSmallIcon(R.drawable.ic_launcher_material)
                    .setContentIntent(pendingIntent)
                    .build();
            initSocketServer();
            startForeground(NOTIFICATION_ID, notification);
        } else {
            initSocketServer();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForeground(NOTIFICATION_ID, new Notification.Builder(this).build());
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        this.context = this;
        if (DevicesUtil.isPax()) {
            paxApi = ITYPaxApi.get(context);
        }

        //todo P8
//        int NOTIFICATION_ID = (int) (System.currentTimeMillis()%10000);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForeground(NOTIFICATION_ID, new Notification.Builder(this).build());
//        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        appendLog("onBind");
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        appendLog("onUnbind: ====>>");
        return super.onUnbind(intent);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        appendLog("onLowMemory: ======");
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        appendLog("onTaskRemoved: ====>");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        appendLog("onDestroy: ----");
//        stopForeground(true);
//        stopSelf();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        appendLog("onTrimMemory: ----");
    }

    private final IBinder mBinder = new LocalBinder();

    public class LocalBinder extends Binder {
        public SocketService getService() {
            return SocketService.this;
        }
    }

    ItfCallback itfCallback;

    interface ItfCallback {
        void onAddOrder(DataOrder data, EMartPresenter.ItfSendResultToClient in);

        void onCancelOrder(EMartPresenter.ItfSendResultToClient in);

        void onVoid(DataVoid data, EMartPresenter.ItfSendResultToClient in);

        void onErrSocket(String msg);

        void isClientConnected(boolean isConnect, String ipClient);
    }

    public void setItfCallBack(ItfCallback itfCallback) {
        this.itfCallback = itfCallback;
    }

    public void initSocketServer() {
        boolean hasOldInstance = SocketServer.checkExitAnCloseOldInstance();
        Utils.LOGD(TAG, "initSocketServer: hasOldInstance=" + hasOldInstance);
        appendLog(hasOldInstance ? "has oldInstance -> close it" : "has not oldInstance");

        SocketServer.getInstance().initParams(new WeakReference(this), logUtil, new EMartPresenter.ItfProcessMsgSocket() {
            @Override
            public void addOrder(DataOrder data, EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onAddOrder(data, in);
                }
            }

            @Override
            public void doCancelOrder(EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onCancelOrder(in);
                }
            }

            @Override
            public void doVoid(DataVoid data, EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onVoid(data, in);
                }
            }

            @Override
            public void doErrSocket(String msg) {
                if (itfCallback != null) {
                    itfCallback.onErrSocket(msg);
                }
            }
        }, (isConnect, ipClient) -> {
            if (itfCallback != null) {
                if (!TextUtils.isEmpty(ipClient)) {
                    itfCallback.isClientConnected(isConnect, ipClient);
                } else {
                    itfCallback.isClientConnected(isConnect, "");
                }
            }
        });

        SocketServer.getInstance().openConnect();
    }

    public void closeTaskSocket() {
        if (SocketServer.getInstance() != null) {
            SocketServer.getInstance().closeTaskSocket();
            itfCallback = null;
//            socketServer = null;
        }
    }

    private void appendLog(String msg) {
        Utils.LOGD(TAG, "serviceSocket->" + msg);
        if (logUtil != null) {
            logUtil.appendLogAction("serviceSocket->" + msg);
        }
    }

    private void beep() {
        if (paxApi != null) {
            paxApi.beep(EBeepMode.FREQUENCE_LEVEL_0, 300);
        }
    }
}