package com.mpos.screen.login;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.Html;
import android.text.InputType;
import android.text.TextUtils;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.mpos.customview.MposDialog;
import com.mpos.customview.MyDialog;
import com.mpos.dspread.DeviceListActivity;
//import com.mpos.pinpad.DeviceActivity;
import com.mpos.screen.ActivityRegisterMerchant;
import com.mpos.screen.FragmentWebview;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.MyUtils;
import com.pps.core.DataUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;

import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import vn.mpos.BuildConfig;
import vn.mpos.R;

public class LoginView implements LoginContract.View {

    private static final String TAG = "LoginView";

    // view
    @BindView(R.id.version) 				protected TextView tvVersion;
    @BindView(R.id.tv_register)   	        protected TextView tvRegister;
    @BindView(R.id.tv_guide_pr01)   	    protected TextView tvGuidePr01;
    @BindView(R.id.tv_guide_pr02)   	    protected TextView tvGuidePr02;
    @BindView(R.id.tv_guide_ar01)   	    protected TextView tvGuideAr01;
    @BindView(R.id.tv_call_support)   	    protected TextView tvCallSupport;
    @BindView(R.id.tv_selected_device)		protected TextView tvSelectedDevice;

    @BindView(R.id.user_id) 				protected EditText edtUserId;
    @BindView(R.id.user_pin) 				protected EditText edtUserPin;
    @BindView(R.id.imv_back) 				protected ImageView imvBack;

    @BindView(R.id.v_splash)				protected View vSplash;
    @BindView(R.id.v_connect)				protected View vConnect;

    @BindView(R.id.btn_config_base_pax)		protected Button btnConfigBasePax;

    private TextView tvCurrGuide;

    private LoginContract.Presenter presenter;
    private final ToastUtil toastUtil;
    private final MyProgressDialog mPgdl;

    private final Context context;
    private Activity activity;

    public LoginView(Activity activity) {
        ButterKnife.bind(this, activity);
        context = activity;
        this.activity = activity;
        toastUtil = new ToastUtil(activity);
        mPgdl = new MyProgressDialog(context);
        initView();
    }

    @Override
    public void setPresenter(LoginContract.Presenter presenter) {
        this.presenter = presenter;
    }

    public void start() {
        presenter.start();
        showPhoneSupport(presenter.getPhoneByTime());
    }

    private void initView() {
        tvRegister.setText(Html.fromHtml(context.getString(R.string.please_register)));
        tvVersion.setText(BuildConfig.VERSION_NAME);

        edtUserPin.setOnEditorActionListener((v, actionId, event) -> {
            onClickLogin();
//            presenter.attemptLogin();
            return false;
        });

        // show config base pax
        if (DevicesUtil.isPaxA920()) {
            btnConfigBasePax.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showPhoneSupport(String phone) {
        tvCallSupport.setText(context.getString(R.string.txt_support, phone));
    }

    @OnClick({R.id.log_in})
    protected void onClickLogin() {
        String mAcc = DataUtils.getTextInEdt(edtUserId);
        String mPin = DataUtils.getTextInEdt(edtUserPin);

        if (TextUtils.isEmpty(mAcc) || TextUtils.isEmpty(mPin)) {
            showToast(context.getString(R.string.ALERT_LOGIN_FAIL_MISSING_FIELD_MSG));
        }
        else {
            presenter.attemptLogin(mAcc, mPin);
        }
    }

    @OnClick({R.id.btn_config_base_pax})
    protected void onClickConfigBasePax() {
        presenter.gotoConfigBasePax();
    }


    @OnClick({
            R.id.v_audio, R.id.v_pinpad, R.id.v_dspread
            , R.id.register_merchant
            , R.id.tv_register
            , R.id.rltNotNeedDevice
            , R.id.imv_back
            , R.id.tv_call_support

    })
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.v_audio:
                hideCurrGuide(tvGuideAr01);
                presenter.selectConnectReader(ConstantsPay.DEVICE_AUDIO);

                break;
            case R.id.v_pinpad:
                hideCurrGuide(tvGuidePr01);
                presenter.selectConnectReader(ConstantsPay.DEVICE_PINPAD);

                break;
            case R.id.v_dspread:
                hideCurrGuide(tvGuidePr02);
                presenter.selectConnectReader(ConstantsPay.DEVICE_DSPREAD);

                break;
            case R.id.rltNotNeedDevice:
                hideCurrGuide(null);
                tvSelectedDevice.setVisibility(View.VISIBLE);
                tvSelectedDevice.setText(context.getString(R.string.selected_qr_pay));

                initViewInputByType(true);
                showUserCacheByType(true);
                presenter.selectConnectReader(ConstantsPay.DEVICE_NONE);
                vConnect.setVisibility(View.GONE);

                break;
            case R.id.register_merchant:
            case R.id.tv_register:
                Intent intent = new Intent(context, ActivityRegisterMerchant.class);
                intent.putExtra("typeLoad", FragmentWebview.TYPE_NONE);
                context.startActivity(intent);
                break;
            case R.id.imv_back:
                goBackScreenSelectTypePayment();
                break;
            case R.id.tv_call_support:
                MyUtils.callToSupport(presenter.getPhoneByTime(), context);
                break;
        }
    }

    private void hideCurrGuide(TextView tv) {
        if (tvCurrGuide != null) {
            tvCurrGuide.setVisibility(View.GONE);
        }
        tvCurrGuide = tv;
        if (tv != null) {
            tv.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void hideImvBack() {
        imvBack.setVisibility(View.GONE);
    }

    private void goBackScreenSelectTypePayment() {
        if (DevicesUtil.isP20L() || DevicesUtil.isPax()) {
            return;
        }

        resetUserCache();
        vConnect.setVisibility(View.VISIBLE);
        tvSelectedDevice.setText("");
        tvSelectedDevice.setVisibility(View.GONE);
        tvSelectedDevice.setOnClickListener(null);

        UIUtil.hideKeyboard(context, edtUserId);

    }
    private void resetUserCache() {
        edtUserId.setText("");
        edtUserPin.setText("");
    }

    private void showUserCacheByType(boolean isLoginWithEmail) {
        String userSave = PrefLibTV.getInstance(context).getUserId();
        Utils.LOGD(TAG, "userSave: "+userSave);
        if (!TextUtils.isEmpty(userSave)) {
            if (isLoginWithEmail) {
                if (userSave.contains("@")) {
                    edtUserId.setText(userSave);
                    edtUserPin.requestFocus();
                } else {
                    edtUserId.setText("");
                    edtUserId.requestFocus();
                }
            }
            // login with reader
            else {
                if (userSave.contains("@")) {
                    edtUserId.setText("");
                    edtUserId.requestFocus();
                } else {
                    edtUserId.setText(userSave);
                    edtUserPin.requestFocus();
                }
            }
        }
    }


    @Override
    public void showProgressBar(boolean show) {
        if (show) {
            mPgdl.showLoading();
        }
        else {
            mPgdl.hideLoading();
        }
    }

    @Override
    public void showToast(String msg) {
        Utils.LOGD(TAG, "showToast: "+msg);
        toastUtil.showToast(msg);
    }

    @Override
    public void showError(int errorCodeLogin, String msg, boolean resetInputPin) {
        if (resetInputPin) {
            edtUserPin.setText("");
        }
        vSplash.setVisibility(View.GONE);

        final MposDialog mposDialogError = MyUtils.initDialogGeneralError(
                context, errorCodeLogin, msg, LoginView.class.getName());
        mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
        mposDialogError.show();
    }

    @Override
    public void showErrorLoadOnFailureMpos(String msg) {
        vSplash.setVisibility(View.GONE);

        final MyDialog mdialog = new MyDialog(context, R.style.SpecialDialog);
        mdialog.setCancelable(true);
        mdialog.setTitle(context.getString(R.string.txt_notice));
        mdialog.setBodyText(context.getString(R.string.error_load_merchant_info_mpos, TextUtils.isEmpty(msg)? "" : "(" + msg + ")"));
        mdialog.setTextOk(context.getString(R.string.retry_button));
        mdialog.setTextCancel(context.getString(R.string.txt_continue));

        mdialog.setOnClickListenerOk(v -> {
            mdialog.dismiss();
            presenter.fetchMerchantInfo();
        });
        mdialog.setOnClickListenerCancel(v -> {
            mdialog.dismiss();
            presenter.handlerLoadFailMpos();
        });
        mdialog.show();

    }

    @Override
    public void showAlertCountDown(String msgCountDown, String timeShow) {

        com.mpos.sdk.view.MposDialog mposDialogAlert = new com.mpos.sdk.view.MposDialog(context);
        mposDialogAlert.setType(com.mpos.sdk.view.MposDialog.TYPE_DIALOG_WARNING);
        mposDialogAlert.setDesDialogErrorTop(msgCountDown);
        mposDialogAlert.setEnableTwoButtonBottom(true);
        mposDialogAlert.setVisibleViaPhoneEmail(false);

        mposDialogAlert.setLabelForButtonOk(context.getString(R.string.LOGIN_BTN_CONTINUE));
        mposDialogAlert.setLabelForButtonCancel(context.getString(R.string.BTN_CANCEL));
        mposDialogAlert.setOnClickListenerButtonOk(view -> {
            Utils.LOGD(TAG, "showDialogAlert: click ok");
            mposDialogAlert.dismiss();
            presenter.initConfigServer();
        });
        mposDialogAlert.setOnClickListenerButtonCancel(view -> {
            Utils.LOGD(TAG, "showDialogAlert: click cancel");
            mposDialogAlert.dismiss();
            ((Activity) context).finish();
        });
        if (!TextUtils.isEmpty(timeShow) && TextUtils.isDigitsOnly(timeShow)) {
            try {
                int timeCountdown = Integer.parseInt(timeShow);
                mposDialogAlert.setTimeoutDisableButton(timeCountdown);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        mposDialogAlert.show();
        mposDialogAlert.startCountDownTime();
    }

    @Override
    public void showErrorLoadMpos(int errorCode, String msg) {
//        logUtil.appendLogAction("show dialog error load mpos");
        final MposDialog mdialog = MyUtils.initDialogGeneralError(
                context,
                errorCode,
                msg,
                LoginView.class.getName());

        mdialog.setOnClickListenerDialogClose(v -> {
            mdialog.dismiss();
            vSplash.setVisibility(View.GONE);
        });
        if (ScreenUtils.canShowDialog(context)) {
            mdialog.show();
        }

//        logUtil.pushLog();
    }

    @Override
    public void showUserId(String userId) {
        edtUserId.setText(userId);
    }

    @Override
    public String getUserIdEnter() {
        return DataUtils.getTextInEdt(edtUserId);
    }

    @Override
    public void showUserPin(String userPin) {
        edtUserPin.setText(userPin);
    }

    @Override
    public void showViewSplash(boolean show) {
        vSplash.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @Override
    public void requestFocusView(int id) {
        ((Activity) context).findViewById(id).requestFocus();
    }

    @Override
    public void showViewConnectDevice(int typeDevice) {
        if (typeDevice == ConstantsPay.DEVICE_DSPREAD) {
            showDeviceDspread();
        } else {
            showActivityListDevicesPR();
        }
    }

    private void showActivityListDevicesPR() {
//        Intent intentRequestDevice = new Intent(context, DeviceActivity.class);
//        intentRequestDevice.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
//        ((Activity)context).startActivityForResult(intentRequestDevice, LoginPresenter.REQUEST_DEVICE);
    }

    private void showDeviceDspread() {
        Intent intentRequestDevice = new Intent(context, DeviceListActivity.class);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_IS_CONNECT_DEVICE, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_AUTO_CONNECT, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_DISCONNECT_WHEN_DESTROY, true);
        intentRequestDevice.putExtra(DeviceListActivity.EXTRA_SHOW_IMG_INFO, true);
        intentRequestDevice.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        ((Activity)context).startActivityForResult(intentRequestDevice, LoginPresenter.REQUEST_DEVICE_DSPREAD);
    }

    @Override
    public void showSerialNumber(int typeDevice, String serialNumber) {
        vConnect.setVisibility(View.GONE);
        tvSelectedDevice.setVisibility(View.VISIBLE);
        tvSelectedDevice.setText(context.getString(R.string.serial_number, serialNumber));
        initViewInputByType(false);
    }

    private void initViewInputByType(boolean isLoginWithEmail) {
        if (isLoginWithEmail) {
            edtUserId.setHint(context.getString(R.string.tv_login_baseon_email_enter_email));
            edtUserPin.setInputType(InputType.TYPE_CLASS_TEXT);
        } else {
            edtUserId.setHint(context.getString(R.string.LOGIN_LBL_STAFF_ID));
            edtUserPin.setInputType(InputType.TYPE_CLASS_NUMBER);
        }
        edtUserPin.setTransformationMethod(PasswordTransformationMethod.getInstance());
    }
}
