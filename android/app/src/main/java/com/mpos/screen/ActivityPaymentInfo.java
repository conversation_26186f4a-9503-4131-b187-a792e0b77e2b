package com.mpos.screen;

import android.app.Dialog;
import android.content.ClipboardManager;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.common.NotifyPushController;
import com.mpos.customview.DialogQuickWithdrawMoney;
import com.mpos.customview.DialogVoid;
import com.mpos.customview.MposDialog;
import com.mpos.customview.ViewCashierReward;
import com.mpos.customview.ViewToolBar;
import com.mpos.dialogs.CancelTransDialogFragment;
import com.mpos.dialogs.SuccessDialogFragment;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataHistoryQrNl;
import com.mpos.models.DataTransStatusQrNl;
import com.mpos.models.DetailItemPaymentEmart;
import com.mpos.models.PaymentItem;
import com.mpos.models.PaymentItemEmart;
import com.mpos.screen.mart.ReceiverManagerFinish;
import com.mpos.screen.printer.BaseActivityPrint;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.TransItemMacq;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.ButterKnifeUtils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.BindViews;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.ViewCollections;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

import static com.mpos.screen.mart.EMartPresenter.EMART_VOUCHER_CARD;

public class ActivityPaymentInfo extends BaseActivityPrint implements CancelTransDialogFragment.OnClickButtonCancelTransListener, SuccessDialogFragment.OnDismissSuccessDialogListener {

    public static final String tag = ActivityPaymentInfo.class.getSimpleName();

    public static final int RESULT_CODE_RE_SIGNATURE = 1;

    //	@BindView(R.id.root) LinearLayout llRootNormal;
    @BindViews({R.id.v_button, R.id.btn_quick_recei_money, R.id.v_top_info_card, R.id.v_info_card})
    List<View> vNormalPay;
    @BindView(R.id.btn_quick_recei_money)
    protected Button btnQuickReceiMoney;
    @BindView(R.id.void_payment)
    protected Button btnVoidPay;
    @BindView(R.id.btn_continue)
    protected Button btnContinue;
    @BindView(R.id.btn_print_receipt)
    protected Button btnPrintReceipt;
    @BindView(R.id.btn_send_mail)
    protected Button btnSendMail;
    @BindView(R.id.ll_btn_vaymuon)
    protected LinearLayout llBtnVaymuon;
    @BindView(R.id.btn_vm_quick_recei_money)
    protected Button btnVMQuickReceiMoney;
    @BindView(R.id.btn_vm_check_status)
    protected Button btnVMCheckStatus;
    @BindView(R.id.log_out)
    protected ImageView imvArrow;
    @BindView(R.id.imv_status)
    protected ImageView imvStatus;
    @BindView(R.id.time)
    protected View vTimeInclude;
    @BindView(R.id.tv_status)
    protected TextView tvStatus;
    @BindView(R.id.tv_amount)
    protected TextView tvAmount;
    @BindView(R.id.number)
    protected TextView tvCardNumber;
    @BindView(R.id.tv_card_type)
    protected TextView tvCardType;
    @BindView(R.id.approval_code)
    protected TextView tvApprovalCode;
    @BindView(R.id.invoice_no)
    protected TextView tvInvoiceNo;
    @BindView(R.id.tv_card_holder_name)
    protected TextView tvCardHolderName;
    @BindView(R.id.tv_batch_no)
    protected TextView tvBatchNo;
    @BindView(R.id.tv_trans_id)
    protected TextView tvTransId;
    @BindView(R.id.description)
    protected TextView tvDesc;
    @BindView(R.id.tv_trans_time)     protected TextView tvTransTime;
    @BindView(R.id.tv_trans_date)     protected TextView tvTransDate;
    @BindView(R.id.tv_tid)     protected TextView tvTid;
    @BindView(R.id.tv_mid)     protected TextView tvMid;
//    @BindView(R.id.time)        protected TextView tvTime;


    @BindView(R.id.v_feedback)
    protected View vRootCashierReward;

    //qr-nl
    @BindView(R.id.tv_status_qr_nl)
    TextView tvStatusQrNl;
    @BindView(R.id.tv_time_paid)
    TextView tvTimePaidQr;

    // mvisa
    @BindView(R.id.llParentDetailInfoMVisa)
    LinearLayout llIncludeMVISA;

    @BindView(R.id.tv_mvisa_money_detail)
    TextView tvSoTien;
    @BindView(R.id.tv_mvisa_magiaodich_detail)
    TextView tvMaGiaoDich;
    @BindView(R.id.tv_mvisa_time_create_detail)
    TextView tvThoiGian;
    @BindView(R.id.tv_mvisa_payment_baseon_detail)
    TextView tvThanhToanQua;
    @BindView(R.id.tv_mvisa_number_card_detail)
    TextView tvSoThe;
    @BindView(R.id.tv_mvisa_email_detail)
    TextView tvEmail;
    @BindView(R.id.tv_note_detail)
    TextView tvNote;

    // vaymuon
    @BindView(R.id.llParentDetailInfoVaymuon)
    LinearLayout llIncludeVaymuon;

    @BindView(R.id.tv_vm_money_detail)
    TextView tvVMAmount;
    @BindView(R.id.tv_vm_cardholdername_detail)
    TextView tvVMCustomerName;
    @BindView(R.id.tv_vm_payment_method)
    TextView tvVMPaymentMethod;
    @BindView(R.id.tv_vm_phone_number)
    TextView tvVMCustomerPhone;
    @BindView(R.id.tv_vm_status)
    TextView tvVMStatus;
    @BindView(R.id.rl_vm_wait_explain)
    RelativeLayout rlVMStatusExplain;
    @BindView(R.id.rl_vm_kt_explain)
    LinearLayout rlVMStatusKTExplain;
    @BindView(R.id.tv_vm_magiaodich_detail)
    TextView tvVMTransCode;
    @BindView(R.id.tv_vm_time_create_detail)
    TextView tvVMTransTime;
    @BindView(R.id.tv_vm_content_payment)
    TextView tvVMPaymentContent;
    @BindView(R.id.tv_vm_void_reason)
    TextView tvVMVoidReason;
    @BindView(R.id.rl_vm_void_reason)
    LinearLayout rlVMVoidReason;

    // Vimolink
    @BindView(R.id.llParentDetailInfoVimoLink)
    LinearLayout llIncludeVimolink;

    @BindView(R.id.tv_link_money_detail)
    TextView tvLinkAmount;
    @BindView(R.id.tv_link_payment_method)
    TextView tvLinkPaymentMethod;
    @BindView(R.id.tv_link_cardholdername_detail)
    TextView tvLinkCardName;
    @BindView(R.id.tv_link_card_type)
    TextView tvLinkCardType;
    @BindView(R.id.tv_link_card_number)
    TextView tvLinkCardNumber;
    @BindView(R.id.tv_link_status)
    TextView tvLinkStatus;
    @BindView(R.id.tv_link_magiaodich_detail)
    TextView tvLinkTransCode;
    @BindView(R.id.tv_link_time_create_detail)
    TextView tvLinkTimeCreate;
    @BindView(R.id.tv_link_content_payment)
    TextView tvLinkContenPayment;
    @BindView(R.id.ll_btn_link)
    protected LinearLayout llBtnVimoLink;

    @BindView(R.id.tv_mvisa_cardholdername_detail)
    TextView tvTenChuThe;
    @BindView(R.id.tv_mvisa_type_card_detail)
    TextView tvLoaiThe;

    @BindViews({R.id.v_info, R.id.v_button, R.id.btn_quick_recei_money})
    List<View> vInfo;

    @BindView(R.id.v_detail_qr_nl)
    View v_detail_qr_nl;

    @BindView(R.id.v_loading)
    ProgressBar v_loading;

    @BindView(R.id.btn_update_status_qr_nl)
    Button btnUpdateStatusQrNl;

    // enter card
    @BindView(R.id.ll_link_pending_reason)
    LinearLayout llLinkPendingReason;
    @BindView(R.id.tv_link_pending_reason)
    TextView tvLinkPendingReason;
    @BindView(R.id.iv_polygon)
    ImageView ivPolygon;

    ViewToolBar vToolBar;

    private String mTranId;
    private String mTokenL2;
    private String trxType;
    private String transactionRequestId;
    private String transactionDate;
    private String itemDescription = "";
    private String udid;

    private String amountPay;
    private String pan;
    private String holderName;
    private boolean isPayInstallment = false;
    private boolean isPayCashBack = false;
    private int transactionStatus;

    private MyProgressDialog mPgdl;
    private ToastUtil mToast;
    private SaveLogController logUtil;

    private ViewCashierReward vCashierReward;
    //params for MVISA
    //-- begin --
    public static final String KEY_TXID = "key_txid";
    //--- end ---

    PaymentItem paymentInfo;
    TransItemMacq  transItemMA;
    PaymentItemEmart.TransItem transItemEmart;
    DataHistoryQrNl.ResultData.Data transItemQrNl;
    String TAG = "ActivityPaymentInfo";
    private boolean needReloadOnBack = false;
    private boolean showVMQuickDraw = false;

    private boolean isDomesticInMVisa;
    private boolean isTranMultiAcquirer = false;

    // Voucher Emart
    String authCode     = "000000";
    String orderCode    = "";
//    String issuerCode = "GIFT CARD";
    String amount = "";
//    String date = "";

    DetailItemPaymentEmart itemPaymentEmartDetail;

    private boolean isVoucher = false;

    @Override
    public StringEntity buildStringEntityReceipt() {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", Config.DOWNLOAD_RECEIPT_IMAGE);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(this).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(this).getSessionKey());
            jo.put("tokenL2", PrefLibTV.getInstance(this).getTKL2());
            if (getIntent().getBooleanExtra("type", true)) {
                jo.put("transactionRequestID", getIntent().getIntExtra("tid", 0));
            }
            else {
                jo.put("transactionID", getIntent().getStringExtra("tid"));
            }
            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(this).getSessionKey()));
            Utils.LOGD(TAG, "Data: "+ jo);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return entity;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_payment_info);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        vToolBar = new ViewToolBar(this, findViewById(R.id.container));
        vToolBar.showTextTitle(getString(R.string.NAV_BAR_TITLE_SALES_DETAIL));
        vToolBar.showButtonBack(true);

        logUtil = MyApplication.self().getSaveLogController();
        mPgdl = new MyProgressDialog(this);
        mToast = new ToastUtil(this);

        initUI();
        showView(vInfo, false);

        if (getIntent() != null) {
            FragmentPaymentHistory.TypePaymentInfor type = (FragmentPaymentHistory.TypePaymentInfor) getIntent().getSerializableExtra("type");
            Utils.LOGD(TAG, "type=== " + type);
            switch (type) {
                case DETAIL_MA:
                    transItemMA = (TransItemMacq) getIntent().getSerializableExtra(IntentsMP.EXTRA_TRANS_INFO);
                    if (transItemMA != null) {
                        isTranMultiAcquirer = true;
                        setLayoutMVisa(false);
                        loadSaleDetailMa(transItemMA.getWfId());
                    }
                    break;
                case DETAIL_BANK:
                    paymentInfo = (PaymentItem) getIntent().getSerializableExtra(IntentsMP.EXTRA_PAYMENT_INFO);
                    if (paymentInfo != null) {
                        handlerPaymentInfor();
                    }
                    break;
                case GIFT_CARD_EMART:
                    transItemEmart = (PaymentItemEmart.TransItem) getIntent().getSerializableExtra(IntentsMP.EXTRA_TYPE_HISTORY_EMART);
                    if (transItemEmart != null) {
                        isVoucher = true;
                        loadDataEmart();
                    }
                    break;
                case QR_NL:
                    transItemQrNl = (DataHistoryQrNl.ResultData.Data) getIntent().getSerializableExtra(IntentsMP.EXTRA_TYPE_HISTORY_QR_NL);
                    if (transItemQrNl != null) {
                        Utils.LOGD(TAG, "transItemQrNl= " + transItemQrNl.getAmount());
                        loadDataQr();
                    }
                    break;
                default:
                    break;
            }

//            transItemMA = (TransItemMacq) getIntent().getSerializableExtra(IntentsMP.EXTRA_TRANS_INFO);
//            if (transItemMA == null) {
//                paymentInfo = (PaymentItem) getIntent().getSerializableExtra(IntentsMP.EXTRA_PAYMENT_INFO);
//                if (paymentInfo == null) {
//                    transItemEmart = (PaymentItemEmart.TransItem) getIntent().getSerializableExtra(IntentsMP.EXTRA_TYPE_HISTORY_EMART);
//                    if (transItemEmart != null) {
//                        isVoucher = true;
//                        loadDataEmart();
//                    }
//                } else {
//                    handlerPaymentInfor();
//                }
//            }
//            else {
//                isTranMultiAcquirer = true;
//                setLayoutMVisa(false);
//                loadSaleDetailMa(transItemMA.getWfId());
//            }
        }

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            ReceiverManagerFinish.getInstance().pushActivityNeedFinish(this);
        }
    }

    private void handlerPaymentInfor() {
        if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
            isDomesticInMVisa = getIntent().getBooleanExtra(IntentsMP.EXTRA_IS_DOMESTIC_IN_MVISA, false);
            this.txId = getIntent().getStringExtra(KEY_TXID);
            if (!TextUtils.isEmpty(txId)) {
                setLayoutMVisa(!isDomesticInMVisa);
                loadDataDetailTransMVisa(txId);
            }
        } else {
            setLayoutMVisa(false);
            loadSalesDetail();
        }
    }

    private void loadDataEmart() {
        logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + "--");
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("orderCode", transItemEmart.getOrderCode());
            jo.put("authCode", transItemEmart.getAuthCode());
            jo.put("transID", transItemEmart.getTransID());
            Utils.LOGD(TAG, jo.toString());

            Utils.LOGD(TAG, "getEmartDetail: send=>" + PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            entity = ApiMultiAcquirerInterface.getInstance()
                    .buildStringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(tag, "getEmartDetail: " + e1.getMessage());
        }

        Utils.LOGD(tag, "getEmartDetail: url=" + ConstantsPay.getUrlServer(this));

        MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, ApiMultiAcquirerInterface.URL_DETAIL_TRANS_EMART_CARD,
                entity, Config.CONTENT_TYPE, new MyTextHttpResponseHandler(this) {

                    @Override
                    public void onFailApi(int i, Header[] headers, String s, Throwable throwable) {
                        Utils.LOGD(TAG, "getSalesHistoryEmart onFailure() called with: statusCode = [" + i + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + s + "], throwable = [" + throwable + "]");
                        String clearData = MacqUtil.getInstance().parseAndDecryptData(s);
                        logUtil.appendLogAction("onFail  sale Emart: " + clearData);

                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                                ActivityPaymentInfo.this, v -> loadSalesDetail(), true);
                    }

                    @Override
                    public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                        try {
                            mPgdl.hideLoading();
                            Utils.LOGD(TAG, "getSalesHistoryEmart onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                            String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                            Utils.LOGD(TAG, "-getSalesHistoryEmart: " + clearData);

                            DetailItemPaymentEmart itemPaymentEmartDetail = MyGson.parseJson(clearData, DetailItemPaymentEmart.class);
                            setupViewEmart(itemPaymentEmartDetail);

                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " exception(timeout)");
                            Utils.LOGE(tag, "Exception", e);
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityPaymentInfo.this);
                        }
                    }
                });
    }

    private void loadDataQr() {
        showView(vNormalPay, false);
        showView(vInfo, true);
        v_detail_qr_nl.setVisibility(View.VISIBLE);
        btnUpdateStatusQrNl.setVisibility(View.VISIBLE);
        btnQuickReceiMoney.setVisibility(View.GONE);
        btnSendMail.setVisibility(View.GONE);
        btnPrintReceipt.setVisibility(View.GONE);

        amount = transItemQrNl.getAmount();
        if (!TextUtils.isEmpty(amount)) {
            String builderAmount = Utils.zenMoney(amount)+ ConstantsPay.CURRENCY_SPACE_PRE;
            tvSoTien.setText(ConstantsPay.CURRENCY_SPACE_POST + " " + builderAmount);
        }

        if (!TextUtils.isEmpty(transItemQrNl.getTime_created())) {
            String transTime = Utils.convertTimestamp(Long.parseLong(transItemQrNl.getTime_created()) * 1000, 4);
            tvThoiGian.setText(transTime);
        }

        if (!TextUtils.isEmpty(transItemQrNl.getTime_paid())) {
            String transTime = Utils.convertTimestamp(Long.parseLong(transItemQrNl.getTime_paid()) * 1000, 4);
            tvTimePaidQr.setText(transTime);
        }

        orderCode = transItemQrNl.getOrder_code();
        if (!TextUtils.isEmpty(orderCode)) {
//            int startIndex = orderCode.indexOf('-') + 1;
//            int endIndex = orderCode.indexOf('|');
//            tvMaGiaoDich.setText(orderCode.substring(startIndex, endIndex));
            tvMaGiaoDich.setText(orderCode.substring(orderCode.length() - 6));
        }

        String methodName = transItemQrNl.getPayment_method_name();
//        String tokenCode = transItemQrNl.getToken_code();
        tvThanhToanQua.setText(methodName);

        new MyUtils().setViewStatusByStatusQrNl(this, transItemQrNl.getStatus(), tvStatusQrNl);
    }

    private void setupViewEmart(DetailItemPaymentEmart itemPaymentEmartDetail) {
        this.itemPaymentEmartDetail = itemPaymentEmartDetail;
        showView(vInfo, true);
        btnQuickReceiMoney.setVisibility(View.GONE);
        btnSendMail.setVisibility(View.GONE);
        btnPrintReceipt.setVisibility(View.GONE);

        MyUtils myUtils = new MyUtils();

        authCode = (TextUtils.isEmpty(itemPaymentEmartDetail.getData().getAuthCode()) ? "" : itemPaymentEmartDetail.getData().getAuthCode());
        amount = itemPaymentEmartDetail.getData().getAmount();
//        date = Utils.convertTimestamp(Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate()), 4);
//        pan = CardUtils.getMaskedPan(itemPaymentEmartDetail.getData().getPan());

        String builderAmount = Utils.zenMoney(amount)+ ConstantsPay.CURRENCY_SPACE_PRE;
        tvAmount.setText(ConstantsPay.CURRENCY_SPACE_POST + " " + builderAmount);
        tvCardNumber.setText(pan);
        tvApprovalCode.setText(getString(R.string.msg_approved_code, authCode));

        myUtils.setViewStatusByStatusGiftCard(this, itemPaymentEmartDetail.getData().getTransactionStatus(), tvStatus);

        tvTransId.setText(itemPaymentEmartDetail.getData().getTransID());

        Utils.LOGD(TAG, "getDate= " + DateFormat.format("dd-MM-yyyy", Long.parseLong("1666977307835")).toString());
//        String transDate = DateFormat.format("dd-MM-yyyy", Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate())).toString();
//        String transTime = DateFormat.format("hh:mm:ss", Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate())).toString();

        String transDate = Utils.convertTimestamp(Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate()), 2);
        String transTime = Utils.convertTimestamp(Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate()), 5);

        tvTransTime.setText(transTime);
        tvTransDate.setText(transDate);

        tvTid.setText(itemPaymentEmartDetail.getData().getMposTID());
        tvMid.setText(itemPaymentEmartDetail.getData().getMposMID());
        tvCardType.setText(EMART_VOUCHER_CARD);

        if (itemPaymentEmartDetail.getData().getTransactionStatus().equals(Config.CODE_STATUS_SUCCESS)) {
            btnPrintReceipt.setVisibility(View.VISIBLE);
        }
    }

    private void setLayoutMVisa(boolean isMVISA) {
        if (isMVISA) {
            if (paymentInfo != null && Constants.VAYMUONQR.equals(paymentInfo.getTransactionPushType())) {
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.VISIBLE);
                llIncludeVimolink.setVisibility(View.GONE);
            } else if (paymentInfo != null && Constants.LINKCARD.equals(paymentInfo.getTransactionPushType())) {
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.GONE);
                llIncludeVimolink.setVisibility(View.VISIBLE);
            } else {
                llIncludeMVISA.setVisibility(View.VISIBLE);
                llIncludeVaymuon.setVisibility(View.GONE);
                llIncludeVimolink.setVisibility(View.GONE);
            }
            showView(vNormalPay, false);
            btnSendMail.setVisibility(View.GONE);
        } else {
            llIncludeMVISA.setVisibility(View.GONE);
            llIncludeVaymuon.setVisibility(View.GONE);
            showView(vNormalPay, true);
            btnSendMail.setVisibility(View.VISIBLE);
            if (DevicesUtil.isP20L()) {
                btnSendMail.setText(getString(R.string.receipt));
            }
        }
    }

    private void initUI() {
        ButterKnife.bind(this);

        imvArrow.setVisibility(View.GONE);
        vTimeInclude.setVisibility(View.GONE);

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            btnPrintReceipt.setVisibility(View.VISIBLE);
            initPrinter();
        }
    }

    @Override
    public void onBackPressed() {
        if (needReloadOnBack) {
            finishSuccess();
        } else {
            super.onBackPressed();
        }
    }

    private void showView(List<View> vInfo, boolean b) {
        ViewCollections.set(vInfo, ButterKnifeUtils.SHOW, b);
    }

    @OnClick({R.id.btn_send_mail, R.id.void_payment, R.id.btn_print_receipt, R.id.btn_continue, R.id.btn_void_vaymuon, R.id.btn_vm_quick_recei_money, R.id.btn_vm_check_status, R.id.btn_link_quick_recei_money, R.id.btn_update_status_qr_nl})
    public void OnClick(View v) {
        switch (v.getId()) {
            case R.id.btn_send_mail:
                gotoSendMail();
                break;
            case R.id.void_payment:
                showDialogConfirmVoid();
                break;
            case R.id.btn_print_receipt:
                if (isVoucher) {
                    printerVoucherEmart();
                } else {
                    handlerPrintReceipt();
                }
                break;
            case R.id.btn_continue:

                gotoSignature(buildDataReversalLogin(), null);
                break;
            case R.id.btn_void_vaymuon:
                CancelTransDialogFragment cancelTransDialogFragment = new CancelTransDialogFragment();
                cancelTransDialogFragment.setOnClickButtonCancelTransListener(this);
                cancelTransDialogFragment.show(getSupportFragmentManager(), "DIALOG_CANCEL_TRANS");
                break;
            case R.id.btn_vm_quick_recei_money:
            case R.id.btn_link_quick_recei_money:
                showDialogQuickWithdrawMoney();
                break;
            case R.id.btn_vm_check_status:
                llBtnVaymuon.setVisibility(View.GONE);
                llBtnVimoLink.setVisibility(View.GONE);
                llIncludeMVISA.setVisibility(View.GONE);
                llIncludeVaymuon.setVisibility(View.GONE);
                if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
                    String txId = getIntent().getStringExtra(KEY_TXID);
                    if (!TextUtils.isEmpty(txId)) {
                        setLayoutMVisa(true);
                        loadDataDetailTransMVisa(txId);
                    }
                }
                break;
            case  R.id.btn_update_status_qr_nl:
                updateStatusQrNl();
                break;
            default:
                break;
        }
    }

    private void updateStatusQrNl() {
        Utils.LOGD(TAG, Constants.FUNCTION_CHECK_ORDER_QR + " updateStatusQrNl");
        logUtil.appendLogRequestApi(Constants.FUNCTION_CHECK_ORDER_QR + "updateStatusQrNl");

        String checkSum = buildCheckSumFetchStatusQr(transItemQrNl.token_code);
        Utils.LOGD(TAG, "checkSum= " + checkSum);
        RequestParams params = new RequestParams();
        params.put("function", Constants.FUNCTION_CHECK_ORDER_QR);
        params.put("merchant_site_code", DataStoreApp.getInstance().getQrNlSiteCode());
        params.put("token_code", transItemQrNl.token_code);
        params.put("checksum", checkSum);

        Utils.LOGD("updateStatusQrNl Data: ", params.toString());
        logUtil.appendLogAction("updateStatusQrNl merchant_site_code= " + DataStoreApp.getInstance().getQrNlSiteCode());

        v_loading.setVisibility(View.VISIBLE);
        MposRestClient.getInstance(ActivityPaymentInfo.this).post(ActivityPaymentInfo.this, BuildConfig.URL_SERVER_QR_NL, params, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    v_loading.setVisibility(View.GONE);

                    String contentResponse = new String(arg2);
                    JSONObject jRoot = new JSONObject(contentResponse);
                    Utils.LOGD(TAG, "onSuccess loadSalesHistoryQrNl: " + contentResponse);
                    logUtil.appendLogAction("onSuccess loadSalesHistoryQrNl: ");
                    Type listType = new TypeToken<DataTransStatusQrNl>() {}.getType();
                    DataTransStatusQrNl dataTransStatusQrNl = new Gson().fromJson(jRoot.toString(), listType);
                    new MyUtils().setViewStatusByStatusQrNl(ActivityPaymentInfo.this, String.valueOf(dataTransStatusQrNl.getResultData().getStatus()), tvStatusQrNl);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(TAG, "onFailure loadSalesHistoryQrNl: " + arg3.getMessage());
                logUtil.appendLogAction("loadSalesHistoryQrNl onFailure: "+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                v_loading.setVisibility(View.GONE);
            }
        });
    }
    private String buildCheckSumFetchStatusQr(String token_code) {
        Utils.LOGD(TAG, "buildCheckSumFetchStatusQr checkSum ===== " +
                DataStoreApp.getInstance().getQrNlSiteCode() + '|' + token_code + '|' + DataStoreApp.getInstance().getQrNlPassCode());

        String checkSum = DataStoreApp.getInstance().getQrNlSiteCode() + '|' + token_code + '|' + DataStoreApp.getInstance().getQrNlPassCode();
        return Utils.md5(checkSum);
//        return Utils.sha256(checkSum);
    }

    public void printerVoucherEmart() {
        libPrinter.actionPrintGiftCardEmart(itemPaymentEmartDetail);

    }

    private void handlerPrintReceipt() {
        this.txId = transItemMA.getTxid();
        processPrintReceipt(PRINT_END_TRANSACTION);
    }

    @NonNull
    private DataReversalLogin buildDataReversalLogin() {
        DataReversalLogin dataReversalLogin = new DataReversalLogin();

        dataReversalLogin.amount = MyUtils.convertMoney(amountPay);
        dataReversalLogin.trxType = trxType;
        dataReversalLogin.pan = pan;
        dataReversalLogin.itemDesc = itemDescription;
        dataReversalLogin.cardholderName = holderName;
        dataReversalLogin.transReqId = transactionRequestId;
        dataReversalLogin.transactionDate = transactionDate;
        dataReversalLogin.paymentIdentify = udid;

        return dataReversalLogin;
    }

    private void doVoidVaymuon(String reason) {
        if (paymentInfo != null) {
            logUtil.appendLogRequestApi(Config.TRANSACTION_PUSH_REFUND);
            StringEntity entity = null;
            try {
                JSONObject jo = new JSONObject();
                jo.put(Constants.STR_SERVICE_NAME, Config.TRANSACTION_PUSH_REFUND);
                jo.put("udid", paymentInfo.getUdid());
                jo.put("type", paymentInfo.getTransactionPushType());
                jo.put("voidReason", new String(reason.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                entity = new StringEntity(jo.toString());
            } catch (Exception e1) {
                Utils.LOGE(tag, "doVoidVaymuon: " + e1.getMessage());
            }
            MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, Config.URL_CHECK_STATUS_MVISA,
                    entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                        @Override
                        public void onStart() {
                            mPgdl.showLoading();
                            super.onStart();
                        }

                        @Override
                        public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                            mPgdl.hideLoading();
                            String msg = null;
                            try {
                                JSONObject jRoot = new JSONObject(new String(arg2));

                                Utils.LOGD(TAG, "RESPONSE: " + jRoot);

                                if (jRoot.has("error")) {
                                    final JSONObject jError = jRoot.getJSONObject("error");
                                    errorCode = jError.getInt("code");
                                    msg = jError.getString("message");
                                    logUtil.appendLogAction("logIn: error=" + errorCode);
                                    logUtil.saveLog();

                                    if (errorCode == 1000) {
                                        SuccessDialogFragment successDialogFragment = new SuccessDialogFragment();
                                        Bundle bundle = new Bundle();
                                        bundle.putString(SuccessDialogFragment.TITLE_DIALOG_SUCCESS, getString(R.string.cancel_transaction_confirm_success));
                                        bundle.putString(SuccessDialogFragment.CONTENT_DIALOG_SUCCESS, "");
                                        successDialogFragment.setArguments(bundle);
                                        successDialogFragment.setOnDismissSuccessDialogListener(ActivityPaymentInfo.this);
                                        successDialogFragment.show(getSupportFragmentManager(), "DIALOG_SUCCESS");
                                    } else {
                                        //  ERROR!!!
                                        final MposDialog mposDialogError = MyUtils.initDialogGeneralError(ActivityPaymentInfo.this, errorCode, msg, ActivityScanQRCode.class.getName());
                                        mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
                                        mposDialogError.show();
                                    }
                                }
                            } catch (Exception e) {
                                logUtil.appendLogRequestApi(Config.URL_CHECK_STATUS_MVISA + " Exception:" + e.getMessage());
                                logUtil.saveLog();
                                Utils.LOGE(TAG, "Exception", e);
                            }

                            if (!TextUtils.isEmpty(msg)) {
                                logUtil.appendLogException(msg);
                                logUtil.saveLog();
                            }

                            logUtil.pushLog();
                        }

                        @Override
                        public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                            Utils.LOGD(TAG, "onFailure: " + arg3.getMessage());
                            logUtil.appendLogRequestApiFail(Config.URL_CHECK_STATUS_MVISA + " onFailure", arg2);
                            mPgdl.hideLoading();
                        }
                    });
        }
    }

    private void gotoSendMail() {
        Intent i = new Intent(ActivityPaymentInfo.this, ActivitySendEmail.class);
        i.putExtra("type", false);
        i.putExtra("tid", mTranId);
        i.putExtra("amount", "-1");

        if (transItemMA != null) {
            i.putExtra("txid", transItemMA.getTxid());
        }

        startActivityForResult(i, 2);
    }

    public void loadSalesDetail() {
        String tid = getIntent().getStringExtra("tid");
        logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + "--" + tid);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.SALES_HISTORY_DETAIL);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityPaymentInfo.this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            jo.put("transactionID", tid);
            jo.put("tokenL2", getIntent().getStringExtra("tokenL2"));
            Utils.LOGD(TAG, jo.toString());
            Utils.LOGD(TAG, "getSalesDetail: send=>" + PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "getSalesDetail: " + e1.getMessage());
        }

        Utils.LOGD(tag, "getSalesDetail: url=" + ConstantsPay.getUrlServer(this));

        MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, ConstantsPay.getUrlServer(this),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            mPgdl.hideLoading();
                            Utils.LOGD(tag, "onSuccess: sskey=" + PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey() + " data=" + new String(arg2));

                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD(tag, "Sales detail: " + response);
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    int errCode = jo.getInt("code");
                                    String msg = String.format("%s %02d: %s", getString(R.string.error), errCode, LibError.getErrorMsg(errCode, ActivityPaymentInfo.this));
//                                    String msg = getString(R.string.error) + " " + String.format("%02d", errCode)
//                                            + ": " + LibError.getErrorMsg(errCode, ActivityPaymentInfo.this);
                                    logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " error server:" + msg);
                                    if (LibError.isSessionExpired(errCode)) {
                                        MyDialogShow.showDialogErrorReLogin(msg, ActivityPaymentInfo.this);
                                    } else {
                                        MyDialogShow.showDialogErrorFinish(msg, ActivityPaymentInfo.this);
                                    }
                                } catch (JSONException e) {
                                    MyDialogShow.showDialogErrorFinish("", ActivityPaymentInfo.this);
                                }
                            } else {
                                try {
                                    logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " onsuccess");
                                    PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                                    mTranId = response.getString("transactionID");
                                    mTokenL2 = response.getString("tokenL2");
                                    showView(vInfo, true);
                                    fillSalesData(response.getJSONObject("transactionDetail"));
                                } catch (JSONException e) {
                                    Utils.LOGE(tag, "Exception", e);
                                    MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.error_try_again),
                                            ActivityPaymentInfo.this, v -> loadSalesDetail(), true);
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.SALES_HISTORY_DETAIL + " exception(timeout)");
                            Utils.LOGE(tag, "Exception", e);
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityPaymentInfo.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Sales detail error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        logUtil.appendLogRequestApiFail(Config.SALES_HISTORY_DETAIL + " onFailure"+MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3), arg2);
                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                                ActivityPaymentInfo.this, v -> loadSalesDetail(), true);
                    }
                });
    }

    private void loadSaleDetailMa(String wfId) {
        mPgdl.showLoading();
        WorkFlow workFlow = new WorkFlow(wfId);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(workFlow));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_GET_TRANS_DETAIL, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "TransDetails onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "TransDetails onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, "TransDetails onSuccess: " + clearData);

                WfDetailRes transItem = MyGson.parseJson(clearData, WfDetailRes.class);
                fillSaleDataMacq(transItem);
            }
        });
    }

    private void gotoSignature(DataReversalLogin dataReversal, String email) {
        Intent i = new Intent(this, ActivityPrePayment.class);

        DataPay dataPay = new DataPay(dataReversal);
        dataPay.setEmail(email);
        if (isTranMultiAcquirer) {
            dataPay.setWfId(transItemMA.getWfId());
        }
        i.putExtra(Intents.EXTRA_DATA_PAY_MP, dataPay);
        startActivityForResult(i, RESULT_CODE_RE_SIGNATURE);
    }
    public void fillSalesDataDomesticInMVisa(JSONObject jo) throws JSONException {
        trxType = JsonParser.getDataJson(jo, "applicationUsageControl");
        initUdid(JsonParser.getDataJson(jo, "udid"));

        transactionRequestId = getIntent().getStringExtra("transactionRequestId");
        holderName = JsonParser.getDataJson(jo,"cardholderName");
        pan = JsonParser.getDataJson(jo,"pan");
        transactionDate = Utils.convertTimestamp(jo.getLong("createdDate"), 3);

        tvCardHolderName.setText(JsonParser.getDataJson(jo,"cardholderName"));
//        tvTransId.setText(JsonParser.getDataJson(jo,"txid"));
        showTransId(JsonParser.getDataJson(jo,"txid"));
        tvTransTime.setText(Utils.convertTimestamp(jo.getLong("createdDate"), 1));
        tvTransDate.setText(Utils.convertTimestamp(jo.getLong("createdDate"), 2));
        tvCardType.setText(JsonParser.getDataJson(jo,"issuerName"));
        tvTid.setText(JsonParser.getDataJson(jo,"tid"));
        tvMid.setText(JsonParser.getDataJson(jo,"mid"));
        showPan();
        tvApprovalCode.setText(getString(R.string.SALES_DETAIL_APPROVAL_CODE) + " " + (jo.has("authCode") ? jo.getString("authCode") : ""));
        amountPay = JsonParser.getDataJson(jo,"amount");
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, Utils.zenMoney(amountPay)));
//        ((TextView) findViewById(R.id.time)).setText(Utils.convertTimestamp(jo.getLong("createdDate"), 1));

        String desc = "";

        if (jo.has("description")) {
            desc = jo.getString("description").trim();
            itemDescription = desc;
        }
        boolean isServicePay = checkPayServiceByDesc(desc);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else {
            checkReceiveMoney();
        }

        transactionStatus = jo.getInt("status");
        MyUtils myUtils = new MyUtils();
        myUtils.setViewStatusByType(this, transactionStatus, tvStatus, tvAmount, false);
        boolean canCashierReward = true;
        if (transactionStatus == Constants.TRANS_TYPE_VOID
                || transactionStatus == Constants.TRANS_TYPE_REVERSAL) {
            imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
            btnVoidPay.setVisibility(View.GONE);
            btnPrintReceipt.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        } else if (transactionStatus == Constants.TRANS_TYPE_SETTLE) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else if (transactionStatus == Constants.TRANS_TYPE_PENDING_SIGNATURE) {
            btnContinue.setVisibility(View.VISIBLE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            btnPrintReceipt.setVisibility(View.GONE);
            btnVoidPay.setVisibility(View.GONE);
            canCashierReward = false;
        }

        if (canCashierReward) {
            initCashierReward();
        }
        if (Constants.TRANS_TYPE_REFUND == transactionStatus || isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }
    }

    private void checkDescForPayService() {
        if (!TextUtils.isEmpty(udid) && TextUtils.isEmpty(itemDescription) && udid.startsWith(ConstantsPay.PREFIX_UDID_SERVICE)) {
            itemDescription = udid.substring(ConstantsPay.PREFIX_UDID_SERVICE.length()).replace(Constants.CHAR_REPLACE_SPACE_OF_UDID," ");
        }
    }

    private boolean checkPayServiceByDesc(String desc) {
        Utils.LOGD(TAG, "checkPayServiceByDesc: desc=" + desc);
        boolean isServicePay = false;
        if (TextUtils.isEmpty(desc)) {
            isServicePay = ConstantsPay.TRX_TYPE_SERVICE.equals(trxType);
        }
        else {
            String descUpperCase = desc.toUpperCase();

            if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID)
                    || descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID)
                    || descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD)
                    || ConstantsPay.TRX_TYPE_SERVICE.equals(trxType)
            ) {
                isServicePay = true;
                if (desc.startsWith("undefined-")) {
                    desc = "";
                }
                else {
                    desc = getDescOfPayService(descUpperCase);
                }
            }
            else if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_INSTALLMENT)) {
                desc = getString(R.string.pay_installment) + ": " + desc.substring(ConstantsPay.PREFIX_DESCRIPTION_INSTALLMENT.length());
                isPayInstallment = true;
            }
            else if (descUpperCase.startsWith(ConstantsPay.PREFIX_DESCRIPTION_CASHBACK)) {
                isPayCashBack = true;
                desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_CASHBACK.length());
            }
            if (!TextUtils.isEmpty(desc)) {
                tvDesc.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_DESC), desc));
            }
        }
        Utils.LOGD(TAG, "checkPayServiceByDesc: isServicePaid=" + isServicePay);
        return isServicePay;
    }

    public void fillSalesData(JSONObject jo) throws JSONException {
        trxType = JsonParser.getDataJson(jo, "trxType");

        transactionRequestId = getIntent().getStringExtra("transactionRequestId");

        initUdid(JsonParser.getDataJson(jo, "udid"));


        String label = jo.getString("applicationLabel");
        MyUtils myUtils = new MyUtils();
        myUtils.setImgThumbByTypeCard(this, label, findViewById(R.id.thumb));

        transactionStatus = jo.getInt("transactionStatus");

        myUtils.setViewStatusByType(this, transactionStatus, tvStatus, tvAmount, false);//Constants.VAYMUONQR.equals(transactionPushType

        amountPay = jo.getString("amountAuthorized");
        holderName = (jo.has("cardHolderName") ? jo.getString("cardHolderName") : "NO NAME");
        pan = jo.getString("maskedPAN");
        transactionDate = jo.getString("transactionDate");

        tvCardHolderName.setText(holderName);
        tvBatchNo.setText(jo.getString("batchNo"));
//        tvTransId.setText(jo.getString("transactionID"));
        showTransId(jo.getString("transactionID"));

        tvTransTime.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 1));
        tvTransDate.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 2));
        tvCardType.setText(jo.getString("applicationLabel"));
        tvTid.setText(jo.getJSONObject("application").getString("TID"));
        tvMid.setText(jo.getJSONObject("application").getString("MID"));
        showPan();
        tvApprovalCode.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_APPROVAL_CODE), jo.has("approvalCode") ? jo.getString("approvalCode") : ""));
        tvInvoiceNo.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_RECEIPT_NO), jo.getString("invoiceNumber")));
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, amountPay));
//        tvTime.setText(Utils.convertTimestamp(jo.getLong("transactionDate"), 1));

        String desc;
        if (jo.has("itemDescription")) {
            desc = jo.getString("itemDescription").trim();
            itemDescription = desc;
        }
        checkDescForPayService();
        boolean isServicePay = checkPayServiceByDesc(itemDescription);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (isServicePay) {
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else {
            checkReceiveMoney();
        }

        boolean canCashierReward = true;
        if (transactionStatus == Constants.TRANS_TYPE_VOID
                || transactionStatus == Constants.TRANS_TYPE_REVERSAL) {
            imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        } else if (transactionStatus == Constants.TRANS_TYPE_SETTLE) {
            btnVoidPay.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else if (transactionStatus == Constants.TRANS_TYPE_PENDING_SIGNATURE) {
            btnContinue.setVisibility(View.VISIBLE);
            btnVoidPay.setVisibility(View.GONE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
            canCashierReward = false;
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }

        if (canCashierReward) {
            initCashierReward();
        }
    }

    private void fillSaleDataMacq(WfDetailRes transItem) {
        trxType = transItem.getTrxType();

        showView(vInfo, true);

        initUdid(transItem.getUdid());

        mTranId = transItem.getTxid();
        amountPay = transItem.getAmount();
        holderName = (TextUtils.isEmpty(transItem.getCardHolderName()) ? "NO NAME" : transItem.getCardHolderName());
        pan = transItem.getPan();
        transactionDate = "0";//transItem.getCreatedTimestamp();

        MyUtils myUtils = new MyUtils();
        myUtils.setImgThumbByTypeCard(this, transItem.getIssuerCode(), findViewById(R.id.thumb));

        tvCardHolderName.setText(holderName);
        tvBatchNo.setText(transItem.getBatchNo());
//        tvTransId.setText(transItem.getTxid());
        showTransId(transItem.getTxid());
        long timeCreate = 0;
        try {
            timeCreate = Long.parseLong(transItem.getTransactionDate());
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        tvTransTime.setText(Utils.convertTimestamp(timeCreate, 1));
        tvTransDate.setText(Utils.convertTimestamp(timeCreate, 2));

        tvCardType.setText(transItem.getIssuerCode());
        tvTid.setText(transItem.getTid());
        tvMid.setText(transItem.getMid());
        showPan();
        tvApprovalCode.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_APPROVAL_CODE), TextUtils.isEmpty(transItem.getAuthCode()) ? "" : transItem.getAuthCode()));
        tvInvoiceNo.setText(String.format("%s %s", getString(R.string.SALES_DETAIL_RECEIPT_NO), transItem.getInvoiceNo()));
        tvAmount.setText(String.format("%s %s", ConstantsPay.CURRENCY_SPACE_POST, Utils.zenMoney(amountPay)));
//        tvTime.setText(Utils.convertTimestamp(timeCreate, 1));

        itemDescription = transItem.getDescription();
        checkDescForPayService();
        boolean isServicePay = checkPayServiceByDesc(itemDescription);

        if (PrefLibTV.getInstance(ActivityPaymentInfo.this).getPermitVoid()) {
            btnVoidPay.setVisibility(View.VISIBLE);
        }

        if (isServicePay) {
            btnQuickReceiMoney.setVisibility(View.GONE);
        } else {
            checkReceiveMoney();
        }

        boolean canCashierReward = true;
        if (TextUtils.isEmpty(transItem.getStatus())) {
            tvStatus.setVisibility(View.GONE);
            canCashierReward = false;
            btnContinue.setVisibility(View.GONE);
            btnVoidPay.setVisibility(View.GONE);
            btnSendMail.setVisibility(View.GONE);
            btnQuickReceiMoney.setVisibility(View.GONE);
        }
        else {
            myUtils.setViewStatusByStatus(this, transItem.getStatus(), tvStatus);

            switch (transItem.getStatus()) {
                case Constants.TRANS_STATUS_APPROVED:
//                    btnVoidPay.setVisibility(View.VISIBLE);
                    btnSendMail.setVisibility(View.VISIBLE);
                    btnPrintReceipt.setVisibility(View.VISIBLE);
                    break;
                case Constants.TRANS_STATUS_REVERSAL:
                case Constants.TRANS_STATUS_VOIDED:
                    imvStatus.setBackgroundResource(R.drawable.img_alert_voided);
                    btnVoidPay.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    canCashierReward = false;
                    btnPrintReceipt.setVisibility(View.GONE);
                    break;
                case Constants.TRANS_STATUS_SETTLE:
                case Constants.TRANS_STATUS_SETTLED:
                    btnVoidPay.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    break;
                case Constants.TRANS_STATUS_PENDING_SIGNATURE:
                    btnContinue.setVisibility(View.VISIBLE);
                    btnVoidPay.setVisibility(View.GONE);
                    btnSendMail.setVisibility(View.GONE);
                    btnQuickReceiMoney.setVisibility(View.GONE);
                    canCashierReward = false;
                    btnPrintReceipt.setVisibility(View.GONE);
                    break;

            }
        }

        if (isServicePay) {
            btnVoidPay.setVisibility(View.GONE);
        }

        if (canCashierReward) {
            initCashierReward();
        }
    }

    private void showPan() {
        if (pan != null && !pan.isEmpty()) {
            tvCardNumber.setText(CardUtils.getMaskedPan(pan));
        }
        else {
            tvCardNumber.setVisibility(View.GONE);
        }
    }

    private void showTransId(String transId) {
        tvTransId.setText(transId);
        MyTextUtils.makeLinks(tvTransId, new String[]{transId}, new ClickableSpan[]{new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                com.mpos.utils.MyTextUtils.pushTextToClipboard(transId, (ClipboardManager) ActivityPaymentInfo.this.getSystemService(CLIPBOARD_SERVICE));
                showToast(getString(R.string.copied));
            }
        }});
    }

    private void checkReceiveMoney() {
        if (DataStoreApp.getInstance().isEnableQuickDrawal()
                && MyUtils.checkMinMaxAmount(amountPay, DataStoreApp.getInstance().getAmountMinQuick(), DataStoreApp.getInstance().getAmountMaxQuick())
//					&& !desc.contains(getString(R.string.pay_installment))
        ) {
            showBtnQuickWithdrawal();
            showVMQuickDraw = true;
        } else {
            btnQuickReceiMoney.setVisibility(View.GONE);
            showVMQuickDraw = false;
        }
    }

    private void initUdid(String udidFromServer) {
        if (paymentInfo != null && !TextUtils.isEmpty(paymentInfo.getUdid())) {
            udid = paymentInfo.getUdid();
        } else {
            udid = udidFromServer;
        }
        Utils.LOGD(tag, "initUdid: curr=" + udid + " udidFromServer=" + udidFromServer);
    }

    private String getDescOfPayService(String desc) {
        String result = getString(R.string.txt_desc_service_pay);
        String type = "";
        if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID)) {
            type = getString(R.string.prepay);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_PREPAID.length());
        } else if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID)) {
            type = getString(R.string.postpay);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_POSTPAID.length());
        } else if (desc.startsWith(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD)) {
            type = getString(R.string.card_code);
            desc = desc.substring(ConstantsPay.PREFIX_DESCRIPTION_SERVICE_BUY_CARD.length());
        }

        result += "-" + type + "-" + (desc.startsWith(ConstantsPay.PREFIX_MOBILE_DESCRIPTION_SERVICE) ? desc.substring(ConstantsPay.PREFIX_MOBILE_DESCRIPTION_SERVICE.length()) : desc);
        return result;
    }

    private void showDialogConfirmVoid() {
        logUtil.appendLogAction("show screen confirm void");
        final DialogVoid dialogVoid = new DialogVoid();
        dialogVoid.initVariable(amountPay, pan, tvCardType.getText().toString(), tvApprovalCode.getText().toString(), tvInvoiceNo.getText().toString(), tvTransId.getText().toString());
        dialogVoid.setClickListener(d -> {
            logUtil.appendLogAction("select -> void");
            if (isTranMultiAcquirer) {
                voidPaymentMA();
            }
            else {
                voidPayment(dialogVoid.getDialog());
            }
        });
        dialogVoid.show(getSupportFragmentManager(), "BaseDialogFragment");
    }

    public void voidPayment(final Dialog dialog) {
        logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " mTranId=" + mTranId + " amount:" + amountPay);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.CONFIRM_VOID);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityPaymentInfo.this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey());
            jo.put("transactionID", mTranId);
            jo.put("tokenL2", mTokenL2);
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "voidPayment: " + e1.getMessage());
        }

        MposRestClient.getInstance(this).post(ActivityPaymentInfo.this, ConstantsPay.getUrlServer(this),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityPaymentInfo.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityPaymentInfo.this).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD("Confirm void: ", response.toString());
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    int errCode =  jo.getInt("code");
                                    String msg = String.format("%s %02d: %s", getString(R.string.error), errCode, LibError.getErrorMsg(errCode, ActivityPaymentInfo.this));
                                    logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " error server:" + msg);
                                    if (LibError.isSessionExpired(errCode)) {
                                        MyDialogShow.showDialogErrorReLogin(msg, ActivityPaymentInfo.this);
                                    } else {
                                        MyDialogShow.showDialogErrorFinish(msg, ActivityPaymentInfo.this);
                                    }
                                } catch (JSONException e) {
                                    Utils.LOGE(tag, "Exception", e);
                                    MyDialogShow.showDialogErrorFinish("", ActivityPaymentInfo.this);
                                }
                            } else {
                                logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " onsuccess");
                                pushNotifyVoid(amountPay, pan, holderName);
                                checkUpdateTransToMpos(dialog);
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.CONFIRM_VOID + " exception(timeout)");
                            Utils.LOGE(tag, "Exception", e);
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityPaymentInfo.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Confirm void error: "+MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        logUtil.appendLogRequestApiFail(Config.CONFIRM_VOID + " onFailure " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3), arg2);

                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityPaymentInfo.this,
                                v -> voidPayment(dialog), true);
                    }
                });
    }

    private void voidPaymentMA() {
        mPgdl.showLoading();
        logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " amount:" + amountPay);
        WorkFlow workFlow = new WorkFlow(transItemMA.getWfId());
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(workFlow));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_DO_VOID, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "voidMultiAcquirer onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);
                logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " -> fail: "+dataError.getMsg());
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();

                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                logUtil.appendLogRequestApi("do-confirm-void wfid=" + transItemMA.getWfId() + " -> success: " + clearData);
                Utils.LOGD(TAG, "voidMultiAcquirer onSuccess: " + clearData);
                if (statusCode == HttpStatus.SC_OK) {
                    finishSuccess();
                }
                else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                    MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivityPaymentInfo.this, true);
                }
            }
        });
    }

    private void pushNotifyVoid(String amountPay, String pan, String holderName) {
        NotifyPushController notifyController = new NotifyPushController(this);
        notifyController.pushNotify(NotifyPushController.NOTIFY_PAY_VOID, amountPay, pan, holderName);
    }

    private void checkUpdateTransToMpos(Dialog dialog) {
        if (isPayInstallment || isPayCashBack) {
            updateTransactionStatusInMpos(dialog, udid, validatedAmount(amountPay), mTranId);
        } else {
            checkSendEmailToMerchant(dialog);
        }
    }

    private String validatedAmount(String amountPay) {
        if (!TextUtils.isEmpty(amountPay)) {
            return amountPay.replaceAll("[\\D]", "");
        }
        return "";
    }

    private void updateTransactionStatusInMpos(final Dialog dialog, String udid, String amount, String txId) {
        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + ": " + udid);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPDATE_TRANSACTION_STATUS);
            if (!TextUtils.isEmpty(udid)) {
                jo.put("udid", udid);
            }
            jo.put("amount", amount);

            jo.put("status", Constants.STATUS_TRANS_VOIDED);
            jo.put("txId", txId);
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            Utils.LOGD("Data: ", jo.toString());

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.UPDATE_TRANSACTION_STATUS + " onFailure", arg2);
                mPgdl.hideLoading();
                checkSendEmailToMerchant(dialog);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                try {
                    JsonParser jsonParser = new JsonParser();
                    BaseObjJson errorBean = new BaseObjJson();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "-updateTransactionStatusInMpos:" + jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " success");
                    } else {
                        msgError = TextUtils.isEmpty(errorBean.message) ?
                                getString(R.string.error_get_info_merchant) : errorBean.message;
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    mToast.showToast(msgError);
                }
                checkSendEmailToMerchant(dialog);
            }
        });
    }

    private void checkSendEmailToMerchant(Dialog dialog) {
        try {
            if (dialog != null) dialog.dismiss();
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        String email = PrefLibTV.getInstance(this).getEmailMerchant();
        if (PrefLibTV.getInstance(this).get(PrefLibTV.sendTrxReceipt, Boolean.class, false)  && !TextUtils.isEmpty(email)) {
//        if (DataStoreApp.getInstance().getSendEmailMerchant() && !TextUtils.isEmpty(email)) {
            sendEmailByType(email);
        } else {
            finishSuccess();
        }
    }

    private void finishSuccess() {
        setResult(3);
        finish();
    }

    public void sendEmailByType(String email) {
        if (!GetData.CheckInternet(getApplicationContext())) {
            showToastResultSendEmail(true);
            finishSuccess();
            return;
        }
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.SEND_RECEIPT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(getApplicationContext()).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(getApplicationContext()).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(getApplicationContext()).getSessionKey());
            jo.put("transactionID", mTranId);
            jo.put("email", email);
            Utils.LOGD(TAG, "Data: "+ jo);
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(tag, "sendEmailByType: " + e1.getMessage());
        }

        MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this),
//				MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(context),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        boolean isError = false;
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
                            PrefLibTV.getInstance(getApplicationContext()).setSessionKey(response.getString("sessionKey"));
                            Utils.LOGD(TAG, "Send receipt response: "+ response);
                            if (response.has("error")) {
                                isError = true;
                            }
                        } catch (Exception e) {
                            Utils.LOGE(tag, "Exception", e);
                            isError = true;
                        }

                        showToastResultSendEmail(isError);
                        finishSuccess();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Send receipt error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                        mPgdl.hideLoading();
                        showToastResultSendEmail(true);
                        finishSuccess();
                    }
                });
    }

    private void showToastResultSendEmail(boolean isError) {
        if (isError) {
            mToast.showToast(getString(R.string.error_send_email_invoice_merchant));
        } else {
            mToast.showToast(getString(R.string.success_send_email_invoice_merchant));
        }
    }

    private void showToast(String msg) {
        if (!TextUtils.isEmpty(msg)) {
            Toast.makeText(this, msg, Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(tag, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        if (requestCode == RESULT_CODE_RE_SIGNATURE && resultCode == RESULT_OK) {
            finishSuccess();
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (logUtil != null) {
            logUtil.saveLog();
        }
    }

    private void showBtnQuickWithdrawal() {
        btnQuickReceiMoney.setVisibility(View.VISIBLE);
        btnQuickReceiMoney.setEnabled(true);
        btnQuickReceiMoney.setOnClickListener(v -> showDialogQuickWithdrawMoney());
    }

    private void showDialogQuickWithdrawMoney() {
        DialogQuickWithdrawMoney mdialog = new DialogQuickWithdrawMoney();
        mdialog.setCancelable(false);

        mdialog.initVariable(amountPay, mTranId, DataStoreApp.getInstance(), mPgdl, logUtil, mToast, null);

        mdialog.setClickListener(new DialogQuickWithdrawMoney.OnMyClickListener() {
            @Override
            public void clickCancel(DialogFragment dialogFragment) {
                dialogFragment.dismiss();
            }

            @Override
            public void backToMain() {
                backToMainAfterQuickWithdrawal();
            }
        });
        mdialog.show(getSupportFragmentManager(), DialogQuickWithdrawMoney.class.getName());
    }

    public void backToMainAfterQuickWithdrawal() {
        setResult(2);
        finish();
    }


    //----------------------------- DETAIL PAYMENT FOR MVISA ---------------------------------------
    //--begin--
    int errorCode;

    private void loadDataDetailTransMVisa(final String txid) {
        Utils.LOGD(tag, "------- loadDataDetailTransMVisa ------");
        Utils.LOGD(tag, ">>> txid: " + txid);

        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(null, getString(R.string.check_internet), this, v -> loadDataDetailTransMVisa(txid));
            return;
        }

        logUtil.appendLogRequestApi(Config.URL_GATEWAY_API);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, "TRANSACTION_VIEW");
            jo.put("txid", txid);
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            Utils.LOGD(tag, "REQUEST: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        MposRestClient.getInstance(ActivityPaymentInfo.this).post(ActivityPaymentInfo.this, Config.URL_GATEWAY_API, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading();
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(tag, "onFailure: " + arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.URL_GATEWAY_API + " onFailure", arg2);
                        mPgdl.hideLoading();
                        finishSuccess();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        String msg = null;
                        try {
//							JsonParser jsonParser = new JsonParser();
                            Utils.LOGE(tag, "RESPONSE: " + new String(arg2));
                            JSONObject jRoot = new JSONObject(new String(arg2));

                            Utils.LOGD(tag, "RESPONSE: " + jRoot);

                            if (jRoot.has("error")) {
                                final JSONObject jError = jRoot.getJSONObject("error");
                                errorCode = jError.getInt("code");
                                msg = jError.getString("message");
                                logUtil.appendLogAction("TRANSACTION_VIEW: error=" + errorCode);

                                //  ERROR!!!
                                final MposDialog mposDialogError = MyUtils.initDialogGeneralError(
                                        ActivityPaymentInfo.this, errorCode, msg, ActivityPaymentInfo.class.getName());
                                mposDialogError.setOnClickListenerDialogClose(v -> {
                                    mposDialogError.dismiss();
                                    finishSuccess();
                                });
                                mposDialogError.show();

                            } else {
                                //  SUCCESSFULLY!!!
                                amountPay = JsonParser.getDataJson(jRoot, "amount");
//								String soTienThanhToan    = JsonParser.getDataJson(jRoot, "amount");
                                String thoiGianTao = JsonParser.getDataJson(jRoot, "createdDate");
                                String soThe = JsonParser.getDataJson(jRoot, "pan");
                                mTranId = JsonParser.getDataJson(jRoot, "txid");
                                String tenChuThe = JsonParser.getDataJson(jRoot, "cardholderName");
                                String loaiThe = JsonParser.getDataJson(jRoot, "issuerCode");
                                String status = JsonParser.getDataJson(jRoot, "status");
                                String description = JsonParser.getDataJson(jRoot, "description");
                                String voidReason = JsonParser.getDataJson(jRoot, "voidReason");
                                String transactionPushType = JsonParser.getDataJson(jRoot, "transactionPushType");
                                String issuerBank = JsonParser.getDataJson(jRoot, "issuerBank");
                                String transactionType = JsonParser.getDataJson(jRoot, "transactionType");
                                String errorMsg = "";
                                if (jRoot.has("transactionPush")) {
                                    JSONObject transactionPush = jRoot.getJSONObject("transactionPush");
                                    String errorCode = JsonParser.getDataJson(transactionPush, "errorCode");
                                    String errorMessage = JsonParser.getDataJson(transactionPush, "errorMessage");
                                    if (!TextUtils.isEmpty(errorCode)) errorMsg += getString(R.string.error_code) + " " + errorCode;
                                    if (!TextUtils.isEmpty(errorMessage)) errorMsg += ": " + errorMessage;
                                }

                                String fullDes = description;
                                if (Constants.LINKCARD.equals(transactionPushType)) {
                                    fullDes =  issuerBank + " - " + description;
                                }

                                if (jRoot.has("transactionInstallment")) {
                                    JSONObject transactionInstallment = jRoot.getJSONObject("transactionInstallment");
                                    String customerMobile = JsonParser.getDataJson(transactionInstallment, "customerMobile");
                                    String period = JsonParser.getDataJson(transactionInstallment, "period");
                                    String periodType = JsonParser.getDataJson(transactionInstallment, "periodType");
                                    if ("month".equals(periodType)) {
                                        fullDes = getString(R.string.payment_period) + " " + String.format("%s %s", period, getString(R.string.month)) + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    } else if ("day".equals(periodType)) {
                                        fullDes = getString(R.string.payment_period) + " " + String.format("%s %s", period, getString(R.string.day)) + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    } else {
                                        fullDes = getString(R.string.payment_period) + " " + period + (TextUtils.isEmpty(fullDes) ? "" : (" - " + fullDes));
                                    }
                                    tvVMCustomerPhone.setText(customerMobile);
                                }

                                tvTenChuThe.setText(tenChuThe);
                                tvThanhToanQua.setText(getByLanguageLocal(jRoot));
                                tvLoaiThe.setText(loaiThe);
                                tvSoTien.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
//								tvSoTien.setText(Utils.zenMoney(soTienThanhToan) + ConstantsPay.CURRENCY_SPACE_PRE);
                                tvMaGiaoDich.setText(mTranId);
                                tvThoiGian.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvSoThe.setText(soThe);

                                if ((("" + Constants.TRANS_TYPE_VOID).equals(status) || ("" + Constants.TRANS_TYPE_REFUND).equals(status)) && !TextUtils.isEmpty(voidReason)) {
                                    rlVMVoidReason.setVisibility(View.VISIBLE);
                                    tvVMVoidReason.setText(String.format("%s: %s", getString(R.string.void_reason), voidReason));
                                } else {
                                    rlVMVoidReason.setVisibility(View.GONE);
                                }

                                tvVMAmount.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
                                tvVMPaymentMethod.setText(loaiThe);
                                tvVMCustomerName.setText(tenChuThe);
                                if (("" + Constants.TRANS_TYPE_PENDING_TC).equals(status)) {
                                    rlVMStatusKTExplain.setVisibility(View.GONE);
                                    rlVMStatusExplain.setVisibility(View.VISIBLE);
                                } else if (("" + Constants.TRANS_TYPE_SETTLE).equals(status)) {
                                    rlVMStatusKTExplain.setVisibility(View.VISIBLE);
                                    rlVMStatusExplain.setVisibility(View.GONE);
                                } else {
                                    rlVMStatusKTExplain.setVisibility(View.GONE);
                                    rlVMStatusExplain.setVisibility(View.GONE);
                                }
                                if (!TextUtils.isEmpty(status)) {
                                    tvVMStatus.setText(MyUtils.getStatusNameType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                    tvVMStatus.setBackground(MyUtils.getStatusBackgroundType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                }
                                tvVMTransCode.setText(mTranId);
                                tvVMTransTime.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvVMPaymentContent.setText(fullDes);

                                showView(vInfo, true);
                                initUdid("");
                                checkReceiveMoney();
                                initCashierReward();

                                if (Constants.VAYMUONQR.equals(transactionPushType)) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                }
                                if (Constants.VAYMUONQR.equals(transactionPushType)
                                        && (("" + Constants.TRANS_TYPE_PENDING_TC).equals(status) || ("" + Constants.TRANS_TYPE_SETTLE).equals(status))) {
                                    llBtnVaymuon.setVisibility(View.VISIBLE);
                                    if (("" + Constants.TRANS_TYPE_SETTLE).equals(status) && showVMQuickDraw) {
                                        btnVMQuickReceiMoney.setVisibility(View.GONE); // Tạm ẩn
                                    } else {
                                        btnVMQuickReceiMoney.setVisibility(View.GONE);
                                    }

                                    if (("" + Constants.TRANS_TYPE_PENDING_TC).equals(status)) {
                                        btnVMCheckStatus.setVisibility(View.VISIBLE);
                                    } else {
                                        btnVMCheckStatus.setVisibility(View.GONE);
                                    }
                                }

                                // Vimo link
                                if (showVMQuickDraw && Constants.LINKCARD.equals(transactionPushType) && (("" + Constants.TRANS_TYPE_SUCCESS).equals(status) || ("" + Constants.TRANS_TYPE_SETTLE).equals(status))) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
//                                    llBtnVimoLink.setVisibility(View.VISIBLE);
                                }
                                tvLinkAmount.setText(String.format("%s %s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SPACE_PRE));
                                tvLinkCardName.setText(tenChuThe);
                                tvLinkCardType.setText(loaiThe);
                                tvLinkCardNumber.setText(soThe.replaceAll("X", "*"));
                                if (!TextUtils.isEmpty(status)) {
                                    tvLinkStatus.setText(MyUtils.getStatusNameType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                    tvLinkStatus.setBackground(MyUtils.getStatusBackgroundType(ActivityPaymentInfo.this, Integer.parseInt(status), Constants.VAYMUONQR.equals(transactionPushType)));
                                }
                                tvLinkTransCode.setText(mTranId);
                                tvLinkTimeCreate.setText(MyUtils.formatTimeTransaction(thoiGianTao));
                                tvLinkContenPayment.setText(fullDes);

                                // enter card
                                if (Constants.LINKCARD.equals(transactionPushType) && Constants.NORMAL.equals(transactionType) && (("" + Constants.TRANS_TYPE_FAILED).equals(status) || ("" + Constants.TRANS_TYPE_PROCESSING).equals(status))) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                    if (!TextUtils.isEmpty(errorMsg)) {
                                        if (("" + Constants.TRANS_TYPE_PROCESSING).equals(status)) {
                                            ivPolygon.setColorFilter(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.orange_overlay));
                                            tvLinkPendingReason.setBackground(ContextCompat.getDrawable(ActivityPaymentInfo.this, R.drawable.bg_status_transaction_orange_overlay));
                                            tvLinkPendingReason.setTextColor(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.orange_1));
                                        } else {
                                            ivPolygon.setColorFilter(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.red_overlay));
                                            tvLinkPendingReason.setBackground(ContextCompat.getDrawable(ActivityPaymentInfo.this, R.drawable.bg_status_transaction_red_overlay));
                                            tvLinkPendingReason.setTextColor(ContextCompat.getColor(ActivityPaymentInfo.this, R.color.red_1));
                                        }
                                        tvLinkPendingReason.setText(errorMsg);
                                        llLinkPendingReason.setVisibility(View.VISIBLE);
                                    }
                                }
                                if (Constants.INSTALLMENT.equals(transactionType)) {
                                    tvLinkPaymentMethod.setText(getString(R.string.type_vimolink));
                                } else if (Constants.NORMAL.equals(transactionType)) {
                                    btnQuickReceiMoney.setVisibility(View.GONE);
                                    tvLinkPaymentMethod.setText(getString(R.string.type_vimonormal));
                                    tvLinkContenPayment.setText(String.format("%s: %s", getString(R.string.description), description));
                                }

                                // isDomesticInMVisa
                                if (isDomesticInMVisa) {
                                    fillSalesDataDomesticInMVisa(jRoot);
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.URL_GATEWAY_API + " Exception:" + e.getMessage());
                            Utils.LOGE(tag, "Exception", e);
                            finishSuccess();
                        }

                        if (!TextUtils.isEmpty(msg)) {
                            logUtil.appendLogException(msg);
                        }
                    }
                });
    }

    private String getByLanguageLocal(JSONObject jsonObject) {
        if ("en".equals(Locale.getDefault().getLanguage())) {
            return JsonParser.getDataJson(jsonObject, "issuerNameEn");
        } else {
            return JsonParser.getDataJson(jsonObject, "issuerName");
        }
    }

    //---end---

    //** cashier reward
    private void initCashierReward() {
//	    if (paymentInfo.getFeedbackStatus())
        vCashierReward = new ViewCashierReward(this, vRootCashierReward, this::registerFeedbackTrans);
        if (!DataStoreApp.getInstance().isCanFeedback() || paymentInfo == null || Constants.STATUS_CR_DENIED.equals(paymentInfo.getFeedbackStatus())) {
            vRootCashierReward.setVisibility(View.GONE);
        } else if (paymentInfo != null && !TextUtils.isEmpty(paymentInfo.getMobileUserPhone())) {
            vCashierReward.showMobileRegistered(paymentInfo.getFeedbackStatus(), paymentInfo.getMobileUserPhone());
            vRootCashierReward.setVisibility(View.VISIBLE);
        } else if (paymentInfo != null && Constants.STATUS_CR_PENDING.equals(paymentInfo.getFeedbackStatus())) {
            vCashierReward.setShowBtnConfirm(true);
            vCashierReward.checkFeedback(DataStoreApp.getInstance(), amountPay);
        }
    }

    public void registerFeedbackTrans(final String mobileFeedback) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.FEEDBACK_TRANSACTION);

            jo.put("description", itemDescription);
            jo.put("udid", udid);
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(this).getUserId());
            jo.put("merchantId", PrefLibTV.getInstance(this).getMerchantsId());
            jo.put("amount", amountPay);
            jo.put("mobileUserPhone", mobileFeedback);
            Utils.LOGD(tag, "REQ FEEDBACK_TRANSACTION: " + jo);
            entity = new StringEntity(jo.toString());

            logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " a:" + amountPay + " m:" + mobileFeedback);
        } catch (Exception ex) {
            Utils.LOGE(tag, "Exception: " + ex.getMessage());
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                String msgError = null;
                try {
                    mPgdl.hideLoading();
                    JSONObject response = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "RES FEEDBACK_TRANSACTION: " + response);

                    JSONObject jsonObjectErr = new JSONObject(JsonParser.getDataJson(response, "error"));
                    String responseCode = JsonParser.getDataJson(jsonObjectErr, "code");
                    String responseMsg = JsonParser.getDataJson(jsonObjectErr, "message");

                    Utils.LOGD(tag, "responseCode:" + responseCode);
                    Utils.LOGD(tag, "responseMsg:" + responseMsg);

                    if (!responseCode.equals(Config.CODE_REQUEST_SUCCESS)) {
                        String msg = getString(R.string.error) + " " + responseCode + ": " + responseMsg;
                        Utils.LOGD(tag, "msg:" + msg);
                        logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " Error on Server: " + msg);
                        if (responseCode.equals("8003")) {
                            MyDialogShow.showDialogInfo(ActivityPaymentInfo.this, msg, true);
                        } else {
                            msgError = msg;
                        }
                    } else {
                        DataStoreApp.getInstance().saveMobileUserPhone(mobileFeedback);
                        logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " onSuccessfully!!!");
                        MyDialogShow.showDialogSuccess(ActivityPaymentInfo.this, responseMsg, false, v -> finishSuccess());
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION + " Error on parse: " + e.getMessage());
                    msgError = getString(R.string.error_default_contact_hotline);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    MyDialogShow.showDialogError(msgError, ActivityPaymentInfo.this);
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(tag, "onFailure");
                logUtil.appendLogRequestApiFail(Config.FEEDBACK_TRANSACTION + " onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityPaymentInfo.this, v -> registerFeedbackTrans(mobileFeedback), true);
            }
        });
    }

    @Override
    public void onClickSkipCancelTrans() {

    }

    @Override
    public void onClickConfirmCancelTrans(String content) {
        doVoidVaymuon(content);
    }

    @Override
    public void onDismissSuccessDialog() {
        llBtnVaymuon.setVisibility(View.GONE);
        llBtnVimoLink.setVisibility(View.GONE);
        llIncludeMVISA.setVisibility(View.GONE);
        llIncludeVaymuon.setVisibility(View.GONE);
        if (getIntent().getExtras() != null && getIntent().getExtras().containsKey(KEY_TXID)) {
            String txId = getIntent().getStringExtra(KEY_TXID);
            if (!TextUtils.isEmpty(txId)) {
                setLayoutMVisa(true);
                loadDataDetailTransMVisa(txId);
            }
        }
        needReloadOnBack = true;
    }
}
