package com.mpos.screen;

import android.os.Bundle;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.customview.ViewToolBar;
import com.mpos.screen.mart.ReceiverManagerFinish;
import com.mpos.screen.printer.BaseActivityPrint;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.DataUtils;
import com.pps.core.ToastUtil;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class ActivitySendEmail extends BaseActivityPrint {

    String TAG = "ActivitySendEmail";

	@BindView(R.id.edt_email)			protected EditText edtEmail;
	@BindView(R.id.btn_cancel)			protected Button btnCancel;


	private ToastUtil mToast;

    @Override
    public StringEntity buildStringEntityReceipt() {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", Config.DOWNLOAD_RECEIPT_IMAGE);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(getApplicationContext()).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(getApplicationContext()).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(getApplicationContext()).getSessionKey());
            jo.put("tokenL2", PrefLibTV.getInstance(this).getTKL2());
            if (getIntent().getBooleanExtra("type", true)) {
                jo.put("transactionRequestID", getIntent().getIntExtra("tid", 0));
            }
            else {
                jo.put("transactionID", getIntent().getStringExtra("tid"));
            }
            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
            Utils.LOGD(TAG, "Data: "+ jo);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return entity;
    }

    @Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_send_mail);
	
		ButterKnife.bind(this);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

		mToast = new ToastUtil(this);

		ViewToolBar vToolBar = new ViewToolBar(this, findViewById(R.id.container));
		vToolBar.showTextTitle(getString(R.string.NAV_BAR_TITLE_RESEND_RECEIPT));
		vToolBar.showButtonBack(true);

		edtEmail.setOnEditorActionListener((v, actionId, event) -> {

            if(actionId == EditorInfo.IME_ACTION_DONE){
                runSendEmail();
                return true;
            }
            return false;
        });

        if (DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) {
            initPrinter();
            btnCancel.setText(getString(R.string.print_receipt));
        }

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            ReceiverManagerFinish.getInstance().pushActivityNeedFinish(this);
        }
	}
	
	@OnClick({R.id.btn_cancel, R.id.send_receipt})
	public void onClick(View v){
		switch (v.getId()) {
		case R.id.btn_cancel:

            if (DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) {
                processPrintReceipt(PRINT_IN_SEND_EMAIL);
            } else{
                finish();
            }
			break;
		case R.id.send_receipt:
			runSendEmail();
			break;
		default:
			break;
		}
	}
	
	private void runSendEmail() {
		String email = DataUtils.getTextInEdt(edtEmail);
		if(DataUtils.validateEmail(email)){
            if (isRunMultiAcquirer) {
                sendReceiptToEmailMA(txId, email);
            }
            else {
                sendEmailReceipt(email);
            }
		}
		else{
			mToast.showToast(getString(R.string.ERROR_5114));
		}
	}
	
	private void sendEmailReceipt(final String email) {
		StringEntity entity = null;
        String ssk = PrefLibTV.getInstance(this).getSessionKey();
		try {
			JSONObject jo = new JSONObject();
			jo.put(Constants.STR_SERVICE_NAME, Config.SEND_RECEIPT);
			jo.put("udid", "0");
			jo.put("readerSerialNo", PrefLibTV.getInstance(ActivitySendEmail.this).getSerialNumber());
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", Config.PLATFORM);
			jo.put("userID", PrefLibTV.getInstance(ActivitySendEmail.this).getUserId());
			jo.put("sessionKey", ssk);
//			jo.put("sessionKey", PrefLibTV.getInstance(ActivitySendEmail.this).getSessionKey());
			if (getIntent().getBooleanExtra("type", true))
				jo.put("transactionRequestID", getIntent().getIntExtra("tid", 0));
			else
				jo.put("transactionID", getIntent().getStringExtra("tid"));
			jo.put("email", email);
			Utils.LOGD("Data: ", jo.toString());
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssk));
//			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivitySendEmail.this).getSessionKey()));
		} catch (Exception e1) {
            Utils.LOGE("ActSendEmail", "sendEmailReceipt: "+e1.getMessage());
        }

		MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this),
//				MposRestClient.getInstance(this).post(ActivitySendEmail.this, ConstantsPay.getUrlServer(context),
				entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
			@Override
			public void onStart() {
				mPgdl.showLoading();
				super.onStart();
			}
			@Override
			public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
				try {
					mPgdl.hideLoading();
					JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssk));
//					JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivitySendEmail.this).getSessionKey()));
					PrefLibTV.getInstance(ActivitySendEmail.this).setSessionKey( response.getString("sessionKey"));
					Utils.LOGD("Send receipt error: ", response.toString());
					if (response.has("error")) {
						try {
							final JSONObject jo = response.getJSONObject("error");
							String msg = getString(R.string.error) + " " + String.format("%02d", jo.getInt("code")) 
									+ ": " + LibError.getErrorMsg(jo.getInt("code"), ActivitySendEmail.this);
							if (jo.getInt("code") == 2002) {
								MyDialogShow.showDialogErrorReLogin(msg, ActivitySendEmail.this);
							}
							else {
								MyDialogShow.showDialogError(msg, ActivitySendEmail.this);
							}
						} catch (JSONException e) {
							Utils.LOGE(TAG, "Exception", e);
						}
					} else {
						mToast.showToast(getString(R.string.SEND_RECEIPT_RESENT));
						finish();
					}
				} catch (Exception e) {
					Utils.LOGE(TAG, "Exception", e);
					MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivitySendEmail.this);
				}
			}

			@Override
			public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
				Utils.LOGE(TAG, "Send receipt error: "+MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
				mPgdl.hideLoading();
				
				MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), 
						ActivitySendEmail.this, v -> sendEmailReceipt(email), true);
			}
		});
	}

    private void sendReceiptToEmailMA(String txid, String email) {
        mPgdl.showLoading();
        ReceiptSend receiptSend = new ReceiptSend(txid, email);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(receiptSend));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_SEND_RECEIPT, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), ActivitySendEmail.this, true);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();

                mToast.showToast(getString(R.string.SEND_RECEIPT_RESENT));
                finish();
            }
        });
    }

	@Override
	protected void onDestroy() {
		super.onDestroy();
	}

}
