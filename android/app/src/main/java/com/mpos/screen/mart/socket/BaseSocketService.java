package com.mpos.screen.mart.socket;

import android.app.*;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import com.mpos.common.MyApplication;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.pax.dal.entity.EBeepMode;
import com.pax.emvdemo.ITYPaxApi;
import vn.mpos.R;

public abstract class BaseSocketService extends Service {

    public static final String TAG = BaseSocketService.class.getSimpleName();
    SaveLogController logUtil = MyApplication.self().getSaveLogController();
    Context context;
    ITYPaxApi paxApi;


    abstract void initSocketServer();
    public abstract void closeTaskSocket();

    @Override
    public void onCreate() {
        super.onCreate();
        this.context = this;
        if (DevicesUtil.isPax()) {
            paxApi = ITYPaxApi.get(context);
        }

        //todo P8
//        int NOTIFICATION_ID = (int) (System.currentTimeMillis()%10000);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForeground(NOTIFICATION_ID, new Notification.Builder(this).build());
//        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        appendLog("onBind");
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        appendLog("onUnbind: ====>>");
        return super.onUnbind(intent);
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        appendLog("onLowMemory: ======");
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        appendLog("onTaskRemoved: ====>");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        appendLog("onDestroy: ----");
//        stopForeground(true);
//        stopSelf();
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        appendLog("onTrimMemory: ----");
    }

    private final IBinder mBinder = new BaseSocketService.LocalBinder();

    public class LocalBinder extends Binder {
        public BaseSocketService getService() {
            return BaseSocketService.this;
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        appendLog("onStartCommand");
        int NOTIFICATION_ID = (int) (System.currentTimeMillis() % 10000);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Utils.LOGD(TAG, "onStartCommand channel1");
            NotificationChannel channel1 = new NotificationChannel(
                    getPackageName(),
                    context.getString(R.string.app_name),
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel1);
//            PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, 0);

            PendingIntent pendingIntent = PendingIntent.getActivity(context,
                    0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_CANCEL_CURRENT);

            Notification notification = new NotificationCompat.Builder(this, getPackageName())
                    .setSmallIcon(R.drawable.ic_launcher_material)
                    .setContentIntent(pendingIntent)
                    .build();
            initSocketServer();
            startForeground(NOTIFICATION_ID, notification);
        } else {
            initSocketServer();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForeground(NOTIFICATION_ID, new Notification.Builder(this).build());
            }
        }
        return super.onStartCommand(intent, flags, startId);

    }

    public void appendLog(String msg) {
        Utils.LOGD(TAG, "serviceSocket->" + msg);
        if (logUtil != null) {
            logUtil.appendLogAction("serviceSocket->" + msg);
        }
    }

    public void beep() {
        if (paxApi != null) {
            paxApi.beep(EBeepMode.FREQUENCE_LEVEL_0, 300);
        }
    }
}
