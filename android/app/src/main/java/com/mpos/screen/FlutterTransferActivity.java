package com.mpos.screen;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogResult;
import com.mpos.customview.MposDialog;
import com.mpos.models.CashierPay;
import com.mpos.models.DataSummaryDeposit;
import com.mpos.models.DepositConfig;
import com.mpos.models.HistoryDepositSend;
import com.mpos.models.MacqMerchantInfo;
import com.mpos.models.ReaderMpos;
import com.mpos.models.RethinkConfig;
import com.mpos.screen.printer.LibPrinterMpos;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.LibLoginMacq;
import com.mpos.sdk.core.control.LibPrinterBluetooth;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.control.MposTransactionsMacq;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.DataSaleSend;
import com.mpos.sdk.core.modelma.DownloadReceiptRes;
import com.mpos.sdk.core.modelma.HistoryRes;
import com.mpos.sdk.core.modelma.LoginRes;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.modelma.WorkFlow;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.whty.smartpos.tysmartposapi.pos.PosConstrants;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;
import vn.mpos.R;

/**
 * Create by anhnguyen on 6/9/20
 */
public class FlutterTransferActivity extends FlutterFragmentActivity {

    public static final int RQC_PAY = 123;

    private static final String TAG = "FlutterTransferActivity";

    public static final String ROUTE_DEPOSIT    = "/depositPage";
    public static final String ROUTE_SUMMARY    = "/transactionSummaryPage";
    public static final String ROUTE_MOTO       = "/motoPaymentInfor";
    public static final String ROUTE_CASHIER    = "/cashier";

    final String CHANNEL            = "Lister_Native_Channel";
    final String ACTION_PAY         = "Action_Pay";
    final String ACTION_HISTORY     = "Action_Get_List_Trans";
    final String ACTION_GET_CONFIG  = "Action_Get_Config";
    final String ACTION_PRINT       = "Action_Print";
    final String ACTION_PAY_MOTO    = "Push_Pay_Moto_Info";
    final String Action_Check_Is_P20L= "Action_Check_Is_P20L";
    final String Action_Check_Is_SP02= "Action_Check_Is_SP02";
    final String Action_Pay_Deposit= "Action_Pay_Deposit";
    final String ActionVoidDeposit = "Action_Void_Deposit";
    final String ActionArdGetDepositConfig = "Action_Get_Deposit_Config";
    final String ActionGetDetailAndPrintReceipt = "Action_getDetail_And_Print_Receipt";
    final String Action_Check_PermitDeposit = "Action_Check_PermitDeposit";
    final String Action_Print_Summary_Receipt_Deposit = "Action_Print_Summary_Receipt_Deposit";

    static final String _actionGetMerchantInfo = "Action_Get_Merchant_Info";
    static final String Action_CALL_API_MACQ = "Action_CALL_API_MACQ";

    final String Action_Check_Is_Pax= "Action_Check_Is_Pax";
    final String CALLBACK_PAY_RESULT    = "CallBack_Pay_Result";
    final String CALLBACK_SETTING       = "Callback_Setting";
    final String CALLBACK_MOTO_PAYMENT  = "Callback_Moto_Payment";
    final String CALLBACK_MACQ_INFO     = "Callback_Macq_Info";
//    final String CALLBACK_IS_P20L   = "Callback_Is_P20l";

    final String Action_Get_Device_Number = "Action_Get_SN";

    final String Action_Check_Is_AutoGoScreenCashier    = "Action_Check_Is_AutoGoScreenCashier";
    final String Action_Set_AutoGoScreenCashier         = "Action_Set_AutoGoScreenCashier";

    MethodChannel methodChannel;

    String dataResultPayCallback;
    String route;

    DataPay dataPay;
    SaveLogController logUtil;

    MethodChannel.Result resultChannel;

    private final Handler handlerTimeoutCallback = new Handler();
    private final Runnable runnableTimeoutCallback = () -> returnResultError("", getCurrentLanguageCode().equals(LanguageCode.LANGUAGE_EN) ? "Connection timeout, please try again later" : "Hết thời gian kết nối, vui lòng thử lại sau.", null);

    protected boolean isRunMultiAcquirer = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        route = getInitialRoute();

        logUtil = SaveLogController.getInstance(this);
        logUtil.appendLogAction("---screen cashier---route="+route);
        Utils.LOGD(TAG, "onCreate: ------>>>>>on sdk cashier<<<<<------route="+route);

        if (ROUTE_SUMMARY.equals(route)) {
            initPrinter();
        }

        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(this).getBankName())) {
            isRunMultiAcquirer = true;
        }
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine);

        Utils.LOGD(TAG, "configureFlutterEngine: ===========>>>");

        methodChannel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);

        methodChannel.setMethodCallHandler(
                (call, result) -> {
                    resultChannel = result;
                    Utils.LOGD(TAG, "configureFlutterEngine: method="+call.method);
                    Utils.LOGD(TAG, "configureFlutterEngine: arguments="+call.arguments);
                    handleMethodReceiver(call);
                }
        );
    }


    void sendCbResultPay(String data) {
        methodChannel.invokeMethod(CALLBACK_PAY_RESULT, data);
    }

    void sendCbSetting() {
        logUtil.appendLogAction("sendCbSetting");

        String data = createConfig();
        Utils.LOGD(TAG, "sendCbSetting: -->" + data);
//        methodChannel.invokeMethod(CALLBACK_SETTING, data);
        resultChannel.success(data);
    }
    void sendCbResultMoto(String data) {
        logUtil.appendLogAction("sendCbMoto");
        methodChannel.invokeMethod(CALLBACK_MOTO_PAYMENT, data);
    }

    void sendCbMacqInfo() {
        logUtil.appendLogAction("sendCbMacqInfo");
        String data = createMacqInfo();
        Utils.LOGD(TAG, "sendCbMacqInfo: " + data);
        methodChannel.invokeMethod(CALLBACK_MACQ_INFO, data);
    }

    private String createMacqInfo() {
        MacqMerchantInfo macqMerchantInfo = new MacqMerchantInfo("",
                PrefLibTV.getInstance(this).get(PrefLibTV.MPOS_MID, String.class, ""),
                PrefLibTV.getInstance(this).get(PrefLibTV.MPOS_TID, String.class, ""),
                PrefLibTV.getInstance(this).getUserId());
        macqMerchantInfo.setMotoBinRanges(PrefLibTV.getInstance(this).get(PrefLibTV.motoBinRanges, String.class, ""));
        return MyGson.getGson().toJson(macqMerchantInfo);
    }

    private String createConfig() {
        RethinkConfig rethinkConfig = new RethinkConfig(
                DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkDbName, String.class),
                DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkHostName, String.class),
                Integer.parseInt(DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkPort, String.class, "0")),
                DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkUsername, String.class),
                PrefLibTV.getInstance(this).getUserId(),
                Integer.parseInt(DataStoreApp.getInstance().getmerchantId()),
                DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkUserPassword, String.class),
                DataStoreApp.getInstance().getDataByKey(DataStoreApp.rethinkAutoPay, Boolean.class)
        );
        rethinkConfig.setIsShowHistory(DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowTransactionHistory, Boolean.class));
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isConfirmPassword, Boolean.class, Boolean.FALSE)) {
            rethinkConfig.setMuPassword(PrefLibTV.getInstance(this).getPW());
            if (DevicesUtil.isP20L()) {
                disableButtonHomeRecent();
            }
        }
        rethinkConfig.setTypeDevice(PrefLibTV.getInstance(getApplicationContext()).getFlagDevices());

        return MyGson.getGson().toJson(rethinkConfig);
    }

    private void disableButtonHomeRecent() {
        try {

            int[] statusBarButton = new int[]{ PosConstrants.STATUS_BAR_DISABLE_HOME, PosConstrants.STATUS_BAR_DISABLE_RECENT, PosConstrants.STATUS_BAR_DISABLE_EXPAND};
            MyApplication.self().getLibP20L().getSmartPosApi().disableExpand(statusBarButton);

            DataStoreApp.getInstance().setIsDisableButton(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    void handleMethodReceiver(MethodCall call) {
        logUtil.appendLogAction("receiver action: " + call.method);
        switch (call.method) {
            case ACTION_PAY:
                processActStartPay(call.arguments.toString());
                break;
            case ACTION_HISTORY:
                processOpenHistory();
                break;
            case ACTION_GET_CONFIG:
                processGetConfig();
                break;
            case ACTION_PRINT:
                processPrintShiftSettlement(call.arguments.toString());
                break;
            case ACTION_PAY_MOTO:
                processPayMoto(call.arguments.toString());
                break;
            case Action_Check_Is_P20L:
                processCheckIsP20l();
                break;
            case Action_Check_Is_SP02:
                processCheckIsSp02();
                break;
            case Action_Check_Is_Pax:
                processCheckIsPax();
                break;
            case Action_Check_Is_AutoGoScreenCashier:
                processCheckIsAutoGoScreenCashier();
                break;
            case Action_Set_AutoGoScreenCashier:
                processSaveAutoGoScreenCashier(call.arguments);
                break;
            case Action_Pay_Deposit:
                processPayDeposit(call.arguments.toString());
                break;
            case ActionVoidDeposit:
                processVoidDeposit(call.arguments.toString());
                break;
            case ActionArdGetDepositConfig:
                processGetDepositConfig();
                break;
            case ActionGetDetailAndPrintReceipt:
                initPrinter();
                processDownAndPrintReceipt(call.arguments.toString());
//                downloadReceiptMA(call.arguments.toString());
                break;
            case Action_Check_PermitDeposit:
                processCheckPermitDeposit();
                break;
            case Action_Print_Summary_Receipt_Deposit:
                processPrintSummaryDeposit(call.arguments.toString());
                break;
            case _actionGetMerchantInfo:
                processGetMerchantInfo();
                break;
            case Action_Get_Device_Number:
                processGetSN();
                break;
            case Action_CALL_API_MACQ:
                String path = call.argument("path");
                String content = call.argument("content");
                processSendApiMacq(path, content);
                break;
        }
    }
    public void processGetSN() {
        String sn = "";
        int readerType = ConstantsPay.DEVICE_NONE;
        if (DevicesUtil.isP20L()) {
            sn = MyApplication.self().getLibP20L().getSerialNumber();
            readerType = ConstantsPay.DEVICE_P20L;
            PrefLibTV.getInstance(FlutterTransferActivity.this).setFlagDevices(ConstantsPay.DEVICE_P20L);
        }
        else if(DevicesUtil.isSP02()){
            sn = MyApplication.self().getLibSP02().getSerialNumber();
            readerType = ConstantsPay.DEVICE_KOZEN;
            PrefLibTV.getInstance(FlutterTransferActivity.this).setFlagDevices(ConstantsPay.DEVICE_KOZEN);
        } else if (DevicesUtil.isPax()) {
            sn = MyApplication.self().getLibPax().getSerialnumber();
            readerType = ConstantsPay.DEVICE_PAX;
            PrefLibTV.getInstance(FlutterTransferActivity.this).setFlagDevices(ConstantsPay.DEVICE_PAX);
        }
        returnResultSuccess(MyGson.getGson().toJson(new ReaderMpos(sn, readerType)));
    }

    public void processSendApiMacq(String path, String content) {
        Utils.LOGD(TAG, "processSendApiMacq: called with: path = [" + path + "], content = [" + content + "]");
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(content);

        MposRestClient.getInstance(FlutterTransferActivity.this).setPaymentMATimeout().post(FlutterTransferActivity.this, BuildConfig.URL_MA_BASE + path, entity,
                ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(this) {
                    @Override
                    public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                        Utils.LOGD(TAG, "SendApiMacq onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
//                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
//
//                        logUtil.appendLogRequestApi("onFail  sale SendApiMacq: " + clearData);
//                        Utils.LOGD(TAG, "SendApiMacq onFailure: " + clearData);
//                        DataError dataError = new DataError();
//                        if (statusCode == 0) {
//                            dataError.build(statusCode, rawJsonData, context.getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
//                        }
//                        else {
//                            dataError.build(statusCode, rawJsonData, context.getString(R.string.error_ma_default, String.valueOf(statusCode)));
//                        }
//                        callbackResult(MyGson.getGson().toJson(dataError));

                        DataError dataError = new DataError();
                        dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                        logUtil.appendLogRequestApi("onFail  doSearchTransDeposit: " + clearData);
                        Utils.LOGD(TAG, "doSearchTransDeposit onFailure: " + clearData);

                        if (statusCode == HttpStatus.SC_UNAUTHORIZED || statusCode == HttpStatus.SC_BAD_REQUEST || statusCode == HttpStatus.SC_PAYMENT_REQUIRED) {
                            MyDialogShow.showDialogRetryCancel("", dataError.getMsg(), FlutterTransferActivity.this, view -> handlerFailApiFetchHistory(new ItfHandlerLoginAgain() {
                                @Override
                                public void doSuccess() {
                                    processSendApiMacq(path, content);
                                }
                            }), view -> handlerOnclickCancelRetry(statusCode, rawJsonData), true);
                        } else {
                            returnResultSuccess(MyGson.getGson().toJson(dataError));
//                            returnResultError(String.valueOf(dataError.getErrorCode()), dataError.getMsg(), "");
                        }
                    }

                    @Override
                    public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                        Utils.LOGD(TAG, "SendApiMacq onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                        Utils.LOGD(TAG, "SendApiMacq onSuccess: " + clearData);
                        returnResultSuccess(clearData);
                    }
                });
    }

    private void processGetMerchantInfo() {
        String data = PrefLibTV.getInstance(getApplicationContext()).getDataLoginMerchant();
        Utils.LOGD(TAG, "processGetConfig: getUserId()=" + PrefLibTV.getInstance(getApplicationContext()).getUserId());
        try {
            JSONObject jsonConfig = new JSONObject(data);
            jsonConfig.put("muid", PrefLibTV.getInstance(getApplicationContext()).getUserId());
            jsonConfig.put("password", PrefLibTV.getInstance(getApplicationContext()).getPW());
            data = jsonConfig.toString();
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        returnResultSuccess(data);
    }

    private void processPrintSummaryDeposit(String data) {
        Utils.LOGD(TAG, "processPrintSummaryDeposit: " + data);
        DataSummaryDeposit dataSummaryDeposit = MyGson.parseJson(data, DataSummaryDeposit.class);
        initPrinter();
        if (libPrinter != null) {
            libPrinter.printReceiptSummaryOfflineByLayout(dataSummaryDeposit);
        }
        returnResultSuccess("print success");
    }
    private void processCheckPermitDeposit() {
//        resultChannel.success(PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class));
        resultChannel.success(PrefLibTV.getInstance(FlutterTransferActivity.this).get(PrefLibTV.allowDeposit, String.class));
    }

    private void processDownAndPrintReceipt(String wfId) {
        mPgdl.showLoading();
        WorkFlow workFlow = new WorkFlow(wfId);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(workFlow));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_GET_TRANS_DETAIL, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                logUtil.appendLogAction("onFail get detail trans " + statusCode);
                Utils.LOGD(TAG, "TransDetails onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), FlutterTransferActivity.this, true);
                returnResultError(String.valueOf(dataError.getErrorCode()), dataError.getMsg(), "");
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                logUtil.appendLogAction("onSuccess get detail trans " + statusCode);
                Utils.LOGD(TAG, "TransDetails onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                Utils.LOGD(TAG, "TransDetails onSuccess: " + clearData);

                WfDetailRes transItem = MyGson.parseJson(clearData, WfDetailRes.class);
                DataPay dataPay = new DataPay(transItem);
                dataPay.setWfDetailRes(transItem);
                if (libPrinter != null) {
                    libPrinter.printReceiptMaOfflineByLayout(dataPay);
                }
                returnResultSuccess("print success");
            }
        });
    }

    private void processVoidDeposit(String data) {
        String transactionIDVoid = "";
        try {
            JSONObject object = new JSONObject(data);
            transactionIDVoid = object.getString("transactionID");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        MposTransactions mposTransactions = new MposTransactions(getApplicationContext());
        mposTransactions.voidTransaction(transactionIDVoid, new MposTransactionsMacq.ItfHandlerActionTrans<Boolean>() {
            @Override
            public void onFailureActionTrans(@androidx.annotation.NonNull DataError dataError, MposTransactionsMacq.ActionTrans actionTrans) {
                Utils.LOGD(TAG, "void deposit failure: " + dataError.getMsg());
                logUtil.appendLogAction("void deposit failure: " + dataError.getErrorCode());
                returnResultError(String.valueOf(dataError.getErrorCode()), dataError.getMsg(), "");
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), FlutterTransferActivity.this, true);
            }

            @Override
            public void onSuccessActionTrans(Boolean aBoolean, MposTransactionsMacq.ActionTrans actionTrans) {
                returnResultSuccess("success");
            }
        });
    }

    private void processGetDepositConfig() {
        logUtil.appendLogAction("sendCbSetting");
        DepositConfig depositConfig = new DepositConfig();
        depositConfig.setpermitVoid(PrefLibTV.getInstance(getApplicationContext()).getPermitVoid());
        depositConfig.setpermitSettle(PrefLibTV.getInstance(getApplicationContext()).getPermitSettlement());
        depositConfig.setMaxDaySettleDeposit(PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.maxDaySettleDeposit, Integer.class));
        depositConfig.setAllowDeposit(PrefLibTV.getInstance(getApplicationContext()).get(PrefLibTV.allowDeposit, String.class, Constants.SVALUE_0));
        String data =  MyGson.getGson().toJson(depositConfig);
        Utils.LOGD(TAG, "sendCbSetting: -->" + data);
        logUtil.appendLogAction("sendCbSetting: -->" + data);
        resultChannel.success(data);
    }

    private void handlerOnclickCancelRetry(int statusCode, String rawJsonData) {
        returnResultError(String.valueOf(statusCode), rawJsonData, "");
    }

    void handlerFailApiFetchHistory(ItfHandlerLoginAgain itfHandlerLoginAgain) {
        logUtil.appendLogAction("handlerFailApiFetchHistory reInitLogin");
        LibLoginMacq libLoginMacq = new LibLoginMacq(getApplicationContext(), PrefLibTV.getInstance(getApplicationContext()).getUserId(), PrefLibTV.getInstance(getApplicationContext()).getPW(), PrefLibTV.getInstance(getApplicationContext()).getSerialNumber(), new LibLoginMacq.ItfHandlerResultLoginMacq() {
            public void appendLogMacq(String log) {
                logUtil.appendLogAction(log);
            }

            @Override
            public void onFailLoginMacq(int typeFail, int statusCode, String rawJsonData) {
                logUtil.appendLogResponseFail("fail MA: code=" + statusCode + " --- typeFail=" + typeFail +  "->" + rawJsonData);
                DataError dataError = new DataError();
                if (statusCode == 0) {
                    dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
                } else {
                    dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                }
                handlerOnclickCancelRetry(statusCode, rawJsonData);
                MyDialogShow.showDialogError(getApplicationContext().getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), getApplicationContext(), true);
            }

            public void onSuccessLoginMacq(LoginRes loginRes) {
                logUtil.appendLogAction("onSuccessLoginMacq -> loadSaleHistoryMultiAcquirer");
                itfHandlerLoginAgain.doSuccess();
            }
        });
        libLoginMacq.initAuthenMA();
    }

    private void processPayDeposit(String arguments) {
        Utils.LOGD(TAG, "processActStartPay() called with: dataContent = " + arguments);
        logUtil.appendLogAction("startPay: " + arguments);
        DataSaleSend payInfo = MyGson.parseJson(arguments, DataSaleSend.class);
        try {
            MposCustom mposCustom = new MposCustom();
            mposCustom.setShowDialogTimeoutSwipeCard(false);

            MyApplication.self().getMposSdk().setMposCustom(mposCustom);
            MyApplication.self().getMposSdk().chargeAmountDeposit(this, payInfo.getDepositID(), Long.parseLong(payInfo.getAmount()), payInfo.getCustomerPhone(), null, payInfo.getUdid(), RQC_PAY);

        } catch (Exception e) {
            e.printStackTrace();
            logUtil.appendLogException(e.getMessage());
            MyDialogShow.showDialogError(getString(R.string.cashier_miss_user), this);
        }
    }

    private void processCheckIsPax() {
        Utils.LOGD(TAG, "processCheckIsPax: =>" + DevicesUtil.isPax());
        resultChannel.success(DevicesUtil.isPax());
    }

    private void processSaveAutoGoScreenCashier(Object arguments) {
        logUtil.appendLogAction("processSaveAutoGoScreenCashier " + arguments);
        DataStoreApp.getInstance().saveAutoGotoCashierActivity((boolean) arguments);
    }

    private void processCheckIsAutoGoScreenCashier() {
        Utils.LOGD(TAG, "processCheckIsAutoGoScreenCashier: =>" + DataStoreApp.getInstance().getIsAutoGotoCashierActivity());
          resultChannel.success(DataStoreApp.getInstance().getIsAutoGotoCashierActivity());
    }

    private void processCheckIsSp02() {
        Utils.LOGD(TAG, "processCheckIsSp02: =>" + DevicesUtil.isSP02());
        resultChannel.success(DevicesUtil.isSP02());
    }

    private void processCheckIsP20l() {
        Utils.LOGD(TAG, "processCheckIsP20l: =>" + DevicesUtil.isP20L());
        resultChannel.success(DevicesUtil.isP20L());
    }

    void processActStartPay(String dataContent) {
        Utils.LOGD(TAG, "processActStartPay() called with: dataContent = " + dataContent);
        // {"amount":1500,"casherId":"anhnt","orderCode":"7710295","rethinkId":"b55f11ba-d16a-4bf2-afaa-55aa73995a19"}

        logUtil.appendLogAction("startPay: " + dataContent);
        CashierPay payInfo = MyGson.parseJson(dataContent, CashierPay.class);

        try {
            MposCustom mposCustom = new MposCustom();
            mposCustom.setShowDialogTimeoutSwipeCard(false);

            MyApplication.self().getMposSdk().setMposCustom(mposCustom);
            MyApplication.self().getMposSdk().chargeAmount(this, payInfo.getOrderId(), payInfo.getAmount(), payInfo.getDescription(), null, payInfo.getUdid(), RQC_PAY);

        } catch (Exception e) {
            e.printStackTrace();
            logUtil.appendLogException(e.getMessage());
            MyDialogShow.showDialogError(getString(R.string.cashier_miss_user), this);
        }
    }

    void processOpenHistory(){
        Intent intent;
        if (MyApplication.self().isRunMA()) {
            intent = new Intent(this, ActivityPaymentHistory.class);
        } else {
            intent = new Intent(this, ActivitySubLogin.class);
            intent.putExtra(IntentsMP.EXTRA_HISTORY_CASHIER, true);
        }
        startActivity(intent);
    }

    void processGetConfig() {
        String config = createConfig();
        Utils.LOGD(TAG, "processGetConfig: "+config);

        sendCbSetting();

        if (ROUTE_MOTO.equals(route)){
            sendCbMacqInfo();
        }
    }

    void processPrintShiftSettlement(String base64Image) {
        actionPrint(base64Image);
    }

    void processPayMoto(String data) {
        Utils.LOGD(TAG, "processPayMoto: " + data);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(data);

        MposRestClient.getInstance(this).setPaymentMATimeout().post(this, ApiMultiAcquirerInterface.URL_NEW_PAYMENT, entity,
                ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "PayMoto onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);

                logUtil.appendLogRequestApi("onFail  sale PayMoto: " + clearData);
                Utils.LOGD(TAG, "PayMoto onFailure: " + clearData);
                callbackResultMoto(clearData);
//                handleFailApiMA(true, statusCode, clearData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "PayMoto onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                Utils.LOGD(TAG, "PayMoto onSuccess: " + clearData);
                logUtil.appendLogAction("PayMoto success=" + clearData);
                callbackResultMoto(clearData);
//                DataSaleRes dataSaleRes = MyGson.parseJson(clearData, DataSaleRes.class);
//                if ("00".equals(dataSaleRes.getSaleResCode())) {
//                    dataSaleSuccess = dataSaleRes;
//                    handleSuccessSaleMacq(dataSale, dataSaleRes);
//                }
//                else {
//                    try {
//                        int saleResponseCode = Integer.parseInt(dataSaleRes.getSaleResCode());
//                        checkErrorIsByCardData(saleResponseCode, dataSale.getEmvTags());
//                        checkNeedResetInjectKey(saleResponseCode);
//                        showDialogError(saleResponseCode);
//                    } catch (NumberFormatException e) {
//                        e.printStackTrace();
//                        dataSaleRes.setFlagPassPinRequired(false);
////                        handleSuccessSale(dataSaleRes);
//                        showDialogError(ERROR_CODE_MA_DEFAULT);
//                    }
//                }
            }

                    private void callbackResultMoto(String data) {
                        sendCbResultMoto(data);
                    }
        });
    }

    void returnResultError(String errorCode, String errorMessage, Object data) {
        Utils.LOGD(TAG,"returnResultError() called with: errorCode = [" + errorCode + "], errorMessage = [" + errorMessage + "], data = [" + data + "]");
        if (resultChannel != null) {
            resultChannel.error(errorCode, errorMessage, data);
            resultChannel = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    private LanguageCode getCurrentLanguageCode() {
        String deviceLanguageCode = Locale.getDefault().getLanguage();
        if ("vi".equals(deviceLanguageCode)) {
            return LanguageCode.LANGUAGE_VI;
        }
        return LanguageCode.LANGUAGE_EN;
    }

    void handleResultPayment(String content){
        returnResultSuccess(content);
        dataResultPayCallback = content;
        dataPay = null;

        ResultPayWrapper data = MyGson.getGson().fromJson(content, ResultPayWrapper.class);
        logUtil.appendLogAction("handle result: " + content);
        sendCbResultPay(dataResultPayCallback);
        if (data.getResult() != null && Constants.STATUS_TRANS_APPROVED.equals(data.getResult().status)) {
            dataPay = convertFromResultPayWrapper(data);
            showDialogSuccess("", dataPay);
        }
//        else {
//            sendCbResultPay(dataResultPayCallback);
////            showDialogFailPay(convertDataErrorFromResultPay(data));
//        }
    }

    private void showDialogSuccess(String msg, DataPay dataPay) {
        logUtil.appendLogAction("show dialog SUCCESS");
        initPrinter();
        DialogResult dialogResult = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, msg, dataPay, null);
        dialogResult.setCancelable(false);
        dialogResult.setCallback(type -> {
            switch (type) {
                case DialogResult.CLICK_PRINT:
                    printReceiptMaOffline(dataPay);
//                    attemptPrintReceipt();
                    break;
                case DialogResult.CLICK_FINISH:
                    sendCbResultPay(dataResultPayCallback);
                    break;
            }
        });
        dialogResult.setCallFinishActivity(false);
        dialogResult.setHandleClickFinish(true);

        dialogResult.show(getSupportFragmentManager(), DialogResult.class.getName());
        dialogResult.startCoundDismissDialog(10000);
    }

    private void showDialogFailPay(DataError dataError) {
        final MposDialog mposDialog = MyUtils.initDialogGeneralError(
                this,
                dataError.getErrorCode(),

                dataError.getMsg(),
                this.getClass().getName());

        mposDialog.setOnClickListenerDialogClose(v -> {
            mposDialog.dismiss();
            sendCbResultPay(dataResultPayCallback);

        });
        if (ScreenUtils.canShowDialog(this)) {
            mposDialog.show();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        logUtil.appendLogAction("onActivityResult: requestCode = [" + requestCode + "]," + "data = [" + data + "]");
        if (requestCode == RQC_PAY) {
            if (data != null) {
                String result = data.getStringExtra(Intents.EXTRA_DATA_CALLBACK);
//                sendCbResultPay(result);
                handleResultPayment(result);
            }
        } else if (requestCode == LibPrinterBluetooth.REQUEST_ENABLE_BT || requestCode == LibPrinterBluetooth.REQUEST_CONNECT_DEVICE) {
            if (libPrinter != null) {
                libPrinter.handlerResultSelectBluetoothPrinter(requestCode, resultCode, data);
            }
        }
    }

    private DataPay convertFromResultPayWrapper(ResultPayWrapper resultPay) {
        DataPay dataPay = new DataPay(resultPay.getResult().amount, "", resultPay.getResult().paymentIdentifier);
        dataPay.setTrId(resultPay.getResult().trId);
        dataPay.setTxId(resultPay.getResult().transId);
        dataPay.setPan(resultPay.getUserCard().getPan());
        dataPay.setLabel(resultPay.getUserCard().getApplicationLabel());
        dataPay.setName(resultPay.getUserCard().getCardHolderName());
        dataPay.setAuthCode(resultPay.getUserCard().getAuthCode());

        dataPay.setWfDetailRes(resultPay.getWfInfo());
        return dataPay;
    }

    private DataError convertDataErrorFromResultPay(ResultPayWrapper resultPay) {
        DataError dataError = new DataError();
        if (resultPay.getResult() != null && resultPay.getResult().error != null) {
            dataError.setErrorCode(resultPay.getResult().error.getErrorCode());
            dataError.setMsg(resultPay.getResult().error.getMsg());
        }
        else {
            dataError.setErrorCode(ConstantsPay.ERROR_CODE_DEFAULT);
        }
        return dataError;
    }


    //  ======================================== PRINTER ========================================
    private LibPrinterMpos libPrinter;
    private String receiptDownloaded;
    private String transactionIDCacheReceipt;

    protected MyProgressDialog mPgdl;

//    MyP20LPrinterListener myPrinterListener =  new MyP20LPrinterListener(this);


    protected void initPrinter() {
        mPgdl = new MyProgressDialog(this);
        if (libPrinter == null) {
            libPrinter = new LibPrinterMpos(this, MyApplication.self().getSaveLogController());
        }
    }

    protected void actionPrint(String msg) {
        Utils.LOGD(TAG, "actionPrint: ");

        if (libPrinter != null) {
            libPrinter.actionPrintImageBase64(msg);
        }
    }

    private void attemptPrintReceipt() {
        logUtil.appendLogAction("print receipt");
        processPrintReceipt();
        // sleep for printer init
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void printReceiptMaOffline(DataPay dataPay) {
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            logUtil.appendLogException("hava data print");
            if (libPrinter == null) {
                libPrinter = new LibPrinterMpos(FlutterTransferActivity.this, MyApplication.self().getSaveLogController());
            }
            if (dataPay != null && dataPay.getWfDetailRes() != null) {
                libPrinter.printReceiptMaOfflineByLayout(dataPay);
            }
        } else {
            try {
                logUtil.appendLogException("can not print: dataPay=>" +
                        (dataPay == null ? "null" : (dataPay.getWfDetailRes() == null ? "WfDetailRes = null" : "WfDetailRes != null")));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public StringEntity buildStringEntityReceipt(){
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", Config.DOWNLOAD_RECEIPT_IMAGE);
            jo.put("udid", "0");
            jo.put("readerSerialNo", PrefLibTV.getInstance(getApplicationContext()).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(getApplicationContext()).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(getApplicationContext()).getSessionKey());
            jo.put("transactionRequestID", dataPay.getTrId());
//            jo.put("transactionID", dataPay.getTrId());

            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            Utils.LOGD(TAG, "Data: "+ jo);
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return entity;
    }

    protected void processPrintReceipt() {
        if (!TextUtils.isEmpty(transactionIDCacheReceipt) && !TextUtils.isEmpty(receiptDownloaded) && dataPay.getTrId().equals(transactionIDCacheReceipt)) {
            actionPrint(receiptDownloaded);
        }
        else {
            initPrinter();
            if (isRunMultiAcquirer) {
                downloadReceiptMA(dataPay.getTxId());
            }
            else {
                downloadReceiptCustomer();
            }
        }
    }

    private void downloadReceiptMA(String txid) {
        ReceiptSend receiptSend = new ReceiptSend(txid, null);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(receiptSend));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_GET_RECEIPT, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG,  "downloadReceiptMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), FlutterTransferActivity.this, true);

            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG,  "downloadReceiptMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                DownloadReceiptRes receiptRes = MyGson.parseJson(clearData, DownloadReceiptRes.class);
                receiptDownloaded = receiptRes.getReceiptBase64();
                actionPrint(receiptDownloaded);
            }
        });
    }

    private void downloadReceiptCustomer() {

        StringEntity entity = buildStringEntityReceipt();

        transactionIDCacheReceipt = dataPay.getTrId();
        receiptDownloaded = "";

        MposRestClient.getInstance(this).post(getApplicationContext(), ConstantsPay.getUrlServer(this), entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                super.onStart();
                mPgdl.showLoading();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
                    PrefLibTV.getInstance(getApplicationContext()).setSessionKey(response.getString("sessionKey"));
                    mPgdl.hideLoading();
                    Utils.LOGD(TAG, "Img receipt: "+ response);
                    if (response.has("error")) {
                        try {
                            final JSONObject jo = response.getJSONObject("error");
                            int code = jo.getInt("code");
                            String msg = getString(R.string.error) + " " + code
                                    + ": " + LibError.getErrorMsg(code, FlutterTransferActivity.this);
                            if (code == ConstantsPay.CODE_REQUEST_TIMEOUT || code == 14004) {
                                MyDialogShow.showDialogErrorReLogin(msg, FlutterTransferActivity.this);
                            }
                            else {
                                MyDialogShow.showDialogError(msg, FlutterTransferActivity.this);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    } else {
                        receiptDownloaded = response.getString("receipt");
                        actionPrint(receiptDownloaded);
//                        processCallback();
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), FlutterTransferActivity.this);
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGE("get receipt error: ", "" + arg3.getMessage());
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                        FlutterTransferActivity.this, v -> downloadReceiptCustomer(), true);
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (libPrinter != null) {
            libPrinter.disconnectPrinter();
        }
    }

    void returnResultSuccess(Object data) {
        Utils.LOGD(TAG, "returnResultSuccess: " + data);
        try {
            String dataCompact = data != null ? (data.toString().length() > 50 ? data.toString().substring(0, 50) : data.toString()) : "";
            logUtil.appendLogAction("-> success: " + dataCompact);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (resultChannel != null) {
            resultChannel.success(data);
            resultChannel = null;
            handlerTimeoutCallback.removeCallbacks(runnableTimeoutCallback);
        }
    }

    interface ItfHandlerLoginAgain {
        void doSuccess();
    }
}
