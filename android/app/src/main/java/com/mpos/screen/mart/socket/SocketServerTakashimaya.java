package com.mpos.screen.mart.socket;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.text.TextUtils;
import com.google.gson.Gson;
import com.mpos.models.DataOrder;
import com.mpos.models.DataOrderTakashimaya;
import com.mpos.models.DataPrePayEMart;
import com.mpos.models.DataVoid;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.MyUtils;
import com.mpos.utils.PacketCryptoTakashimaya;
import vn.mpos.R;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.lang.ref.WeakReference;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.mpos.screen.mart.EMartPresenter.ORDER_CODE_FAIL_TAKA;

public class SocketServerTakashimaya {
    private static final String TAG = SocketServerTakashimaya.class.getSimpleName();

    //Service
    public static final String ADD_ORDER        = "ADD_ORDER_INFOR";
    public static final String CANCEL_ORDER     = "CANCEL_ORDER";
    public static final String UPDATE_TRANS     = "UPDATE_TRANSACTION";
    public static final String VOID_TRANSACTION = "VOID_TRANSACTION";
    public static final String NOTIFICATION     = "NOTIFICATION";

     // 1 for SALE, 2 for REFUND, 3 for VOID, 4 for TRANSFER, 5 for SETTLE
    public static final String TXN_SALE         = "1";          // CARD
    public static final String TXN_REFUND       = "2";
    public static final String TXN_VOID         = "3";
    public static final String TXN_TRANSFER     = "4";          // QR
    public static final String TXN_SETTLE       = "5";

    private static final int PORT = 1110;

    private static final int maxLengthDataInputStream = 10000;

    private static final String endString = "nextpay";
    private final String tagSocketServer = "SocSV: "; // use for push log

    private static SocketServerTakashimaya socketServerManager;

    ServerSocket serverSocket;
    Socket socket;
    DataInputStream dataInputStream;
    DataOutputStream dataOutputStream;
    EMartPresenter.ItfProcessMsgSocketTakashimaya itfProcessMsgSocket;
    EMartPresenter.ItfClientConnect itfClientConnect;

    WeakReference<BaseSocketService> weakReference;

    ExecutorService executor = Executors.newSingleThreadExecutor();
    Handler handler = new Handler(Looper.getMainLooper());

    boolean accept = true;

    SaveLogController logUtil;

    String msg_callback_data = "Data received";

    public static synchronized boolean checkExitAnCloseOldInstance() {
        Utils.LOGD(TAG, "checkExitAnCloseOldInstance: ");
        if (socketServerManager != null) {
            socketServerManager.closeTaskSocket();
            return true;
        }
        return false;
    }

    public static synchronized SocketServerTakashimaya getInstance() {
        if (socketServerManager == null) {
            Utils.LOGD(TAG, "-> create new instance");
            socketServerManager = new SocketServerTakashimaya();
            socketServerManager.appendLog("-> create new instance");
        }
        else {
            Utils.LOGD(TAG, "->use curr instance");
            socketServerManager.appendLog("->use curr instance");
        }

        return socketServerManager;
    }

    private SocketServerTakashimaya() {
    }

    public void initParams(WeakReference<BaseSocketService> weakReference, SaveLogController logUtil,
                           EMartPresenter.ItfProcessMsgSocketTakashimaya itfProcessMsgSocket, EMartPresenter.ItfClientConnect clientConnect) {
        Utils.LOGD(TAG, "initParams: -->");
        this.weakReference = weakReference;
        this.itfProcessMsgSocket = itfProcessMsgSocket;
        this.itfClientConnect = clientConnect;
        this.logUtil = logUtil;
    }

    public void openConnect() {
        executor.execute(() -> {
            //Background work here
            try {
                appendLogException("step1: start socketServer");
                serverSocket = new ServerSocket(PORT);
                Utils.LOGD(TAG, "openConnect: timeoutSK=" + serverSocket.getSoTimeout());
            } catch (IOException e) {
                Utils.LOGE(TAG, "openServer socket err= " + e.getMessage());
                appendLog("openServer socket err= " + e.getMessage());
                e.printStackTrace();
            }

            Utils.LOGD(TAG, "doInBackground: is Open Server");
            appendLog("openConnect socket");

            handlerObverseSocket(true);
        });
    }

    private void handlerObverseSocket(boolean isReset) {
        if (accept) {
            try {
                if (serverSocket != null) {
                    handlerServerOpen();
                }
            } catch (IOException e) {
                Utils.LOGE(TAG, "SocketServer: err " + e.getMessage());
                e.printStackTrace();
                runOnUIThread(() -> {
                    appendLogException("SocketServer err: " + e.getMessage());
                    logUtil.pushLog();
                });
                Utils.LOGD(TAG, "isClose reset= " + isReset);

                if (isReset) {
                    Utils.LOGD(TAG, "run isClose reset= ");
                    resetAcceptSocket(); // catch IOException serverSocket
                }
            }
        }
    }

    private void handlerServerOpen() throws IOException {
        appendLog("server waiting....");
        socket = serverSocket.accept();
        Utils.LOGD(TAG, "doInBackground: serverSocket accept success");
        if ((socket != null) && (socket.isConnected())) {
            appendLog("step2 socket server acppent sucess");
            try {
                updateClientConnect(true, socket.getInetAddress().getHostAddress());
            } catch (Exception e) {
                e.printStackTrace();
            }

            dataInputStream = new DataInputStream(new BufferedInputStream(socket.getInputStream()));
            Utils.LOGD(TAG, "----#1----available=" + socket.getInputStream().available());
            try {
                String clearData = PacketCryptoTakashimaya.decryptPacket(dataInputStream, "X7WVTM4TOEALACIUOOZACRZA");
                Utils.LOGD(TAG, "----clearData: " + clearData);
                processDataFromClient(clearData);
            } catch (Exception e) {
                appendLog("handlerServerOpen err: " + e.getMessage());
                handlerServerOpen();
            }
        } else {
            appendLogException("socketServer accept close");
            handlerServerOpen();
        }
    }

    public String decryptReceivedMessage(byte[] received, String keyStr) throws Exception {
        if (received[0] != 0x02) throw new IllegalArgumentException("Invalid STX");

        int len = ((received[1] & 0xFF) << 8) | (received[2] & 0xFF);
        byte[] encryptedData = Arrays.copyOfRange(received, 3, 3 + len);

        if (received[3 + len] != 0x03) throw new IllegalArgumentException("Invalid ETX");

        // LRC check
        byte calcLRC = 0x00;
        for (int i = 1; i <= 3 + len; i++) {
            calcLRC ^= received[i];
        }

        if (calcLRC != received[4 + len]) throw new IllegalArgumentException("Invalid LRC");

        // Decrypt
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv = new byte[8];

        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(encryptedData);

        return new String(decrypted, StandardCharsets.US_ASCII).trim(); // Remove padding zeros
    }

    private void processDataFromClient(String clearData) {
        wakeLockScreen();
        appendLog("received data from client success --> " + clearData);
        try {
            Map<String, String> map = parseDecryptedData(clearData);
            DataOrderTakashimaya dataOrder = DataOrderTakashimaya.fromMap(map);

            //todo check valid data
//            if (dataOrder.getTXN_TYPE().equals("1") || dataOrder.getTXN_TYPE().equals("4")) {

//            }
            if (!accept) {
                return;
            }
//Transaction type: 1 for SALE, 2 for REFUND, 3 for VOID, 4 for TRANSFER, 5 for SETTLE
            setAccept(false);
            runOnUIThread(() -> itfProcessMsgSocket.addOrder(dataOrder, msg -> {
                Utils.LOGD(TAG, "itfCallbackSuccess != null");
                appendLog("step end: callback msg to client= " + msg);
                Thread thread = new Thread(() -> sendMsgToClient(true, msg));
                thread.start();
            }));
        }catch (Exception e) {
            Utils.LOGD(TAG, "doInBackground: warning_invalid_data ");
            appendLog("warning_invalid_data: " + clearData);
            processMsgError();
        }
    }

    public Map<String, String> parseDecryptedData(String data) {
        Map<String, String> result = new LinkedHashMap<>();
        if (data == null || data.isEmpty()) return result;

        // Loại bỏ dấu ; cuối nếu có
        if (data.endsWith(";")) {
            data = data.substring(0, data.length() - 1);
        }

        String[] pairs = data.split(";");
        for (String pair : pairs) {
            int index = pair.indexOf(":");
            if (index != -1) {
                String key = pair.substring(0, index).trim();
                String value = pair.substring(index + 1).trim();
                result.put(key, value);
            }
        }
        return result;
    }

    private void wakeLockScreen() {
        PowerManager pm = (PowerManager) weakReference.get().getSystemService(Context.POWER_SERVICE);
        PowerManager.WakeLock wl = pm.newWakeLock(PowerManager.FULL_WAKE_LOCK | PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE, "mpos:socket");
        wl.acquire(2000);
        wl.release();
    }

    private void updateClientConnect(boolean isConnect, String ipAdressClient) {
        appendLog("connect: " + isConnect + "--- Ip Client= " + ipAdressClient);
        runOnUIThread(() -> {
            if (!TextUtils.isEmpty(ipAdressClient)) {
                itfClientConnect.clientConnected(isConnect, ipAdressClient);
            } else {
                itfClientConnect.clientConnected(isConnect, "");
            }
        });
    }

    private void processMsgError() {
        runOnUIThread(() -> itfProcessMsgSocket.doErrSocket(weakReference.get().getString(R.string.warning_invalid_data)));
        sendMsgToClient(true, PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_FAIL_TAKA));
    }

    /* push msg to client */

    private void sendMsgToClient(boolean isResetSocket, String msg) {
        Utils.LOGD(TAG, "sendMsgToClient() called with: isResetSocket = [" + isResetSocket + "], msg = [" + msg + "]");
        try {
            appendLog("action: sendMsgToClient: " + ((socket != null) ? socket.isConnected() : "socket is null"));
            pushMsgToClient(msg);
            appendLog("sendMsgToClient: success");
        } catch (IOException e) {
            appendLogException("sendMsgToClient: push result err: " + e.getMessage());
            Utils.LOGE(TAG, "sendMsgToClient: ", e);
            e.printStackTrace();
        } finally {
            if (isResetSocket) {
                updateClientConnect(false, "");
                resetAcceptSocket();
            }
        }
    }

    private void pushMsgToClient(String data) throws IOException {
        appendLog("func pushMsgToClient> ");
        if (socket != null && socket.isConnected()) {
            dataOutputStream = new DataOutputStream(socket.getOutputStream());
            byte[] dataSend;
            try {
                dataSend = PacketCryptoTakashimaya.buildEncryptedMessage(data, "X7WVTM4TOEALACIUOOZACRZA");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            dataOutputStream.write(dataSend, 0, dataSend.length);
            appendLog("pushMsgToClient success" + data);
        } else {
            Utils.LOGD(TAG, "pushMsgToClient socket = null/notConnected data=" + data);
            appendLogException("func pushMsgToClient socket = null/notConnected");
        }
    }

    public void closeTaskSocket() {
        Utils.LOGD(TAG, "closeTaskSocket");
        try {
            appendLog("closeTaskSocket-->start");
            setAccept(false);
            if (dataInputStream != null) {
                dataInputStream.close();
                dataInputStream = null;
            }
            if (dataOutputStream != null) {
                dataOutputStream.close();
                dataOutputStream = null;
            }

            if (socket != null) {
                socket.close();
                socket = null;
                Utils.LOGD(TAG, "1--socket close");
                appendLog("socket close");
            }

            if (serverSocket != null) {
                serverSocket.close();
                serverSocket = null;
                Utils.LOGD(TAG, "server Socket close");
                appendLog("server Socket close");
            }

            if (executor != null) {
                executor.shutdownNow();
            }
            appendLog("closeTaskSocket-->end");
        } catch (IOException e) {
            appendLogException("closeTaskSocket err= " + e.getMessage());
            Utils.LOGD(TAG, "closeTaskSocket err= " + e.getMessage());
            e.printStackTrace();
        } finally {
            socketServerManager = null;
        }
    }

    private void resetAcceptSocket() {
        appendLog("reset Socket");
        setAccept(true);
        try {
            if (socket != null) {
                socket.close();
                appendLog("resetAcceptSocket socket close");
            }
            if (dataInputStream != null) {
                dataInputStream.close();
                dataInputStream = null;
            }
            if (dataOutputStream != null) {
                dataOutputStream.close();
                dataOutputStream = null;
            }
        } catch (IOException e) {
            appendLog("resetAcceptSocket: err" + e.getMessage());
            e.printStackTrace();
        }
        handlerObverseSocket(false);
    }

    private void setAccept(boolean accept) {
        this.accept = accept;
    }

    private void appendLog(String log) {
        Utils.LOGD(TAG, log);
        if (logUtil != null) {
            logUtil.appendLogAction(tagSocketServer + log);
        }
    }
    
    private void appendLogException(String log) {
        if (logUtil != null) {
            logUtil.appendLogException(tagSocketServer + log);
        }
    }

    private void runOnUIThread(Runnable runnable) {
        AppExecutors.getInstance().mainThread().execute(runnable);
    }
}
