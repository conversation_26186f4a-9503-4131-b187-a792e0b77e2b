package com.mpos.screen.mart;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.annotation.NonNull;

import com.mpos.sdk.util.Utils;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by anhn<PERSON><PERSON>n on 03/11/2022
 */
public class ReceiverManagerFinish {

    private static final String TAG = "ReceiverManagerFinish";

    static ReceiverManagerFinish managerFinish;

    ReceiverFinishActivity receiverFinishActivity;

    List<Activity> listActivityNeedFinish;

    public static final String FILTER_ACTION_FINISH = "finish_other_activities";
    public static final String EXTRA_NAME_IS_FINISH = "is_finish";

    public static synchronized ReceiverManagerFinish getInstance(){
        if (managerFinish == null) {
            managerFinish = new ReceiverManagerFinish();
        }
        return managerFinish;
    }

    private ReceiverManagerFinish(){
        listActivityNeedFinish = new ArrayList<>();
    }

    void registerBroadcast(@NonNull Context context){
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(FILTER_ACTION_FINISH);

        try {
            //Register or UnRegister your broadcast receiver here
            receiverFinishActivity = new ReceiverFinishActivity();
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException ReceiverFinishActivity= " + e.getMessage());
            e.printStackTrace();
        }

        context.getApplicationContext().registerReceiver(receiverFinishActivity, intentFilter);

    }

    void unregisterBroadcast(@NonNull Context context) {
        if (receiverFinishActivity != null) {
            context.getApplicationContext().unregisterReceiver(receiverFinishActivity);
        }
        listActivityNeedFinish.clear();
        managerFinish = null;
    }

    public void pushActivityNeedFinish(@NonNull Activity activity) {
        listActivityNeedFinish.add(activity);
    }

    public void finishAllActivities() {
        Utils.LOGD(TAG, "finishAllActivities: sizeActivities=" + listActivityNeedFinish.size());
        if (listActivityNeedFinish.size() > 0) {
            for (Activity activity : listActivityNeedFinish) {
                try {
                    if (activity != null) {
                        activity.finish();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            listActivityNeedFinish.clear();
        }
    }


    public static class ReceiverFinishActivity extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            Utils.LOGD(TAG, "ReceiverFinishActivity onReceive" + intent.getAction());
            if (intent.getBooleanExtra(EXTRA_NAME_IS_FINISH, false)) {
                ReceiverManagerFinish.getInstance().finishAllActivities();
            }
        }
    }
}
