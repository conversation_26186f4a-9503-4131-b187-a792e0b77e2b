package com.mpos.screen;


import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.bumptech.glide.Glide;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.adapters.MainItemRemoteAdapter;
import com.mpos.adapters.ViewPagerBannerAdapter;
import com.mpos.common.CheckCodePTIController;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.common.MyAsyncHttpResponseHandler;
import com.mpos.customview.MposDialog;
import com.mpos.customview.MyDialogWeb;
import com.mpos.dialogs.AffiliateDialogFragment;
import com.mpos.models.BasePopup;
import com.mpos.models.DataFromPartner;
import com.mpos.models.DataIntegrated;
import com.mpos.models.ItemHomeRemote;
import com.mpos.models.responses.FooterBannerHome;
//import com.mpos.rnmodules.MyReactActivity;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.CommonConfig;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.ConfigIntegrated;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.DataUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;
//import com.synnapps.carouselview.CarouselView;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.BuildConfig;
import vn.mpos.R;

import static com.mpos.screen.FlutterTransferActivity.ROUTE_CASHIER;
import static com.mpos.screen.FlutterTransferActivity.ROUTE_MOTO;
import static com.mpos.screen.FlutterTransferActivity.ROUTE_SUMMARY;

public class FragmentHomeNew extends BaseFragment implements AffiliateDialogFragment.OnDismissAffiliateDialogListener, MainItemRemoteAdapter.IClickItemChildVovRemote {//MainItemAdapter.IClickItemChildVov,

    String TAG = "FragmentHomeNew";

    private final String HOME_ITEM_PROMOTION = "SUPPLIER_PROMO_CODE";

    @BindView(R.id.rcv_content)
    RecyclerView rcvContent;
    @BindView(R.id.imv_top)
    ImageView imvTop;
    @BindView(R.id.imgQRCode)
    ImageView imvQrCode;
    @BindView(R.id.tv_info)
    TextView tvInfo;
//    @BindView(R.id.tv_version)
//    TextView tvVersion;
    @BindView(R.id.main_layout_guide)
    View layoutGuide;
//    @BindView(R.id.main_layout_terms)
//    RelativeLayout layoutTerms;
    @BindView(R.id.main_layout_hotline)
    LinearLayout layoutHotline;

    @BindView(R.id.main_icon_account)
    AppCompatImageView imgAccount;
    @BindView(R.id.main_layout_payment_history)
    RelativeLayout layoutPayHistory;
    @BindView(R.id.main_layout_affiliate)
    RelativeLayout layoutAffiliate;
    @BindView(R.id.main_layout_statistic_shift)
    RelativeLayout layoutStatisticShift;
    @BindView(R.id.main_layout_report)
    RelativeLayout layoutReport;
    @BindView(R.id.main_layout_account)
    RelativeLayout layoutAccount;
    @BindView(R.id.main_layout_notify)
    RelativeLayout layoutNotify;
    @BindView(R.id.main_home_tv_notify)
    AppCompatTextView tvNotify;

//    @BindView(R.id.ll_banner_footer)
//    LinearLayout llBannerFooter;
//    @BindView(R.id.vg_banner)
//    View viewRootBanner;
    @BindView(R.id.vp_banner)
    ViewPager2 vpBanner;

    @BindView(R.id.tl_banner)
    TabLayout tabLayoutBanner;

//    @BindView(R.id.carousel_banner)
//    CarouselView carouselBanner;
    @BindView(R.id.ll_banner)
    LinearLayout llBanner;
    @BindView(R.id.v_cashier)
    View vCashier;
    @BindView(R.id.v_update_config)
    View vUpdateConfig;
    @BindView(R.id.ic_setting)
    View icSettings;

    private View v;

    private MainItemRemoteAdapter mainItemRemoteAdapter;
    private ToastUtil mToast;
    private MyApplication mData;
    private SaveLogController logUtil;

//    private ArrayList<ItemHome> arrHome = new ArrayList<>();
    private final ArrayList<ItemHomeRemote> arrHomeRemote = new ArrayList<>();
    private boolean isUseReader = false;

    private boolean canSaleService = false;
    private boolean canInstallment = false;
    private boolean isShowInstallment = true;
    private boolean showCheckOrderId = false;
    private boolean isNormalPayLink = false;
    private boolean isShowPromotion = false;
    private boolean isShowOTA = false;
    private boolean isShowUnivestLink = false;
    private String createLinkPaymentFlag = "0";
    private String dataVaymuonJson = "";
    private String dataVaymuonExchangeJson = "";
//    private boolean isMerchantUseMVisa = false;
    private String stringDataLoginMerchant;
    // pti
    private String mQueryCode = "";
    private boolean isShowPopup = true;
    private MyProgressDialog mPgdl;

    String menuLendingLink;

    private ItfActionHomeScreen itfActionHomeScreen;

    public FragmentHomeNew() {
        // Required empty public constructor
    }

    public void setItfActionHomeScreen(ItfActionHomeScreen itfActionHomeScreen) {
        this.itfActionHomeScreen = itfActionHomeScreen;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        v = inflater.inflate(R.layout.fragment_home, container, false);
        ButterKnife.bind(this, v);

        tvNotify.setVisibility(View.GONE);

        stringDataLoginMerchant = PrefLibTV.getInstance(context).getDataLoginMerchant();
//        stringDataLoginMerchant = DataStoreApp.getInstance().getDataLoginMerchant();
        try {
            JSONObject jRoot = new JSONObject(stringDataLoginMerchant);
            menuLendingLink = JsonParser.getDataJson(jRoot, "menuLendingLink");
            String nextlendRequestLink = JsonParser.getDataJson(jRoot, "nextlendRequestLink");
            String nextlendNotifyTitle = JsonParser.getDataJson(jRoot, "nextlendNotifyTitle");
            String nextlendNotifyDescription = JsonParser.getDataJson(jRoot, "nextlendNotifyDescription");
            String menuHome = JsonParser.getDataJson(jRoot, "menuHome");
            if (!TextUtils.isEmpty(menuHome)) {
                JSONArray jsonArrayMenuHome = new JSONArray(menuHome);
                for (int i = 0; i < jsonArrayMenuHome.length(); i++) {
                    JSONObject itemHome = jsonArrayMenuHome.getJSONObject(i);
                    String title = JsonParser.getDataJson(itemHome, "title");
                    String titleEn = JsonParser.getDataJson(itemHome, "titleEn");
                    String iconLink = JsonParser.getDataJson(itemHome, "iconLink");
                    String serviceCode = JsonParser.getDataJson(itemHome, "serviceCode");
                    String position = JsonParser.getDataJson(itemHome, "position");
                    ItemHomeRemote itemHomeRemote =
                            new ItemHomeRemote(title, titleEn, iconLink, serviceCode, Integer.parseInt(position), false);
                    arrHomeRemote.add(itemHomeRemote);
                    Utils.LOGD(TAG, "itemHome: serviceCode="+serviceCode+" iconLink="+iconLink);
                }
            }
            initBanner(nextlendRequestLink);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        initDataCache();
        initView();
        onClickHotline();
//        onClickGuide();
//        onClickTerms();
        onClickAccount();
        onClickPayHistory();
        onClickReport();
        onClickNotify();
        onClickStatisticShift();

        int checkAffiliate = DataStoreApp.getInstance().getDataCheckAffiliate();
        String eventLinkAffiliate = DataStoreApp.getInstance().getDataLinkAffiliateEvent();
        String coverLinkAffiliate = DataStoreApp.getInstance().getDataLinkAffiliateCover();

        if (TextUtils.isEmpty(eventLinkAffiliate)) {
            eventLinkAffiliate = "https://mpos.vn/";
        }
        if (TextUtils.isEmpty(coverLinkAffiliate)) {
            coverLinkAffiliate = "https://mpos.vn/assets/home/<USER>/logo/logo.png";
        }

        String stringTimeLastSeenPopup = DataStoreApp.getInstance().getDataLTSHP();
        if (!TextUtils.isEmpty(stringTimeLastSeenPopup)) {
            SimpleDateFormat df = new SimpleDateFormat("dd-MM-yyyy HH:mm", Locale.US);
            try {
                Date date = df.parse(stringTimeLastSeenPopup);
                Date current = Calendar.getInstance().getTime();
                long diff = current.getTime() - date.getTime();
                isShowPopup = diff >= 8 * 60 * 60 * 1000;
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (checkAffiliate == 1) {
            layoutAffiliate.setVisibility(View.VISIBLE);
            String finalEventLinkAffiliate = eventLinkAffiliate;
            layoutAffiliate.setOnClickListener(v -> {
                if (!TextUtils.isEmpty(finalEventLinkAffiliate)) {
                    Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
                    intent.putExtra(Constants.LINK_WEBVIEW, finalEventLinkAffiliate);
                    intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.earn_money));
                    startActivity(intent);
                }
            });
        } else {
            layoutAffiliate.setVisibility(View.GONE);
        }
        if (checkAffiliate == 1 && isShowPopup) {
            SimpleDateFormat df = new SimpleDateFormat("dd-MM-yyyy HH:mm", Locale.US);
            String formattedDate = df.format(Calendar.getInstance().getTime());
            DataStoreApp.getInstance().createDataLTSHP(formattedDate);
            isShowPopup = false;
            AffiliateDialogFragment affiliateDialogFragment = new AffiliateDialogFragment();
            affiliateDialogFragment.setOnDismissAffiliateDialogListener(this);
            Bundle bundle = new Bundle();
            bundle.putString(AffiliateDialogFragment.COVER_LINK_DIALOG_AFFILIATE, coverLinkAffiliate);
            bundle.putString(AffiliateDialogFragment.EVENT_LINK_DIALOG_AFFILIATE, eventLinkAffiliate);
            affiliateDialogFragment.setArguments(bundle);
            affiliateDialogFragment.show(getChildFragmentManager(), "AFFILIATE_DIALOG");
        }
        mPgdl = new MyProgressDialog(context);

        Utils.LOGD(TAG, "onCreateView: ------>>>");
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class)) {
//            gotoScreenCashier();
//            vCashier.setVisibility(View.VISIBLE);
//            vCashier.setOnClickListener(view1 -> gotoScreenCashier());

            ItemHomeRemote itemHomeRemote =
                    new ItemHomeRemote("TT Điểm bán hàng", "TT Điểm bán hàng", "iconLink", Constants.HOME_SERVICE_CODE_CASHIER_PAY, 0, true);
            itemHomeRemote.setResourceId(R.drawable.ic_supermarket_new);
            mainItemRemoteAdapter.addItemToIndex(0, itemHomeRemote);
        }

        // note: don't show in mode certify + debug
        if (!Utils.checkTypeBuildIsCertify() && !Utils.checkTypeBuildIsDebug()) {
            initFooterBanner();
        }

        if (DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) {
            icSettings.setVisibility(View.VISIBLE);
            icSettings.setOnClickListener(view1 -> {
                addFragment(new FragmentSettings(), FragmentSettings.class.getName());
            });
        }
        
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class) && DataStoreApp.getInstance().getIsAutoGotoCashierActivity()) {
            gotoScreenCashier();
        }

        return v;
    }

    public void checkShowViewUpgrade() {
        boolean needUpdateConfig = PrefLibTV.getInstance(context).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE);
        boolean needUpdateFw = PrefLibTV.getInstance(context).get(PrefLibTV.upgradeFw, Boolean.class, Boolean.FALSE);
        Utils.LOGD(TAG, "checkShowViewUpgrade: needUpdateConfig=" + needUpdateConfig + " needUpdateFw=" + needUpdateFw);
        if (DevicesUtil.isP20L() && needUpdateFw) {
            String versionNew = PrefLibTV.getInstance(context).get(PrefLibTV.versionFwUpgrade, String.class, "");
            needUpdateFw = MyApplication.self().getLibP20L().canUpgradeFw(versionNew);
        }
        if (needUpdateConfig || needUpdateFw) {
            vUpdateConfig.setVisibility(View.VISIBLE);
            vUpdateConfig.setOnClickListener(view -> {
                if (itfActionHomeScreen != null) {
                    int typeUpdate;
                    if (needUpdateConfig) {
                        typeUpdate = ItfActionHomeScreen.TYPE_CONFIG;
                    }
                    else {
                        typeUpdate = ItfActionHomeScreen.TYPE_FW;
                    }
                    itfActionHomeScreen.onSelectUpdateConfigReader(typeUpdate);
                }
            });
        }
        else {
            vUpdateConfig.setVisibility(View.GONE);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        checkShowViewUpgrade();
        Utils.LOGD(TAG, "onResume: hasStoppedLoop=" + isStoppedLoopFooterBanner);
        if (totalFooterBanner > 0 && isStoppedLoopFooterBanner) {
            startLoopFooterBanner();
        }
    }

    boolean isStoppedLoopFooterBanner = false;
    @Override
    public void onStop() {
        super.onStop();
        Utils.LOGD(TAG, "onStop: ");
        if (totalFooterBanner > 0) {
            isStoppedLoopFooterBanner = true;
            handlerLoop.removeCallbacksAndMessages(null);
        }
    }

    private void initView() {
        initDataItem();
//        mainItemAdapter = new MainItemAdapter(context, arrHome, this);
        ArrayList<ItemHomeRemote> arrHomeRemoteShow = new ArrayList<>();
        for (int i = 0; i < arrHomeRemote.size(); i++) {
            if (arrHomeRemote.get(i).isShow()) arrHomeRemoteShow.add(arrHomeRemote.get(i));
        }
        if (!TextUtils.isEmpty(menuLendingLink)) {
            arrHomeRemoteShow.add(new ItemHomeRemote(getResources().getString(R.string.lend_money), getResources().getString(R.string.lend_money), Uri.parse("android.resource://"+context.getPackageName()+"/drawable/" + R.drawable.ic_nextlend_home).toString(), "NEXT_LEND", arrHomeRemoteShow.size(), true));
        }
        Collections.sort(arrHomeRemoteShow);
        mainItemRemoteAdapter = new MainItemRemoteAdapter(context, arrHomeRemoteShow, this);
        rcvContent.setAdapter(mainItemRemoteAdapter);
        rcvContent.setLayoutManager(new GridLayoutManager(context, 3));

        imvQrCode.setVisibility(View.GONE);
        tvInfo.setVisibility(View.GONE);
        imvTop.setVisibility(View.VISIBLE);
//        tvVersion.setText(getString(R.string.txt_version, BuildConfig.VERSION_NAME));

        layoutGuide.setOnClickListener(view1 -> {
            Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
            intent.putExtra(Constants.LINK_WEBVIEW, Config.URL_MANUAL);
            intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.guide_use));
            startActivity(intent);
        });

        getPopUpInfo();
    }

    private void initBanner(String nextlendRequestLink) {
        JSONObject jBanners = new JSONObject();
        String hasPasswordLv2 = "";
        List<TopBannerHome> listImageBanner = new ArrayList<>();

        try {
//            String stringDataLoginMerchant = DataStoreApp.getInstance().getDataLoginMerchant();
            JSONObject jRoot = new JSONObject(stringDataLoginMerchant);
            String banners = JsonParser.getDataJson(jRoot, "banners");
            jBanners = new JSONObject(banners);
            hasPasswordLv2 = JsonParser.getDataJson(jRoot, "hasPasswordLv2");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (Constants.SVALUE_FALSE.equals(hasPasswordLv2)) {
            if (jBanners.has("ACTIVE_ATM360")) {
                listImageBanner.add(new TopBannerHome("ACTIVE_ATM360", JsonParser.getDataJson(jBanners, "ACTIVE_ATM360")));
            }
        }
        if (!TextUtils.isEmpty(nextlendRequestLink)) {
            if (jBanners.has("UNG_VON")) {
                listImageBanner.add(new TopBannerHome("UNG_VON", JsonParser.getDataJson(jBanners, "UNG_VON")));
            }
        }

        if (listImageBanner.size() > 0) {
            new GetImageSizeTask().execute(listImageBanner.get(0).url);
            llBanner.setVisibility(View.VISIBLE);
//            carouselBanner.setPageCount(listImageBanner.size());
//            carouselBanner.setViewListener(position -> {
//                LayoutInflater layoutInflater = LayoutInflater.from(context);
//                View customView = layoutInflater.inflate(R.layout.item_home_banner, null);
//                ImageView imageView = customView.findViewById(R.id.iv_banner);
//                Glide.with(getActivity()).load(listImageBanner.get(position).url).into(imageView);
//                imageView.setOnClickListener(v -> {
//                    switch (listImageBanner.get(position).type) {
//                        case "ACTIVE_ATM360":
//                            Intent intent = new Intent(context, MyReactActivity.class);
//                            intent.putExtra("RN_ROUTE_NAME", "ActiveVasWalletContainer");
//                            startActivity(intent);
//                            break;
//                        case "UNG_VON":
//                            Intent intent2 = new Intent(getActivity(), ActivityNewsDetail.class);
//                            intent2.putExtra(Constants.LINK_WEBVIEW, nextlendRequestLink);
//                            intent2.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.lend_money_title));
//                            intent2.putExtra(Constants.TITLE_WEBVIEW_OVERLAY, true);
//                            startActivity(intent2);
//                            break;
//                    }
//                });
//                return customView;
//            });
        } else {
            llBanner.setVisibility(View.GONE);
        }

    }

    private void initFooterBanner() {
        String stringDataBanner = DataStoreApp.getInstance().getDataBanner();
        if (!TextUtils.isEmpty(stringDataBanner)) {
            List<FooterBannerHome> arrFooterBanner = MyGson.parseJsonToList(stringDataBanner, FooterBannerHome.class);

            if (arrFooterBanner != null && arrFooterBanner.size() > 0) {
                totalFooterBanner = arrFooterBanner.size();
                ViewPagerBannerAdapter vpBannerAdapter = new ViewPagerBannerAdapter(context, arrFooterBanner, position -> {
                    if (arrFooterBanner.get(position) != null && !TextUtils.isEmpty(arrFooterBanner.get(position).getLinkWebview())) {
                        Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
                        intent.putExtra(Constants.LINK_WEBVIEW, arrFooterBanner.get(position).getLinkWebview());
                        intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.app_name));
                        startActivity(intent);
                    }
                });
                vpBanner.setAdapter(vpBannerAdapter);

                new TabLayoutMediator(tabLayoutBanner, vpBanner,
                        (tab, position) -> {}
                ).attach();

                vpBanner.setVisibility(View.VISIBLE);
                tabLayoutBanner.setVisibility(View.VISIBLE);
                startLoopFooterBanner();
            }
        }
    }

    int currIndexFooterBanner = 0;
    int totalFooterBanner = 0;
    Handler handlerLoop = new Handler();
    private void startLoopFooterBanner() {
        // todo fake test
        if (BuildConfig.DEBUG && Utils.checkTypeBuildIsCertify()) {
            return;
        }
        handlerLoop.postDelayed(() -> {
//            Utils.LOGD(TAG, "startLoopFooterBanner: currIndexFooterBanner=" + currIndexFooterBanner + " total=" + totalFooterBanner);
            if (currIndexFooterBanner >= totalFooterBanner-1) {
                currIndexFooterBanner = 0;
            }
            else {
                currIndexFooterBanner++;
            }
            vpBanner.setCurrentItem(currIndexFooterBanner, true);


            startLoopFooterBanner();
        }, 3000);
    }

    public static int dpToPx(int dp) {
        return (int) (dp * Resources.getSystem().getDisplayMetrics().density);
    }

    private void onClickHotline() {
        layoutHotline.setOnClickListener(v -> {
            if (getActivity() != null) {
                Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
                intent.putExtra(Constants.LINK_WEBVIEW, "https://mpos.vn/public/apps/support-center.html");
                intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.SUPPORT_SUBHEADER_HOTLINE));
                startActivity(intent);
            }
//            showDialogSupportHotLine();
        });
    }

//    private void onClickGuide() {
//        layoutGuide.setOnClickListener(v -> addFragment(FragmentSupports.newInstance(), "FragmentSupports"));
//    }

    private void onClickStatisticShift() {
        layoutStatisticShift.setOnClickListener(v -> gotoScreenCashierStatisticShift());
    }

    private void onClickNotify() {
        layoutNotify.setOnClickListener(v -> addFragment(FragmentNews.newInstance(), FragmentNews.tag));
    }

//    private void onClickTerms() {
//        layoutTerms.setOnClickListener(v -> addFragment(FragmentWebview.newInstance(Config.URL_TERM), FragmentWebview.tag));
//    }

    private void onClickAccount() {
//        layoutAccount.setOnClickListener(v -> {
//
//            Intent intent = new Intent(context, MyReactActivity.class);
//            intent.putExtra("RN_ROUTE_NAME", "UserInfoContainer");
//            startActivity(intent);
//        });
//        if (BuildConfig.DEBUG) {
//            layoutAccount.setOnLongClickListener(view1 -> {
//                MyDialogShow.showDialogError("Mã 2002", getString(R.string.ERROR_2002), context);
////                MyDialogShow.showDialogErrorReLogin(getString(R.string.ERROR_2002), context);
//                return true;
//            });
////            layoutAccount.setOnLongClickListener(view1 -> {
////                gotoSetting();
////                return true;
////            });
//        }
    }

    private void gotoScreenCashierStatisticShift() {
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route",ROUTE_SUMMARY);
        startActivity(intent);
    }

    private void handleSelectMotoPayment() {
        LibLoginHandler libLoginHandler = new LibLoginHandler(context, PrefLibTV.getInstance(context).getFlagDevices());
        if (hasMotoBinRangesCache()) {
            gotoScreenMotoPayment();
            libLoginHandler.fetchCommonConfig(true, false, PrefLibTV.getInstance(context).getUserId(),
                    PrefLibTV.getInstance(context).getSerialNumber(), (result, commonConfig) -> {
                logUtil.appendLogAction("load commonConfig after: " + result);
            });
        }
        else {
            libLoginHandler.fetchCommonConfig(true, true, PrefLibTV.getInstance(context).getUserId(),
                    PrefLibTV.getInstance(context).getSerialNumber(), (result, commonConfig) -> {
                logUtil.appendLogAction("load commonConfig before: " + result);
                gotoScreenMotoPayment();
            });
        }
    }

    private boolean hasMotoBinRangesCache() {
        LibLoginHandler libLoginHandler = new LibLoginHandler(context, PrefLibTV.getInstance(context).getFlagDevices());
        CommonConfig commonConfig = libLoginHandler.getCmConfigFromCache();
        if (commonConfig != null && commonConfig.getMotoBinRanges() != null) {
            return true;
        }
        return false;
    }

    private void gotoScreenMotoPayment() {
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route", ROUTE_MOTO);
        startActivity(intent);
    }

    //    private void showDialogSuccess() {
//        String msg = "test";
//        DataPay dataPay = new DataPay("1000", "desc", "udid");
//        Promotion promotion = new Promotion();
//        promotion.setSupplierPromoCodeId("234");
//        promotion.setTransactionPromoCodeId("567");
//
//        DialogResult dialogResult = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, msg, dataPay, null, promotion);
//        dialogResult.setCancelable(false);
//        dialogResult.setCallFinishActivity(false);
//        dialogResult.setHandleClickFinish(true);
//
//        dialogResult.show(getFragmentManager(), DialogResult.class.getName());
//    }
//    private void test() {
//        Intent intent = new Intent(context, ActivityEnterCardWebView.class);
////        intent.putExtra(Constants.LINK_WEBVIEW, linkCheckout);
////        intent.putExtra(Constants.KEY_MVISA_UDID, udid);
//        startActivityForResult(intent, 1000);
//    }

    private void gotoSetting() {
        startActivity(new Intent(android.provider.Settings.ACTION_SETTINGS));
    }

    private void gotoScreenCashier() {
//        if (DevicesUtil.isP20L() && itfActionHomeScreen != null) {
//            itfActionHomeScreen.onSelectCashier();
//        }
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route", ROUTE_CASHIER);
        startActivity(intent);

    }


    private void onClickPayHistory() {
        layoutPayHistory.setOnClickListener(v -> gotoHistory());
    }

    private void onClickReport() {
        layoutReport.setOnClickListener(v -> Toast.makeText(getActivity(), "Tính năng đang trong quá trình phát triển", Toast.LENGTH_LONG).show());
    }

    private void initDataItem() {

        for (int i = 0; i < arrHomeRemote.size(); i++) {
            if (arrHomeRemote.get(i) != null) {
                switch (arrHomeRemote.get(i).getServiceCode()) {
                    case "CARD_PAYMENT":
                    case "QRCode_PAYMENT":
                        arrHomeRemote.get(i).setShow(true);
                        break;
                    case "QRCode_International_PAYMENT":
                        arrHomeRemote.get(i).setShow(isUseReader && DataStoreApp.getInstance().getHaveListQrInternational());
                        break;
                    case Constants.HOME_SERVICE_CODE_MACQ_MOTO:
                        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isPayMotoMacq, Boolean.class, false)) {
                            arrHomeRemote.get(i).setShow(true);
                        }
                        break;
                    case "INSTALLMENT_PAYMENT":
                        int isPayLink = DataStoreApp.getInstance().getDataCheckIsPayLink();
                        int isPayCard = DataStoreApp.getInstance().getDataCheckIsPayCard();
                        arrHomeRemote.get(i).setShow((isShowInstallment && ((isPayLink == 1) || (isPayCard == 1 && isUseReader))));
                        break;
                    case "VAS_PAYMENT":
                    case "NCOV_PAYMENT":
                        arrHomeRemote.get(i).setShow(canSaleService);
                        break;
                    case "VayMuonQR_PAYMENT":
                        try {
                            JSONArray jsonArray = new JSONArray(dataVaymuonJson);
                            if (jsonArray.length() > 0) {
                                arrHomeRemote.get(i).setShow(true);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        break;
                    case "Integrated_PAYMENT":
                        arrHomeRemote.get(i).setShow(showCheckOrderId);
                        break;
                    case "LinkCard_PAYMENT":
                        arrHomeRemote.get(i).setShow(isNormalPayLink || "1".equals(createLinkPaymentFlag));
                        break;
                    case HOME_ITEM_PROMOTION:
                        arrHomeRemote.get(i).setShow(isShowPromotion);
                        break;
                    case "OTA_PAYMENT":
                        arrHomeRemote.get(i).setShow(isShowOTA);
                        break;
                    case "UNIVEST_INVEST":
                        arrHomeRemote.get(i).setShow(isShowUnivestLink);
                        break;
                    default:
                        break;
                }
            }
        }

    }

    private void initDataCache() {
//        mPgdl = new MyProgressDialog(context);
        mToast = new ToastUtil(context);
        mData = (MyApplication) ((Activity) context).getApplication();
        logUtil = MyApplication.self().getSaveLogController();

        canSaleService = DataStoreApp.getInstance().getCanSaleService();
        canInstallment = DataStoreApp.getInstance().getHaveInstallment();
        isShowInstallment = DataStoreApp.getInstance().getIsShowInstallment();
        isShowOTA = DataStoreApp.getInstance().getIsShowOTA();
        isShowUnivestLink = DataStoreApp.getInstance().getIsShowUnivestLink();
        isShowPromotion = DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowPromotion, Boolean.class, false);
        isUseReader = DataStoreApp.getInstance().isUseReader();
//        isMerchantUseMVisa = dataStore.isMerchantRegistedQR();
        dataVaymuonJson = DataStoreApp.getInstance().getDataVaymuonInstallment();
        dataVaymuonExchangeJson = DataStoreApp.getInstance().getDataExchangeInfo();

        DataIntegrated dataIntegrated = mData.getDataIntegrated();
        if (dataIntegrated != null
                && ConfigIntegrated.TYPE_APP2_SER2_SER2.equals(dataIntegrated.getPreType())
        ) {
            showCheckOrderId = true;
        }
        isNormalPayLink = DataStoreApp.getInstance().getIsNormalPayLink();
        try {
//            String stringDataLoginMerchant = DataStoreApp.getInstance().getDataLoginMerchant();
            JSONObject jRoot = new JSONObject(stringDataLoginMerchant);
            createLinkPaymentFlag = JsonParser.getDataJson(jRoot, "createLinkPaymentFlag");
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void clickItemHomeRemote(ItemHomeRemote itemHomeRemote) {
        switch (itemHomeRemote.getServiceCode()) {
            case "CARD_PAYMENT":
                if (!isUseReader) {
                    MyDialogShow.showDialogInfo(getActivity(), getString(R.string.message_card_unavailable));
                } else if (!DataStoreApp.getInstance().isEnableNormalPayment()) {
                    MyDialogShow.showDialogInfo(getActivity(), getString(R.string.message_can_not_normal_payment));
                } else {
                    gotoHomeEnter(Constants.CARD_PAYMENT, itemHomeRemote);
                }
                break;
            case "INSTALLMENT_PAYMENT":
                checkInstallment();
                break;
            case "QRCode_PAYMENT":
                checkRegisterRQ(false);
                break;
            case "QRCode_International_PAYMENT":
                checkRegisterRQ(true);
                break;
            case "NCOV_PAYMENT":
//                if (isUseReader) {
//                    Intent intent = new Intent(context, MyReactActivity.class);
//                    intent.putExtra("RN_ROUTE_NAME", "NCovWebview");
//                    startActivity(intent);
//                } else {
//                    gotoServiceList("NCovWebview");
//                }
                break;
            case "VAS_PAYMENT":
//                if (isUseReader) {
//                    Intent intent = new Intent(context, MyReactActivity.class);
//                    intent.putExtra("RN_ROUTE_NAME", "ServiceList");
//                    startActivity(intent);
//                } else {
//                    gotoServiceList("ServiceList");
//                }
                break;
            case "VayMuonQR_PAYMENT":
                gotoInstallmentByVaymuon();
                break;
            case "Integrated_PAYMENT":
                checkPayBill();
                break;
            case "LinkCard_PAYMENT":
                if (!DataStoreApp.getInstance().isEnableNormalPayment()) {
                    MyDialogShow.showDialogInfo(getActivity(), getString(R.string.message_can_not_normal_payment));
                } else {
                    gotoHomeEnter(Constants.CARD_ENTER_PAYMENT, itemHomeRemote);
                }
                break;
            case HOME_ITEM_PROMOTION:
                gotoPromotion();
                break;
            case "NEXT_LEND":
                gotoNextLend();
                break;
            case "OTA_PAYMENT":
                gotoOTA();
                break;
            case "UNIVEST_INVEST":
                gotoUnivestInvest();
                break;
            case Constants.HOME_SERVICE_CODE_CASHIER_PAY:
                gotoScreenCashier();
                break;
            case Constants.HOME_SERVICE_CODE_MACQ_MOTO:
                handleSelectMotoPayment();
                break;
            default:
                break;
        }
    }

    private void gotoNextLend(){
        Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
        intent.putExtra(Constants.LINK_WEBVIEW, menuLendingLink);
        intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.lend_money_title));
        intent.putExtra(Constants.TITLE_WEBVIEW_OVERLAY, true);
        startActivity(intent);
    }

    /*private boolean checkMVISAPayment() {
        return DataStoreApp.getInstance().getListQrPay() != null && DataStoreApp.getInstance().getListQrPay().size() > 0
                && DataStoreApp.getInstance().haveQrInternational();
    }*/

    private void checkPayBill() {
        if (!isUseReader && !DataStoreApp.getInstance().isMerchantRegistedQR()) {
            guideUserRegisterQR();
        } else {
            if (showCheckOrderId) {
                showDialogInputOrderCode();
            }
        }
    }

    private void checkRegisterRQ(boolean isInternational) {
//        if (!isInternational && ((!isUseReader && !DataStoreApp.getInstance().isMerchantRegistedQR()) || !DataStoreApp.getInstance().getHaveListQrSource())) {
//            guideUserRegisterQR();
//        } else {
//            Intent intent = new Intent(context, MyReactActivity.class);
//            intent.putExtra("RN_ROUTE_NAME", "QRPaymentListSourceContainer");
//            intent.putExtra("RN_QR_IS_INTERNATIONAL", isInternational);
//            startActivity(intent);
//        }
    }

    private void checkInstallment() {
        if (canInstallment) {
            gotoInstallment();
        } else {
            showLandingInstallment();
        }
    }


    private void gotoInstallment() {
        // React Native

//        Intent intent = new Intent(context, MyReactActivity.class);
//        intent.putExtra("RN_ROUTE_NAME", "InstallmentListBankContainer");
//        intent.putExtra("RN_INSTALLMENT_LIST_BANK", PrefLibTV.getInstance(context).get(PrefLibTV.installmentInfo, String.class));
////        intent.putExtra("RN_INSTALLMENT_LIST_BANK", DataStoreApp.getInstance().getDataInstallment());
//        intent.putExtra("RN_INSTALLMENT_LIST_FEE_CARD", DataStoreApp.getInstance().getDataExchangeInfo());
//        intent.putExtra("RN_INSTALLMENT_LIST_FEE_CARD_LINK", DataStoreApp.getInstance().getDataExchangeLinkInfo());
//        intent.putExtra("RN_INSTALLMENT_ALLOW_CHANGE_FEE", DataStoreApp.getInstance().getAllowChangeFee());
//        intent.putExtra("RN_INSTALLMENT_ENABLE_FEE_TRANS", DataStoreApp.getInstance().getEnalbeFeeTrans());
//        intent.putExtra("RN_INSTALLMENT_ENABLE_FEE_INSTALLMENT", DataStoreApp.getInstance().getEnalbeFeeInstallment());
//        intent.putExtra("RN_CHECK_BILL_INSTALLMENT", DataStoreApp.getInstance().getDataCheckBinInstallment());
//        intent.putExtra("RN_CHECK_IS_PAY_LINK", DataStoreApp.getInstance().getDataCheckIsPayLink());
//        intent.putExtra("RN_CHECK_IS_PAY_CARD", DataStoreApp.getInstance().getDataCheckIsPayCard());
//        startActivity(intent);
    }

    private void gotoInstallmentByVaymuon() {
        // React Native

//        Intent intent = new Intent(context, MyReactActivity.class);
//        intent.putExtra("RN_ROUTE_NAME", "InstallmentByVaymuonContainer");
//        intent.putExtra("RN_INSTALLMENT_BY_VAYMUON_DATA", dataVaymuonJson);
//        intent.putExtra("RN_EXCHANGE_BY_VAYMUON_DATA", dataVaymuonExchangeJson);
//        startActivity(intent);
    }

    private void gotoPromotion() {
        // React Native
//        Intent intent = new Intent(context, MyReactActivity.class);
//        intent.putExtra("RN_ROUTE_NAME", "PromotionListContainer");
//        intent.putExtra("RN_INSTALLMENT_LIST_BANK", PrefLibTV.getInstance(context).get(PrefLibTV.installmentInfo, String.class));
//        intent.putExtra("RN_INSTALLMENT_LIST_FEE_CARD", DataStoreApp.getInstance().getDataExchangeInfo());
//        intent.putExtra("RN_INSTALLMENT_LIST_FEE_CARD_LINK", DataStoreApp.getInstance().getDataExchangeLinkInfo());
//        intent.putExtra("RN_INSTALLMENT_ALLOW_CHANGE_FEE", DataStoreApp.getInstance().getAllowChangeFee());
//        intent.putExtra("RN_INSTALLMENT_ENABLE_FEE_TRANS", DataStoreApp.getInstance().getEnalbeFeeTrans());
//        intent.putExtra("RN_INSTALLMENT_ENABLE_FEE_INSTALLMENT", DataStoreApp.getInstance().getEnalbeFeeInstallment());
//        intent.putExtra("RN_CHECK_BILL_INSTALLMENT", DataStoreApp.getInstance().getDataCheckBinInstallment());
//        intent.putExtra("RN_CHECK_IS_PAY_LINK", DataStoreApp.getInstance().getDataCheckIsPayLink());
//        intent.putExtra("RN_CHECK_IS_PAY_CARD", DataStoreApp.getInstance().getDataCheckIsPayCard());
//        startActivity(intent);
    }

    private void gotoOTA() {
        // React Native
//        Intent intent = new Intent(context, MyReactActivity.class);
//        intent.putExtra("RN_ROUTE_NAME", "OTAEnterInfoContainer");
//        startActivity(intent);
    }

    private void gotoUnivestInvest() {
        // React Native
//        Intent intent = new Intent(context, MyReactActivity.class);
//        intent.putExtra("RN_ROUTE_NAME", "UnivestInvestContainer");
//        startActivity(intent);
    }

    private void gotoServiceList(String routeName) {
//        StringEntity entity = null;
//        try {
//            JSONObject jo = new JSONObject();
//            jo.put(Constants.STR_SERVICE_NAME, Config.CHECK_ACTIVE_VAS_WALLET);
//            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
//            Utils.LOGD("Data: ", jo.toString());
//            Utils.LOGD(TAG, "REQ: " + jo);
//            entity = new StringEntity(jo.toString());
//        } catch (JSONException | UnsupportedEncodingException e1) {
//            Utils.LOGE(TAG, "checkVAS: " + e1);
//        }
//
//        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
//            @Override
//            public void onStart() {
//                mPgdl.showLoading("");
//                super.onStart();
//            }
//
//            @Override
//            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
//                mPgdl.hideLoading();
//                try {
//                    JSONObject jRoot = new JSONObject(new String(arg2));
//                    Utils.LOGD(TAG, "RESPONSE: " + jRoot);
//                    if (jRoot.has("error")) {
//                        final JSONObject jError = jRoot.getJSONObject("error");
//                        int errorCode = jError.getInt("code");
//                        String msg = jError.getString("message");
//                        logUtil.appendLogAction("logIn: error=" + errorCode);
//                        logUtil.saveLog();
//                        if (errorCode == 1000) {
//                            Intent intent = new Intent(context, MyReactActivity.class);
//                            intent.putExtra("RN_ROUTE_NAME", routeName);
//                            startActivity(intent);
//                        } else if (errorCode == -8110) {
//                            MyDialogShow.showDialogInfo(getActivity(), msg);
//                        } else {
//                            final MposDialog mposDialogError = MyUtils.initDialogGeneralError(context, errorCode, msg, FragmentHomeNew.class.getName());
//                            mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
//                            mposDialogError.show();
//                        }
//                    }
//                } catch (Exception e) {
//                    logUtil.appendLogRequestApi(Config.URL_CHECK_STATUS_MVISA + " Exception:" + e.getMessage());
//                    logUtil.saveLog();
//                    Utils.LOGE(TAG, "Exception", e);
//                }
//                logUtil.pushLog();
//            }
//
//            @Override
//            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//                Utils.LOGE(TAG, "checkVAS Error: ", arg3);
//                mPgdl.hideLoading();
//            }
//        });
    }


    private void gotoHistory() {
        Intent i;
        Utils.LOGD(TAG, "gotoHistory: " + PrefLibTV.getInstance(context).getBankName());
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(context).getBankName())) {
            i = new Intent(context, ActivityPaymentHistory.class);
        }
        else {
            if (isUseReader && (MposUtil.getInstance().checkNeedLoginLevel2() || TextUtils.isEmpty(PrefLibTV.getInstance(context).getTKL2()))) {
                i = new Intent(context, ActivitySubLogin.class);
            }
            else {
                i = new Intent(context, ActivityPaymentHistory.class);
                i.putExtra("tokenL2", PrefLibTV.getInstance(context).getTKL2());
            }
        }
        startActivity(i);
    }


    private void gotoHomeEnter(String typePayment) {
        Intent intent = new Intent();
        intent.setClass(context, ActivityHomeEnter.class);
        intent.putExtra(Constants.EXTRA_INTENT, typePayment);
        startActivity(intent);
    }

    private void gotoHomeEnter(String typePayment, ItemHomeRemote itemHomeRemote) {
        Intent intent = new Intent();
        intent.setClass(context, ActivityHomeEnter.class);
        intent.putExtra(Constants.EXTRA_INTENT, typePayment);
        intent.putExtra(Constants.EXTRA_SERVICE_CODE, itemHomeRemote.getServiceCode());
        startActivity(intent);
    }

    private void gotoHomeEnter(Intent intent) {
        startActivity(intent);
    }

    private void guideUserRegisterQR() {
        //note: On 11/16/17, at 1:35 PM, Tap Ngo Minh wrote:
        //note: Trường hợp login bằng email MC, bọn em có check là MC đó có được phép thanh toán QR hay không không?
        //note: ĐVCNT chưa đăng ký thanh toán MPOS QR. Vui lòng liên hệ hotline &hot_line; để đăng ký hoặc đăng nhập lại với thiết bị đọc thẻ để thanh toán.

        String msgError = getString(R.string.txt_login_email_and_not_accept_mvisa);
        logUtil.appendLogRequestApi(" error when user login use email, not use mpos's device, not accept MVISA");
        if (!TextUtils.isEmpty(msgError)) {
            logUtil.appendLogException(msgError);
//            if (!isUseReader) {
//                showErrorLoadMposNotUseDevice(0, msgError);
//            }
            showErrorLoadMposNotUseDevice(0, msgError);
        }
    }


    private void showDialogInputOrderCode() {
        final Dialog dialog = new Dialog(context, R.style.SpecialDialog);
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
        View dialogLayout = inflater.inflate(R.layout.dialog_input_order_id, null);
        final EditText et = dialogLayout.findViewById(R.id.edt_order_code);
        dialogLayout.findViewById(R.id.btn_invoice_pay).setOnClickListener(v -> {
            mQueryCode = DataUtils.getTextInEdt(et);
            if (TextUtils.isEmpty(mQueryCode)) {
                mToast.showToast(getString(R.string.ALERT_MISSING_FIELD_TITLE));
            } else {
                dialog.dismiss();
                checkOrderCodePTI(mQueryCode);
            }
        });

        dialogLayout.findViewById(R.id.btn_normal_pay).setOnClickListener(v -> {
            gotoHomeEnter(Constants.QR_PAYMENT);
            dialog.dismiss();
        });
        dialogLayout.findViewById(R.id.imv_close).setOnClickListener(v -> dialog.dismiss());

        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
        dialog.setCanceledOnTouchOutside(false);
        dialog.setContentView(dialogLayout);
        dialog.show();
    }

    private void checkOrderCodePTI(String code) {
        final String udid = Utils.zenUdid();
        CheckCodePTIController ptiController = new CheckCodePTIController(context, true, false,
                new CheckCodePTIController.ResultCheckCodePTI() {
                    @Override
                    public void onResultCheckCodePTI(DataFromPartner dataPartner) {
                        if (!dataPartner.isHaveError()) {
                            Intent intent = new Intent(context, ActivityHomeEnter.class);
                            intent.putExtra(Intents.EXTRA_DATA_PARTNER, dataPartner);
                            intent.putExtra(IntentsMP.EXTRA_PARTNER_IS_INPUT_CODE, true);
                            intent.putExtra(Constants.EXTRA_INTENT, Constants.CARD_PAYMENT);

                            gotoHomeEnter(intent);
                        }
                    }

                    @Override
                    public void onResultGotoMVisaWithQRID(DataFromPartner dataPartner, String qrid) {
                        gotoPaymentMVisa(dataPartner, qrid, udid);
                    }
                });
        ptiController.getInfoOrderId(code, udid);
    }

    private void gotoPaymentMVisa(DataFromPartner dataPartner, String qrid, String udid) {
        Utils.LOGD(TAG, "---------------------");
        Utils.LOGD(TAG, ">>> gotoPaymentMVisa");
        Intent i = new Intent(context, ActivityScanQRCode.class);

        Bundle bundle = new Bundle();
        bundle.putString(Constants.KEY_MVISA_AMOUNT, dataPartner.getAmount());
        bundle.putString(Constants.KEY_MVISA_NOTE, dataPartner.getDescription());
        bundle.putString(Constants.KEY_MVISA_EMAIL_CUSTOMER, dataPartner.getEmailReceipt());
        bundle.putString(Constants.KEY_MVISA_QRID, qrid);
        bundle.putString(Constants.KEY_MVISA_QRCODE, dataPartner.getQrCode());
        bundle.putString(Constants.KEY_MVISA_UDID, udid);

        if (!TextUtils.isEmpty(dataPartner.getQrType())) {
            bundle.putString(Constants.KEY_MVISA_TYPE, dataPartner.getQrType());
        }

        i.putExtra(Constants.KEY_EXTRA_MVISA, bundle);
        Utils.LOGD(TAG, "qrid = " + qrid + " amount = " + dataPartner.getAmount() + " email = " + dataPartner.getEmailReceipt() + " descMVisa = " + dataPartner.getDescription());
        logUtil.appendLogAction("qrid = " + qrid + " amount = " + dataPartner.getAmount() + " email = " + dataPartner.getEmailReceipt() + " descMVisa = " + dataPartner.getDescription());
        logUtil.saveLog();

        startActivity(i);
    }


    private void showErrorLoadMposNotUseDevice(int errorCode, String msg) {
        final MposDialog MposDialog = MyUtils.initDialogGeneralError(
                context,
                errorCode,
                msg,
                FragmentHomeNew.class.getName());

        MposDialog.setOnClickListenerDialogClose(v -> MposDialog.dismiss());
        if (ScreenUtils.canShowDialog(getActivity())) {
            MposDialog.show();
        }
    }


    private void showLandingInstallment() {
        addFragment(FragmentWebview.newInstance(Config.URL_LANDING_INSTALLMENT, FragmentWebview.TYPE_LANDING_INSTALLMENT), "FragmentWebview");
    }

    private void getPopUpInfo() {
        StringEntity entity = null;
        try {
//            'muid': 'longld', 'category': 'home'
            JSONObject jo = new JSONObject();
            jo.put("serviceName", "POPUP_PAYMENT");
            jo.put("category", "home");
//            jo.put("muid", dataStore.getUserLogin());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            Utils.LOGD("getPopUpInfo Data: ", jo.toString());
            entity = new StringEntity(jo.toString(), "UTF-8");
        } catch (Exception e) {
//            System.err.println(e.getMessage());
            e.printStackTrace();
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE,
                new MyAsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
//                        progressDialog.showLoading("");
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(TAG, "onFailure: getPopUpInfo() -->");
                    }


                    @Override
                    public void onSuccessString(int i, Header[] headers, String result) {
                        Utils.LOGD(TAG, "onSuccessOk: getPopUpInfo() -->" + result);

                        BasePopup popup = MyGson.parseJson(result, BasePopup.class);
                        Utils.LOGD(TAG, "onSuccessOk: popup.code=" + popup.getErrObj().code);
                        if (Config.CODE_REQUEST_SUCCESS.equals(popup.getErrObj().code)) {
                            showPopupPromotion(popup);
                        }
                    }
                });
    }

    private void showPopupPromotion(BasePopup popup) {
        final MyDialogWeb myDialogWeb = new MyDialogWeb(context, R.style.DialogRadius);
        myDialogWeb.setCancelable(true);

        String url = Locale.getDefault().getLanguage().equals("vi") ? popup.getUrlContentVi() : popup.getUrlContentEn();

        Utils.LOGD(TAG, "showPopupPromotion: url=" + url);
        boolean resultLoad = myDialogWeb.loadUrl(url);
        if (resultLoad && ScreenUtils.canShowDialog(context)) {
            myDialogWeb.show();
        }
    }

    @Override
    public void onDismissAffiliateDialog() {

    }

    @Override
    public void onClickCoverAffiliateDialog(String eventLink) {
        if (!TextUtils.isEmpty(eventLink) && getActivity() != null) {
            Intent intent = new Intent(getActivity(), ActivityNewsDetail.class);
            intent.putExtra(Constants.LINK_WEBVIEW, eventLink);
            intent.putExtra(Constants.TITLE_WEBVIEW, getString(R.string.earn_money));
            startActivity(intent);
        }
    }

    static class TopBannerHome {
        String type;
        String url;

        public TopBannerHome(String type, String url) {
            this.type = type;
            this.url = url;
        }
    }

    private class GetImageSizeTask extends AsyncTask<String, Integer, Bitmap> {

        @Override
        protected Bitmap doInBackground(String... strings) {
            Bitmap bitmap = null;
            try {
                URL url = new URL(strings[0]);
                bitmap = BitmapFactory.decodeStream(url.openConnection().getInputStream());
            } catch (IOException e) {
                e.printStackTrace();
            }
            return bitmap;
        }

        @Override
        protected void onPostExecute(Bitmap bitmap) {
            super.onPostExecute(bitmap);
            if (bitmap != null) {
                try {
                    DisplayMetrics displayMetrics = new DisplayMetrics();
                    getActivity().getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
                    int height = displayMetrics.heightPixels;
                    int width = displayMetrics.widthPixels;
                    llBanner.getLayoutParams().height = Math.round(((float) (width - dpToPx(24)) / bitmap.getWidth()) * bitmap.getHeight());
                    llBanner.requestLayout();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public interface ItfActionHomeScreen{
        int TYPE_CONFIG = 1;
        int TYPE_FW = 2;
        void onSelectUpdateConfigReader(int type);

//        void onSelectCashier();
    }
}
