package com.mpos.screen.mart;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.mpos.common.MyApplication;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.BaseDevice;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;
import com.pax.baselink.api.BaseLinkApi;
import com.pax.baselink.listener.IBluetoothDevice;
import com.pax.baselink.listener.IBluetoothSearchListener;
import com.pps.core.MyProgressDialog;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import vn.mpos.R;
import vn.mpos.databinding.ActivityBtConnectTcpBinding;

public class ActivityBtConnectTcp extends AppCompatActivity {

    private static final String TAG = ActivityBtConnectTcp.class.getSimpleName();

    ActivityBtConnectTcpBinding binding;

    private BaseLinkApi baseLinkApi;

    BluetoothAdapter bluetoothAdapter;
    public static IBluetoothDevice device;
    private MyProgressDialog mPgdl;
    private SaveLogController logUtil;

    private ExecutorService backgroundExecutor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityBtConnectTcpBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        this.setFinishOnTouchOutside(false);

        Utils.LOGD(TAG, "onCreate");
//        backgroundExecutor = ThreadPoolManager.getInstance().getExecutor();
        backgroundExecutor = Executors.newSingleThreadExecutor();

        mPgdl = new MyProgressDialog(ActivityBtConnectTcp.this);
        logUtil = MyApplication.self().getSaveLogController();

        baseLinkApi = BaseLinkApi.getInstance(ActivityBtConnectTcp.this);

        checkPermission();
        initView();
    }

    public void showProgressBar(boolean show) {
        if (show) {
            mPgdl.showLoading();
        }
        else {
            mPgdl.hideLoading();
        }
    }

    private void initView() {
        binding.btnScanPrinter.setOnClickListener(v -> {
            doScanBt();
        });
        bluetoothAdapter = new BluetoothAdapter(this);

        binding.pairedDevices.setAdapter(bluetoothAdapter);
        binding.pairedDevices.setOnItemClickListener(mDeviceClickListener);
    }

    private void doScanBt() {
        showProgressBar(true);

        baseLinkApi.stopBluetoothSearch();
        SystemClock.sleep(500);
        baseLinkApi.startBluetoothSearch(new IBluetoothSearchListener() {
            @Override
            public void onFinished() {
                Utils.LOGI(TAG, "bluetooth search finish");
                logUtil.appendLogAction("bluetooth search finish");
                showProgressBar(false);
            }

            @Override
            public void onDiscovered(IBluetoothDevice dev) {
                Utils.LOGD(TAG, "onDiscovered: " + dev.getName() + "\n" + dev.getIdentifier() + "\n" + dev.isBle());
                if (dev.getName().startsWith("L920PRO")) {
                    binding.tvNotDevicesBt.setVisibility(View.GONE);
                    bluetoothAdapter.add(dev);

                    showProgressBar(false);
                }
            }
        }, 10);
    }

    private void checkPermission() {
        int currentapiVersion = android.os.Build.VERSION.SDK_INT;
        Utils.LOGD(TAG, "currentapiVersion=" + currentapiVersion);
        if (currentapiVersion >= android.os.Build.VERSION_CODES.M){//API LEVEL 18
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_DENIED) {
                Utils.LOGD(TAG, "checkPermission - requestPermissions");
                ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.ACCESS_COARSE_LOCATION}, 1001);
            } else{
                Utils.LOGD(TAG, "checkPermission - no need requestPermissions");
            }
        } else {
            Utils.LOGD(TAG, "checkPermission - SDK Vesion < 23");
        }
    }

    private AdapterView.OnItemClickListener mDeviceClickListener = (parent, view, position, id) -> {
        // todo click MAC BT
        baseLinkApi.stopBluetoothSearch();
        logUtil.appendLogAction("Connect Base= " + bluetoothAdapter.getItem(position).getName());
        Utils.LOGD(TAG, "bluetoothAdapter: " + bluetoothAdapter.getItem(position).getName());
        device = bluetoothAdapter.getItem(position);

        runInBackground(() -> {
            runOnUiThread(() -> {
                showProgressBar(true);
            });
            SystemClock.sleep(500);
            boolean isConnect = BaseDevice.getInstance(ActivityBtConnectTcp.this).isBaseConnect(device);
            if(isConnect){
                //todo save cache device
                String saveDevice = MyGson.getGson().toJson(device);
                Utils.LOGD(TAG, "saveDevice=: " + saveDevice);
                PrefLibTV.getInstance(ActivityBtConnectTcp.this).createBluetoothAddressPAX(saveDevice);

                boolean res = BaseDevice.getInstance(ActivityBtConnectTcp.this).resetWifiStatus();
                Utils.LOGD(TAG, "res resetWifi= " + res);
                logUtil.appendLogAction("resetWifiStatus= " + res);
                boolean isConnect2nd = BaseDevice.getInstance(ActivityBtConnectTcp.this).isBaseConnect(device);
                Utils.LOGD(TAG, "res isConnect2nd= " + isConnect2nd);
                logUtil.appendLogAction("isConnect2nd= " + isConnect2nd);
                if (res && isConnect2nd) {
//                    if (type.equals(Constants.EMART_SETTING)) {
//                        setConfigWifi(false);
//                    } else {
//                        setConfigWifi(true);
//                    }
                    setConfigWifi(true);
                } else {
                    connectFail();
                }
            }else{
                connectFail();
            }
        });
    };

    private void connectFail() {
        updateResult("connect fail!");
        runOnUiThread(() -> {
            showProgressBar(false);
        });
    }

    private void setConfigWifi(boolean isShowSSID) {
        boolean restult = BaseDevice.getInstance(ActivityBtConnectTcp.this).setWifiStart(isShowSSID);
        Utils.LOGD(TAG, "setWifiStart= " + restult);
        logUtil.appendLogAction("setWifiStart= " + restult);
        if (restult) {
            updateResult("config wifi success!");
        } else {
            updateResult("config wifi fail!");
        }
        runOnUiThread(() -> {
            showProgressBar(false);
        });
        Intent intent = new Intent();
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    private class BluetoothAdapter extends ArrayAdapter<IBluetoothDevice> {
        public BluetoothAdapter(Context context){
            super(context, R.layout.item_list_bt_device);
        }

        @Override
        public View getView(final int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View rowView = inflater.inflate(R.layout.item_list_bt_device, parent, false);
            TextView tvName             = rowView.findViewById(R.id.tv_content);

            IBluetoothDevice bluetoothObj = bluetoothAdapter.getItem(position);
            tvName.setText(bluetoothObj.getName());

            return rowView;
        }
    }

    private void updateResult(String s) {
        Utils.LOGD(TAG, "updateResult: " + s);
        logUtil.appendLogAction("is connect Base= " + s);
        runOnUiThread(() -> {
            Toast.makeText(this, s, Toast.LENGTH_SHORT).show();
        });
    }

    public void runInBackground(final Runnable runnable) {
        backgroundExecutor.execute(runnable);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        Utils.LOGD(TAG, "onDestroy");

        if (baseLinkApi != null) {
            baseLinkApi.stopBluetoothSearch();
        }
        if (backgroundExecutor != null) {
            Utils.LOGD(TAG, "backgroundExecutor shutdown");
            backgroundExecutor.shutdownNow();
        }
    }
}

