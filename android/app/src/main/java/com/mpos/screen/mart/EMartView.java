package com.mpos.screen.mart;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.mpos.common.DataStoreApp;
import com.mpos.customview.DialogGiftCardInfo;
import com.mpos.screen.FlutterTransferActivity;
import com.mpos.sdk.core.model.GiftCardInfor;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.MyProgressDialog;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import vn.mpos.BuildConfig;
import vn.mpos.R;

import static com.mpos.screen.FlutterTransferActivity.ROUTE_CASHIER;
import static com.mpos.screen.FlutterTransferActivity.ROUTE_DEPOSIT;
import static com.mpos.screen.FlutterTransferActivity.ROUTE_SUMMARY;
import static com.mpos.sdk.core.control.LibPax.END_REQUEST_GIFT_CARD;
import static com.mpos.sdk.core.control.LibPax.START_REQUEST_GIFT_CARD;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;

public class EMartView implements EMartContract.View {

    private static final String TAG = EMartView.class.getSimpleName();

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_logout)
    AppCompatImageView btn_logout;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.tv_status_trans_emart)
    TextView tv_status_trans;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.tv_detail_Ip_Tcp)
    TextView tv_detail_Ip_Tcp;

//    @SuppressLint("NonConstantResourceId")
//    @BindView(R.id.tv_Ip_GateWay)
//    TextView tvIpGateWay;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.tv_status_server_accept)
    TextView tvStatusServerAccept;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.img_client_connect)
    ImageView imgClientConnect;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.img_client_connect_err)
    ImageView imgClientConnectErr;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.tv_infor_device)
    TextView tvInforDevice;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.tv_wait_swipe_giftcard)
    TextView tvWaitSwipeGiftCard;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.pgb_giftcard)
    ProgressBar prgGiftCard;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_go_history)
    RelativeLayout btnGoHistory;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_retry_card)
    Button btn_retry_card;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_giftcard_back)
    Button btnBackGiftCardInfo;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_setting)
    AppCompatImageView btnSetting;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.ll_input_amount)
    RelativeLayout btnInputAmount;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_get_info_card)
    RelativeLayout btnFetchInforGiftCard;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_go_deposit)
    RelativeLayout btnGoDeposit;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_goto_pushpayment)
    RelativeLayout btnGotoPushPayment;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.ll_bar_home_mart)
    RelativeLayout llHome;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.v_process_giftcard)
    View viewProcessGiftCard;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.ll_infor_gift_card)
    View viewInforCard;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.contianer_home_bar)
    LinearLayout contianerHomeBar;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.container_home_infor_ip)
    LinearLayout contianerHomeInforIP;

    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.main_layout_statistic_shift)
    RelativeLayout containerStaticShift;

    Context context;

    EMartContract.Presenter presenter;
    MyProgressDialog mPgdl;

    public EMartView(Activity activity) {
        this.context = activity;
        ButterKnife.bind(this, activity);
        mPgdl = new MyProgressDialog(context);
        initView();
    }

    public void start() {
        presenter.start();
    }

    private void initView() {
        btn_logout.setOnClickListener((v) -> {
            presenter.logOut();
        });

        btnGoHistory.setOnClickListener((v) -> presenter.gotoHistory());
        btnGoDeposit.setOnClickListener((v) -> presenter.gotoDeposit());

        btnInputAmount.setOnClickListener((v) -> presenter.gotoPaymentEnterAmount());

        tvInforDevice.setText(PrefLibTV.getInstance(context).getUserId() + " - " +PrefLibTV.getInstance(context).getSerialNumber() + " - " + BuildConfig.VERSION_CODE);

        btnBackGiftCardInfo.setOnClickListener((view) -> {
            if (viewProcessGiftCard.getVisibility() == View.VISIBLE) {
                viewProcessGiftCard.setVisibility(View.GONE);
            }
            presenter.closeSearchGiftCard();
        });

        btnSetting.setOnClickListener(view -> {
            presenter.handlerActionSetting();
        });

        if (PrefLibTV.getInstance(context).getPermitPayGiftCard().equals(Constants.SVALUE_1) && MyUtils.isTypeAppEmart()) {
            btnFetchInforGiftCard.setVisibility(View.VISIBLE);
        }

        if (PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class).equals(Constants.SVALUE_2) && DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class) && !MyUtils.isTypeAppEmart() && !MyUtils.isTypeAppTakashimaya()) {
            btnGotoPushPayment.setVisibility(View.VISIBLE);
        }

        if (PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class).equals(Constants.SVALUE_1) && !MyUtils.isTypeAppEmart() && !MyUtils.isTypeAppTakashimaya()) {
            btnGoDeposit.setVisibility(View.VISIBLE);
        }

        if (!MyUtils.isTypeAppEmart()) {
            containerStaticShift.setVisibility(View.VISIBLE);
        }

        containerStaticShift.setOnClickListener((v) -> {
            gotoStaticShift();
        });
    }

    private void gotoStaticShift() {
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route",ROUTE_SUMMARY);
        context.startActivity(intent);
    }

    @OnClick({R.id.btn_load_ip_socket})
    protected void onClickLoadIpSocket() {
        String ipServer = presenter.loadIpSocketServer();
        if (ipServer != null) {
            MyDialogShow.showDialogInfo(context, context.getString(R.string.tv_show_ip_socket, ipServer), false, (v) -> {
            });
        } else {
            MyDialogShow.showDialogInfo(context, context.getString(R.string.tv_nothing_ip_socket), false, (v) -> {
            });
        }
    }

    @OnClick({R.id.btn_goto_pushpayment})
    public void onClickGotoPushPayment() {
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route", ROUTE_CASHIER);
        context.startActivity(intent);
    }

//    @OnClick({R.id.btn_reset_socket})
//    protected void onClickResetSocket() {
//        MyDialogShow.showDialogContinueCancel(context.getString(R.string.tv_confirm_reset_socket), context, true, view -> {
////                presenter.printerVoucherEmart();
//            presenter.resetSocket();
//        });
//    }

    @OnClick({R.id.btn_get_info_card})
    protected void onClickGetGiftCardInfo() {
        viewProcessGiftCard.setVisibility(View.VISIBLE);
        if (viewInforCard.getVisibility() == View.VISIBLE) {
            viewInforCard.setVisibility(View.INVISIBLE);
        }

        tvWaitSwipeGiftCard.setVisibility(View.VISIBLE);
        prgGiftCard.setVisibility(View.VISIBLE);

        btn_retry_card.setVisibility(View.GONE);
        presenter.startProcessReadGiftCard();
    }

    @OnClick({R.id.btn_retry_card})
    protected void onClickRetryGetGiftCardInfo() {
        btn_retry_card.setVisibility(View.GONE);

        tvWaitSwipeGiftCard.setVisibility(View.VISIBLE);
        prgGiftCard.setVisibility(View.VISIBLE);

        presenter.startProcessReadGiftCard();
    }

    @Override
    public void setPresenter(EMartContract.Presenter presenter) {
        this.presenter = presenter;
    }

    @Override
    public void showLoading(String msg) {
        mPgdl.showLoading(msg);
    }

    @Override
    public void hideLoading() {
        mPgdl.hideLoading();
    }

    @Override
    public void updateIpAddress(String ipLan) {
        if (!TextUtils.isEmpty(ipLan)) {
            tv_detail_Ip_Tcp.setText(context.getString(R.string.tv_show_ip_socket, ipLan));
        }
    }

    @Override
    public void updateStateSocket(EMartPresenter.SocketState state, String ipLan) {
        switch (state) {
            case ONLINE:
                Utils.LOGE(TAG, "serverOnline update view online");
//                tvIpGateWay.setVisibility(View.VISIBLE);
                tv_status_trans.setText(context.getString(R.string.tv_wait_order));
                if (!TextUtils.isEmpty(ipLan)) {
                    tv_detail_Ip_Tcp.setText(context.getString(R.string.tv_show_ip_socket, ipLan));
                }
                break;
            case OFFLINE:
                Utils.LOGE(TAG, "serverOnline update view offline");
//                tvIpGateWay.setVisibility(View.GONE);
                tv_status_trans.setText(context.getString(R.string.noti_socket_disconnect));
                tv_detail_Ip_Tcp.setText(context.getString(R.string.tv_nothing_ip_socket));
                break;
            default:
                break;
        }
    }

    @Override
    public void updateStatus(EMartPresenter.TransStatus status) {
        switch (status) {
            case WAIT_TRANS:
                tv_status_trans.setText(context.getString(R.string.tv_wait_order));
                break;
            case START_TRANS:
                tv_status_trans.setText(context.getString(R.string.noti_start_trans));
                break;
            case VOID_TRANS:
                tv_status_trans.setText(context.getString(R.string.noti_void_trans));
                break;
            default:
                break;

        }
    }

//    @Override
//    public void updateIPGateWay(String ipGateWay) {
//        if (!TextUtils.isEmpty(ipGateWay)) {
//            tvIpGateWay.setVisibility(View.VISIBLE);
//            tvIpGateWay.setText(context.getString(R.string.tv_ip_gateway, ipGateWay));
//        }
//    }

    @Override
    public void updateClientConnect(boolean isConnect, String ipAdressClient) {
        if (!isConnect) {
            tv_status_trans.setText(context.getString(R.string.tv_wait_order));
        }

//        if (isConnect) {
//            imgClientConnect.setVisibility(View.VISIBLE);
//            imgClientConnectErr.setVisibility(View.INVISIBLE);
//            tvStatusServerAccept.setText(context.getString(R.string.tv_client_connect, ipAdressClient));
//        } else {
//            imgClientConnect.setVisibility(View.INVISIBLE);
//            imgClientConnectErr.setVisibility(View.VISIBLE);
//            tvStatusServerAccept.setText(context.getString(R.string.tv_wait_client));
//        }
    }

    @Override
    public void hideViewGiftCard() {
        Utils.LOGD(TAG, "hideViewGiftCard");
        if (viewProcessGiftCard.getVisibility() == View.VISIBLE) {
            viewProcessGiftCard.setVisibility(View.GONE);
        }
    }

    @Override
    public void errGetInforCard() {
        Utils.LOGD(TAG, "errGetInforCard");
        btn_retry_card.setVisibility(View.VISIBLE);

        // process,text hide
        tvWaitSwipeGiftCard.setVisibility(View.GONE);
        prgGiftCard.setVisibility(View.GONE);
    }

    @Override
    public void onStateCardInfo(int state) {
        Utils.LOGD(TAG, "onStateCardInfo");
        switch (state) {
            case START_REQUEST_GIFT_CARD:
                tvWaitSwipeGiftCard.setText(context.getString(R.string.msg_check_giftcard_info));
                break;
            case END_REQUEST_GIFT_CARD:
                tvWaitSwipeGiftCard.setText(context.getString(R.string.msg_wait_giftcard_info));
                break;
            default:
                break;
        }
    }

    @Override
    public void showFullUI() {
        contianerHomeBar.setVisibility(View.VISIBLE);
//        tvIpGateWay.setVisibility(View.VISIBLE);
        tv_detail_Ip_Tcp.setVisibility(View.VISIBLE);
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) llHome.getLayoutParams();
        params.height = context.getResources().getDimensionPixelSize(R.dimen.height_home_bar);
        llHome.requestLayout();
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class) && PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class).equals(Constants.SVALUE_2) && !MyUtils.isTypeAppEmart() && !MyUtils.isTypeAppTakashimaya()) {
            btnGotoPushPayment.setVisibility(View.VISIBLE);
        }
        if (PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class).equals(Constants.SVALUE_1) && !MyUtils.isTypeAppEmart() && !MyUtils.isTypeAppTakashimaya()) {
            btnGoDeposit.setVisibility(View.VISIBLE);
        }
//        contianerHomeInforIP.setVisibility(View.VISIBLE);
    }

    @Override
    public void onlyShowUIWaitOrder() {
        contianerHomeBar.setVisibility(View.GONE);
//        tvIpGateWay.setVisibility(View.GONE);
        tv_detail_Ip_Tcp.setVisibility(View.GONE);
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) llHome.getLayoutParams();
        params.height = context.getResources().getDimensionPixelSize(R.dimen.height_home_bar_hide);
        llHome.requestLayout();
        btnGotoPushPayment.setVisibility(View.GONE);
        btnGoDeposit.setVisibility(View.GONE);
//        contianerHomeInforIP.setVisibility(View.GONE);
    }

    public void updateViewInfoCard(GiftCardInfor data) {
        DialogGiftCardInfo dialogGiftCardInfo = new DialogGiftCardInfo(context);
        dialogGiftCardInfo.initVariable(data);
        dialogGiftCardInfo.setContentView(viewProcessGiftCard);
        dialogGiftCardInfo.setClickListener(() -> {
            if (viewInforCard.getVisibility() == View.VISIBLE) {
                viewInforCard.setVisibility(View.GONE);
            }
            btn_retry_card.setVisibility(View.GONE);
            presenter.startProcessReadGiftCard();
        });
        viewInforCard.setVisibility(View.VISIBLE);
    }
}
