package com.mpos.screen;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.gson.JsonSyntaxException;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.common.NotifyPushController;
import com.mpos.customview.DialogResult;
import com.mpos.customview.MposDialog;
import com.mpos.models.BankInstallmentObj;
import com.mpos.models.BankPeriodObject;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataFromPartner;
import com.mpos.models.DataIntegrated;
import com.mpos.models.Promotion;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.screen.printer.BaseActivityPrint;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.BankInstallment;
import com.mpos.sdk.core.model.DataConfirmCard;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.model.UserCard;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.InstallmentUtils;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.ConfigIntegrated;
import com.mpos.utils.Constants;
import com.mpos.utils.FireBaseUtils;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pax.emvdemo.ITYPaxApi;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

import static com.mpos.utils.Constants.RESULT_FAILURE_PAY;

public class ActivityPrePayment extends BaseActivityPrint implements ItfAppendLog, LibReaderController.ItfResultPay {

    private static final String TAG = "ActivityPrePayment";

    public static final int RC_PAY = 11;

//    private final byte EMAIL_PASSENGER = 0;
    private final byte EMAIL_MERCHANT = 1;

    protected Context context;

    private Intent intentDataPayment;
    private Intent intentBroadcastEmartAction;
    private DataPay dataPay;
    private DataConfirmCard dataCard;
    private BankInstallmentObj bankInstallment;
    private DataIntegrated dataIntegrated;
    protected DataFromPartner dataFromPartner;


    private ItfShowScreenLoading itfShowScreenLoading;

    boolean isContinueSignature = false;
    boolean isSaveInstanceState = false;
    boolean isResumeCalled = false;
    boolean isWaitShowSuccess = false;
    boolean isEmart = false;

    private String userId;
    private String serialNumber;

    private String desc;
    private String email;
    private String trxType;
    private String typePostPay = "";

    protected SaveLogController logUtil;
    protected ToastUtil mToast;

    DialogResult dialogResult;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        context = ActivityPrePayment.this;

        dataIntegrated = MyApplication.self().getDataIntegrated();

        logUtil = MyApplication.self().getSaveLogController();

        userId = PrefLibTV.getInstance(context).getUserId();
        serialNumber = PrefLibTV.getInstance(context).getSerialNumber();
        isEmart = PrefLibTV.getInstance(this).getPermitSocket();

        if (!checkActionResignTrans(getIntent())) {
            initMposSdk();
        }

        if (isEmart) {
            initPrinter();
        }
    }

    public void setItfShowScreenLoading(ItfShowScreenLoading itfShowScreenLoading) {
        this.itfShowScreenLoading = itfShowScreenLoading;
    }

    private boolean checkActionResignTrans(Intent intent) {
        DataPay dataPayRecei = (DataPay) intent.getSerializableExtra(Intents.EXTRA_DATA_PAY_MP);

        if (dataPayRecei != null) {
            appendLog(ItfAppendLog.TypeLog.action, "--- continue signature --- " + dataPayRecei.getAmount());
            isContinueSignature = true;
            dataPayRecei.setResign(true);
            initLibReaderForSignature(dataPayRecei, intent);
            if (isEmart) {
                sendActionToEmartByBroadcast(com.mpos.sdk.util.Constants.AP_ACTION_START);
            }
            return true;
        }
        return false;
    }

    private void initLibReaderForSignature(DataPay dataPay, Intent intent) {
        LibReaderController libReaderController = new LibDspreadReader(this);
        initMoreInfoTrans(libReaderController, intent);
        libReaderController.setCallback(this);
        libReaderController.setCallBackSaveLog(this);
//        libReaderController.setShowErrorCodeInMsg(false);
        libReaderController.startSignature(dataPay);
    }

    private void initMoreInfoTrans(LibReaderController readerController, Intent intent) {
        readerController.setUseNewSignature(true);
        readerController.setShowErrorCodeInMsg(false);

        BankPeriodObject bankPeriodObject = (BankPeriodObject) intent.getSerializableExtra(IntentsMP.EXTRA_INSTALLMENT_PERIOD_OBJ);
        if (bankInstallment != null) {
            readerController.setMoreInfoOfTrans(getResources().getString(R.string.bank_installment_month, bankInstallment.bankName, bankPeriodObject.period));
        } else {
            DataPay dataPay = (DataPay) intent.getSerializableExtra(Intents.EXTRA_DATA_PAY_MP);
            String udidReSignature = dataPay != null ? dataPay.getUdid() : "";
            Utils.LOGD(TAG, "initMoreInfoTrans: udidReSignature=" + udidReSignature);
            if (!TextUtils.isEmpty(udidReSignature)) {
                try {
                    udidReSignature = udidReSignature.substring(ConstantsPay.PREFIX_UDID_INSTALLMENT.length()).replace("_", " ");
                    Utils.LOGD(TAG, "initMoreInfoTrans: 11--udidReSignature=" + udidReSignature);

                    int pos = udidReSignature.indexOf("/");
                    if (pos > 0) {
                        String descEn = udidReSignature.substring(0, pos);
                        String descVi = udidReSignature.substring(pos + 1, udidReSignature.indexOf("-"));
                        Utils.LOGD(TAG, "initMoreInfoTrans: 22--descEn=" + descEn + " descVi=" + descVi + " currLanguage=" + Locale.getDefault().getLanguage());
                        readerController.setMoreInfoOfTrans(Locale.getDefault().getLanguage().equalsIgnoreCase("VI") ? descVi : descEn);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    readerController.setMoreInfoOfTrans("");
                }
            }
        }
    }

    private void initMposSdk() {
        if (dataFromPartner != null && dataIntegrated != null
                && !TextUtils.isEmpty(dataFromPartner.getStageHandler())
        ) {
            MyApplication.self().getMposSdk().setHandleState(dataFromPartner.getStageHandler());
        }
        else {
            MyApplication.self().getMposSdk().setHandleState("");
        }
    }

    protected void callSdkPayment(Intent intent) {
        this.intentDataPayment = intent;

        String amountPay = intent.getStringExtra(Intents.EXTRA_V_AMOUNT);
        String udid    = intent.getStringExtra(Intents.EXTRA_V_UDID);
        trxType = intent.getStringExtra(Intents.EXTRA_V_TRX_TYPE);
        if (TextUtils.isEmpty(trxType)) {
            trxType = ConstantsPay.TRX_TYPE_NORMAL;
        }
        String amountDomestic = intent.getStringExtra(Intents.EXTRA_V_AMOUNT_DOMESTIC);
        String amountInternational = intent.getStringExtra(Intents.EXTRA_V_AMOUNT_INTERNATIONAL);

        desc = intent.getStringExtra(Intents.EXTRA_V_DESC);
        email= intent.getStringExtra(Intents.EXTRA_V_EMAIL);

        long amount = Long.parseLong(amountPay);
        long lAmountDomestic = 0;
        try {
            lAmountDomestic = Long.parseLong(amountDomestic);
        } catch (NumberFormatException ignored) {
        }
        long lAmountInternational = 0;
        try {
            lAmountInternational = Long.parseLong(amountInternational);
        } catch (NumberFormatException ignored) {
        }
        Utils.LOGD(TAG, "startCallSdkPayment: timePay 1--" + System.currentTimeMillis());
        Bundle extraBundle = null;
        BankInstallmentObj bankInstallment = (BankInstallmentObj) intent.getSerializableExtra(IntentsMP.EXTRA_INSTALLMENT_BANK_OBJ);
        if (bankInstallment != null) {
            extraBundle = new Bundle();
            extraBundle.putSerializable(Intents.EXTRA_DATA_INSTALLMENT, convertBankInstall(bankInstallment));
        }
        try {
            logUtil.saveLog();
            MyApplication.self().getMposSdk().chargeAmount(context, "", amount, lAmountDomestic, lAmountInternational, desc, email, udid, extraBundle, RC_PAY);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Utils.LOGD(TAG, "startCallSdkPayment: timePay 2--"+System.currentTimeMillis());

    }

    private BankInstallment convertBankInstall(BankInstallmentObj bankInstallment) {

        BankInstallment bankInstallmentSdk = null;
        try {
            String jsonData = MyGson.getGson().toJson(bankInstallment);
            Utils.LOGD(TAG, "convertBankInstall: " + jsonData);
            bankInstallmentSdk = MyGson.getGson().fromJson(jsonData, BankInstallment.class);
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        return bankInstallmentSdk;
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");
        // requestCode == 1: case webview React Native
        if (requestCode != 1) {
            boolean handlerResult = false;
            if (requestCode == RC_PAY) {
                if (resultCode == RESULT_OK) {
                    if (data != null) {
                        String contentResult = data.getStringExtra(Intents.EXTRA_DATA_CALLBACK);
                        ResultPayWrapper resultWrapper = MyGson.parseJson(contentResult, ResultPayWrapper.class);
                        handlerResultPay(resultWrapper);
                        handlerResult = true;
                    }
                }
            }
            if (!handlerResult) {
                finishByType(false);
            }
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        Utils.LOGD(TAG, "onSaveInstanceState: --->");
        appendLog("onSaveInstanceState: --->");
        isSaveInstanceState = true;
    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.LOGD(TAG, "onResume: isNotFirstResume=" + isResumeCalled + " isWaitShowSuccess=" + isWaitShowSuccess + " isSaveInstanceState=" + isSaveInstanceState);
        appendLog( "onResume: isNotFirstResume=" + isResumeCalled + " isWaitShowSuccess=" + isWaitShowSuccess + " isSaveInstanceState=" + isSaveInstanceState);

        isSaveInstanceState = false;
        if (!isResumeCalled) {
            isResumeCalled = true;
        }
        else {
            if (isWaitShowSuccess) {
                showDialogSuccess();
            }
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (isEmart && (dialogResult != null)) {
            Utils.LOGD(TAG, "onStop ---> dismissDialogFormMart");
            dialogResult.dismissDialogFormMart();
        }
    }

    private void handlerResultPay(ResultPayWrapper resultWrapper) {
        if (itfShowScreenLoading != null) {
            itfShowScreenLoading.onShowScreenLoadingAfterPayment();
        }
        boolean isSuccessPay = false;
        if (resultWrapper.getResult() != null) {
            DataPay dataPay = convertToDataPay(resultWrapper);
            if (MposIntegrationHelper.TRANS_STATUS_APPROVED.equals(resultWrapper.getResult().status)) {
                onSuccessPay(dataPay);
                isSuccessPay = true;

                // send G(
                FirebaseAnalytics mFirebaseAnalytics = FirebaseAnalytics.getInstance(context);
                FireBaseUtils.getInstance().logEvent(mFirebaseAnalytics, "Installment-Success-Button", "");
            } else {
                onFailPay(dataPay, resultWrapper.getResult().error, 0, 0, false);
            }
        }
        if (!isSuccessPay) {
            finishByType(false);
        }
    }

    private DataPay convertToDataPay(ResultPayWrapper resultPayWrapper) {
        UserCard userCard = resultPayWrapper.getUserCard();
        DataPay dataPay = new DataPay(String.valueOf(userCard.amountAuthorized), desc, resultPayWrapper.getResult().paymentIdentifier);
//        dataPay.setDesc(desc);
        dataPay.setEmail(email);
        dataPay.setAuthCode(userCard.authCode);
        dataPay.setLabel(userCard.applicationLabel);
        dataPay.setPan(userCard.pan);
        dataPay.setName(userCard.cardHolderName);
        dataPay.setTrxType(trxType);
        dataPay.setTrId(resultPayWrapper.getResult().trId);
        dataPay.setTxId(resultPayWrapper.getResult().transId);
        dataPay.setTransactionDate(userCard.transactionDate);
//        dataPay.setUdid(resultPayWrapper.getResult().paymentIdentifier);
        dataPay.setSignatureBase64(resultPayWrapper.getUserCard().userSignature);

        dataPay.setWfDetailRes(resultPayWrapper.getWfInfo());
        dataPay.setTransactionDetail(resultPayWrapper.getTransactionDetail());

        if (MposIntegrationHelper.TRANS_METHOD_RESIGN.equals(resultPayWrapper.getResult().method)) {
            dataPay.setResign(true);
        }

        return dataPay;
    }

    private void initMoreDataPay(Intent intent) {
        if (intent == null) {
            return;
        }
//        dataFromPartner = (DataFromPartner) intent.getSerializableExtra(Intents.EXTRA_DATA_PARTNER);
        dataCard = (DataConfirmCard) intent.getSerializableExtra(Intents.EXTRA_DATA_CARD);
        bankInstallment = (BankInstallmentObj) intent.getSerializableExtra(IntentsMP.EXTRA_INSTALLMENT_BANK_OBJ);
    }

    @Override
    public void onSuccessPay(DataPay dataPay) {
        Utils.LOGD(TAG, "onSuccess sale: ------->>");
        this.dataPay = dataPay;
        initMoreDataPay(intentDataPayment);
        checkContinuePayService(dataPay);
    }

    @Override
    public void onFailPay(DataPay dataPay, DataError dataError, int typeFail, int typeVoidFail, boolean requestLogin) {
        Utils.LOGD(TAG, "onFailPay: isContinueSignature="+isContinueSignature);
        if (dataFromPartner != null && !ConfigIntegrated.TYPE_APP2_SER2_SER2.equals(typePostPay) && "1".equals(dataFromPartner.isPushbackError())) {
            callbackFailToPartner(dataError);
        }
        if (isContinueSignature) {
            showDialogReason(dataError, requestLogin);
        }
    }

    @Override
    public void onFinishWithTypeError(int typeErrorFinish) {
        Utils.LOGD(TAG, "onFinishWithTypeError: " + typeErrorFinish);
        appendLog(" finish with error=" + typeErrorFinish);
        if (typeErrorFinish == LibReaderController.STATUS_FINISH_PR_PIN_LENGTH) {
            runOnUiThread(() -> MyDialogShow.showDialogError(null, getString(R.string.error_input_length_pin), this, false, v -> finishByType(false)));
        } else {
            finishByType(false);
        }
    }


    private void finishByType(boolean flag) {
        Utils.LOGD(TAG, "finishByType: " + flag);
        setResult(flag ? RESULT_OK : RESULT_CANCELED);
        finish();
    }

    // _____________________________ START SERVICE _____________________________

    private void checkContinuePayService(DataPay dataPay) {
        appendLog(" - checkContinuePayService:trxType=" + dataPay.getTrxType());

//        tid = dataPay.getTrId();
        txId = dataPay.getTxId();
        email = dataPay.getEmail();
        if (dataCard != null) {
            dataCard.amountPay = dataPay.getAmount();
            if (TextUtils.isEmpty(email)) {
                email = dataCard.email;
            }
        }

        Utils.LOGD(TAG, "checkContinuePayService:trxType:" + dataPay.getTrxType() + " udid=" + dataPay.getUdid()
                + " txId:" + txId + " isReSign=" + dataPay.isResign()); //+ " tid:" + tid

        pushNotify(NotifyPushController.NOTIFY_PAY_SUCCESS, dataPay.getPan(), dataPay.getName(), dataPay.getAmount());

        if (ConstantsPay.TRX_TYPE_SERVICE.equals(dataPay.getTrxType())) {
            payServiceByType(txId);
        } else {
            appendLogSuccessPay();

            String installmentId = null;
            boolean payCashBack = false;
            if (intentDataPayment != null) {
                installmentId = intentDataPayment.getStringExtra(IntentsMP.EXTRA_INSTALLMENT_OUT_ID);
                payCashBack = intentDataPayment.getBooleanExtra(IntentsMP.EXTRA_IS_PAY_CASHBACK, false);
            }
            boolean payInstallment = false;

            Utils.LOGD(TAG, "11: installmentId=" + installmentId);
            // case ReSignature: check is installment in udid
            if (TextUtils.isEmpty(installmentId)) {
                String udidReSignature = dataPay.getUdid();
                Utils.LOGD(TAG, "checkContinuePayService: udidReSignature=" + udidReSignature);

                if (!TextUtils.isEmpty(udidReSignature) && udidReSignature.startsWith(ConstantsPay.PREFIX_UDID_INSTALLMENT)) {
                    payInstallment = true;
                }
            } else {
                if (dataPay.isResign()) {
                    if (dataPay.getUdid().startsWith(ConstantsPay.PREFIX_UDID_INSTALLMENT)) {
                        payInstallment = true;
                    }
                }
                else {
                    payInstallment = true;
                }
            }
            Utils.LOGD(TAG, "payInstallment--->>" + payInstallment + " payCashBack=" + payCashBack);

            if (payInstallment || payCashBack) {
                if (payInstallment) {
                    if (checkCardInstallment(dataPay)) {
                        updateTransactionStatusInMpos();
                    }
                    else {
                        showDialogSuccess();
                    }
                } else {
                    updateTransactionStatusInMpos();
                }
                return;
            }

            // check pay for partner
            Utils.LOGD(TAG, "checkContinuePayService: orderId=" + (dataFromPartner == null ? "null" : dataFromPartner.getOrderId()));
            if ((dataFromPartner != null && !TextUtils.isEmpty(dataFromPartner.getOrderId()))
                    || dataIntegrated != null) {
                checkIsPayForMerchantIntegrated(dataIntegrated, dataFromPartner, dataPay);
            } else {
                checkEmailMerchantForSend(txId);
            }
        }
    }

    private boolean checkCardInstallment(DataPay dataPay) {
        boolean result = true;
        if (bankInstallment != null) {
//        if (bankInstallment != null && bankInstallment.hmBinBank != null) {
            String binPaid = dataPay.getPan();
            appendLog("checkCardInstallment:" + binPaid);
            if (!TextUtils.isEmpty(binPaid) && binPaid.length() > 6) {
                binPaid = binPaid.substring(0, 6);

                boolean isCardPaidInBankSelected = false;
                for (String key : bankInstallment.binList) {
                    if (key.startsWith(binPaid)) {
                        isCardPaidInBankSelected = true;
                        break;
                    }
                }

                if (!isCardPaidInBankSelected) {
                    InstallmentUtils installmentUtils = new InstallmentUtils();
                    String configFromMpos = PrefLibTV.getInstance(context).get(PrefLibTV.installmentInfo,String.class,"");
                    HashMap<String, BankInstallment> hmBank = installmentUtils.parseHashMapBinList(configFromMpos);

                    if (hmBank.containsKey(binPaid)) {
                        BankInstallment bankInstallmentCorrect = hmBank.get(binPaid);
                        appendLog("check match bankName: select:" + bankInstallment.bankName + " real:" + (bankInstallmentCorrect == null ? "null" : bankInstallmentCorrect.bankName));
                        result = false;
                        if (bankInstallmentCorrect != null && !bankInstallmentCorrect.bankName.equals(bankInstallment.bankName)) {
                            appendLog(String.format("---> thẻ (%s) ko thuộc ngân hàng đã chọn (%s)", bankInstallmentCorrect.bankName, bankInstallment.bankName));
                            bankInstallment.msgError = getString(R.string.error_installment_invalid_bank, dataPay.getPan(), bankInstallment.bankName, bankInstallmentCorrect.bankName);
                        } else {
                            appendLog(String.format("---> thẻ (%s) ko thuộc ngân hàng đã chọn (%s)", dataPay.getPan(), ""));
                            bankInstallment.msgError = getString(R.string.error_installment_invalid_bank, dataPay.getPan(), bankInstallment.bankName, "");
                        }
                    }
                    else {
                        appendLog("---> thẻ ko được ngân hàng support");
                        result = false;
                        bankInstallment.msgError = getString(R.string.mp_error_installment_not_support_card, dataPay.getPan());
                    }

                }

            }
        }

        return result;
    }

    private void payServiceByType(final String txid) {
        appendLogRequest("PAY_SERVICE_TRANSACTION");
        String qrid = null;
        if (intentDataPayment != null) {
            qrid = intentDataPayment.getStringExtra("VP_BANK_QRID");
        }
        String paymentMethod = null;
        if (intentDataPayment != null) {
            paymentMethod = intentDataPayment.getStringExtra("VP_BANK_PAYMENT_METHOD");
        }
        String description = null;
        if (intentDataPayment != null) {
            description = intentDataPayment.getStringExtra("VP_BANK_DESCRIPTION");
        }

        StringEntity entity = null;
        try {

            JSONObject jo = new JSONObject();

            jo.put(Constants.STR_SERVICE_NAME, Config.PAY_SERVICE_TRANSACTION);
            jo.put("udid", dataPay != null ? dataPay.getUdid() : "");
            jo.put("readerSerial", serialNumber);
            jo.put("txid", txid);
            jo.put("muid", userId);
            jo.put("description", description);
            if (!TextUtils.isEmpty(qrid)) {
                jo.put("qrid", qrid);
            }
            if (!TextUtils.isEmpty(paymentMethod)) {
                jo.put("paymentMethod", paymentMethod);
            }

            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "zen json: ", e);
        }
        MposRestClient.getInstance(this).setCustomTimeout(60000).post(this, Config.URL_GATEWAY, entity, Config.CONTENT_TYPE,
                new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading("");
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        mPgdl.hideLoading();
                        String udid = dataCard == null ? (dataPay == null ? "" : dataPay.getUdid()) : dataCard.udid;
                        appendLogRequest(Config.PAY_SERVICE_TRANSACTION + " onFailure");
                        checkTransStatusByType(1, udid, txid);
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        Utils.LOGD(TAG, "service content:" + (new String(arg2)));
                        try {
                            JsonParser jsonParser = new JsonParser();
                            BaseObjJson errorBean = new BaseObjJson();
                            JSONObject jRoot = new JSONObject(new String(arg2));
                            jsonParser.checkHaveError(jRoot, errorBean);
                            if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code) || "1013".equals(errorBean.code)) {
                                appendLogRequest(Config.PAY_SERVICE_TRANSACTION + " success " + errorBean.code);
                                appendLogSuccessPay();
                                checkEmailMerchantForSend(txid);
                            } else {
                                appendLogRequest(Config.PAY_SERVICE_TRANSACTION + " error server: code=" + errorBean.code);
                                showDialogFailService(false, errorBean.code);
                            }
                        } catch (Exception e) {
                            appendLogRequest(Config.PAY_SERVICE_TRANSACTION + " Exception: " + e.getMessage());
                            Utils.LOGE(TAG, "exception:", e);
                            showDialogFailService(false, null);
                        }
                    }
                });
    }

    private void checkTransStatusByType(final int numRetry, final String udid, final String txid) {
        StringEntity entity = null;
        appendLogRequest("CHECK_TRANSACTION_STATUS retry=" + numRetry + " udid=" + udid);
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.CHECK_TRANSACTION_STATUS);
            jo.put("udid", udid);
            jo.put("muid", userId);
            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "zen json: ", e);
        }
        MposRestClient.getInstance(this).setCustomTimeout(60000).post(this, Config.URL_GATEWAY, entity, Config.CONTENT_TYPE,
                new AsyncHttpResponseHandler() {
                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        mPgdl.hideLoading();
                        appendLogRequest(Config.CHECK_TRANSACTION_STATUS + " onFailure");
                        if (numRetry <= 2) {
                            MyDialogShow.showDialogRetry(
                                    getString(R.string.txt_notice),
                                    getString(R.string.error_onfaild_request),
                                    context,
                                    v -> checkTransStatusByType(numRetry + 1, udid, txid));
                        } else {
                            showDialogFailService(false, null);
                        }
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        Utils.LOGD(TAG, "content:" + (new String(arg2)));
                        try {
                            JSONObject jRoot = new JSONObject(new String(arg2));
                            String status = JsonParser.getDataJson(jRoot, "status");
                            if (Config.CODE_STATUS_SUCCESS.equalsIgnoreCase(status)) {
                                appendLogRequest(Config.CHECK_TRANSACTION_STATUS + " success");
                                appendLogSuccessPay();
                                checkEmailMerchantForSend(txid);
                            } else {
                                appendLogRequest(Config.CHECK_TRANSACTION_STATUS + " error server");
                                showDialogFailService(false, null);
                            }
                        } catch (Exception e) {
                            appendLogRequest(Config.CHECK_TRANSACTION_STATUS + " Exception: " + e.getMessage());
                            Utils.LOGE(TAG, "Exception: ", e);
                            showDialogFailService(false, null);
                        }
                    }
                });
    }

    private void checkEmailMerchantForSend(String txid) {
        // email of user sent in sdk
        String emailMerchant = PrefLibTV.getInstance(context).getEmailMerchant();
        if (!PrefLibTV.getInstance(context).get(PrefLibTV.sendTrxReceipt, Boolean.class, false) || TextUtils.isEmpty(emailMerchant)) {
//        if (!DataStoreApp.getInstance().getSendEmailMerchant() || TextUtils.isEmpty(emailMerchant)) {
            processAfterSendEmail();
        }
        else {
            sendReceiptByTypeEmail(emailMerchant, EMAIL_MERCHANT, txid);
        }
    }

    private void sendReceiptByTypeEmail(String email, final int typeEmail, final String txId) {
        if (!GetData.CheckInternet(context)) {
            showToastResultSendEmail(typeEmail, true);
            processAfterSendEmail();
            return;
        }

        appendLogRequest(Config.SEND_RECEIPT + " -- " + typeEmail);
        if (isRunMultiAcquirer) {
            sendReceiptToEmailMA(email, typeEmail, txId);
        } else {
            sendReceiptToMposByTypeEmail(email, typeEmail, txId);
        }
    }

    private void sendReceiptToMposByTypeEmail(String email, final int typeEmail, final String txId) {
        StringEntity entity = null;
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.SEND_RECEIPT);
            jo.put("udid", "0");
            jo.put("readerSerialNo", serialNumber);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", userId);
            jo.put("sessionKey", ssK);
            jo.put("transactionRequestID", dataPay.getTrId());
            jo.put("email", email);
            Utils.LOGD(TAG, "SEND_RECEIPT Data: " + jo);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "sendEmailByType: ", e1);
        }

        MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this),
                entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading("");
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        boolean isError = false;
                        String msgError = null;
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssK));
                            PrefLibTV.getInstance(context).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD("Send receipt: ", response.toString());
                            if (response.has("error")) {
                                if (response.has("message")) {
                                    final JSONObject jError = response.getJSONObject("error");
                                    msgError = jError.getString("code");
                                }
                                isError = true;
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "Exception: ", e);
                            msgError = e.getMessage();
                            isError = true;
                        }

                        appendLogRequest(Config.SEND_RECEIPT + " --isError= " + isError
                                + (TextUtils.isEmpty(msgError) ? "" : " Error=" + msgError));

                        handlerResultSendEmail(txId, typeEmail, isError);
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "Send receipt error: ", arg3);
                        appendLogRequest(Config.SEND_RECEIPT + " --onFailure ");
                        mPgdl.hideLoading();
                        handlerResultSendEmail(txId, typeEmail, true);

                    }
                });
    }

    private void handlerResultSendEmail(String txId, int typeEmail, boolean isError) {
        processAfterSendEmail();
    }

    private void sendReceiptToEmailMA(String email, final int typeEmail, final String txId) {
        ReceiptSend receiptSend = new ReceiptSend(txId, email);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(com.mpos.sdk.core.common.MyGson.getGson().toJson(receiptSend));
        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_SEND_RECEIPT, entity, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                appendLogRequest(Config.SEND_RECEIPT + " MA--onFailure ");
                try {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "sendReceiptToEmailMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                appendLogRequest(Config.SEND_RECEIPT + " MA--success");
            }
        });
        handlerResultSendEmail(txId, typeEmail, false);
    }

    private void showToastResultSendEmail(int typeEmail, boolean isError) {

        if (typeEmail == EMAIL_MERCHANT) {
            if (isError) {
                showToast(getString(R.string.error_send_email_invoice_merchant));
            } else {
                showToast(getString(R.string.success_send_email_invoice_merchant));
            }
        } else {
            if (isError) {
                showToast(getString(R.string.error_send_email_invoice_passenger));
            } else {
                showToast(getString(R.string.success_send_email_invoice_passenger));
            }
        }
    }

    private void showToast(String msg) {
        if (mToast == null) {
            mToast = new ToastUtil(this);
        }
        mToast.showToast(msg);
    }

    private void processAfterSendEmail() {
        showDialogSuccess();
    }

    /* _____________________________ START INTEGRATE _____________________________ */

    private void checkIsPayForMerchantIntegrated(DataIntegrated dataIntegrated, DataFromPartner dataFromPartner, DataPay dataPay) {
        typePostPay = dataIntegrated != null ? dataIntegrated.getPostType() : "";
        appendLog("checkIsPayForMerchantIntegrated: typePostPay=" + typePostPay);
        if ((ConfigIntegrated.TYPE_APP2_SER2_SER2.equals(typePostPay) || ConfigIntegrated.TYPE_APP1_SER2_SER2_APP1.equals(typePostPay))
                && dataFromPartner != null && !TextUtils.isEmpty(dataFromPartner.getOrderId())
        ) {
            pushDataMerchantIntegratedToServer(dataFromPartner, dataPay);
        } else {
            checkEmailMerchantForSend(dataPay.getTxId());
        }
    }

    private void pushDataMerchantIntegratedToServer(final DataFromPartner dataFromPartner, final DataPay dataPay) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.MERCHANT_INTEGRATED_CHECK_POSTPAY);
            jo.put("orderId", dataFromPartner.getOrderId());
            jo.put("readerSerialNumber", serialNumber);
            jo.put("merchantName", DataStoreApp.getInstance().getMerchantName());
            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
            jo.put("transactionStatus", "100"); // success pay
            jo.put("amount", dataPay.getAmount());
            jo.put("txid", dataPay.getTxId());
            jo.put("udid", dataPay.getUdid());
            jo.put("issuer", dataPay.getLabel());
            jo.put("flag", dataFromPartner.isFlag());
            jo.put("errorCode", "1");
            jo.put("cardHolderName", dataPay.getName());
            jo.put("description", dataPay.getDesc());
            jo.put("cardNumber", dataPay.getPan());
            jo.put("mobileUserId", PrefLibTV.getInstance(context).getMerchantsId());
            jo.put("muid", userId);
            Utils.LOGD("Data integrated: ", jo.toString());

            entity = new StringEntity(jo.toString());
            appendLogRequest(Config.MERCHANT_INTEGRATED_CHECK_POSTPAY+" udid="+dataPay.getUdid()+" orderId="+dataFromPartner.getOrderId());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception: ", e);
        }

        MposRestClient.getInstance(this).setCustomTimeout(60000).post(this, Config.URL_INTERGRATED, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading("");
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        mPgdl.hideLoading();
                        logUtil.appendLogRequestApiFail(Config.MERCHANT_INTEGRATED_CHECK_POSTPAY+" onFailure", arg2);

                        checkEmailMerchantForSend(dataPay.getTxId());
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        String response = new String(arg2);
                        Utils.LOGD(TAG, "Integrated: " + response);
                        appendLogRequest(Config.MERCHANT_INTEGRATED_CHECK_POSTPAY + " success");
                        checkEmailMerchantForSend(dataPay.getTxId());
                    }
                });
    }

    private void callbackSuccessToPartner() {
        String urlCallback = dataFromPartner.getUrlCallBack();
        Utils.LOGD(TAG, "callbackSuccessToPartner: urlCallback=" + urlCallback);
        appendLog("callbackSuccessToPartner: success " + urlCallback + " txId=" + txId + " udid=" + dataPay.getUdid() +" orderId="+dataFromPartner.getOrderId());
        if (TextUtils.isEmpty(urlCallback)) {
            if (isContinueSignature) {
                showDialogErrorCallbackPartner(getString(R.string.not_url_callback_re_signature));
            }
            else {
                showDialogErrorCallbackPartner(getString(R.string.not_url_callback));
            }
        } else {
            try {
                //dataFromPartner.getContent()
                JSONObject jRoot = new JSONObject();
                jRoot.put("transType", 1);
                jRoot.put("transCode", txId);
                jRoot.put("transDesc", dataPay.getDesc());
                jRoot.put("transDate", System.currentTimeMillis());
                jRoot.put("transAmount", Long.parseLong(dataPay.getAmount()));
                jRoot.put("orderId", dataFromPartner.getOrderId());
                jRoot.put("muid", userId);
                jRoot.put("issuerCode", dataPay.getLabel());

                if (!TextUtils.isEmpty(dataFromPartner.getExtParam())) {
                    jRoot.put("extParam", dataFromPartner.getExtParam());
                }

                Utils.LOGD(TAG, "callbackSuccessToPartner: " + jRoot);

                appendLog("callback success=" + jRoot);

                StringBuilder secretKey = new StringBuilder(DataStoreApp.getInstance().getIntergratSecrectKey());
                // if secretKey = "" -> default: 16 character F
                for (int i = secretKey.length(); i < 16; i++) {
                    secretKey.append("F");
                }
                Utils.LOGD(TAG, "key: " + secretKey);
                String content = EncodeDecode.doAESEncrypt(jRoot.toString(), secretKey.toString());
                Utils.LOGD(TAG, "callbackSuccessToPartner: contentEncryptAES=" + content);

                content = Uri.encode(content);
                Utils.LOGD(TAG, "callbackSuccessToPartner: content encode=" + content);

                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(urlCallback + content)));
//                finish();
                finishByType(true);
            } catch (Exception e) {
                Utils.LOGE(TAG, "callBackToPartner: ", e);
                showDialogErrorCallbackPartner(String.format(getString(R.string.txt_copy_tid_to_clipboard), urlCallback));
            }
        }
    }

    private void callbackFailToPartner(DataError dataError) {
        try {
            appendLog("callbackFailToPartner: fail error:" + dataError.getErrorCode() + " - " + dataError.getMsg() + " orderId=" + dataFromPartner.getOrderId());
            MposUtil.getInstance().callbackFailToPartner(this, dataError, dataFromPartner, logUtil);
        } catch (Exception e) {
            e.printStackTrace();
            showDialogErrorCallbackPartner(e.getMessage());
        }

    }

    private void showDialogErrorCallbackPartner(String msg) {
        appendLog("showDialogErrorCallbackPartner: "+msg);
        MyDialogShow.showDialogCancelAndClick("", msg, getString(R.string.ALERT_BTN_OK), this, v -> {
            pushTextToClipboard(txId);
            finishByType(true);
        }, false, true);
    }

    private void pushTextToClipboard(String text) {
        ClipboardManager clipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("TransactionId", text);
        if (clipboard != null) {
            clipboard.setPrimaryClip(clip);
        }
    }

    // _____________________________ UPDATE TRANSACTION STATUS _____________________________
    private void updateTransactionStatusInMpos() {
        logUtil.appendLogRequestApi("UPDATE_TRANSACTION_STATUS: " + dataPay.getUdid());
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPDATE_TRANSACTION_STATUS);
            jo.put("udid", dataPay.getUdid());
            jo.put("amount", dataPay.getAmount());
            jo.put("pan", dataPay.getPan());
            jo.put("cardHolder", dataPay.getName());

            jo.put("status", Constants.STATUS_TRANS_APPROVED);
            jo.put("txId", txId);
            jo.put("muid", userId);
            Utils.LOGD("Data: ", jo.toString());

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.UPDATE_TRANSACTION_STATUS + " onFailure", arg2);
                mPgdl.hideLoading();
                checkEmailMerchantForSend(txId);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                try {
                    JsonParser jsonParser = new JsonParser();
                    BaseObjJson errorBean = new BaseObjJson();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(TAG, "-updateTransactionStatusInMpos:" + jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " success");
                    } else {
                        msgError = TextUtils.isEmpty(errorBean.message) ?
                                getString(R.string.error_update_trans_mpos) : errorBean.message;
                        logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(TAG, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    showToast(msgError);
                }
                checkEmailMerchantForSend(txId);
            }
        });
    }

    private void sendActionToEmartByBroadcast(String action) {
        initIntentBroadcastAction();
        intentBroadcastEmartAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, action);
        sendBroadcast(intentBroadcastEmartAction);
    }

    private void initIntentBroadcastAction() {
        if (intentBroadcastEmartAction == null) {
            intentBroadcastEmartAction = new Intent(EMartPresenter.nameFilterActionPayment);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (isContinueSignature && isEmart) {
            sendActionToEmartByBroadcast(com.mpos.sdk.util.Constants.AP_ACTION_END);
        }
    }

    // _____________________________ NOTIFY - DIALOG SUCCESS/FAILURE _____________________________
    private void pushNotifyVoidFail(String pan, String holderName, String amount) {
        pushNotify(NotifyPushController.NOTIFY_PAY_VOID_FAIL, pan, holderName, amount);
    }

    private void pushNotify(int type, String pan, String holderName, String amount) {
        NotifyPushController notifyController = new NotifyPushController(this);
        notifyController.pushNotify(type, amount, "", "");
    }


    private void showDialogSuccess() {
        appendLog("showDialogSuccess: isSaveInstanceState=" + isSaveInstanceState);
        if (dataFromPartner != null && !ConfigIntegrated.TYPE_APP2_SER2_SER2.equals(typePostPay)) {
            Utils.LOGD(TAG, "showDialogSuccess: dataFromPartner != null");
            callbackSuccessToPartner();
            return;
        }

        if (isSaveInstanceState) {
            isWaitShowSuccess = true;
        }
        else {
            isWaitShowSuccess = false;
            String moreInfo = getString(R.string.SALES_DETAIL_TRANS_ID) + " " + txId;
            showDialogSuccess(moreInfo, dataPay);
        }
    }

    private void showDialogSuccess(String msg, DataPay dataPay) {
        this.dataPayUsePrint = dataPay;
        setResult(RESULT_OK);

        BankPeriodObject bankPeriodObject = null;
        if (intentDataPayment != null) {
            bankPeriodObject = (BankPeriodObject) intentDataPayment.getSerializableExtra(IntentsMP.EXTRA_INSTALLMENT_PERIOD_OBJ);
        }
        boolean isResignAndNotInstallment = false;
        if (dataPay.isResign() && !dataPay.getUdid().startsWith(ConstantsPay.PREFIX_UDID_INSTALLMENT)) {
            isResignAndNotInstallment = true;
        }

        if (bankInstallment != null && bankPeriodObject != null && !isResignAndNotInstallment) {
            Promotion promotion = (Promotion) intentDataPayment.getSerializableExtra(IntentsMP.EXTRA_INSTALLMENT_PROMOTION);
            dialogResult = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, msg, dataPay, dataCard, bankInstallment, bankPeriodObject, promotion);
            dialogResult.setApplyAnimation(false);
        } else {
            dialogResult = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, msg, dataPay, dataCard);
        }
        dialogResult.setCancelable(false);

        //todo need improve code

        if (isEmart && !TextUtils.isEmpty(dataPay.getLabel())) {
            if (dataPay.getLabel().equals(EMartPresenter.EMART_VOUCHER_CARD)) {
                dialogResult.setCallback(type -> printReceiptGiftCardOffline(dataPay));
            } else {
                dialogResult.setCallback(type -> printReceiptMaOffline(dataPay));
            }
        } else {
//            dialogResult.setCallback(type -> attemptPrintReceipt());
            dialogResult.setCallback(type -> {
                if (type == DialogResult.CLICK_PRINT) {
                    attemptPrintReceipt();
                }
            });
        }

        Utils.LOGD(TAG, "isStateSaved= 1" + getSupportFragmentManager().isStateSaved());

        appendLog("isStateSaved=" + getSupportFragmentManager().isStateSaved());
        if (ScreenUtils.canShowDialog(context) && !getSupportFragmentManager().isStateSaved()) {
            dialogResult.show(getSupportFragmentManager(), DialogResult.class.getName());

            if (isEmart) {
                dialogResult.startCoundDismissDialog(25000);
            }
        }
        logUtil.saveLog();
        logUtil.pushLog();
    }

    private void showDialogFailService(boolean startLogin, String errorCode) {
        DataError dataError = new DataError();
        if (!TextUtils.isEmpty(errorCode)) {
            try {
                dataError.setErrorCode(Integer.parseInt(errorCode));
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        // VPBank
        if ("50030".equals(errorCode)) {
            dataError.setMsg(getString(R.string.error_code_vp_50030));
        } else {
            dataError.setMsg(getString(R.string.error_pay_service));
        }
        //
        showDialogReason(dataError, startLogin);
    }

    private void showDialogReason(DataError dataError, final boolean startLogin) {
        appendLog("showDialogReason");
        logUtil.saveLog();
        logUtil.pushLog();

        // NOTE: added by noe
        Utils.LOGD(TAG, ">>> ERROR USE CARD ON PR \n>>> errorCode: " + dataError.getErrorCode()
                + " \n>>> errorMsg: " + dataError.getMsg());

        final MposDialog dialogErrorSwipeCard = MyUtils.initDialogGeneralError(
                context,
                dataError.getErrorCode(),

                dataError.getMsg(),
                ActivityPrePayment.class.getName());

        dialogErrorSwipeCard.setOnClickListenerDialogClose(v -> {
            if (startLogin && !isRunMultiAcquirer) {
                MyDialogShow.gotoReLogin(context);
            }
            else {
                setResult(RESULT_FAILURE_PAY);
            }
            dialogErrorSwipeCard.dismiss();
            finish();
        });
        if (ScreenUtils.canShowDialog(this)) {
            dialogErrorSwipeCard.show();
        }
    }

    private void showDialogTimeout() {
        appendLog("showDialogTimeout");
        logUtil.saveLog();
        logUtil.pushLog();
        if (ScreenUtils.canShowDialog(this)) {
            final MposDialog dialogError = MyUtils.initDialogError_GoToHistory(this,
                    getString(R.string.txt_error_connect_bank), Html.fromHtml(getString(R.string.error_time_out_sale)));
            if (dialogError != null) {
                showDialogGoToHistory(dialogError, false);
            }
        }
    }

    private void showDialogGoToHistory(final MposDialog dialogError, final boolean b) {
        dialogError.setOnClickListenerButtonCancel(v -> {
            dialogError.dismiss();
            finishByType(b);
        });
        dialogError.setOnClickListenerButtonOk(v -> {
            dialogError.dismiss();
            Intent i = new Intent(context, ActivitySubLogin.class);
            startActivity(i);
            finishByType(b);

        });
        dialogError.show();
    }


    private void appendLog(String log) {
        appendLog(ItfAppendLog.TypeLog.action, log);
    }

    private void appendLogRequest(String log) {
        appendLog(ItfAppendLog.TypeLog.request, log);
    }

    private void appendLogSuccessPay() {
        appendLog("*** SUCCESS PAY ***");
    }

    public void appendLog(ItfAppendLog.TypeLog typeLog, String log) {
        // view log
//        Utils.LOGD(tag, "appendLog11-->> "+log);
        String action = typeLog == ItfAppendLog.TypeLog.action ? SaveLogController.TEXT_ACTION : SaveLogController.TEXT_REQUEST;
        logUtil.appendLog(action, log);
    }

    //  ======================================== PRINTER ========================================

    private void attemptPrintReceipt() {
        processPrintReceipt(PRINT_END_TRANSACTION);
        // sleep for printer init
        sleepWaitInitPrinter();
    }

    private void printReceiptGiftCardOffline(DataPay dataPay) {
        libPrinter.actionPrintGiftCardEmart(dataPay);
    }

    private void printReceiptMaOffline(DataPay dataPay) {
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            libPrinter.printReceiptMaOfflineByLayout(dataPay);
        }
    }

    private void showDialogErr(String msg) {
        runOnUiThread(() -> {
            MyDialogShow.showDialogError(msg, context);
        });
    }

    private void printText(ITYPaxApi paxApi, String text) {
        Utils.LOGD(TAG, "printText: ->" + text);
        paxApi.printStr(text, "UTF-8");
        paxApi.printStr("\n", "UTF-8");
    }

    @Override
    public StringEntity buildStringEntityReceipt(){
        StringEntity entity = null;
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", Config.DOWNLOAD_RECEIPT_IMAGE);
            jo.put("udid", "0");
            jo.put("readerSerialNo", serialNumber);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", userId);
            jo.put("sessionKey", ssK);
            jo.put("transactionRequestID", dataPay.getTrId());

            jo.put("receiptWidth", Constants.WIDTH_IMAGE_RECEIPT);
            Utils.LOGD(TAG, "DOWNLOAD_RECEIPT_IMAGE Data: "+ jo);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return entity;
    }

    public interface ItfShowScreenLoading{
        void onShowScreenLoadingAfterPayment();
    }

}