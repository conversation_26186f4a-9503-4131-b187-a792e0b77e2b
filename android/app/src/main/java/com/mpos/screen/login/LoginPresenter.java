package com.mpos.screen.login;

import android.app.Activity;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Base64;
import android.widget.Toast;

import androidx.annotation.NonNull;

//import com.datecs.audioreader.AudioReader;
//import com.datecs.audioreader.AudioReaderManager;
//import com.datecs.pinpad.DeviceInfo;
//import com.datecs.pinpad.Pinpad;
//import com.datecs.pinpad.PinpadException;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.MyPresenter;
//import com.mpos.audioreaderemv.backend.EMVProcessor;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.dspread.DeviceListActivity;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataFromPartner;
import com.mpos.models.DataMposCache;
//import com.mpos.pinpad.PinpadHelper;
//import com.mpos.pinpad.PinpadManager;
//import com.mpos.pinpad.TransactionException;
import com.mpos.screen.ActivityLoginActivation;
import com.mpos.screen.ActivityMainNew;
import com.mpos.screen.mart.ActivityHomeMart;
import com.mpos.screen.mart.ActivitySettingSP04;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataCache;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.BaseDevice;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MyOnClickListenerView;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.LibErrorMpos;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.mpos.utils.VolumeControl;
import com.pax.baselink.listener.IBluetoothDevice;
import com.whty.smartpos.tysmartposapi.pos.PosConstrants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.ref.WeakReference;
import java.util.Calendar;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

import static android.app.Activity.RESULT_OK;

public class LoginPresenter extends MyPresenter implements LoginContract.Presenter {

    private static final String TAG = "LoginPresenter";

    static final int REQUEST_ENABLE_BT = 1;
    static final int REQUEST_DEVICE = 2;
    static final int REQUEST_ACTIVATION = 3;    // goto screen activation code
    static final int REQUEST_AUDIO_PERMISSION = 4;
    static final int REQUEST_DEVICE_DSPREAD = 5;
    final int requestCodePermissionBluetooth = 6;
//    public static final int REQUEST_DEVICE_EMART = 7;               // todo callback emart


    private final LoginActivity loginActivity;
    private final LoginContract.View viewer;

    private LibP20L libP20L;
//    private LibPax libPax;
    private LibLoginHandler libLoginHandler;
//    private PinpadManager mPinpadManager;
    private final SaveLogController logUtil;
//    private TaskAudioReaderConnect taskConnectAR;
    private ConfigurationUser configUtils = new ConfigurationUser();;

    private DataReversalLogin dataReversalLogin;
    private Intent intent;

    private int mFlagDevices = -1;//0: select email //1: Audio //2: PR-01 //3: PR-02

    private boolean isAutoLogin = false;
    private boolean isAutoLoginForPartner = false;
    private boolean isUseReader = false;

    private boolean isConnectedAR;
    private boolean isClickSelectDevice = false;
    private boolean isRegisterHeadset = false;

    private boolean isLoadConfigFromMacq = true;

    private String bankName;
    private String mAcc = "";
    private String mPin = "";
    private String serialNumber = "";
    private String mEMVVersion;
    private String mFWVersion;
    private String bluetoothAddressDspread;

    private String currBankName = "";
    private int currNumRunLogin = 0;
    private int errorCodeBank;

    LoginPresenter(Context context, @NonNull LoginContract.View viewer, LoginActivity loginActivity) {
        super(context);
        this.viewer = viewer;
        this.loginActivity = loginActivity;
        viewer.setPresenter(this);
        logUtil = MyApplication.self().getSaveLogController();
        logUtil.pushLog();
    }

    public void setIntent(Intent intent) {
        this.intent = intent;
    }

    @Override
    public void start() {
        if (DevicesUtil.isSP02()) {
            processEnableNavbar();
        }

        checkAutoLogin();
        if (!isAutoLogin) {
            initPrefixUserByDevice();
        }
    }

    private void processEnableNavbar() {
        logUtil.appendLogAction("processEnableNavbar ");
        Utils.LOGD(TAG, "processEnableNavbar = ");
        MposUtil.getInstance().handlerActionDisableStatusBar(context, false);
        MposUtil.getInstance().handlerActionDisplayNavbar(context, true);
    }

    private void clearOldShPr(String shPrName) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(shPrName, Context.MODE_MULTI_PROCESS);
        sharedPreferences.edit().clear().apply();
    }

    public void checkAutoLogin() {
        // todo need remove next version
        clearOldShPr("LIBNOTIFY");
        clearOldShPr("mpos_vimo_share_prefs");

        String userSave = PrefLibTV.getInstance(context).getUserId();

        DataFromPartner dataFromPartner = checkHaveDataLoginFromPartner();
        isUseReader = DataStoreApp.getInstance().isUseReader();
        isAutoLogin = PrefLibTV.getInstance(context).getIsSaveLogin();

        String passSave = null;
        if (dataFromPartner != null) {
            if (dataFromPartner.checkCanAutoLogin()) {
                userSave        = dataFromPartner.getMobileUser();
                passSave        = dataFromPartner.getMobilePass();
                serialNumber    = dataFromPartner.getReaderSN();
                mFlagDevices    = MposUtil.getInstance().convertTypeReaderToFlagDevice(dataFromPartner.getReaderType());
                isAutoLoginForPartner = true;
                isAutoLogin = false;
                isUseReader = true;
            }
            // can miss some field
            else if (dataFromPartner.checkMissDataLogin()){
                isAutoLogin = false;
                showError(R.string.integration_missing_filed_login);
            }
        }
        Utils.LOGD(TAG, "checkAutoLogin: " + isAutoLogin);
        if (isAutoLogin) {
            passSave = PrefLibTV.getInstance(context).getPW();
            serialNumber = PrefLibTV.getInstance(context).getSerialNumber();
            mFlagDevices = PrefLibTV.getInstance(context).getFlagDevices();
        }

        Utils.LOGD(TAG, "userSave: "+userSave);
        if (!TextUtils.isEmpty(userSave)) {
            viewer.showUserId(userSave);
            if (!isAutoLogin) {
                viewer.requestFocusView(R.id.user_pin);
            }
        }

        if (DevicesUtil.isP20L()) {
            initViewP20L();
        } else if (DevicesUtil.isSP02()) {
            initViewSP02();
        } else if (DevicesUtil.isPax()) {
            initViewPax();
        }

        Utils.LOGD(TAG, "--> isUseReader: " + isUseReader + "--> userSave: " + userSave+ "--> mFlagDevices: " + mFlagDevices);
        if( (isAutoLogin || isAutoLoginForPartner)
                && !TextUtils.isEmpty(passSave) && !TextUtils.isEmpty(userSave)
                && (!TextUtils.isEmpty(serialNumber) || !isUseReader)) {
            logUtil.appendLogAction("- Auto login");
            viewer.showViewSplash(true);
            viewer.showUserPin(passSave);
            viewer.hideImvBack();

            if (!DevicesUtil.isP20L() && !DevicesUtil.isSP02()) {
                showSerialNumber(mFlagDevices, serialNumber);
            }

            attemptLogin(userSave, passSave);
        } else {
            isAutoLogin = false;
            new Handler().postDelayed(() -> {
                Utils.LOGD(TAG, "checkAutoLogin: Build.MODEL=" + Build.MODEL);
                viewer.showViewSplash(false);
            }, 1500);
            fetchFileConfig();
        }
    }

    private void initViewPax() {
        serialNumber = getSerialNumberPax();
        if (!TextUtils.isEmpty(serialNumber)) {
            isUseReader = true;
            viewer.hideImvBack();
            showSerialNumber(ConstantsPay.DEVICE_PAX, serialNumber);
        } else {
            logUtil.appendLogAction("smartpos cant get SN");
            MyDialogShow.showDialogErrorFinish(getString(R.string.error_sp01_cant_get_SN), context);
        }
    }

    private String getSerialNumberPax() {
//        libPax = MyApplication.self().getLibPax();
//        return libPax.getSerialnumber();
        return MyApplication.self().getLibPax().getSerialnumber();
    }

    private void initPrefixUserByDevice() {
        if (TextUtils.isEmpty(viewer.getUserIdEnter())) {
            if (DevicesUtil.isP20L()) {
                viewer.showUserId("sp");
            }
            else if (DevicesUtil.isSP02P8()) {
                viewer.showUserId("sp3");
            }
            else if (DevicesUtil.isSP02()) {
                viewer.showUserId("sp2");
            }
        }
    }

    private void initViewP20L() {
        serialNumber = getSerialNumberP20L();
        if (!TextUtils.isEmpty(serialNumber)) {
            isUseReader = true;
            viewer.hideImvBack();
            showSerialNumber(ConstantsPay.DEVICE_P20L, serialNumber);
        } else {
            logUtil.appendLogAction("smartpos cant get SN");
            MyDialogShow.showDialogErrorFinish(getString(R.string.error_sp01_cant_get_SN), context);
        }
        if (DataStoreApp.getInstance().getIsDissableButton()) {
            try {
                libP20L.getSmartPosApi().disableExpand(new int[]{PosConstrants.DISABLE_NONE});
                DataStoreApp.getInstance().setIsDisableButton(false);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String getSerialNumberP20L() {
        libP20L = MyApplication.self().getLibP20L();
        return libP20L.getSerialNumber();
    }

    private void initViewSP02() {
        serialNumber = getSerialNumberSP02();
        if (!TextUtils.isEmpty(serialNumber)) {
            isUseReader = true;
            viewer.hideImvBack();
            showSerialNumber(ConstantsPay.DEVICE_KOZEN, serialNumber);
        } else {
            logUtil.appendLogAction("SP02 cant get SN");
            MyDialogShow.showDialogErrorFinish(getString(R.string.error_sp01_cant_get_SN), context);
        }

        if (DataStoreApp.getInstance().getIsDissableButton()) {
            try {
                DataStoreApp.getInstance().setIsDisableButton(false); //enable button
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
//        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isDisableButtonSP01, Boolean.class, Boolean.FALSE)) {
//            try {
//                libP20L.getSmartPosApi().disableExpand(new int[]{PosConstrants.DISABLE_NONE});
//                DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isDisableButtonSP01, false);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
    }

    private String getSerialNumberSP02() {
        return MyApplication.self().getLibSP02().getSerialNumber();
    }


    private void showError(int stringId) {
        showError(getString(stringId), false);
    }
    private void showError(String text, boolean resetInputPin) {
        if (isAutoLogin) {
            isAutoLogin = false;
        }
        viewer.showError(errorCodeBank, text, resetInputPin);
        logUtil.pushLog();
    }

    private void showAlertCountDown(String msgCountDown, String timeShow) {
        viewer.showAlertCountDown(msgCountDown, timeShow);
    }

    private void hideLoading() {
        showLoading(false);
    }

    private void showLoading(boolean b) {
        viewer.showProgressBar(b);
    }


    @Override
    public void attemptLogin(String user, String pass) {
        boolean haveError = false;
        if (isUseReader) {
            if (mFlagDevices == 0) {
                showToast(R.string.txt_select_device);
                haveError = true;
            } else if (TextUtils.isEmpty(serialNumber)) {
                showToast(R.string.no_reader_device_plugged);
                haveError = true;
            }
        }
        if (!haveError) {
            this.mAcc = user;
            this.mPin = pass;
            fetchMerchantInfo();
        }
    }

    private void showToast(int p) {
        viewer.showToast(getString(p));
    }

    @Override
    public String getPhoneByTime() {
        return MyUtils.getPhoneByTime(DataStoreApp.getInstance().getPhoneInTime(), DataStoreApp.getInstance().getPhoneOutTime());
    }

    @Override
    public void selectConnectReader(int type) {
        mFlagDevices = type;
        serialNumber = "";
//        PrefLibTV.getInstance(context).setFlagDevices( mFlagDevices);
        logUtil.appendLogAction("selectConnectReader:" + type);

        switch (mFlagDevices) {
            case ConstantsPay.DEVICE_AUDIO:
                selectConnectAudioReader();
                break;
            case ConstantsPay.DEVICE_PINPAD:
//                selectConnectPr01();
                break;
            case ConstantsPay.DEVICE_DSPREAD:
                selectConnectPr02();
                break;
            case ConstantsPay.DEVICE_NONE:
                isUseReader = false;
                break;
        }
    }

    private void selectConnectAudioReader() {
        isUseReader = true;
        registerHeadsetReceiver();
        isConnectedAR = false;
        isClickSelectDevice = true;
    }

    private void registerHeadsetReceiver() {
        VolumeControl.setMaxVolume(loginActivity);
        if (!MyUtils.checkHaveAudioPermission(context)) {
            requestAudioPermission();
        }
        if (!isRegisterHeadset) {
            context.registerReceiver(mHeadsetReceiver, new IntentFilter(Intent.ACTION_HEADSET_PLUG));
            isRegisterHeadset = true;
        }
    }

    private void requestAudioPermission() {
        MyUtils.requestAudioPermission(loginActivity, REQUEST_AUDIO_PERMISSION);
    }

//    private void selectConnectPr01() {
//        isUseReader = true;
//        if (mPinpadManager == null) {
//            setupPinpad();
//        }
//        initBluetoothAndDevice();
//    }

    private void selectConnectPr02() {
        isUseReader = true;
        initBluetoothAndDevice();
    }

//    private void setupPinpad() {
//        mPinpadManager = PinpadManager.getInstance(context);
//        try {
//            mPinpadManager.disconnect();
//        } catch (Exception e) {
//            Utils.LOGE(TAG, "Exception: ", e);
//        }
//        mPinpadManager.setOnConnectionClosedListener(() -> {
//            Utils.LOGD(TAG, " setOnConnectionClosedListener in ActivityLogin");
//            if (!((Activity) context).isFinishing()) {
//                showToast(R.string.msg_pinpad_connection_is_closed);
//                initBluetoothAndDevice();
//            }
//        });
//        mPinpadManager.setAppendLog((typeLog, log) -> {
//            if (logUtil != null) {
//                logUtil.appendLogAction(log);
//            }
//        });
//    }

    private void initBluetoothAndDevice() {

        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        if (btAdapter != null) {
            boolean havePermissionBluetooth = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !UtilsSystem.checkHaveBluetoothPermission(context)) {

                havePermissionBluetooth = false;
                requestBluetoothPermission();
            }
            if (havePermissionBluetooth) {
                if (btAdapter.isEnabled()) {
                    viewer.showViewConnectDevice(mFlagDevices);
                } else {
                    UtilsSystem.enableBluetooth(context, REQUEST_ENABLE_BT);
                }
            }
        } else {
            showToast(R.string.msg_bluetooth_is_not_supported);
        }
    }

//    public void checkBluetoothTcp() {
//        Utils.LOGD(TAG, "checkBluetooth: ");
//        String bluetoothAddress = PrefLibTV.getInstance(this.context).getBluetoothAddressPAX();
//        Utils.LOGD(TAG, "checkBluetooth: bluetoothAddress=" + bluetoothAddress);
//        //todo: need remove in next version
//        if (TextUtils.isEmpty(bluetoothAddress)) {
//            bluetoothAddress = getCacheBleAddressPaxDockInOldShPr();
//            Utils.LOGD(TAG, "CacheBleAddressPaxDockInOldShPr=" + bluetoothAddress);
//            if (!TextUtils.isEmpty(bluetoothAddress)) {
//                changeOldShPrToNewShPr();
//                clearOldShPr("LIBNOTIFY");
//                clearOldShPr("mpos_vimo_share_prefs");
//            }
//        }
//
//        if (TextUtils.isEmpty(bluetoothAddress)) {
//            BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
//            if (btAdapter != null) {
//                if (btAdapter.isEnabled()) {
//                    showDeviceList();
//                } else {
//                    UtilsSystem.enableBluetooth(this.context, REQUEST_ENABLE_BT_MART);
//                }
//            } else {
//                Toast.makeText(this.context, this.context.getString(R.string.msg_bluetooth_is_not_supported), Toast.LENGTH_SHORT).show();
//            }
//        } else {
//            checkAutoLogin();
//        }
//    }

    private String getCacheBleAddressPaxDockInOldShPr() {
        SharedPreferences sharedPreferences = context.getSharedPreferences("LIBNOTIFY", Context.MODE_MULTI_PROCESS);
        return sharedPreferences.getString("BLUETOOTH_ADDRESS_PAX", "");
    }

    private void clearOldShPr() {
        clearOldShPr("LIBNOTIFY");
        clearOldShPr("mpos_vimo_share_prefs");

    }

    private void connect2BlueToothDevice(IBluetoothDevice device) {
        viewer.showProgressBar(true);
        Utils.LOGD(TAG, "connect2BlueToothdevice: " + device.getName());
        boolean isConnect = BaseDevice.getInstance(this.context).isBaseConnect(device);
        if (isConnect) {
            //todo save cache device
            BaseDevice.getInstance(context).setWifiStart(false);
            Toast.makeText(loginActivity, "Kết nối thành công", Toast.LENGTH_SHORT).show();
            checkAutoLogin();
        } else {
            Toast.makeText(loginActivity, "Kết nối không thành công", Toast.LENGTH_SHORT).show();
        }
        viewer.showProgressBar(false);
    }

    private void requestBluetoothPermission() {
        Toast.makeText(context, getString(R.string.msg_request_permission_bluetooth), Toast.LENGTH_LONG).show();
        UtilsSystem.requestBluetoothPermission(loginActivity, requestCodePermissionBluetooth);
    }

    @Override
    public void handlerActivityResult(int requestCode, int resultCode, Intent data) {
        Utils.LOGD(TAG, "onActivityResult: requestCode=" + requestCode + " resultCode=" + resultCode);
        switch (requestCode) {
            case REQUEST_ENABLE_BT: {
                if (resultCode == RESULT_OK) {
                    viewer.showViewConnectDevice(mFlagDevices);
                }
                break;
            }
            case REQUEST_DEVICE: {
                if (resultCode == RESULT_OK) {
//                    connectPr01();
                }
                break;
            }
            case REQUEST_ACTIVATION: {
                if (resultCode == RESULT_OK) {
                    ((Activity) context).finish();
                }
                break;
            }
            case REQUEST_DEVICE_DSPREAD: {
                // When DeviceListActivity returns with a device to connect
                if (resultCode == RESULT_OK) {
                    // Get the device MAC address
                    bluetoothAddressDspread = data.getExtras() == null ? "" : data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    serialNumber = data.getExtras() == null ? "" : data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_SERIALNUMBER);

                    Utils.LOGD(TAG, "onActivityResult: bluetoothAddressDspread=" + bluetoothAddressDspread + " serialNumber=" + serialNumber);
                    showSerialNumber(ConstantsPay.DEVICE_DSPREAD, serialNumber);
                }
                break;
            }
            default:
                break;
        }
    }

    public void handleRequestPermission(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == requestCodePermissionBluetooth &&
                ((grantResults.length == 1 && grantResults[0] != PackageManager.PERMISSION_GRANTED)
                        || grantResults.length == 2 && (grantResults[0] != PackageManager.PERMISSION_GRANTED || grantResults[1] != PackageManager.PERMISSION_GRANTED))
        ) {
            MyDialogShow.showDialogWarning(context, getString(R.string.msg_warning_denial_permission_bluetooth), false);
        } else {
            initBluetoothAndDevice();
        }
    }

    private void showSerialNumber(int type, String serialNumber) {
        logUtil.appendLogAction("show SN:" + serialNumber + " type:" + type);
        if (mFlagDevices == -1) {
            mFlagDevices = type;
        }
        viewer.showSerialNumber(type, serialNumber);
    }

//    private void connectPr01() {
//
//        invokeHelper(pinpad -> {
//            // Log device information
//            final DeviceInfo devInfo = pinpad.getIdentification();
//
//            mFWVersion = "6";
//
//            serialNumber = devInfo.getDeviceSerialNumber();
//
//
//            runOnUiThread(() -> showSerialNumber(ConstantsPay.DEVICE_PINPAD, devInfo.getDeviceSerialNumber()));
////            setSerialNumber(ConstantsPay.DEVICE_PINPAD, devInfo.getDeviceSerialNumber());
//
//            // Set set initial screen.
//            PinpadHelper.initScreen(pinpad);
//
//            // Set current time.
//            pinpad.sysSetDate(Calendar.getInstance());
//
//            // Disable all events.
//            pinpad.sysEnableEvents(Pinpad.ENABLE_MAGNETIC_CARD | Pinpad.ENABLE_SMART_CARD);
//        });
//    }


    // A helper method to invoke Pinpad methods in
//    private void invokeHelper(final MethodInvoker invoker) {
//        final ProgressDialog dialog = new ProgressDialog(context);
//        dialog.setCancelable(false);
//        dialog.setCanceledOnTouchOutside(false);
//        dialog.setMessage(getString(R.string.msg_please_wait));
//        dialog.setOnKeyListener((dialog1, keyCode, event) -> true);
//
//        dialog.show();
//
//        final Pinpad pinpad = mPinpadManager.getPinpad();
//
//        final Thread t = new Thread(() -> {
//            try {
//                invoker.invoke(pinpad);
//            } catch (PinpadException e) { // Non critical exception
//                Utils.LOGE(TAG, "Exception", e);
//                StringWriter sw = new StringWriter();
//                e.printStackTrace(new PrintWriter(sw));
//            } catch (IOException e) { // Critical exception
//                Utils.LOGE(TAG, "Exception", e);
//                mPinpadManager.disconnect();
//                viewer.showToast(e.getMessage());
//                viewer.showViewConnectDevice(mFlagDevices);
//            } catch (TransactionException e) { // Non critical exception
//                Utils.LOGE(TAG, "Exception", e);
//            } finally {
//                dialog.dismiss();
//            }
//        });
//        t.start();
//    }
//
//    private interface MethodInvoker {
//        void invoke(Pinpad pinpad) throws TransactionException, IOException;
//    }

    /* ****************************************
     *              request api
     * ****************************************/

    /**
     * load config from server: mobile number support
     */
    private void fetchFileConfig() {
        Utils.LOGD(TAG, "run load file config");
        MposRestClient.getInstance(context).get(Config.URL_CONFIG, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int i, Header[] headers, byte[] bytes) {
                String content = new String(bytes);
                Utils.LOGD(TAG, "onSuccess: content=" + content);

                //todo hardcode phone vcb 3 ben
                viewer.showPhoneSupport(getPhoneByTime());

//                try {
//                    JSONObject jRoot = new JSONObject(content);
//                    String config = JsonParser.getDataJson(jRoot, "contact");
//
//                    ConfigSupport configSupport = MyGson.parseJson(config, ConfigSupport.class);
//                    DataStoreApp.getInstance().savePhoneInTime(configSupport.getIntime());
//                    DataStoreApp.getInstance().savePhoneOutTime(configSupport.getOuttime());
//
//                    viewer.showPhoneSupport(getPhoneByTime());
//                } catch (JSONException e) {
//                    e.printStackTrace();
//                }
            }

            @Override
            public void onFailure(int i, Header[] headers, byte[] bytes, Throwable throwable) {
                Utils.LOGD(TAG, "onFailure: loadconfig----");
            }
        });
    }

    @Override
    public void fetchMerchantInfo() {
        configUtils = new ConfigurationUser();
        if (isLoadConfigFromMacq) {
            fetchMerchantInfoFromMacq();
        } else {
            fetchMerchantInfoFromMpos(1);
        }
//        fetchMerchantInfoFromMpos(1);
    }

    private void fetchMerchantInfoFromMacq() {
        initLibLoginHandler();
        DataCache.getInstance().clearCache();
        libLoginHandler.setItfHandlerMposConfig(s -> {
            try {
                Utils.LOGD(TAG, "fetchMerchantInfoFromMacq: " + s);
                if (!TextUtils.isEmpty(s)) {
                    configUtils.readConfigFromMpos(context, new JSONObject(s));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        });

//        libLoginHandler.setTypeUseCache(com.mpos.sdk.util.Constants.TYPE_CACHE_SESSION_IN_DAY);
        libLoginHandler.setTypeUseCache(com.mpos.sdk.util.Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER);
        libLoginHandler.setCanUseStorageCacheFetch(true);

        libLoginHandler.fetchMerchantConfigMacq(mAcc, serialNumber, mPin);
    }

    /**
     * load merchant_info from mpos server
     *
     * @param counterLoad : == 1 -> onfail -> reload; != 1: show error
     */
    private void fetchMerchantInfoFromMpos(final int counterLoad) {
        Utils.LOGD(TAG, "------- loadMerchantInfo ------");
        if (!GetData.CheckInternet(context)) {
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), context, v -> fetchMerchantInfoFromMpos(counterLoad));
            return;
        }
        showLoading(true);

        String language = Locale.getDefault().getLanguage();
        logUtil.appendLogRequestApi(Config.GATEWAY_MERCHANT_LOGIN_SDK);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.GATEWAY_MERCHANT_LOGIN_SDK);

            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("os", "Android");

            jo.put("language", language);
            if (isUseReader) {
                Utils.LOGD(TAG, "CO SU DUNG THIET BI MPOS");
                jo.put("muid", mAcc);
                jo.put("readerSerial", serialNumber);
            } else {
                Utils.LOGD(TAG, "KHONG SU DUNG THIET BI MPOS");
                jo.put("email", mAcc);
                jo.put("password", mPin);
            }
            jo.put("bundle", context.getPackageName());

            Utils.LOGD(TAG, "REQUEST--MERCHANT_INFO--: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        Utils.LOGD(TAG, "loadMerchantInfo: url=" + Config.URL_GATEWAY);

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGD(TAG, "onFailure: " + arg3.getMessage());
                        logUtil.appendLogRequestApiFail(Config.GATEWAY_MERCHANT_LOGIN_SDK + " onFailure--" + arg3.getMessage() + "<-->", arg2);
                        hideLoading();
                        if (counterLoad == 1) {
                            fetchMerchantInfoFromMpos(2);
                        } else {
                            callbackFailToPartner();
                            viewer.showErrorLoadOnFailureMpos(DevicesUtil.isP20L() ? getString(R.string.warning_network_sp01) : null);
                        }
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        hideLoading();
                        String msgError = null;
                        int errorCode = 0;
                        try {
                            String contentResponse = new String(arg2);
                            JsonParser jsonParser = new JsonParser();
                            BaseObjJson errorBean = new BaseObjJson();
                            JSONObject jRoot = new JSONObject(contentResponse);
                            Utils.LOGD(TAG, "RESPONSE--MERCHANT_INFO-->: " + jRoot);

                            jsonParser.checkHaveError(jRoot, errorBean);
                            boolean outage = Constants.SVALUE_TRUE.equalsIgnoreCase(JsonParser.getDataJson(jRoot, "outage", Constants.SVALUE_FALSE));
                            if (outage) {
                                String outageMsg = JsonParser.getDataJson(jRoot, "outageMessage");
                                if (TextUtils.isEmpty(outageMsg)) {
                                    outageMsg = getString(R.string.error_outage);
                                }
                                MyDialogShow.showDialogCancelAndClick(null, outageMsg, getString(R.string.retry_button), context,
                                        view -> fetchMerchantInfo(), null, true, true, true);
                            } else if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                                logUtil.appendLogRequestApi(Config.GATEWAY_MERCHANT_LOGIN_SDK + " success");

                                handlerSuccessMerchantInfo(jRoot);

                            } else {
                                Utils.LOGD(TAG, "ERROR CODE: " + errorBean.code);
                                try {
                                    errorCode = Integer.parseInt(errorBean.code);
                                } catch (NumberFormatException e) {
                                    e.printStackTrace();
                                }
                                msgError = LibErrorMpos.getErrorMsg(context, errorBean.code);
                                if (TextUtils.isEmpty(msgError)) {
                                    msgError = LibError.getErrorMsg(errorCode, context);
                                    if (TextUtils.isEmpty(msgError)) {
                                        msgError = TextUtils.isEmpty(errorBean.message) ? getString(R.string.error_get_info_merchant) : errorBean.message;
                                    }
                                }
                                logUtil.appendLogRequestApi(Config.GATEWAY_MERCHANT_LOGIN_SDK + " error:" + errorBean.code + "-" + msgError);
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi(Config.GATEWAY_MERCHANT_LOGIN_SDK + " Exception:" + e.getMessage());
                            msgError = getString(R.string.error_get_info_merchant);
                            Utils.LOGE(TAG, "Exception", e);
                        }

                        if (!TextUtils.isEmpty(msgError)) {
                            logUtil.appendLogException(msgError);
                            viewer.showErrorLoadMpos(errorCode, msgError);
                        }
                    }
                });
    }

    private void handlerSuccessMerchantInfo(JSONObject jRoot) throws Exception {
//        DataStoreApp.getInstance().createDataLoginMerchant(jRoot.toString());
        PrefLibTV.getInstance(context).createDataLoginMerchant(jRoot.toString());

//		CHECK ROOT
        String isAllowRooted = JsonParser.getDataJson(jRoot, "isAllowRooted");
        if (!Constants.SVALUE_1.equals(isAllowRooted) && !Utils.mDEBUG && Utils.isRooted()) {
            logUtil.appendLogAction("---->>>>Device is rooted");
            MyDialogShow.showDialogErrorFinish(getString(R.string.ALERT_JAIL_BROKEN_DEVICE_MSG), context);
            return;
        }

        configUtils.readConfigFromMpos(context, jRoot);

        String encryptData = JsonParser.getDataJson(jRoot, "kData");
        String clearData = EncodeDecode.doAESDecrypt(encryptData, context.getPackageName());
        Utils.LOGD(TAG, "encryptData=" + encryptData + " clearData=" + clearData);

        JSONObject jKeyData = new JSONObject(clearData);
        String mpk = JsonParser.getDataJson(jKeyData, "keyInit");

        PrefLibTV.getInstance(context).saveMpK(mpk);

        String bankNameLoaded;//JsonParser.getDataJson(jRoot, "bankName");


        String isMacqFlow = JsonParser.getDataJson(jRoot, "isMacqFlow", com.mpos.sdk.util.Constants.SVALUE_0);

        if (Constants.SVALUE_1.equals(isMacqFlow)) {
            bankNameLoaded = ConstantsPay.MPOS_MULTI_ACQUIRER;
            MyApplication.self().setRunMA(true);
        } else {
            bankNameLoaded = JsonParser.getDataJson(jRoot, "bankName");
            MyApplication.self().setRunMA(false);
        }

//        PrefLibTV.getInstance(context).put(PrefLibTV.DATA_LOGIN_CACHE, jRoot.toString());
        PrefLibTV.getInstance(context).setMcConfigCache(jRoot.toString());

        saveDataCache(bankNameLoaded);
        bankName = bankNameLoaded;
        Utils.LOGD(TAG, "handlerSuccessMerchantInfo: bankName=" + bankName);
        String popupMessage = JsonParser.getDataJson(jRoot, "popupMessage");
        Utils.LOGD(TAG, "popupMessage: " + popupMessage);
        // macq + popup show in lib -> fix error macq: double show count down
        if (TextUtils.isEmpty(popupMessage) || ConstantsPay.MPOS_MULTI_ACQUIRER.equals(bankName)) {
            initConfigServer();
        } else {
            String timeShow = com.mpos.sdk.core.control.JsonParser.getDataJson(jRoot, "popupDisplayTime");
            showAlertCountDown(popupMessage, timeShow);
        }
    }

    private void fetchInitData() {
        fetchInitData(1);
    }

    private void fetchInitData(final int counterFetch) {
        PrefLibTV.getInstance(context).setTKL2("");

        if (!isAutoLogin) {
            PrefLibTV.getInstance(context).setBluetoothAddress("");
        }

        if (!GetData.CheckInternet(context)) {
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), context, v -> fetchInitData());
            return;
        }
        logUtil.appendLogRequestApi("INIT");

        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put("readerSerialNo", serialNumber);
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            Utils.LOGD(TAG, "Data 11: " + jo);
            Utils.LOGD(TAG, "Data 22: " + EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getMpK()));
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getMpK()));
        } catch (Exception e) {
            Utils.LOGE(TAG, "init Exception: ", e);
        }

        logUtil.appendLogAction("typeServer=" + PrefLibTV.getInstance(context).getFlagServer() + " typedevice=" + PrefLibTV.getInstance(context).getFlagDevices());
        Utils.LOGD(TAG, "init: " + ConstantsPay.getINIT());

        MposRestClient.getInstance(context).post(context, ConstantsPay.getINIT(), entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        showLoading(true);
                        super.onStart();
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        showLoading(false);
                        logUtil.appendLogRequestApiFail("INIT onFailure", arg2);
                        callbackFailToPartner();
                        MyDialogShow.showDialogRetry(getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                                context, v -> fetchInitData());

                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getMpK()));
                            PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                            loginUser(false);
                        } catch (Exception e) {
                            logUtil.appendLogException("INIT exception:", e.getMessage());
                            Utils.LOGE(TAG, "Exception", e);
                            showLoading(false);
                            showError(R.string.error_try_again);
                        }
                    }
                });
    }

    private void loginUser(boolean haveForceReversal) {
        if (loginActivity.isForceUpdate) {
            showLoading(false);
            return;
        }
        logUtil.appendLogRequestApi("LOGIN: haveForceReversal=" + haveForceReversal + " -- SERIAL=" + serialNumber);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.LOGIN);
            jo.put("readerSerialNo", serialNumber);
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", mAcc);
            jo.put("userPIN", mPin);
            jo.put("appId", "mpos.vn");
            jo.put("forceReversal", haveForceReversal ? 1 : 0); //false

            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getSessionKey()));
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context), entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        showLoading(true);
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            hideLoading();
                            JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                            PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));
                            Utils.LOGD("Login: ", jRoot.toString());
                            if (jRoot.has("error")) {
                                handlerErrorLogin(jRoot);
                            } else {
                                PrefLibTV.getInstance(context).setSerialNumber(serialNumber);
                                PrefLibTV.getInstance(context).setFlagDevices(mFlagDevices);
                                PrefLibTV.getInstance(context).setUserId(mAcc);

                                if (checkHaveForceReversal(jRoot)) {
                                    loginUser(true);
                                    return;
                                }

                                JSONObject jConfig = new JSONObject();
                                JSONObject jNotify;
                                if (jRoot.has("androidNotification")) {
                                    jNotify = new JSONObject(jRoot.getString("androidNotification"));
                                } else {
                                    jNotify = new JSONObject();
                                }

                                try {
                                    if (jRoot.has("readerEncryptMode"))
                                        PrefLibTV.getInstance(context).setReaderEncryptMode(jRoot.getString("readerEncryptMode"));
                                    if (jRoot.has("parameterList"))
                                        PrefLibTV.getInstance(context).setShopName(((JSONObject) jRoot.getJSONArray("parameterList").get(0)).getString("value"));

                                    if (mFlagDevices == ConstantsPay.DEVICE_PINPAD) {
                                        if (jRoot.has("readerConfiguration")) {
                                            readConfigTlvPr01(jRoot.getJSONObject("readerConfiguration"));
                                        } else {
                                            logUtil.appendLogException("ERROR: not found readerConfiguration");
                                        }

                                        PrefLibTV.getInstance(context).setCAKey(jRoot.getBoolean("caPublicKeyExpired"));
                                        if (PrefLibTV.getInstance(context).getCAKey()) {
                                            PrefLibTV.getInstance(context).setCAData(jRoot.getJSONObject("caPublicKey").getJSONArray("keyList").toString());
                                        }
                                    } else {
                                        PrefLibTV.getInstance(context).setCAKey(false);
                                    }
                                } catch (Exception e) {
                                    Utils.LOGE(TAG, "Exception", e);
                                }

                                if (jRoot.has("applicationList")) {
                                    JSONArray jAppList = jRoot.getJSONArray("applicationList");
                                    if (jAppList.length() > 0) {
                                        JSONObject jApp = jAppList.getJSONObject(0);
                                        PrefLibTV.getInstance(context).setMId(JsonParser.getDataJson(jApp, "MID"));
                                        PrefLibTV.getInstance(context).setTId(JsonParser.getDataJson(jApp, "TID"));
                                    }
                                }

                                String ezpk = "";
                                if (jRoot.has("eZPK")) {
                                    ezpk = jRoot.getString("eZPK");
                                }
                                Utils.LOGD(TAG, "ezpk=" + ezpk);
                                PrefLibTV.getInstance(context).setWorkingKey(ezpk);

                                Utils.LOGD(TAG, "------->>: mid=" + PrefLibTV.getInstance(context).getMId());
                                int loginMode = jRoot.getInt("loginMode");
                                Intent i = null;
                                boolean isWaitResult = false;

                                i = new Intent(context, ActivityHomeMart.class);
                                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                                i.putExtra("flag_devices", 2);
//                                i.putExtra("notify", jNotify.toString());

//                                if (PrefLibTV.getInstance(context).getPermitSocket()) {
//                                    logUtil.appendLogAction("isMerchantEmart = true");
//                                    i = new Intent(context, ActivityHomeMart.class);
//                                    i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                                    i.putExtra("flag_devices", 2);
//                                    i.putExtra("notify", jNotify.toString());
//                                } else {
//                                    switch (mFlagDevices) {
//                                        case ConstantsPay.DEVICE_AUDIO:
//                                            try {
//                                                jConfig.put("emv_config", jRoot.getString("readerEMVConfiguration"));
//                                                //not update
//                                                jConfig.put("emv_version", !mEMVVersion.equals(JsonParser.getDataJson(jRoot, "readerEMVConfigurationVersion")));
//                                                if (mFWVersion.equals(jRoot.getString("ARFirmwareVersion"))) {
//                                                    jConfig.put("fw_url", "");
//                                                } else {
//                                                    jConfig.put("fw_url", jRoot.getString("ARFirmwareURL"));
//                                                }
//                                            } catch (Exception e) {
//                                                Utils.LOGE(TAG, "Exception", e);
//                                            }
//
//                                            if (loginMode != 10) {
//                                                i = new Intent(context, ActivityMainNew.class);
//                                                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                                                i.putExtra("flag_devices", 1);
//                                                i.putExtra("config", jConfig.toString());
//                                                i.putExtra("notify", jNotify.toString());
//                                                isWaitResult = false;
//                                            } else {
//                                                i = new Intent(context, ActivityLoginActivation.class);
//                                                i.putExtra("flag_devices", 1);
//                                                i.putExtra("upin", mPin);
//                                                i.putExtra("config", jConfig.toString());
//                                                i.putExtra("notify", jNotify.toString());
//                                                isWaitResult = true;
//                                            }
//                                            break;
//                                        case ConstantsPay.DEVICE_PINPAD:
//                                        case ConstantsPay.DEVICE_DSPREAD:
//                                        case ConstantsPay.DEVICE_P20L:
//                                        case ConstantsPay.DEVICE_KOZEN:
//                                        case ConstantsPay.DEVICE_PAX:
//                                            if (loginMode != 10) {
//                                                i = new Intent(context, ActivityMainNew.class);
//                                                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                                                i.putExtra("flag_devices", 2);
//                                                i.putExtra("notify", jNotify.toString());
//                                                isWaitResult = false;
//                                            } else {
//                                                i = new Intent(context, ActivityLoginActivation.class);
//                                                i.putExtra("flag_devices", 2);
//                                                i.putExtra("notify", jNotify.toString());
//                                                i.putExtra("upin", mPin);
//                                                isWaitResult = true;
//                                            }
//                                            break;
//                                    }
//                                }

                                if (i != null) {
                                    gotoNextScreen(i, isWaitResult);
                                }
                            }
                        } catch (Exception e) {
                            logUtil.appendLogRequestApi("LOGIN Exception(timeout||parse json)");
                            Utils.LOGE(TAG, "Exception", e);
                            showError(R.string.error_try_again);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        logUtil.appendLogRequestApiFail("LOGIN onFailure", arg2);
                        Utils.LOGE(TAG, "Login Error: ", arg3);
                        hideLoading();
                        callbackFailToPartner();
                        showError(R.string.SERVICE_ERROR_REQUEST_TIMEOUT);
                    }
                });
    }

    private void handlerErrorLogin(JSONObject jRoot) {
        String msg = null;
        try {
            final JSONObject jError = jRoot.getJSONObject("error");
            errorCodeBank = jError.getInt("code");
            Utils.LOGD(TAG, "onSuccess: error=" + errorCodeBank + " currNumRunLogin=" + currNumRunLogin + " currBankName=" + currBankName);
            logUtil.appendLogAction("loginUser: error=" + errorCodeBank + " currNumRunLogin=" + currNumRunLogin + " currBankName=" + currBankName);

            if (errorCodeBank == 5010 && currNumRunLogin == 1) { //|| errorCodeBank == 3041
                tryLoginOtherBank(2);
            } else if (errorCodeBank == 10001) {
                msg = getString(R.string.error_10001_login);
            } else {
                msg = LibError.getErrorMsg(errorCodeBank, context);
            }
        } catch (JSONException e) {
            Utils.LOGE(TAG, "Exception", e);
            msg = getString(R.string.error_try_again);
        }
        if (!TextUtils.isEmpty(msg)) {
            logUtil.appendLogRequestApi("LOGIN error return by server:" + msg);
            showError(msg, true);
        }
    }

    private boolean checkHaveForceReversal(JSONObject jRoot) {
        String amount = JsonParser.getDataJson(jRoot, "amount");
        String trxType = JsonParser.getDataJson(jRoot, "trxType");
        Utils.LOGD(TAG, "amount:" + amount + " trxType:" + trxType + "<---");
        logUtil.appendLogAction("- checkHaveForceReversal: trxType=" + trxType + " amount=" + amount);
        if (!TextUtils.isEmpty(amount) && !ConstantsPay.TRX_TYPE_PAY_MOTO.equals(trxType)) {
            dataReversalLogin = new DataReversalLogin();
            dataReversalLogin.amount = amount;
            dataReversalLogin.trxType = trxType;
            dataReversalLogin.pan = JsonParser.getDataJson(jRoot, "PAN");
            dataReversalLogin.itemDesc = JsonParser.getDataJson(jRoot, "itemDesc");
            dataReversalLogin.cardholderName = JsonParser.getDataJson(jRoot, "cardholderName");
            dataReversalLogin.transReqId = JsonParser.getDataJson(jRoot, "transactionRequestID");
            dataReversalLogin.transactionDate = JsonParser.getDataJson(jRoot, "transactionDate");
            String udid = JsonParser.getDataJson(jRoot, "udid");
            dataReversalLogin.paymentIdentify = udid;
            dataReversalLogin.pw = mPin;

            if (TextUtils.isEmpty(dataReversalLogin.itemDesc) && !TextUtils.isEmpty(udid)) {
                int indexCharSplit = udid.lastIndexOf(Constants.CHAR_SPLIT_DESC_UDID);
                if (indexCharSplit > 0) {
                    dataReversalLogin.itemDesc = udid.substring(0, indexCharSplit).replace(Constants.CHAR_REPLACE_SPACE_OF_UDID, " ");
                }
            }
        }
        return false;
    }

    @Override
    public void initConfigServer() {
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(bankName)) {
            processLoginMA();
        } else {
            initConfigServerByBankName(bankName);
        }
    }

    @Override
    public void gotoConfigBasePax() {
        Intent intent = new Intent(context, ActivitySettingSP04.class);
        intent.putExtra(Constants.TYPE_SETTING, Constants.CONFIG_BASE);
        context.startActivity(intent);
    }

    private void initLibLoginHandler() {
        if (libLoginHandler == null) {
            libLoginHandler = new LibLoginHandler(context, new LibLoginHandler.ItfHandlerResultLogin() {
                @Override
                public void showLoading(boolean b) {
                    LoginPresenter.this.showLoading(b);
                }

                @Override
                public void onFailureLogin(@NonNull DataError dataError, int i) {
                    viewer.showErrorLoadMpos(dataError.getErrorCode(), dataError.getMsg());
                }

                @Override
                public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {

                }

                @Override
                public void onSuccessLogin() {
                    gotoNextScreenByMA();
                }
            }, mFlagDevices);
        }

        LanguageCode language;
        if (Locale.getDefault().getLanguage().equalsIgnoreCase(LanguageCode.LANGUAGE_VI.getLanguageCode())) {
            language = LanguageCode.LANGUAGE_VI;
        } else {
            language = LanguageCode.LANGUAGE_EN;
        }
        libLoginHandler.setCustomLanguage(language, true);

        libLoginHandler.setItfHandlerBankSelected(s -> bankName = s);
        libLoginHandler.setHandlerTranWaitSignature(true);
        libLoginHandler.setItfHandlerWaitSignatureMA(
                list -> MyDialogShow.showDialogContinueCancel(getString(R.string.alert_have_reversal_trans_ma),
                        context, false, new MyOnClickListenerView(view -> {
                            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasWaitSignatureMacq, true);
                            gotoNextScreenByMA();
                        })));
        Utils.LOGD(TAG, "initConfigServer: Locale.getDefault().getLanguage()=" + Locale.getDefault().getLanguage());
        logUtil.appendLogAction("language: " + Locale.getDefault().getLanguage());
    }

    private void processLoginMA() {
        initLibLoginHandler();

//        libLoginHandler.loginBankWithConfigMpos(PrefLibTV.getInstance(context).get(PrefLibTV.DATA_LOGIN_CACHE, String.class), serialNumber, mAcc, mPin);
        logUtil.appendLogAction("language: "+Locale.getDefault().getLanguage());
        setupLanguageForLibLogin(libLoginHandler);

//        libLoginHandler.loginBankWithConfigMpos(PrefLibTV.getInstance(context).get(PrefLibTV.DATA_LOGIN_CACHE, String.class), serialNumber, mAcc, mPin);
        libLoginHandler.loginBankWithConfigMpos(PrefLibTV.getInstance(context).getMcConfigCache(), serialNumber, mAcc, mPin);

    }

    private void setupLanguageForLibLogin(@NonNull LibLoginHandler libLoginHandler) {
        LanguageCode language;
        if (Locale.getDefault().getLanguage().equalsIgnoreCase(LanguageCode.LANGUAGE_VI.getLanguageCode())) {
            language = LanguageCode.LANGUAGE_VI;
        }
        else {
            language = LanguageCode.LANGUAGE_EN;
        }
        libLoginHandler.setCustomLanguage(language, true);
    }

    private void gotoNextScreenByMA() {
        PrefLibTV.getInstance(context).setSerialNumber(serialNumber);
        PrefLibTV.getInstance(context).setFlagDevices(mFlagDevices);
//        PrefLibTV.getInstance(context).setUserId( mAcc);
        DataStoreApp.getInstance().setIsUseReader(isUseReader);
        PrefLibTV.getInstance(context).saveBankName(bankName);
        Intent intent = getIntentNextScreen();
        gotoNextScreen(intent, false);
    }

    private void initConfigServerByBankName(String bankName) {
        DataStoreApp.getInstance().setIsUseReader(isUseReader);
        PrefLibTV.getInstance(context).saveBankName(bankName);
        if (isUseReader) {
            Utils.LOGD(TAG, "initConfigServer() called with: bankName = [" + bankName + "]");
            currBankName = bankName;
            logUtil.appendLogAction("initConfigServer:" + bankName);
            ConstantsPay.initApiByBankName(context, bankName);
            Utils.LOGD(TAG, "initConfigServer: flagserver=" + PrefLibTV.getInstance(context).getFlagDevices());
            initBank();
            fetchInitData();
        } else {
            PrefLibTV.getInstance(context).setUserId(mAcc);
            PrefLibTV.getInstance(context).setFlagDevices(ConstantsPay.DEVICE_NONE);
            Intent i = getIntentNextScreen();
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            gotoNextScreen(i, false);
        }
    }

    private Intent getIntentNextScreen() {
        Intent intent;
//        if (PrefLibTV.getInstance(context).getPermitSocket()) {
//            logUtil.appendLogAction("isMerchantEmart = true");
//            intent = new Intent(context, ActivityHomeMart.class);
//        } else {
//            intent = new Intent(context, ActivityMainNew.class);
//        }
        intent = new Intent(context, ActivityHomeMart.class);
        return intent;
    }

    private void initBank() {
        logUtil.appendLogAction("initB:" + PrefLibTV.getInstance(context).getFlagServer());
        ConstantsPay.initUrlServerByServerType(PrefLibTV.getInstance(context).getFlagServer());
    }

    @Override
    public void handlerLoadFailMpos() {
        DataMposCache mposCache = new DataMposCache();
        String cacheBank = DataStoreApp.getInstance().getCacheSerialBank();
        Utils.LOGD(TAG, "handlerLoadFailMpos: cache=" + cacheBank);

        logUtil.appendLogAction("handler load fail mpos: cache=" + cacheBank + " serialNumber=" + serialNumber);
        mposCache.parseDataCache(cacheBank);
        if (serialNumber.equalsIgnoreCase(mposCache.getSerial())) {
            currNumRunLogin = 1;
            initConfigServerByBankName(mposCache.getBank());
        } else {
            tryLoginOtherBank(1);
        }
    }

    private void saveDataCache(String bankName) {
        DataMposCache mposCache = new DataMposCache(serialNumber, bankName);
        DataStoreApp.getInstance().createCacheSerialBank(mposCache.buildDataCache());
        logUtil.appendLogAction("save cache:" + DataStoreApp.getInstance().getCacheSerialBank());
    }

    private void tryLoginOtherBank(int numRun) {
        Utils.LOGD(TAG, "tryLoginOtherBank() called with: numRun = [" + numRun + "]");
        logUtil.appendLogAction(" tryLogB: numRun=" + numRun);
        if (numRun == 1) {
            currNumRunLogin = numRun;
            if (mFlagDevices == ConstantsPay.DEVICE_AUDIO) {
                initConfigServerByBankName(Config.VIETINBANK);
            } else if (mFlagDevices == ConstantsPay.DEVICE_PINPAD) {
                initConfigServerByBankName(Config.SACOMBANK);
            } else if (mFlagDevices == ConstantsPay.DEVICE_DSPREAD) {
                initConfigServerByBankName(Config.SACOMBANK);
            }
        } else if (numRun == 2) {
            currNumRunLogin = numRun;
            initConfigServerByBankName(currBankName.equalsIgnoreCase(Config.VIETINBANK) ? Config.SACOMBANK : Config.VIETINBANK);
        }
    }

    private void gotoNextScreen(Intent i, boolean isWaitResult) {
        if (loginActivity.isForceUpdate) {
            return;
        }
//        saveCachePinpad();
        saveMerchantInfo();
        logUtil.appendLogAction("- goto Next Screen");

        if (dataReversalLogin != null) {
            logUtil.appendLogAction("have dataReversalLogin");
            i.putExtra("dataReversalLogin", dataReversalLogin);
        } else {
            if (isUseReader && !ConstantsPay.MPOS_MULTI_ACQUIRER.equals(bankName)) {
                Utils.LOGD(TAG, "----333-->> call setLastErrorSale");
                PrefLibTV.getInstance(context).setLastErrorSale(LibReaderController.TYPE_ERROR_NONE);
            }
        }

        configUtils.checkActionsCanOfMerchant(context);
        // Integrate
        configUtils.checkIntegrated();

        // need save for sdk payment
        PrefLibTV.getInstance(context).setPW(mPin);
        i.putExtra(Intents.EXTRA_DATA_PARTNER, getDataFromPartner());

        // screen orientation
//        boolean isLandscape = false;
        DataStoreApp.getInstance().createIsLandscape(false);

        logUtil.saveLog();
        if (isWaitResult) {
            loginActivity.startActivityForResult(i, REQUEST_ACTIVATION);
        } else {
            processCacheBank();
            loginActivity.startActivity(i);
            loginActivity.finish();
        }
    }

    private void processCacheBank() {
        PrefLibTV.getInstance(context).setLastErrorSale( LibReaderController.TYPE_ERROR_NONE);
        DataCache.getInstance().putDataCache(bankName, serialNumber, mAcc, encP(mPin), PrefLibTV.getInstance(context).getDataLoginMerchant());
    }

    private String encP(String p) {
        try {
            return EncodeDecode.doAESEncrypt(p, context.getPackageName());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

//    private void saveCachePinpad() {
//        try {
//            if (mFlagDevices == ConstantsPay.DEVICE_PINPAD && mPinpadManager != null) {
//                PrefLibTV.getInstance(context).setBluetoothAddress(mPinpadManager.getBluetoothAddress());
//                PrefLibTV.getInstance(context).setBluetoothName(mPinpadManager.getBluetoothName());
//            } else if (mFlagDevices == ConstantsPay.DEVICE_DSPREAD && !TextUtils.isEmpty(bluetoothAddressDspread)) {
//                PrefLibTV.getInstance(context).setBluetoothAddress(bluetoothAddressDspread);
//
//            }
//        } catch (Exception e) {
//            Utils.LOGE(TAG, "saveCachePinpad:", e);
//        }
//    }

    private void saveMerchantInfo() {
        try {
            String merchantInfo = mAcc + " | " + serialNumber
                    + (TextUtils.isEmpty(bankName) ? "" : " | " + MposUtil.getShortBankName(bankName));
            Utils.LOGD(TAG, "saveMerchantInfo: mcINfo=" + merchantInfo + " bankname: " + bankName);
            DataStoreApp.getInstance().saveMerchantInfo(merchantInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void readConfigTlvPr01(JSONObject jReaderConfiguration) {

        if(jReaderConfiguration==null){
            Utils.LOGW(TAG, "jReaderConfiguration null");
            logUtil.appendLogException("ERROR: jReaderConfiguration null");
            return;
        }
        try {
            JSONArray jInitTagsValuesList = jReaderConfiguration.getJSONArray("initTagsValuesList");
            if (jInitTagsValuesList.length() > 0) {
                String readType;
                JSONObject jItemTemp;
                for (int i = 0, length = jInitTagsValuesList.length(); i < length; i++) {
                    jItemTemp = jInitTagsValuesList.getJSONObject(i);
                    readType = JsonParser.getDataJson(jItemTemp, "readerType");
                    if (String.valueOf(ConstantsPay.DEVICE_PINPAD).equals(readType)) {
                        PrefLibTV.getInstance(context).setTagConfig(jItemTemp.toString());

                        // parse isserValuesList
                        JSONArray jIsserValuesList = jItemTemp.getJSONArray("isserValuesList");
                        if (jIsserValuesList.length() > 0) {
                            JSONObject jTemp;
                            for (int j = 0, lengtIsser = jIsserValuesList.length(); j < lengtIsser; j++) {
                                jTemp = jIsserValuesList.getJSONObject(j);
                                String issuerName = JsonParser.getDataJson(jTemp, "issuerName");
                                if ("MasterCard".equalsIgnoreCase(issuerName)) {
                                    PrefLibTV.getInstance(context).setTagConfigMaster(jTemp.toString());
                                } else if ("VISA".equalsIgnoreCase(issuerName)) {
                                    PrefLibTV.getInstance(context).setTagConfigVisa(jTemp.toString());
                                } else if ("JCB".equalsIgnoreCase(issuerName)) {
                                    PrefLibTV.getInstance(context).setTagConfigJCB(jTemp.toString());
                                }
                            }
                        } else {
                            logUtil.appendLogException("ERROR: jIsserValuesList is null or length == 0");
                        }
                        break;
                    }
                }
            } else {
                logUtil.appendLogException("ERROR: initTagsValuesList is null or length == 0");
            }
        } catch (Exception e) {
            logUtil.appendLogException("ERROR: error parse readConfigTlvPr01>" + e.getMessage());
            Utils.LOGE(TAG, "Exception", e);
        }
    }

    // The headset connection receiver instance
    private final HeadsetReceiver mHeadsetReceiver = new HeadsetReceiver();

    private class HeadsetReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            // state - 0 for unplugged, 1 for plugged.
            boolean headsetAvailable = intent.getIntExtra("state", 0) == 1;
            // microphone - 1 if headset has a microphone, 0 otherwise
            boolean microphoneAvailable = intent.getIntExtra("microphone", 0) == 1;
            isConnectedAR = headsetAvailable && microphoneAvailable;

            logUtil.appendLogAction("result heaset: headset=" + headsetAvailable + " micro=" + microphoneAvailable);
            Utils.LOGD("ActivityLogin", " result heaset: headset=" + headsetAvailable + " micro=" + microphoneAvailable);

            if (isConnectedAR) {
                showToast(R.string.LIGHT_BOX_READER_DETECTED);
//                doTaskConnectAR();
            } else {
                if (isClickSelectDevice) {
                    showToast(R.string.error_time_out_read_audio_please_reconnect);
                }
            }
            isClickSelectDevice = false;
        }
    }

//    private void doTaskConnectAR() {
//        Utils.LOGD(TAG, "----doTaskConnectAR---");
//        if (taskConnectAR != null) {
//            taskConnectAR.cancel(true);
//        }
//        if (mFlagDevices == 0) {
//            mFlagDevices = ConstantsPay.DEVICE_AUDIO;
//        }
//        taskConnectAR = new TaskAudioReaderConnect(this);
//        taskConnectAR.execute();
//    }

//    private static class TaskAudioReaderConnect extends AsyncTask<Void, Void, Void> {
//
//        WeakReference<LoginPresenter> weakReference;
//
//        TaskAudioReaderConnect(LoginPresenter weakReference) {
//            this.weakReference = new WeakReference<>(weakReference);
//        }
//
//        @Override
//        protected synchronized Void doInBackground(Void... params) {
//            Utils.LOGD(TAG, "do bg TaskAudioReaderConnect");
//            if (weakReference == null || weakReference.get().context == null) {
//                return null;
//            }
//            AudioReader.setDebug(Utils.mDEBUG);
//
//            Context context = weakReference.get().context;
//            AudioReader reader = null;
//            try {
//                reader = AudioReaderManager.getReader(context);
//                reader.powerOn();
//                AudioReader.Identification ident = reader.getIdent();
//
//                AudioReader.Battery battery = reader.getBattery();
//                if (battery.level <= 20) {
//                    weakReference.get().showToast(R.string.warning_ar_battery_low);
//                }
//                final String bate = " | " + battery.level + "%";
//                // final String bate = " \nPin: " + (double) battery.volTAGe / 1000 + "V (" + battery.level + "%)";
//                //EMV Version
//                weakReference.get().mEMVVersion = Constants.SVALUE_1;
//                try {
//                    EMVProcessor emvProcessor = new EMVProcessor(reader);
//                    EMVProcessor.ConfigrationParametersResponse configParamsResponse = emvProcessor.getConfigrationParameters();
//                    if (configParamsResponse.result == AudioReader.MessageProcessingResults.OK) {
//                        weakReference.get().mEMVVersion = Integer.toString(configParamsResponse.version);
//                    }
//                } catch (Exception e) {
//                    Utils.LOGE(TAG, "Exception", e);
//                }
//
//                weakReference.get().mFWVersion = ident.version;
//
//                weakReference.get().serialNumber = reader.getSerialNumber();
//
//                if (weakReference.get().mFlagDevices == ConstantsPay.DEVICE_AUDIO) {
////                    setSerialNumber(ConstantsPay.DEVICE_AUDIO, serialNumber + bate);
//                    weakReference.get().runOnUiThread(() -> weakReference.get().showSerialNumber(ConstantsPay.DEVICE_AUDIO, weakReference.get().serialNumber + bate));
//
//                }
//            } catch (Exception e) {
//                weakReference.get().logUtil.appendLogException("Error connect AR: " + e.getMessage());
////                logUtil.appendLogException("Error connect AR: " + e.getMessage() + myAppData.getStackTraceString(e, ""));
//                Utils.LOGE(TAG, "Exception", e);
//                weakReference.get().runOnUiThread(() -> MyDialogShow.showDialogError(context.getString(R.string.error_time_out_read_audio), context));
//            } finally {
//                if (reader != null) {
//                    try {
//                        reader.powerOff();
//                    } catch (IOException e) {
//                        Utils.LOGE(TAG, "Exception", e);
//                    }
//                    reader.close();
//                }
//            }
//
//            return null;
//        }
//    }

    private void runOnUiThread(Runnable runnable) {
        loginActivity.runOnUiThread(runnable);
    }

    /* ***************************************************
     *          communicate with app-partner
     * ***************************************************/
    private DataFromPartner checkHaveDataLoginFromPartner() {
        String dataPartner = getContentFromPartner();
        DataFromPartner dataFromPartner = null;
        if (!TextUtils.isEmpty(dataPartner)) {
            try {
                assert dataPartner != null;
                JSONObject jRoot = new JSONObject(dataPartner);
                dataFromPartner = new DataFromPartner();
                dataFromPartner.parseDataLogin(jRoot);

            } catch (JSONException e) {
                e.printStackTrace();
                showToast(R.string.warning_integration_invaild_data);
            }
        }
        return dataFromPartner;
    }

    private String getContentFromPartner() {
        try {
            boolean isFromHistory = false;
            int flags = intent.getFlags();
            if ((flags & Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY) != 0) {
                isFromHistory = true;
            }
            Utils.LOGD(TAG, "parseDataFromPartner: flags=" + flags + " isFromHistory=" + isFromHistory);
            if (!isFromHistory && intent != null && intent.getData() != null) {
                Uri dataIntent = intent.getData();
//				Uri dataIntent = getIntent().getData();

                String path = dataIntent.getSchemeSpecificPart();
                Utils.LOGD(TAG, "1path:" + path);
                if (path.startsWith("//")) {
                    path = path.substring(2);
                }

                logUtil.appendLogAction("Data from Partner:" + path);

                byte[] decodedString = Base64.decode(path, Base64.DEFAULT);

                String content = new String(decodedString);
                Utils.LOGD(TAG, "2path:" + path + " \ndecode:" + content);
                return content;
            }
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }
        return null;
    }

    private DataFromPartner getDataFromPartner() {
        String contentPartner = getContentFromPartner();
        DataFromPartner dataFromPartner = null;
        if (!TextUtils.isEmpty(contentPartner)) {
            try {
                assert contentPartner != null;
                JSONObject jRoot = new JSONObject(contentPartner);
                dataFromPartner = new DataFromPartner();
                dataFromPartner.parseDataLogin(jRoot);
                dataFromPartner.parseOtherData(jRoot);

                dataFromPartner.setContent(contentPartner);
                DataStoreApp.getInstance().saveOrderIdFromPartner(JsonParser.getDataJson(jRoot, "orderId"));
                DataStoreApp.getInstance().saveExtParamPartner(JsonParser.getDataJson(jRoot, "extParam"));
                DataStoreApp.getInstance().saveUrlCallbackPartner(JsonParser.getDataJson(jRoot, "urlCallBack"));

                if (dataFromPartner.checkCanAutoLogin() && !TextUtils.isEmpty(dataFromPartner.getReaderBluetoothAddress())) {
                    PrefLibTV.getInstance(context).setBluetoothAddress(dataFromPartner.getReaderBluetoothAddress().trim());
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return dataFromPartner;
    }

    private void callbackFailToPartner() {
        DataFromPartner dataPartner = getDataFromPartner();
        if (dataPartner != null) {
            DataError dataError = new DataError(Constants.ERROR_CODE_FAIL_LOAD_DATA);
            dataError.setMsg(getString(R.string.msg_error_load_data, String.valueOf(dataError.getErrorCode())));

            try {
                MposUtil.getInstance().callbackFailToPartner(context, dataError, dataPartner);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
