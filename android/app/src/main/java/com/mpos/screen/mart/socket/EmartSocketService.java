package com.mpos.screen.mart.socket;

import android.text.TextUtils;
import com.mpos.models.DataOrder;
import com.mpos.models.DataVoid;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.sdk.util.Utils;

import java.lang.ref.WeakReference;

public class EmartSocketService extends BaseSocketService implements ISocketServiceWithCallback {

    ItfCallback itfCallback;

    @Override
    void initSocketServer() {
        boolean hasOldInstance = SocketServer.checkExitAnCloseOldInstance();
        Utils.LOGD(TAG, "initSocketServer: hasOldInstance=" + hasOldInstance);
        appendLog(hasOldInstance ? "has oldInstance -> close it" : "has not oldInstance");

        SocketServer.getInstance().initParams(new WeakReference<>(this), logUtil, new EMartPresenter.ItfProcessMsgSocket() {
            @Override
            public void addOrder(DataOrder data, EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onAddOrder(data, in);
                }
            }

            @Override
            public void doCancelOrder(EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onCancelOrder(in);
                }
            }

            @Override
            public void doVoid(DataVoid data, EMartPresenter.ItfSendResultToClient in) {
                beep();
                if (itfCallback != null) {
                    itfCallback.onVoid(data, in);
                }
            }

            @Override
            public void doErrSocket(String msg) {
                if (itfCallback != null) {
                    itfCallback.onErrSocket(msg);
                }
            }
        }, (isConnect, ipClient) -> {
            if (itfCallback != null) {
                if (!TextUtils.isEmpty(ipClient)) {
                    itfCallback.isClientConnected(isConnect, ipClient);
                } else {
                    itfCallback.isClientConnected(isConnect, "");
                }
            }
        });

        SocketServer.getInstance().openConnect();
    }

    @Override
    public void closeTaskSocket() {
        if (SocketServer.getInstance() != null) {
            SocketServer.getInstance().closeTaskSocket();
            itfCallback = null;
        }
    }

    @Override
    public void setItfCallBack(Object callback) {
        this.itfCallback = (ItfCallback) callback;
    }

    public interface ItfCallback {
        void onAddOrder(DataOrder data, EMartPresenter.ItfSendResultToClient in);

        void onCancelOrder(EMartPresenter.ItfSendResultToClient in);

        void onVoid(DataVoid data, EMartPresenter.ItfSendResultToClient in);

        void onErrSocket(String msg);

        void isClientConnected(boolean isConnect, String ipClient);
    }

//    public void setItfCallBack(ItfCallback itfCallback) {
//        this.itfCallback = itfCallback;
//    }
}
