package com.mpos.screen;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.ProgressBar;

import androidx.appcompat.app.AppCompatActivity;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogResult;
import com.mpos.customview.ViewToolBar;
import com.mpos.models.BaseObjJson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.LibErrorMpos;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;

import org.json.JSONObject;

import java.nio.charset.StandardCharsets;

import butterknife.BindView;
import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class ActivityEnterCardWebView extends AppCompatActivity {

    String tag = "ActivityEnterCardWebView";

	ViewToolBar vToolBar;
	
	@BindView(R.id.progress_bar)		protected ProgressBar progressBar;
	@BindView(R.id.ib_home)		protected ImageButton ibHome;
	@BindView(R.id.webView1)		protected WebView webview;

	private MyProgressDialog mPgdl;
	SaveLogController logUtil;

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_enter_card_webview);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());
		logUtil = MyApplication.self().getSaveLogController();

		vToolBar = new ViewToolBar(this, findViewById(R.id.container));
		vToolBar.showTextTitle(getString(R.string.title_enter_card));
		vToolBar.showButtonCancel(true, v -> finish());
		
		mPgdl = new MyProgressDialog(this);
		
		ButterKnife.bind(this);

		progressBar.getIndeterminateDrawable().setColorFilter(getResources()
				.getColor(R.color.white), PorterDuff.Mode.SRC_IN);

		ibHome.setOnClickListener(v -> onBackPressed());

		String url = getIntent().getStringExtra(Constants.LINK_WEBVIEW);
		String udid = getIntent().getStringExtra(Constants.KEY_MVISA_UDID);
		if (!TextUtils.isEmpty(url)) {
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
				webview.getSettings().setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
			webview.getSettings().setJavaScriptEnabled(true);
			webview.clearCache(true);
			webview.setWebViewClient(new WebViewClient() {
				@Override
				public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
					view.loadUrl(url);
					progressBar.setVisibility(View.VISIBLE);
					ibHome.setVisibility(View.GONE);
					return true;
				}

				@Override
				public void onPageStarted(WebView view, String url, Bitmap favicon) {
					super.onPageStarted(view, url, favicon);
					progressBar.setVisibility(View.VISIBLE);
					ibHome.setVisibility(View.GONE);
                    Log.e("----URL REDIRECT----", url);
                    if (url.contains("v1/paynow/notification.html")) {
                        // error or pending
						vToolBar.setVisible(View.GONE);
                        Uri uri = Uri.parse(url);
                       	String errorCode = uri.getQueryParameter("errorCode");
                       	String card_fullname = uri.getQueryParameter("card_fullname");
                       	String card_number = uri.getQueryParameter("card_number");
                       	String card_type = uri.getQueryParameter("card_type");
                       	String message = uri.getQueryParameter("message");
                       	String card_email = uri.getQueryParameter("card_email");
                       	String card_phone = uri.getQueryParameter("card_phone");

                       	if (!"SUCCESS".equals(errorCode)) {
                            String msgShow = TextUtils.isEmpty(message) ? "" : (new String(message.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
							if ("480".equals(errorCode) || "481".equals(errorCode)) {
								// pending
								updateTransactionStatus(udid, "PAYMENT_PENDING", card_fullname, card_type, card_number, errorCode, msgShow, card_email, card_phone);
							} else {
								// error
								updateTransactionStatus(udid, "FAIL", card_fullname, card_type, card_number, errorCode, msgShow, card_email, card_phone);
							}
						} else {
                       		// success
							webview.stopLoading();
							DialogResult dialog = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, "", null, null);
							dialog.setCancelable(false);
							dialog.setEnterCard(true);
							dialog.setAmountEnterCard(uri.getQueryParameter("amount"));
							dialog.setTxIDEnterCard(uri.getQueryParameter("transaction_id"));
							dialog.show(getSupportFragmentManager(), DialogResult.class.getName());
						}
					} else if (url.contains("v1/paynow/notification-success.html")) {
					    // success
						webview.stopLoading();
                        Uri uri = Uri.parse(url);
						DialogResult dialog = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, "", null, null);
						dialog.setCancelable(false);
						dialog.setEnterCard(true);
						dialog.setAmountEnterCard(uri.getQueryParameter("amount"));
						dialog.setTxIDEnterCard(uri.getQueryParameter("transaction_id"));
						dialog.show(getSupportFragmentManager(), DialogResult.class.getName());
                    } else if (url.contains("v1/paynow/notification-error.html")) {
						// back home
						Intent returnIntent = new Intent();
						setResult(Activity.RESULT_OK, returnIntent);
						finish();
					} else if (url.contains("v1/paynow/notification-pending.html")) {
                    	// open pending screen
						Intent intent = new Intent(ActivityEnterCardWebView.this, ActivityResultPending.class);
						startActivityForResult(intent, 1001);
					}
				}

				@Override
				public void onPageFinished(WebView view, String url) {
					super.onPageFinished(view, url);
					progressBar.setVisibility(View.GONE);
					ibHome.setVisibility(View.VISIBLE);
				}

				@Override
				public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
					super.onReceivedError(view, request, error);
					progressBar.setVisibility(View.GONE);
					ibHome.setVisibility(View.VISIBLE);
				}
			});
			webview.loadUrl(url);
		}
	}

	private void updateTransactionStatus(String udid, String status, String cardName, String cardType, String cardNumber, String errorCode, String errorMsg, String cardEmail, String cardMobile) {
		logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS);
		StringEntity entity = null;
		try {
			JSONObject jo = new JSONObject();
			jo.put(Constants.STR_SERVICE_NAME, Config.UPDATE_TRANSACTION_STATUS);
			jo.put("paymentMethod", "LINKCARD");
			jo.put("udid", udid);
			jo.put("status", status);
			jo.put("cardHolderName", new String((cardName == null ? "" : cardName).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
			jo.put("cardType", cardType);
			jo.put("cardNumber", cardNumber);
			jo.put("errorCode", errorCode);
			jo.put("errorMsg", errorMsg);
			jo.put("cardEmail", cardEmail);
			jo.put("cardMobile", cardMobile);

			Utils.LOGD(tag, "UPDATE_TRANSACTION_STATUS | REQ: " + jo);

			entity = new StringEntity(jo.toString());
		} catch (Exception e) {
			Utils.LOGE(tag, "Exception", e);
		}

		MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
			@Override
			public void onStart() {
				mPgdl.showLoading();
				super.onStart();
			}

			@Override
			public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
				logUtil.appendLogRequestApiFail(Config.UPDATE_TRANSACTION_STATUS + " onFailure", arg2);
				mPgdl.hideLoading();
			}

			@Override
			public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
				mPgdl.hideLoading();
				String msgError = null;
				BaseObjJson errorBean = null;
				try {
					JsonParser jsonParser = new JsonParser();
					errorBean = new BaseObjJson();
					JSONObject jRoot = new JSONObject(new String(arg2));
					Utils.LOGD(tag, "-UPDATE_TRANSACTION_STATUS:" + jRoot);
					jsonParser.checkHaveError(jRoot, errorBean);
					if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
						logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " success");
					} else {
						msgError = LibErrorMpos.getErrorMsg(ActivityEnterCardWebView.this, errorBean.code);
						logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " error:" + msgError);
					}
				} catch (Exception e) {
					logUtil.appendLogRequestApi(Config.UPDATE_TRANSACTION_STATUS + " Exception:" + e.getMessage());
					msgError = getString(R.string.error_try_again);
					Utils.LOGE(tag, "Exception", e);
				}
				if (!TextUtils.isEmpty(msgError)) {
					if (errorBean != null && "55000".equals(errorBean.code) || "1004".equals(errorBean.code)) {
						MyDialogShow.showDialogError("", msgError, ActivityEnterCardWebView.this, false, v -> {

						});
					}
				}
			}
		});
	}

	@Override
	protected void onActivityResult(int requestCode, int resultCode, Intent data) {
		super.onActivityResult(requestCode, resultCode, data);
		Utils.LOGD(tag, "onActivityResult: requestCode=" + requestCode + " resultCode=" + resultCode);
		if (requestCode == 1001 && resultCode == RESULT_OK) {
			// back tu man hinh webview enter card
			Intent returnIntent = new Intent();
			setResult(Activity.RESULT_OK, returnIntent);
			finish();
		}
	}

//	@Override
//	public void onBackPressed() {
//		if (webview != null && webview.canGoBack()) {
//			webview.goBack();
//		} else {
//			super.onBackPressed();
//		}
//	}

}
