package com.mpos.screen;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;

import androidx.fragment.app.Fragment;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.MyApplication;
import com.mpos.customview.ViewToolBar;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.model.ChangePass;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.HttpStatus;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class FragmentChangePin extends BaseFragment{

    public static String TAG = "FragmentChangePin";

    private View v;
	@BindView(R.id.user_pin)				protected EditText edtUserPin;
	@BindView(R.id.user_pin_new)			protected EditText edtUserPinNew;
	@BindView(R.id.user_pin_new_retype)	protected EditText edtUserPinRe;

	MyProgressDialog mPgdl;
	ToastUtil mToast;
	
	String oldPin, newPin;
	private String user;

	public static Fragment newInstance() {
		FragmentChangePin fragment = new FragmentChangePin();
		Bundle args = new Bundle();
		fragment.setArguments(args);
		return fragment;
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
		v = inflater.inflate(R.layout.fragment_change_pin, container, false);

		mPgdl = new MyProgressDialog(context);
		mToast = new ToastUtil(context);
		
		initView();

		return v;
	}

	private void initView() {
		ButterKnife.bind(this, v);

        ViewToolBar vToolbar = new ViewToolBar(context, v);
        vToolbar.showTextTitle(getString(R.string.change_pass));
        vToolbar.showButtonBack(true);

		edtUserPinRe.setOnEditorActionListener((v, actionId, event) -> {
            if(actionId == EditorInfo.IME_ACTION_DONE){
                startRunChangePin();
                return true;
            }
            return false;
        });
	}

    private void clearDataInput() {
        edtUserPin.setText("");
        edtUserPinNew.setText("");
        edtUserPinRe.setText("");
    }

    private void saveNewPass(String newPin) {
        PrefLibTV.getInstance(context).setPW( newPin);
    }


    @OnClick({R.id.btn_continue, R.id.cancel})
	public void onClick(View v){
		switch (v.getId()) {
		case R.id.btn_continue:
			startRunChangePin();
			break;
		case R.id.cancel:
			
			break;
		default:
			break;
		}
	}
	
	private void startRunChangePin() {
		if(validateField()){
            if (MyApplication.self().isRunMA()) {
                if (TextUtils.isEmpty(user)) {
                    user = PrefLibTV.getInstance(context).getUserId();
                }
                changePinMA(user, newPin, oldPin);
            }
            else {
                changePin(oldPin, newPin);
            }
		}
	}
	
	private boolean validateField() {
		boolean flag = true;
		oldPin = edtUserPin.getText().toString();
		newPin = edtUserPinNew.getText().toString();
		String rePin = edtUserPinRe.getText().toString();
		if (oldPin.equals("") || newPin.equals("") || rePin.equals("")) {
			mToast.showToast(getString(R.string.ALERT_MISSING_FIELD_TITLE));
			flag = false;
		}
		else if (!newPin.equals(rePin)) {
			mToast.showToast(getString(R.string.CHANGE_PIN_LBL_PIN_NOT_MATCH));
			flag = false;
		}
		else if (newPin.length() < 6) {
			mToast.showToast(getString(R.string.CHANGE_PIN_LBL_INSTRUCTION));
			flag = false;
		}

		return flag;
	}
	
	public void changePin(String oldPin, String newPin) {
		StringEntity entity = null;

		try {
			JSONObject jo = new JSONObject();
			jo.put(Constants.STR_SERVICE_NAME, Config.CHANGE_PASSWORD);
			jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
			jo.put("udid", "0");
			jo.put("versionNo", android.os.Build.VERSION.RELEASE);
			jo.put("platform", Config.PLATFORM);
			jo.put("userID", PrefLibTV.getInstance(context).getUserId());
			jo.put("userPIN", oldPin);
			jo.put("newPIN", newPin);
			jo.put("sessionKey", PrefLibTV.getInstance(context).getSessionKey());

			Utils.LOGD("Data: ", jo.toString());
			entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(context).getSessionKey()));
		}
        catch (Exception e) {
			Utils.LOGE(TAG, "Exception", e);
		}

		MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
//				MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context),
				entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
			
			@Override
			public void onStart() {
				mPgdl.showLoading("");
				super.onStart();
			}
			@Override
			public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
				try {
					mPgdl.hideLoading();
					JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
					PrefLibTV.getInstance(context).setSessionKey( response.getString("sessionKey"));
					Utils.LOGD("Login: ", response.toString());
					if (response.has("error")) {
						try {
							final JSONObject jo = response.getJSONObject("error");
							String msg = getString(R.string.error) + " " + String.format("%02d", jo.getInt("code"))
									+ ": " + LibError.getErrorMsg(jo.getInt("code"), context);
							MyDialogShow.showDialogError(msg, context);
						} catch (JSONException e) {
							Utils.LOGE(TAG, "Exception", e);
						}
					} else {
                        processUpdateSuccessPin(newPin);
                    }
				} catch (Exception e) {
					Utils.LOGE(TAG, "Exception", e);
					MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), context);
				}
			}

			@Override
			public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
				Utils.LOGE("Login Error: ", arg3.getMessage());
				mPgdl.hideLoading();
				MyDialogShow.showDialogError(getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), context);
			}
		});
	}

    private void processUpdateSuccessPin(String newPin) {
        MyDialogShow.showDialogSuccess(context, getString(R.string.change_pin), false);
        clearDataInput();
//        if (PrefLibTV.getIsSaveLogin(context)) {
            saveNewPass(newPin);
//        }
    }

    private void changePinMA(String user, String pass, String oldPin) {
        StringEntity entity = null;
        try {
            ChangePass changePass = new ChangePass(user, pass, pass, oldPin);
//            changePass.setUsername(user);
//            changePass.setNewPassword(pass);
//            changePass.setConfirmPassword(pass);
//            changePass.setPassword(oldPin);
            Utils.LOGD(TAG, "DataClear: "+ MyGson.getGson().toJson(changePass));

            String cipherText = CryptoInterface.getInstance().encryptData(MyGson.getGson().toJson(changePass));

            DataBaseObj dataBaseObj = new DataBaseObj(cipherText);
            entity = new StringEntity(MyGson.getGson().toJson(dataBaseObj));
        } catch (Exception e) {
            e.printStackTrace();
        }

        MposRestClient.getInstance(context).put(context, ApiMultiAcquirerInterface.URL_CHANGE_PASS, entity, ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(context) {
            @Override
            public void onStart() {
                mPgdl.showLoading("");
                super.onStart();
            }
            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "changePinMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, false);
                clearDataInput();
//                MyDialogShow.showDialogError(getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), context);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "changePinMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());
                if (statusCode == HttpStatus.SC_OK) {
                    Utils.LOGD(TAG, "onSuccess changePinMA: " + clearData);
                    processUpdateSuccessPin(newPin);
                } else {
                    DataError dataError = new DataError();
                    dataError.build(statusCode, clearData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                    MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
                }
            }
        });
    }
}
