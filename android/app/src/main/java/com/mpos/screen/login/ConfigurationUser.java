package com.mpos.screen.login;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.models.DataIntegrated;
import com.mpos.models.QrPayResponse;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class ConfigurationUser {

    private static final String TAG = "ConfigurationUser";

//    private JSONObject jEvents = null;
    private JSONObject jConfigIntegration = null;
    private JSONObject jMerchantConfig = null;
    private Context context;

    //todo hardCode flag permit QR emart
    private String permitQrNl = Constants.SVALUE_0;
    private String permitVietQrNl = Constants.SVALUE_0;
    private String permitQrMomoNl = Constants.SVALUE_0;    // QR MOMO NL
    private String permitPayZaloQr = Constants.SVALUE_0;    // QR MOMO NL

    public void readConfigFromMpos(Context context, JSONObject jRoot) throws JSONException {
        this.context = context;
        readConfigQrCode(jRoot);

        checkUpgradeEmvConfig(jRoot);

        // update firmware pr02
        checkUpgradeFwPr02(jRoot);

        checkConfigAffiliate(jRoot);

        checkConfigRethink(jRoot);

        // config fee installment
        DataStoreApp.getInstance().saveAllowChangeFee(JsonParser.getDataJson(jRoot, "checkFeeChange", Constants.SVALUE_0));
        DataStoreApp.getInstance().saveFeeInstallment(JsonParser.getDataJson(jRoot, "checkFeeInstallment", Constants.SVALUE_0));
        DataStoreApp.getInstance().saveFeeTrans(JsonParser.getDataJson(jRoot, "checkFeeTrans", Constants.SVALUE_0));
        DataStoreApp.getInstance().saveIsReceiptFromServer(JsonParser.getDataJson(jRoot, "skipSignature", Constants.SVALUE_0));

        DataStoreApp.getInstance().saveMandatoryCheckLimit(JsonParser.getDataJson(jRoot, "mandatoryCheckLimit", Constants.SVALUE_0));

        String signatureMinAmount = JsonParser.getDataJson(jRoot, "signatureMinAmount", ConstantsPay.VALUE_DEF_NEGATIVE_1);
        PrefLibTV.getInstance(context).setMaxAmountSignature( signatureMinAmount);

        PrefLibTV.getInstance(context).put(PrefLibTV.allowDeposit, JsonParser.getDataJson(jRoot, "allowDeposit", Constants.SVALUE_0));
        //todo test type deposit
//        PrefLibTV.getInstance(context).put(PrefLibTV.allowDeposit, Constants.SVALUE_1);
        try {
            int intValue = jRoot.getInt("maxDaySettleDeposit");
            PrefLibTV.getInstance(context).put(PrefLibTV.maxDaySettleDeposit, intValue);
        } catch (Exception e) {
            e.printStackTrace();
        }


        jMerchantConfig = jRoot;
//        if (jRoot.has("events")) {
//            JSONArray jArrEvent = jRoot.getJSONArray("events");
//            if (jArrEvent != null && jArrEvent.length() > 0) {
//                jEvents = jArrEvent.getJSONObject(0);
//            }
//        }

        if (jRoot.has("config")) {
            jConfigIntegration = jRoot.getJSONObject("config");
            Utils.LOGD(TAG, "readConfigFromMpos: jConfigIntegration=" + jConfigIntegration);
            String connectType = JsonParser.getDataJson(jConfigIntegration, "connectType");
            DataStoreApp.getInstance().saveConnectType(connectType);

            if (connectType.equals("4")) {
                PrefLibTV.getInstance(context).savePermitSocket(true);
                PrefLibTV.getInstance(context).setEnableReceiverCancelOrder(true);

//                String hasPermitCachePreSale = JsonParser.getDataJson(jConfigIntegration, "appPresaleCache", Constants.SVALUE_0);
//                PrefLibTV.getInstance(context).put(PrefLibTV.hasPermitCachePreSale, Constants.SVALUE_1.equals(hasPermitCachePreSale));
            }
            else {
                saveEmartOff();
            }

            String permitVoidSocket = JsonParser.getDataJson(jConfigIntegration, "permitVoidSocket");
            DataStoreApp.getInstance().savePermitVoidSocket(permitVoidSocket);

            String permitPayGiftCard = JsonParser.getDataJson(jConfigIntegration, "permitPayGiftCard");
            PrefLibTV.getInstance(context).setPermitPayGiftCard(permitPayGiftCard);

            //config QR NL
            permitQrNl = JsonParser.getDataJson(jConfigIntegration, "permitQrNl");
            DataStoreApp.getInstance().savePermitQrNl(permitQrNl);

            permitQrMomoNl = JsonParser.getDataJson(jConfigIntegration, "permitQrMomoNl");        // QR MOMO NL
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.permitQrMomoNl, permitQrMomoNl);

            permitPayZaloQr = JsonParser.getDataJson(jConfigIntegration, "permitQrZaloNl");
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.permitQrZaloNl, permitPayZaloQr);

            String qrNlSiteCode = JsonParser.getDataJson(jConfigIntegration, "qrNlSiteCode");
            DataStoreApp.getInstance().setQrNlSiteCode(qrNlSiteCode);

            String qrNlPassCode = JsonParser.getDataJson(jConfigIntegration, "qrNlPassCode");
            DataStoreApp.getInstance().setQrNlPassCode(qrNlPassCode);

            String binLocals = JsonParser.getDataJson(jConfigIntegration, "binLocals");
            PrefLibTV.getInstance(context).setBinLocals( binLocals);
        }
        else {
            jConfigIntegration = null;
            DataStoreApp.getInstance().saveConnectType("");
            saveEmartOff();
        }
        checkVersionBinLocal(context, jRoot);
    }

    private void saveEmartOff() {
        PrefLibTV.getInstance(context).savePermitSocket(false);
//        PrefLibTV.getInstance(context).put(PrefLibTV.hasPermitCachePreSale, Constants.SVALUE_0);
    }

    private void checkConfigAffiliate(JSONObject jRoot) {
        if (jRoot.has("optCheckAffiliate")) {
            try {
                int intValue = jRoot.getInt("optCheckAffiliate");
                DataStoreApp.getInstance().createDataCheckAffiliate(intValue);
                Utils.LOGD(TAG, "checkAffiliate: optCheckAffiliate="+DataStoreApp.getInstance().getDataCheckAffiliate());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkAffiliate: optCheckAffiliate error= ",e);
            }
        }
        if (jRoot.has("affiliateImage")) {
            try {
                String affiliateImage = jRoot.getString("affiliateImage");
                DataStoreApp.getInstance().createDataLinkAffiliateCover(affiliateImage);
                Utils.LOGD(TAG, "checkAffiliate: affiliateImage="+DataStoreApp.getInstance().getDataLinkAffiliateCover());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkAffiliate: affiliateImage error= ",e);
            }
        }
        if (jRoot.has("affiliateLink")) {
            try {
                String affiliateLink = jRoot.getString("affiliateLink");
                DataStoreApp.getInstance().createDataLinkAffiliateEvent(affiliateLink);
                Utils.LOGD(TAG, "checkAffiliate: affiliateLink="+DataStoreApp.getInstance().getDataLinkAffiliateEvent());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkAffiliate: affiliateLink error= ",e);
            }
        }
    }

    private void checkUpgradeEmvConfig(JSONObject jRoot) {
        boolean update = false;
        if (jRoot != null && jRoot.has("upgradeEMVConfig")) {
            try {
                update = jRoot.getBoolean("upgradeEMVConfig");
                String urlEmvApp = "";
                if (jRoot.has("urlEmvApp")) {
                    urlEmvApp = jRoot.getString("urlEmvApp");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvApp, urlEmvApp);
                String urlEmvCapk = "";
                if (jRoot.has("urlEmvCapk")) {
                    urlEmvCapk = jRoot.getString("urlEmvCapk");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlEmvCapk, urlEmvCapk);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, update);
    }

    private void checkUpgradeFwPr02(JSONObject jRoot) {
        boolean upgrade = false;
        if (jRoot != null && jRoot.has("isFirmwareUpdate")) {
            try {
                upgrade = jRoot.getBoolean("isFirmwareUpdate");
                String urlFw = "";
                if (upgrade && jRoot.has("linkFirmwareUpdate")) {
                    urlFw = jRoot.getString("linkFirmwareUpdate");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.urlUpgradeFw, urlFw);
                String versionFw = "";
                if (upgrade && jRoot.has("versionFirmwareUpdate")) {
                    versionFw = jRoot.getString("versionFirmwareUpdate");
                }
                PrefLibTV.getInstance(context).put(PrefLibTV.versionFwUpgrade, versionFw);
            } catch (JSONException e) {
                e.printStackTrace();
                upgrade = false;
            }
        }
        // test
//        upgrade = true;
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeFw, upgrade);
    }

    private void readConfigQrCode(JSONObject jRoot) throws JSONException {
        //note: for mVISA
        if (jRoot.has("merchantId")) {
            String merchantId = JsonParser.getDataJson(jRoot, "merchantId");
            DataStoreApp.getInstance().saveMerchantId(merchantId);
        }
        if (jRoot.has("mvisaMid")) {
            String mvisaMid = JsonParser.getDataJson(jRoot, "mvisaMid");
            DataStoreApp.getInstance().saveMvisaMid(mvisaMid);
            DataStoreApp.getInstance().setIsMerchantRegistedQR(Boolean.TRUE);
        }
        else {
            DataStoreApp.getInstance().setIsMerchantRegistedQR(Boolean.FALSE);
        }

        if (jRoot.has("isNormalPayLink")) {
            String isNormalPayLink = JsonParser.getDataJson(jRoot, "isNormalPayLink");
            if ("1".equals(isNormalPayLink)) {
                DataStoreApp.getInstance().setIsNormalPayLink(Boolean.TRUE);
            } else {
                DataStoreApp.getInstance().setIsNormalPayLink(Boolean.FALSE);
            }
        } else {
            DataStoreApp.getInstance().setIsNormalPayLink(Boolean.FALSE);
        }

        if (jRoot.has("mvisaMidMaster")) {
            String mvisaMidMaster = JsonParser.getDataJson(jRoot, "mvisaMidMaster");
            DataStoreApp.getInstance().saveMvisaMidMaster(mvisaMidMaster);
        }

        String sendEmailMerchant = JsonParser.getDataJson(jRoot, "sendTrxReceipt", Constants.SVALUE_1);
        PrefLibTV.getInstance(context).put(PrefLibTV.sendTrxReceipt, Constants.SVALUE_1.equals(sendEmailMerchant));

//        DataStoreApp.getInstance().saveSendEmailMerchant(Constants.SVALUE_1.equals(sendEmailMerchant));

        if (jRoot.has("emailMerchant")) {
            String emailMerchant = JsonParser.getDataJson(jRoot, "emailMerchant");
            DataStoreApp.getInstance().saveEmailMerchant(emailMerchant);
        }

        if (jRoot.has("merchantName")) {
            String merchantNameShortcut = JsonParser.getDataJson(jRoot, "merchantName");
            DataStoreApp.getInstance().saveMerchantNameShortcut(merchantNameShortcut);
        }

        List<QrPayResponse> listQrPayResponse = new ArrayList<>();
        if (jRoot.has("listQr")) {
            JSONArray jArr = jRoot.getJSONArray("listQr");
            Type listType = new TypeToken<List<QrPayResponse>>() {}.getType();
            listQrPayResponse = new Gson().fromJson(jArr.toString(), listType);
        }
//        DataStoreApp.getInstance().saveHaveQrInternational(listQrPayResponse);
        DataStoreApp.getInstance().saveListQrPay(listQrPayResponse);

        if (jRoot.has("listBankQR")) {
            JSONArray jArr = jRoot.getJSONArray("listBankQR");
            DataStoreApp.getInstance().saveHaveListQrSource(jArr.length() > 0);
        } else {
            DataStoreApp.getInstance().saveHaveListQrSource(false);
        }

        if (jRoot.has("listInternationalQR")) {
            JSONArray jArr = jRoot.getJSONArray("listInternationalQR");
            DataStoreApp.getInstance().saveHaveListQrInternational(jArr.length() > 0);
        } else {
            DataStoreApp.getInstance().saveHaveListQrInternational(false);
        }

        DataStoreApp.getInstance().saveTerminalId(JsonParser.getDataJson(jRoot, "mvisaTid", ""));

        DataStoreApp.getInstance().saveMerchantCategoryCode(JsonParser.getDataJson(jRoot, "mcc"));
        Utils.LOGD(TAG, "onSuccess: MerchantCategoryCode=" + DataStoreApp.getInstance().getMerchantCategoryCode());
        //end mVisa


        String isShowVimoQr = JsonParser.getDataJson(jRoot, "isShowVimoQr", "");
        DataStoreApp.getInstance().setIsShowVimoQR(Constants.SVALUE_1.equals(isShowVimoQr));
    }


    public void checkActionsCanOfMerchant(Context context) {
        Utils.LOGD(TAG, "### checkActionsCanOfMerchant");
        boolean canSaleService;
        boolean canAutoLogin;
        boolean autoSelectApp = Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isAutoSelectApp", "0"));
        String isSaleService = JsonParser.getDataJson(jMerchantConfig, "isSaleService");
        String isSaveLogin = JsonParser.getDataJson(jMerchantConfig, "isSaveLogin");
        Utils.LOGD(TAG, "### isSaveLogin: "+isSaveLogin);
        String restrictInternationalCard = JsonParser.getDataJson(jMerchantConfig, "restrictInternationalCard");
        String restrictEmvInternationalCard = JsonParser.getDataJson(jMerchantConfig, "emvRestrictInternationalCard");
        String emailMerchant = JsonParser.getDataJson(jMerchantConfig, "emailMerchant");

        PrefLibTV.getInstance(context).setMerchantId( JsonParser.getDataJson(jMerchantConfig, "merchantId"));
//                        DataStoreApp.getInstance().createMerchantName(JsonParser.getDataJson(jMerchantConfig, "merchantName"));

        canSaleService = Constants.SVALUE_1.equals(isSaleService);
        canAutoLogin   = Constants.SVALUE_1.equals(isSaveLogin);
        PrefLibTV.getInstance(context).setIsSaveLogin( canAutoLogin);

        PrefLibTV.getInstance(context).setEmailMerchant( emailMerchant);
        PrefLibTV.getInstance(context).setRestrictInternationalCard( Constants.SVALUE_1.equals(restrictInternationalCard));
        PrefLibTV.getInstance(context).setEmvRestrictInternationalCard( Constants.SVALUE_1.equals(restrictEmvInternationalCard));

        PrefLibTV.getInstance(context).put(PrefLibTV.BUSINESS_NAME, JsonParser.getDataJson(jMerchantConfig, "businessName"));
        PrefLibTV.getInstance(context).put(PrefLibTV.BUSINESS_ADDRESS, JsonParser.getDataJson(jMerchantConfig, "businessAddress"));

        DataStoreApp.getInstance().createCanSaleService(canSaleService);
        DataStoreApp.getInstance().createHaveCashback(checkCashbackInfo(jMerchantConfig));
        DataStoreApp.getInstance().createHaveInstallment(checkHaveInstallment(jMerchantConfig));
        DataStoreApp.getInstance().createAutoSelectApp(autoSelectApp);
        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isShowTransactionHistory,
                Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isShowTransactionHistory", Constants.SVALUE_1)));
        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isConfirmPassword,
                Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isConfirmPassword", Constants.SVALUE_0)));

        // CONVERT VIMO
        if (jMerchantConfig != null && jMerchantConfig.has("optConvertVimo")) {
            try {
                int intValue = jMerchantConfig.getInt("optConvertVimo");
                DataStoreApp.getInstance().createDataConvertVimo(intValue);
                Utils.LOGD(TAG, "optConvertVimo: optConvertVimo=" + DataStoreApp.getInstance().getDataConvertVimo());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "optConvertVimo: optConvertVimo error= ", e);
            }
        }
        if (jMerchantConfig != null && jMerchantConfig.has("optConvertVimoAmount")) {
            try {
                double doubleValue = jMerchantConfig.getDouble("optConvertVimoAmount");
                DataStoreApp.getInstance().createDataConvertVimoAmount(doubleValue);
                Utils.LOGD(TAG, "optConvertVimoAmount: optConvertVimoAmount=" + DataStoreApp.getInstance().getDataConvertVimoAmount());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "optConvertVimoAmount: optConvertVimoAmount error= ", e);
            }
        }

        // BANNER
        if (jMerchantConfig != null && jMerchantConfig.has("listBanner")) {
            try {
                String stringListBanner = jMerchantConfig.getString("listBanner");
                DataStoreApp.getInstance().createDataBanner(stringListBanner);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        // QUICK WITHDRAWAL
        if (jMerchantConfig!=null && jMerchantConfig.has("quickWithdrawInfo")) {
            try {
                JSONObject jQuickWithdrawalInfo = new JSONObject(jMerchantConfig.getString("quickWithdrawInfo"));

                String amountMin = JsonParser.getDataJson(jQuickWithdrawalInfo, "amountMin", "0");
                String amountMax = JsonParser.getDataJson(jQuickWithdrawalInfo, "amountMax", "0");
                DataStoreApp.getInstance().saveAmountMinQuick(amountMin);
                DataStoreApp.getInstance().saveAmountMaxQuick(amountMax);

                if (jQuickWithdrawalInfo.has("jsonQuickWithdrawList")) {
                    JSONArray jListQuickDraw = jQuickWithdrawalInfo.getJSONArray("jsonQuickWithdrawList");
                    DataStoreApp.getInstance().saveQuickWithdrawList(jListQuickDraw.toString());
                }

//                String flatFee = JsonParser.getDataJson(jQuickWithdrawalInfo, "flatFee", "0");
//                String percenTAGeFee = JsonParser.getDataJson(jQuickWithdrawalInfo, "percenTAGeFee", "0");
//                DataStoreApp.getInstance().saveVatQuickWithdrawal(flatFee);
//                DataStoreApp.getInstance().savePercentQuickWithdrawal(percenTAGeFee);

                Utils.LOGD(TAG, " amountMin: " + amountMin + " amountMax: " + amountMax); // "flatFee: " + flatFee + " percenTAGeFee: " + percenTAGeFee +
                DataStoreApp.getInstance().saveEnableQuickDrawal(true);
            } catch (JSONException e) {
                Utils.LOGE(TAG, "Exception", e);
                DataStoreApp.getInstance().saveEnableQuickDrawal(false);
            }
        } else {
            DataStoreApp.getInstance().saveEnableQuickDrawal(false);
        }

        boolean isDisableCheckGps = false;
        if (jMerchantConfig!=null && jMerchantConfig.has("isDisableCheckGps")) {
            isDisableCheckGps = Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isDisableCheckGps"));
        }
        DataStoreApp.getInstance().createDisableCheckGps(isDisableCheckGps);

        boolean enableNormalPay = Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "enableNormalPayment", Constants.SVALUE_1));
        Utils.LOGD(TAG, "checkActionsCanOfMerchant: enableNormalPayment="+enableNormalPay);
        DataStoreApp.getInstance().saveEnableNormalPayment(enableNormalPay);

        DataStoreApp.getInstance().createShowNew(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"isShowNew")));
        DataStoreApp.getInstance().createShowChangePass(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"isShowChangePassword")));
        DataStoreApp.getInstance().createShowInstallment(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"isShowInstallmentPayment")));
        DataStoreApp.getInstance().createShowOTA(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"OTAPayout")));
        DataStoreApp.getInstance().createShowUnivestLink(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"isUnivestLink")));
        DataStoreApp.getInstance().createDataVaymuonInstallment(JsonParser.getDataJson(jMerchantConfig, "installmentVaymuonInfo"));
        DataStoreApp.getInstance().createDataVaymuonExchange(JsonParser.getDataJson(jMerchantConfig, "exchangeVaymuonInfo"));

        if (jMerchantConfig!=null && jMerchantConfig.has("isShowNormalPayment")) {
            DataStoreApp.getInstance().createShowNormalPayment(Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig,"isShowNormalPayment")));
        }

        // feedback
        boolean canFeedback   = Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isFeedback"));
        DataStoreApp.getInstance().saveCanFeedback(canFeedback);
        DataStoreApp.getInstance().saveFeedbackAmountMin(JsonParser.getDataJson(jMerchantConfig, "feedbackAmountMin"));
        DataStoreApp.getInstance().saveMobileUserPhone(JsonParser.getDataJson(jMerchantConfig, "mobileUserPhone"));

        boolean canMotoMacq = Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "isPayMoto", ""));
        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isPayMotoMacq, canMotoMacq);

        // bin range
        String blockBinRange = JsonParser.getDataJson(jMerchantConfig, "blockPinRange", ConstantsPay.VALUE_DEF_EMPTY);
//        "500000-599999|200000-299999|400000-499999|300000-399999";
        Utils.LOGD(TAG, "readMerchantConfig: BLOCK_PAN_RANGE=" + blockBinRange);
        PrefLibTV.getInstance(context).put(PrefLibTV.BLOCK_PAN_RANGE, blockBinRange);

        PrefLibTV.getInstance(context).setPermitVoid( Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "permitVoid", Constants.SVALUE_0)));
        PrefLibTV.getInstance(context).setPermitSettlement( Constants.SVALUE_1.equals(JsonParser.getDataJson(jMerchantConfig, "permitSettlement", Constants.SVALUE_0)));

    }

    private boolean checkCashbackInfo(JSONObject jRoot) {
        if (jRoot!=null && jRoot.has(Constants.FIELD_CASHBACK_INFO)) {
            try {
                JSONObject jCashBack = jRoot.getJSONObject(Constants.FIELD_CASHBACK_INFO);
                DataStoreApp.getInstance().createCashBackId(JsonParser.getDataJson(jCashBack,"cashbackProgramId"));
                DataStoreApp.getInstance().createCashBackName(JsonParser.getDataJson(jCashBack,"name"));
                return true;
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkCashbackInfo: ",e);
            }
        }
        return false;
    }

    private boolean checkHaveInstallment(JSONObject jRoot) {

        boolean result = false;
        if (jRoot!=null && jRoot.has("installmentInfo")) {
            try {
                JSONArray jArr = jRoot.getJSONArray("installmentInfo");
                if (jArr.length()>0) {
//                    DataStoreApp.getInstance().createDataInstallment(jArr.toString());
//                    Utils.LOGD(TAG, "checkHaveInstallment: installmentInfo="+DataStoreApp.getInstance().getDataInstallment());
                    PrefLibTV.getInstance(context).put(PrefLibTV.installmentInfo, jArr.toString());
                    Utils.LOGD(TAG, "checkHaveInstallment: installmentInfo="+PrefLibTV.getInstance(context).get(PrefLibTV.installmentInfo, String.class));

                    result = true;
                }
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: ",e);
            }
        }

        if (result && jRoot.has("exchangeInfo")) {
            try {
                JSONArray jArr = jRoot.getJSONArray("exchangeInfo");
                if (jArr.length()>0) {
                    DataStoreApp.getInstance().createDataExchangeInfo(jArr.toString());
                    Utils.LOGD(TAG, "checkHaveInstallment: exchangeInfo="+DataStoreApp.getInstance().getDataExchangeInfo());
                }
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: exchangeInfo error= ",e);
            }
        }

        if (result && jRoot.has("exchangeLinkCardInfo")) {
            try {
                JSONArray jArr = jRoot.getJSONArray("exchangeLinkCardInfo");
                if (jArr.length() > 0) {
                    DataStoreApp.getInstance().createDataExchangeLinkInfo(jArr.toString());
                    Utils.LOGD(TAG, "checkHaveInstallment: exchangeLinkCardInfo=" + DataStoreApp.getInstance().getDataExchangeLinkInfo());
                    result = true;
                }
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: exchangeLinkCardInfo error= ", e);
            }
        }

        if (result && jRoot.has("optCheckInstallmentBin")) {
            try {
                int intValue = jRoot.getInt("optCheckInstallmentBin");
                DataStoreApp.getInstance().createDataCheckBinInstallment(intValue);
                Utils.LOGD(TAG, "checkHaveInstallment: optCheckInstallmentBin="+DataStoreApp.getInstance().getDataCheckBinInstallment());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: optCheckInstallmentBin error= ",e);
            }
        }

        if (result && jRoot.has("isPayLink")) {
            try {
                int intValue = jRoot.getInt("isPayLink");
                DataStoreApp.getInstance().createDataCheckIsPayLink(intValue);
                Utils.LOGD(TAG, "checkHaveInstallment: isPayLink="+DataStoreApp.getInstance().getDataCheckIsPayLink());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: isPayLink error= ",e);
            }
        }

        if (result && jRoot.has("isPayCard")) {
            try {
                int intValue = jRoot.getInt("isPayCard");
                DataStoreApp.getInstance().createDataCheckIsPayCard(intValue);
                Utils.LOGD(TAG, "checkHaveInstallment: isPayCard="+DataStoreApp.getInstance().getDataCheckIsPayCard());
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: isPayCard error= ",e);
            }
        }

        if (jRoot!=null && jRoot.has("isSupplierPromotion")) {
            try {
                int intValue = jRoot.getInt("isSupplierPromotion");
                DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isShowPromotion, intValue==1);
                Utils.LOGD(TAG, "checkHaveInstallment: isPromotion=" + DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowPromotion, Boolean.class, false));
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveInstallment: isPromotion error= ",e);
            }
        }
        return result;
    }

    public void checkIntegrated() {
        if (jConfigIntegration != null) {
            checkConfigIntegrate(jConfigIntegration);
        } else {
            MyApplication.self().setDataIntegrated(null);
            DataStoreApp.getInstance().clearIntegrateData();
        }
    }

    private void checkConfigIntegrate(JSONObject jConfig) {
        Utils.LOGD(TAG, "checkConfigIntegrate---->");

        DataIntegrated dataIntegrated = new DataIntegrated();

        String connectType = JsonParser.getDataJson(jConfig, "connectType");

        dataIntegrated.setPreType(connectType);
        dataIntegrated.setPostType(connectType);

//        logUtil.appendLogAction("- checkConfigIntegrate---->connType="+connectType);

        DataStoreApp.getInstance().createIntergratSecrectKey(JsonParser.getDataJson(jConfig, "secretKey"));
        DataStoreApp.getInstance().createIntergratLogoUrl(JsonParser.getDataJson(jConfig, "logoUrl"));
        DataStoreApp.getInstance().createMerchantName(JsonParser.getDataJson(jConfig, "merchantName"));

        Utils.LOGD(TAG, "------------------------------");

        Utils.LOGD(TAG, "checkConfigIntegrate");
        Utils.LOGD(TAG, "jConfig: "+jConfig.toString());
        Utils.LOGD(TAG, ">>> myAppData.setDataIntegrated(dataIntegrated)");
        MyApplication.self().setDataIntegrated(dataIntegrated);
    }

//    void checkEvent() {
//        if (jEvents != null) {
//            DataEvent dataEvent = new DataEvent();
//            dataEvent.typeAction = JsonParser.getDataJson(jEvents, "urlAction");
//            dataEvent.title = JsonParser.getDataJson(jEvents, "title");
//            dataEvent.desc = JsonParser.getDataJson(jEvents, "description");
//            dataEvent.name = JsonParser.getDataJson(jEvents, "name");
//            dataEvent.url = JsonParser.getDataJson(jEvents, "url");
//            i.putExtra("dataEvent", dataEvent);
//            MyApplication.self().setHaveEvent(true);
//        } else {
//            MyApplication.self().setHaveEvent(false);
//        }
//    }

    private void checkConfigRethink(JSONObject jObject) {
        boolean enableRethinkdb = false;
        if (jObject.has("enableRethinkdb")) {
            try {
                enableRethinkdb = jObject.getBoolean("enableRethinkdb");
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.enableRethink, enableRethinkdb);
        if (enableRethinkdb) {
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkHostName, JsonParser.getDataJson(jObject, "rethinkHostName"));
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkPort, JsonParser.getDataJson(jObject, "rethinkPort", "0"));
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkDbName, JsonParser.getDataJson(jObject, "rethinkDbName"));
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkUsername, JsonParser.getDataJson(jObject, "rethinkUsername"));
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkUserPassword, JsonParser.getDataJson(jObject, "rethinkUserPassword"));
            DataStoreApp.getInstance().saveDataByKey(DataStoreApp.rethinkAutoPay, Constants.SVALUE_1.equals(JsonParser.getDataJson(jObject, "isAutoPay", Constants.SVALUE_1)));
        }

    }

    private void checkVersionBinLocal(Context context, JSONObject jRoot) {
        int version = 0;
        String sVersion = JsonParser.getDataJson(jRoot, "versionBinLocal", Constants.SVALUE_0);
        if (TextUtils.isDigitsOnly(sVersion)) {
            version = Integer.parseInt(sVersion);
        }
        PrefLibTV.getInstance(context).put(PrefLibTV.serverVerBinLocal, version);
    }
}
