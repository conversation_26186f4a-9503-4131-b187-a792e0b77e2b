package com.mpos.screen.mart;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.net.ConnectivityManager;
import android.net.DhcpInfo;
import android.net.Network;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.IBinder;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.MyPresenter;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogResult;
import com.mpos.models.*;
import com.mpos.screen.ActivityHomeEnter;
import com.mpos.screen.ActivityPaymentHistory;
import com.mpos.screen.FlutterTransferActivity;
import com.mpos.screen.login.LoginActivity;
import com.mpos.screen.mart.socket.BaseSocketService;
import com.mpos.screen.mart.socket.EmartSocketService;
import com.mpos.screen.mart.socket.SocketServerTakashimaya;
import com.mpos.screen.mart.socket.TakashimayaSocketService;
import com.mpos.screen.printer.LibPrinterMpos;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.DownloadConfigReader;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.LibInjectKey;
import com.mpos.sdk.core.control.LibPax;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.control.MposTransactionsMacq;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.GiftCardInfor;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.model.UserCard;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.DownloadReceiptRes;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.*;
import com.pax.emvdemo.manager.clss.CardReaderHelper;
import com.pps.core.ScreenUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.ByteOrder;
import java.text.SimpleDateFormat;
import java.util.*;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

import static android.app.Activity.RESULT_OK;
import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static android.content.Context.WIFI_SERVICE;
import static com.mpos.screen.ActivityPrePayment.RC_PAY;
import static com.mpos.screen.FlutterTransferActivity.ROUTE_DEPOSIT;
import static com.mpos.sdk.util.ConstantsPay.FORMAT_UDID_EMART_SDK;
import static com.mpos.screen.mart.socket.SocketServer.ADD_ORDER;
import static com.mpos.screen.mart.socket.SocketServer.CANCEL_ORDER;
import static com.mpos.screen.mart.socket.SocketServer.UPDATE_TRANS;

public class EMartPresenter extends MyPresenter implements EMartContract.Presenter {

    private static final String TAG = EMartPresenter.class.getSimpleName();

    public Context context;
    private final EMartContract.View viewer;
    ActivityHomeMart activity;

    private int deviceType;

    private final SaveLogController logUtil;

    ItfSendResultToClient itfSendResultToClient;
    ItfSendResultToClient itfSendResultCancelToClient;

    DataOrder dataOrderPresent;
    DataVoid dataVoidPayment;
    ArrayList<DataStatusOrder> dataCacheOrders = new ArrayList<>();
    DataPay dataPay;
    DataPrePayEMart dataPrePayEMart;
    ResultPayWrapper resultPay;
    DialogResult dialogResult;

    public static final String ORDER_CODE_SUCCESS = "00";
    public static final String ORDER_CODE_CANCEL = "-1";
    public static final String ORDER_CODE_FAIL = "-3";
    public static final String ORDER_CODE_NOT_FINISH_PREVIOUS_PAYMENT = "-2";

    public static final String ORDER_CODE_CANCEL_TAKA = "0001";
    public static final String ORDER_CODE_NOT_FINISH_PREVIOUS_PAYMENT_TAKA = "0002";
    public static final String ORDER_CODE_FAIL_TAKA = "0003";

    public static final String METHOD_RESIGN = "RESIGN"; // status ký lại

    // stastus callback
    public static final String RESIGN           = "RESIGNED";
    public static final String CANCEL           = "CANCELED";
    public static final String FAIL             = "FAILED";
    public static final String APPROVED         = "APPROVED";
    public static final String UNABLE_CANCEL    = "UNABLE_CANCEL";

    public static String nameFilterActionPayment = "vn.mpos.emart_action_payment";

    public static final String EMART_VOUCHER_CARD = "EMART_VOUCHER_CARD";

    private SocketState stateSocket = SocketState.OFFLINE;
    protected boolean isRunMacq = false;
    protected boolean isShowScreenGiftCard = false;
    protected boolean isGotoHomeEnter = false;
    private boolean permitGotoInputAmount = true;
    MposTransactions mposTransactions;
    private Timer timerCheckNetwork;

    public static final String SUCCESS = "0000";
    public static final String INVALID_CARD_NUMBER = "0003";

    private int TYPE_LOGOUT = 0;
    private int TYPE_SETTING = 1;

    private Intent intentServiceSocket;
    private String actionPayment = "";

    boolean isCallbackDataToClient = false;

    MyReceiver myReceiver;

    Dialog dialogUpgrade;
    LibPrinterMpos libPrinter;
    String maskPan; // only Pax

    public EMartPresenter(Context context, EMartContract.View viewer, ActivityHomeMart activity) {
        super(context);
        this.context = context;
        this.viewer = viewer;
        this.activity = activity;

        this.viewer.setPresenter(this);
        checkRunMacq();
        deviceType = PrefLibTV.getInstance(context).getFlagDevices();
        logUtil = MyApplication.self().getSaveLogController();
        logUtil.saveLog();
    }

    ConnectivityManager cm;
    WifiManager wifiManager;

    @Override
    public void start() {
        Utils.LOGD(TAG, "presenter start");
        logUtil.appendLogAction("presenter start");
        checkNeedAutoInjectKey();

        cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        wifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);

        initServiceSocket();

        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class) && DataStoreApp.getInstance().getIsAutoGotoCashierActivity()) {
//            gotoScreenCashier();
            viewer.onClickGotoPushPayment();
        }
    }

    /*
    *    EMV Config
    */
    public void checkUpgradeEmvConfigPax() {
        boolean needUpdateConfig = PrefLibTV.getInstance(context).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE);
        if (needUpdateConfig) {
            logUtil.appendLogAction("not exist emvConfig or upgradeEMVConfig");
            Utils.LOGD(TAG, "not exist Emv Config or need upgradeEMVConfig");
            fetchEmvConfigPax();

//            int numSkipUpdate = getNumSkipUpdate();
//            boolean hideCancel = numSkipUpdate <= 0;
//            String nameBtnClose = numSkipUpdate > 0 ? String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSkipUpdate) : "";
//            String msg = getString(R.string.warning_have_upgrade_evm_config);
//
//            showDialogWarningUpdateReader(msg, hideCancel, nameBtnClose, view -> fetchEmvConfigPax(), view -> {
//                setNumSkipUpdate(numSkipUpdate - 1);
//            });
        }
    }

    private int getNumSkipUpdate() {
        return PrefLibTV.getInstance(context).get(PrefLibTV.numSaleRemain, Integer.class, com.mpos.sdk.util.Constants.NUM_SALE_REMAIN_DEFAULT);
    }

    private void setNumSkipUpdate(int data) {
        PrefLibTV.getInstance(context).put(PrefLibTV.numSaleRemain, data);
    }

    DownloadConfigReader downloadConfigReader;
    private void fetchEmvConfigPax() {
        if (!GetData.CheckInternet(context)) {
            logUtil.appendLogRequestApi("fetchEmvConfigPax err because no internet");
//            MyDialogShow.showDialogRetry(getString(R.string.check_internet), context, v -> fetchEmvConfigPax());
            return;
        }
        logUtil.appendLogRequestApi("fetchEmvConfigPax");
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(context, new DownloadConfigReader.ItfProcessDownloadConfigPax() {
                @Override
                public void showLoading(boolean show) {
//                    viewer.showLoading("");
                }

                @Override
                public void onErrorDownloadConfig(String msg) { 
                    Utils.LOGD(TAG, "onFailure: " + msg);
                    logUtil.appendLogRequestApi("fetchEmvConfigPax onFailure--" +msg);
//                    viewer.hideLoading();
//                    MyDialogShow.showDialogError(getString(R.string.msg_error_load_data_no_param), context);
                }

                @Override
                public void onSuccessDownloadConfigPax(String config) {
                    logUtil.appendLogAction("onSuccessDownloadConfigPax: " + serialNumber);
                    Utils.LOGD(TAG, "onSuccessDownloadConfigPax= " + config);
                    PrefLibTV.getInstance(context).saveEmvConfigStorage(config);
                    tickUpdateEmvConfigSuccess();
                }

                @Override
                public void appendLog(String log) {
                    appendLogEmvConfig(log);
                }
            });
        }
        downloadConfigReader.processDownloadConfig();
    }

    private void appendLogEmvConfig(String log) {
        logUtil.appendLogAction("U_EMV_CONFIG " + log );
    }

    public void tickUpdateEmvConfigSuccess() {
        logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS+" sn="+serialNumber);
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, false);

        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPGRADE_EMV_CONFIG_SUCCESS);
            jo.put("readerSerial", serialNumber);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            Utils.LOGD("Data: ", jo.toString());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "updateCAKey: "+ e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
//                        viewer.showLoading("");
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
//                        viewer.hideLoading();
                        try {
                            JsonParser jsonParser = new JsonParser();
                            BaseObjJson errorBean = new BaseObjJson();
                            JSONObject jRoot = new JSONObject(new String(arg2));
                            jsonParser.checkHaveError(jRoot, errorBean);
                            Utils.LOGD(TAG, "onSuccess: "+ jRoot);
                            if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                                PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, false);
//                                MyDialogShow.showDialogInfo(context, getString(R.string.update_config_reader_success));
//                                closeDialogUpgrade();
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "Exception", e);
                            logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS +" error:"+e.getMessage());
//                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), context);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//                        viewer.hideLoading();
                        Utils.LOGE(TAG, "confirm payment Error: ", arg3);
                        logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS+" error: request time out");
//                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), context,
//                                v -> tickUpdateEmvConfigSuccess(), true);
                    }
                });
    }

    private void showDialogWarningUpdateReader(String msg, boolean hideCancel, String nameBtnClose, View.OnClickListener onClickOk, View.OnClickListener onClickCancel) {
        dialogUpgrade = new Dialog(context, R.style.SpecialDialog);
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(LAYOUT_INFLATER_SERVICE);
        View dialogLayout = inflater.inflate(R.layout.dialog_upgrade_fw_reader, null);

        ((TextView) dialogLayout.findViewById(R.id.tv_description)).setText(msg);

        dialogLayout.findViewById(R.id.btn_update_now).setOnClickListener(v -> {
            if (onClickOk != null) {
                onClickOk.onClick(v);
            }
            dialogUpgrade.dismiss();
        });

        Button btnClose = dialogLayout.findViewById(R.id.btn_close);
        if (hideCancel) {
            btnClose.setVisibility(View.GONE);
        }
        else {
            btnClose.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(nameBtnClose)) {
                btnClose.setText(nameBtnClose);
            }
            btnClose.setOnClickListener(v -> {
                if (onClickCancel != null) {
                    onClickCancel.onClick(v);
                }
                dialogUpgrade.dismiss();
            });
        }

        dialogUpgrade.setCancelable(false);
        dialogUpgrade.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialogUpgrade.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;

        dialogUpgrade.setCanceledOnTouchOutside(false);
        dialogUpgrade.setContentView(dialogLayout);
        dialogUpgrade.show();
    }

    private void closeDialogUpgrade() {
        if (dialogUpgrade != null) {
            dialogUpgrade.dismiss();
        }
    }

    private void initServiceSocket() {
        Utils.LOGD(TAG, "initServiceSocket");
        registerReceiver();
        //Start service
        startServiceSocket();
    }

//    SocketService mService;
    BaseSocketService mService;
    private final ServiceConnection mServiceConnection = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
//            SocketService.LocalBinder binder = (SocketService.LocalBinder) service;
            BaseSocketService.LocalBinder binder = (BaseSocketService.LocalBinder) service;
            mService = binder.getService();

            Log.d(TAG, "onServiceConnected: ");
            logUtil.appendLogAction("onServiceConnected: ");

            if (mService instanceof EmartSocketService) {
                ((EmartSocketService) mService).setItfCallBack(new EmartSocketService.ItfCallback() {
                    @Override
                    public void onAddOrder(DataOrder data, ItfSendResultToClient in) {
                        processAddOrder(data, in);
                    }

                    @Override
                    public void onCancelOrder(ItfSendResultToClient in) {
                        processCancelTrans(in);
                    }

                    @Override
                    public void onVoid(DataVoid data, ItfSendResultToClient in) {
                        processVoid(data, in);
                    }

                    @Override
                    public void onErrSocket(String msg) {
                        closeDialogUpgrade();
                        processErrorSocket(msg);
                    }

                    @Override
                    public void isClientConnected(boolean isConnect, String ipClient) {
                        viewer.updateClientConnect(isConnect, ipClient);
                    }
                });
            } else if (mService instanceof TakashimayaSocketService) {
                ((TakashimayaSocketService) mService).setItfCallBack(new TakashimayaSocketService.ItfCallback() {
                    @Override
                    public void onAddOrder(DataOrderTakashimaya data, ItfSendResultToClient in) {
                        handlerAddOrderTaka(data, in);
                    }

                    @Override
                    public void onCancelOrder(ItfSendResultToClient in) {
                        processCancelTrans(in);
                    }

                    @Override
                    public void onErrSocket(String msg) {
                        closeDialogUpgrade();
                        processErrorSocket(msg);
                    }

                    @Override
                    public void isClientConnected(boolean isConnect, String ipClient) {
                        viewer.updateClientConnect(isConnect, ipClient);
                    }
                });
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            // service is off when turn off screen and hold on 10 mins => but socket server still exit => receiver/push socket normally
            Utils.LOGD(TAG, "onServiceDisconnected: " + name.getClassName());
            logUtil.appendLogAction("onServiceDisconnected");
        }
    };

    private void processCancelTrans(ItfSendResultToClient in) {
        itfSendResultCancelToClient = in;
        Utils.LOGD(TAG, "processCancelTrans");
        appendLog("processCancelTrans");
        // todo handler call sdk
        Utils.LOGD(TAG, "sendEventCloseOtherActivities");
        Intent intentState = new Intent(Intents.nameFilterActionCancelOrder);
        intentState.putExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, true);
        context.sendBroadcast(intentState);
    }

    private void processErrorSocket(String msg) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
        Utils.LOGD(TAG, "socket err: " + msg);
        logUtil.appendLogException("socket err: " + msg);
        logUtil.pushLog();
    }

    private void perpareHandlerProcess() {
        dismissDialogResult();
        MyDialogShow.dismissCurrDialog();
        hideScreenGiftCard();
        closeDialogUpgrade();
    }

    private void processVoid(DataVoid data, ItfSendResultToClient in) {
        itfSendResultToClient = in;
        perpareHandlerProcess();
        isGotoHomeEnter = false;
        isCallbackDataToClient = false;

        if (!checkIsFinishPayment()) {
            Utils.LOGD(TAG, "processVoid: ->not finish previous payment");
            logUtil.appendLogAction("processVoid: ->not finish previous payment");
            String msgError = buildDataErrNotFinishPreviousPayment(data.getOrderId());
            callbackTrans(msgError);
            return;
        }
        sendEventCloseOtherActivities();

        String permitVoidSocket = DataStoreApp.getInstance().getPermitVoidSocket();
        Utils.LOGD(TAG, "processVoid: "  + ", permitVoidSocket= " + permitVoidSocket);
        logUtil.appendLogAction("processVoid: "  + ", permitVoidSocket= " + permitVoidSocket);
        if (permitVoidSocket.equals(Constants.SVALUE_1) && (data.getTransCode() != null && !TextUtils.isEmpty(data.getTransCode()))) {
            handlerVoidTrans(data);
        } else {
            String msgResult = buildDataErr(data.getOrderId());
            itfSendResultToClient.onSendResultToClient(msgResult);
            runOnUIThread(() -> Toast.makeText(context, context.getString(R.string.msg_permit_void_socket), Toast.LENGTH_SHORT).show());
        }
    }

    private void handlerAddOrderTaka(DataOrderTakashimaya data, ItfSendResultToClient in) {
        itfSendResultToClient = in;
        perpareHandlerProcess();
        if (isGotoHomeEnter) {
            //todo callback
            handleFailTransaction(new DataError(-7, context.getString(R.string.msg_service_not_ready)));
            return;
        }

        isGotoHomeEnter = false;
        permitGotoInputAmount = false;
        isCallbackDataToClient = false;

        logUtil.appendLogAction("processAddOrder typePayment: " + data.getTXN_TYPE());

        if (data.getTXN_TYPE().equals(SocketServerTakashimaya.TXN_TRANSFER)) {
            handlerPaymentQrNL(Long.parseLong(data.getAMOUNT()), data.getBILL_ID(), Constants.TYPE_PAY_QR_VNPAY);
        } else if (data.getTXN_TYPE().equals(SocketServerTakashimaya.TXN_VOID)) {
            DataVoid dataVoid = new DataVoid();
            dataVoid.setTransCode(data.getINVOICE());
            dataVoid.setOrderId(data.getINVOICE());
            dataVoid.setConfirmVoid(1);
            processVoid(dataVoid, in);
        } else if (data.getTXN_TYPE().equals(SocketServerTakashimaya.TXN_SALE)) {
            if (!checkIsFinishPayment()) {
                Utils.LOGD(TAG, "processAddOrder: ->not finish previous payment");
                logUtil.appendLogAction("processAddOrder: ->not finish previous payment");
                String msgError = buildDataErrNotFinishPreviousPayment(data.getBILL_ID());
                callbackTrans(msgError);
                return;
            }
            sendEventCloseOtherActivities();
            Utils.LOGD(TAG, "serviceName order= " + data.getBILL_ID());
            payment(data.getBILL_ID(), Long.parseLong(data.getAMOUNT()), data.getPRINT_MSG());
        } else {
            String msgResult = buildDataErr("");
            itfSendResultToClient.onSendResultToClient(msgResult);
        }
    }

    private void processAddOrder(DataOrder data, ItfSendResultToClient in) {
        itfSendResultToClient = in;
        perpareHandlerProcess();
        if (isGotoHomeEnter) {
            //todo callback
            handleFailTransaction(new DataError(-7, context.getString(R.string.msg_service_not_ready)));
            return;
        }

        isGotoHomeEnter = false;
        permitGotoInputAmount = false;
        isCallbackDataToClient = false;

        if ((data.typePayment != null) && (data.typePayment.equals(Constants.TYPE_PAY_QR_VNPAY))) {
            handlerPaymentQrNL(data.getTotalAmount(), data.getOrderId(), Constants.TYPE_PAY_QR_VNPAY);
        } else if ((data.typePayment != null) && (data.typePayment.equals(Constants.TYPE_PAY_QR_MOMO))) {
            handlerPaymentQrNL(data.getTotalAmount(), data.getOrderId(), Constants.TYPE_PAY_QR_MOMO);
        } else if ((data.typePayment != null) && (data.typePayment.equals(Constants.TYPE_PAY_QR_ZALOPAY_NL))) {
            handlerPaymentQrNL(data.getTotalAmount(), data.getOrderId(), Constants.TYPE_PAY_QR_ZALOPAY_NL);
        } else {
            logUtil.appendLogAction("processAddOrder typePayment= Card");
            handlerNormalPayment(data);
        }
    }

    private void handlerPaymentQrNL(long totalAmount, String orderID, String typeQR) {
        dataPrePayEMart = null;
        logUtil.appendLogAction("handlerPaymentQrNl " + typeQR + "-" + totalAmount);
//        MyDialogShow.dismissCurrDialog();

        int limitAmountQR = 5000;
        String permitQr = Constants.SVALUE_0;
        if (typeQR.equals(Constants.TYPE_PAY_QR_VNPAY)) {
            permitQr = DataStoreApp.getInstance().getPermitQrNl();
        } else if (typeQR.equals(Constants.TYPE_PAY_QR_MOMO)) {
            permitQr = DataStoreApp.getInstance().getDataByKey(DataStoreApp.permitQrMomoNl ,String.class);
            limitAmountQR = 2000;
        } else if (typeQR.equals(Constants.TYPE_PAY_QR_ZALOPAY_NL)) {
            permitQr = DataStoreApp.getInstance().getDataByKey(DataStoreApp.permitQrZaloNl ,String.class);
            limitAmountQR = 2000;
        }

        if (TextUtils.isEmpty(permitQr) || permitQr.equals(Constants.SVALUE_0)) {
            handleFailTransaction(new DataError(-1, context.getString(R.string.msg_alert_not_support_qr_nl)));
            MyDialogShow.showDialogError("", context.getString(R.string.msg_alert_not_support_qr_nl), context, false, view -> Utils.LOGD(TAG, context.getString(R.string.msg_alert_not_support_qr_nl)));
            return;
        }

        if (totalAmount < limitAmountQR) {
            handleFailTransaction(new DataError(-1, context.getString(R.string.msg_alert_invalid_amount_qr_nl, String.valueOf(limitAmountQR))));
            MyDialogShow.showDialogError("", context.getString(R.string.msg_alert_invalid_amount_qr_nl, String.valueOf(limitAmountQR)), context, false, view -> Utils.LOGD(TAG, context.getString(R.string.msg_alert_invalid_amount_qr_nl)));
        } else {
            gotoActivityScanQrNl(String.valueOf(totalAmount), orderID, typeQR);
        }
    }

    private void gotoActivityScanQrNl(String amount ,String orderID, String typeQR) {
        logUtil.appendLogAction("gotoActivityScanQr");
        Intent intent = new Intent(context, ActivityQrEmart.class);
        intent.putExtra(Constants.TYPE_QR_NL, typeQR);
        intent.putExtra(Constants.AMOUNT_QR, amount);
        intent.putExtra(Constants.ORDER_CODE, orderID);
        context.startActivity(intent);
    }

    private void handlerNormalPayment(DataOrder data) {
        if (!checkIsFinishPayment()) {
            Utils.LOGD(TAG, "processAddOrder: ->not finish previous payment");
            logUtil.appendLogAction("processAddOrder: ->not finish previous payment");
            String msgError = buildDataErrNotFinishPreviousPayment(data.getOrderId());
            callbackTrans(msgError);
            return;
        }
//        MyApplication.self().getMposSdk().setHandleState(nameFilterActionPayment);
        sendEventCloseOtherActivities();
        Utils.LOGD(TAG, "serviceName order= " + data.getServiceName());
        logUtil.appendLogAction("serviceName = " + data.getServiceName());

        initCacheDataOrder();
        handlerRevicedOrder(data);
    }

    @Override
    public String loadIpSocketServer() {
        String ipAddress = "";

//        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        Network[] networks = cm.getAllNetworks();

        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        if (activeNetwork == null) {
            return ipAddress;
        }

        if (activeNetwork.getType() == ConnectivityManager.TYPE_ETHERNET) {
            try {
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface networkInterface = networkInterfaces.nextElement();
                    Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                    while (inetAddresses.hasMoreElements()) {
                        InetAddress inetAddress = inetAddresses.nextElement();
                        if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                            ipAddress = inetAddress.getHostAddress();
                            // Địa chỉ IP của thiết bị trong mạng LAN|
                            break;
                        }
                    }
                }
            } catch (SocketException e) {
                e.printStackTrace();
            }
        } else {
//            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
            int ip = wifiManager.getConnectionInfo().getIpAddress();
            // Convert little-endian to big-endianif needed
            if (ByteOrder.nativeOrder().equals(ByteOrder.LITTLE_ENDIAN)) {
                ip = Integer.reverseBytes(ip);
            }
            byte[] ipByteArray = BigInteger.valueOf(ip).toByteArray();
            try {
                ipAddress = InetAddress.getByAddress(ipByteArray).getHostAddress();
            } catch (UnknownHostException ex) {
                Utils.LOGE(TAG, "Unable to get host address: " + ex.getMessage());
            }

//            final DhcpInfo dhcp = wifiManager.getDhcpInfo();
//            final String address = Formatter.formatIpAddress(dhcp.gateway);
        }

        return ipAddress;
    }

    private void initCacheDataOrder() {
        String date_cache_emart = DataStoreApp.getInstance().getDateCacheEmart();
        String currentDate = Utils.convertTimestamp(System.currentTimeMillis(), 2);
        Utils.LOGD(TAG, "day_cache_emart=" + date_cache_emart + " --- currentDate= " + currentDate);
        logUtil.appendLogAction("day_cache_emart= " + date_cache_emart + " --- currentDate= " + currentDate);

        if (date_cache_emart.equals(currentDate)) {
            // cache data success is used
            String json = DataStoreApp.getInstance().getDataEmart();
            if (!TextUtils.isEmpty(json)) {
                Type type = new TypeToken<ArrayList<DataStatusOrder>>() {
                }.getType();
                dataCacheOrders = MyGson.getGson().fromJson(json, type);
            }
        } else {
            // remove cache data success
            DataStoreApp.getInstance().saveDataEmart("");
        }
    }

    private void handlerVoidTrans(DataVoid dataVoid) {
        boolean isShowConfirm = DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowConfirmVoidTcp, Boolean.class);
        this.dataVoidPayment = dataVoid;
        logUtil.appendLogAction("handlerVoidTrans");
        if ((dataVoid.getConfirmVoid() == 1) || isShowConfirm) {
            MyDialogShow.showDialogCancelAndClick("", context.getString(R.string.order_void_trans, dataVoid.getOrderId()), context.getString(R.string.txt_agree), context, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    logUtil.appendLogAction("Confirm VOID socket");
                    doVoid();
                }
            }, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    logUtil.appendLogAction("Cancel VOID socket");
                    handlerDataVoidErr(new DataError(Integer.parseInt(ORDER_CODE_CANCEL)), false);
                }
            }, true, true);
        } else {
            doVoid();
        }
    }

    private void doVoid() {
        updateStatusTrans(TransStatus.VOID_TRANS);
        this.mposTransactions = new MposTransactions(context);
        getTransactionDetail();
    }

    private void initParamForMposTransaction() {
        mposTransactions.setRunMacq(isRunMacq);
        mposTransactions.setSerialNumber(PrefLibTV.getInstance(context).getSerialNumber());
        mposTransactions.setUserId(PrefLibTV.getInstance(context).getUserId());
    }

    public void getTransactionDetail() {
        initParamForMposTransaction();
        logUtil.appendLogAction("txid void = " + dataVoidPayment.getTransCode());
        viewer.showLoading("Đang hủy giao dịch");


        mposTransactions.getTransactionDetail(dataVoidPayment.getTransCode(), new MposTransactionsMacq.ItfHandlerActionTrans<WfDetailRes>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactionsMacq.ActionTrans actionTrans) {
                Utils.LOGD(TAG, "onFailureActionTrans");
                logUtil.appendLogAction("onFailureActionTrans: code= " + dataError.getErrorCode() + " msg= " + dataError.getMsg());
                viewer.hideLoading();
                handlerDataVoidErr(dataError, true);
            }

            @Override
            public void onSuccessActionTrans(WfDetailRes detailRes, MposTransactionsMacq.ActionTrans actionTrans) {
//                dataPay = new DataPay(transItem);
                TransItem transItem = new TransItem(detailRes);
                int transactionStatus = transItem.getTransactionDetail().getTransactionStatus();
                logUtil.appendLogAction("success transactionStatus= " + transactionStatus + "isRunMacq= " + isRunMacq);
                if (transactionStatus == ConstantsPay.TRANS_TYPE_SUCCESS || transactionStatus == ConstantsPay.TRANS_TYPE_PENDING_TC
                        || transactionStatus == ConstantsPay.TRANS_TYPE_PENDING_SIGNATURE) {
                    voidPayment(detailRes);
                } else if (transactionStatus == ConstantsPay.TRANS_TYPE_VOID || transactionStatus == ConstantsPay.TRANS_TYPE_REVERSAL) {
                    // đã void callback lại
                    viewer.hideLoading();
                    handlerDataVoidSuccess(detailRes);
                } else {
                    // err
                    viewer.hideLoading();
                    handlerDataVoidErr(new DataError(Integer.parseInt(ORDER_CODE_CANCEL)), true);
                }
            }
        });
    }

    private void voidPayment(WfDetailRes detailRes) {
        TransItem transItem = new TransItem(detailRes);
        String transId;
        if (isRunMacq) {
            transId = transItem.getTransactionDetail().getWfId();
        } else {
            transId = dataVoidPayment.getTransCode();
        }
        logUtil.appendLogAction("void payment: transID= " + transId);
        mposTransactions.voidTransaction(transId, new MposTransactions.ItfHandlerActionTrans<Boolean>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                Utils.LOGD(TAG, "onFailureActionTrans: errCode= " + dataError.getErrorCode());
                logUtil.appendLogRequestApi("onFailureActionTrans: errCode= " + dataError.getErrorCode());
                viewer.hideLoading();
                handlerDataVoidErr(dataError, true);
            }

            @Override
            public void onSuccessActionTrans(Boolean obj, MposTransactions.ActionTrans typeAction) {
                Utils.LOGD(TAG, "onSuccessActionTrans: success");
                logUtil.appendLogAction("onSuccessActionTrans void success");
                viewer.hideLoading();
                handlerDataVoidSuccess(detailRes);
            }
        });
    }

    private void checkRunMacq() {
        String currBankName = PrefLibTV.getInstance(context).getBankName();
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(currBankName)) {
            isRunMacq = true;
        }
    }

    private void handlerRevicedOrder(DataOrder data) {
        dataOrderPresent = data;
        if (!isOrderPaid(data)) {
            payment(dataOrderPresent.getOrderId(), dataOrderPresent.getTotalAmount(), dataOrderPresent.getDescription());
        }
    }

    private boolean isOrderPaid(DataOrder data) {
        if (!dataCacheOrders.isEmpty()) {
            for (DataStatusOrder dataStatusOrder : dataCacheOrders) {
//                Utils.LOGD(TAG, "dataStatusOrder= " + dataStatusOrder.getDataOrder().getOrderId() + " data= " + data.getOrderId());
                if (dataStatusOrder.getDataOrder().getOrderId().equals(data.getOrderId()) && (dataStatusOrder.getDataOrder().getTotalAmount() == data.getTotalAmount())) {      // giao dịch đã được thực hiện
                    Utils.LOGD(TAG, "dataOrderPresent: " + dataStatusOrder.getDataOrder().getOrderId() + ", amount= " + dataStatusOrder.getDataOrder().getTotalAmount());
                    logUtil.appendLogAction("transaction has been done. orderId= " + data.getOrderId());
                    String msgTransHasBeenDone = context.getString(R.string.order_hasbeen_done, dataStatusOrder.getDataOrder().getOrderId()) + "\n" + context.getString(R.string.msg_amount, Utils.zenMoney(dataStatusOrder.getDataOrder().getTotalAmount()));
                    runOnUIThread(() -> {
                                permitGotoInputAmount = true;
                                MyDialogShow.showDialogError(
                                        "", msgTransHasBeenDone, context, false,
                                        context.getString(R.string.ALERT_BTN_OK), context.getString(R.string.ALERT_BTN_NO),
                                        (v) -> handlerCallbackOrderPaid(dataStatusOrder.getResultPayWrapper()),
                                        (v) -> {
                                            String msgResult = buildDataErr(data.getOrderId());
                                            itfSendResultToClient.onSendResultToClient(msgResult);
                                        });
                            }
                    );
                    return true;
                }
            }
        }
        return false;
    }

    private void handlerCallbackOrderPaid(ResultPayWrapper resultPay) {
        dataPrePayEMart = convertToDataPrePayEMart(resultPay);
        String content = createDataprePayEMartSuccess(APPROVED, resultPay);
        callbackTrans(content);
    }

    @Override
    public void closeSocketServer() {
        Utils.LOGD(TAG, "close SocketServer");
        logUtil.appendLogAction("close Task Socket");

        try {
            if (mService != null) {
                logUtil.appendLogAction("mService close");
                mService.closeTaskSocket();
            }
        } catch (Exception e) {
            e.printStackTrace();
            logUtil.appendLogException("closeTaskSocket err= " + e.getMessage());
        }

        if (intentServiceSocket != null) {
            try {
                logUtil.appendLogAction("unbindService close");
                context.unbindService(mServiceConnection);
                context.stopService(intentServiceSocket);
            } catch (Exception e) {
                logUtil.appendLogException("unbind/stop Service");
                e.printStackTrace();
            }
        }
    }

    private void closeServiceSocketRunning(Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            Utils.LOGD(TAG, "running= " + service.service.getClassName());
            if (serviceClass.getName().equals(service.service.getClassName())) {
                Utils.LOGD(TAG, "closeServiceSocketRunning= " + service.service.getClassName());
                try {
                    Intent intentstop = new Intent();
                    intentstop.setComponent(service.service);
                    context.stopService(intentstop);
                    Utils.LOGD(TAG, "stopService success");
                } catch (SecurityException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public void payment(String orderId, long amount, String description) {
        logUtil.appendLogAction("startCallSdkPayment orderID= " + orderId + " ,AMOUNT= " + amount);
        updateStatusTrans(TransStatus.START_TRANS);
        cancelTimer();
        String udid = createUdidByOrderId(orderId);
        logUtil.saveLog();
        try {
            String udidFromSdk = MyApplication.self().getMposSdk().chargeAmount(context, orderId, amount, description, null, udid, RC_PAY);
            appendLog("udidFromSDK=" + udidFromSdk);
        } catch (Exception e) {
            permitGotoInputAmount = true;
            e.printStackTrace();
            logUtil.appendLogAction("startCallSdkPayment error:" + e.getMessage());
        }
    }

    @Override
    public void callbackTrans(String msg) {
        isCallbackDataToClient = true;
        updateStatusTrans(TransStatus.END_TRANS);
        Utils.LOGD(TAG, "callbackTrans " + msg);
        logUtil.appendLogAction("step end msg callback: " + msg);
        if (itfSendResultToClient != null) {
            itfSendResultToClient.onSendResultToClient(msg);
        }
        else {
            Utils.LOGD(TAG, "callbackTrans: itfSendResultToClient==null => can not send to client");
        }
        logUtil.saveLog();
        logUtil.pushLog();
    }

    @Override
    public void callbackCancelOrder(String msg) {
//        isCallbackDataToClient = true;
//        updateStatusTrans(TransStatus.END_TRANS);
        Utils.LOGD(TAG, "callbackTrans " + msg);
        logUtil.appendLogAction("step end msg callback: " + msg);
        if (itfSendResultCancelToClient != null) {
            itfSendResultCancelToClient.onSendResultToClient(msg);
        }
        else {
            Utils.LOGD(TAG, "callbackCancelOrder: itfSendResultCancelToClient == null => can not send to client");
        }
        logUtil.saveLog();
        logUtil.pushLog();
    }

    private String createUdidByOrderId(String orderId) {
        String udid;
        if (MyUtils.isTypeAppTakashimaya()) {
            String currentTime = new SimpleDateFormat("ssmmHH").format(new Date());
            udid = String.format("%s-%s-%s-%s", PrefLibTV.getInstance(context).getUserId(), TextUtils.isEmpty(orderId) ? "" : orderId, currentTime, Utils.zenUdid());
        } else {
            udid = String.format(FORMAT_UDID_EMART_SDK, PrefLibTV.getInstance(context).getUserId(), TextUtils.isEmpty(orderId) ? "" : orderId, Utils.zenUdid());
        }
        return udid;
    }

    @Override
    public void handlerActionSetting() {
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isConfirmPassword, Boolean.class, Boolean.FALSE)) {
            //show Confim password
            showDlgAndCheckPassword(TYPE_SETTING);
        } else {
            gotoActivitySetting();
        }
    }

    private void gotoActivitySetting() {
        Intent intent = new Intent(context, ActivitySettingSP04.class);
        intent.putExtra(Constants.TYPE_SETTING, Constants.UPDATE_APP);
        context.startActivity(intent);
    }

    @Override
    public void logOut() {
        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isConfirmPassword, Boolean.class, Boolean.FALSE)) {
            //show Confim password
            showDlgAndCheckPassword(TYPE_LOGOUT);
        } else {
            MyDialogShow.showDialogContinueCancel(context.getString(R.string.ALERT_LOGOUT_MSG), context, true, view -> doLogout());
        }
    }

    private void showDlgAndCheckPassword(int type) {
        final int typeCheckPass = type;

        Dialog dialog = new Dialog(context);
        dialog.setCanceledOnTouchOutside(false);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.dialog_input_pass);
        Button buttonCancel = dialog.findViewById(R.id.btnDialogCancel);
        buttonCancel.setOnClickListener(v -> {
            closeKeyboard();
            dialog.dismiss();
        });

        showKeyboard();
        dialog.findViewById(R.id.btnDialogOk).setOnClickListener(v -> {
            EditText textPass = dialog.findViewById(R.id.edtxt_set_name) ;
            if (textPass.getText().toString().equals(PrefLibTV.getInstance(context).getPW())) {
                Utils.LOGD(TAG, "compare = true");
                if (typeCheckPass == TYPE_LOGOUT) {
                    doLogout();
                } else if (typeCheckPass == TYPE_SETTING){
                    gotoActivitySetting();
                }
            } else {
                MyDialogShow.showDialogError(context.getString(R.string.msg_pass_incorrect), context);
                Utils.LOGD(TAG, "compare = false");
            }
            closeKeyboard();
            dialog.dismiss();
        });
        dialog.show();
    }

    public void showKeyboard(){
        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
    }

    public void closeKeyboard(){
        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);
    }

    private void doLogout() {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.GATEWAY_MERCHANT_LOGOUT);
            jo.put("readerSerial", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("os", "Android");
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            logUtil.appendLogAction("logout muId= " + PrefLibTV.getInstance(context).getUserId());
            Utils.LOGD("Data: ", jo.toString());
            Utils.LOGD(TAG, "ChechCodePTI: " + DataStoreApp.getInstance().getRegisterId());
            entity = new StringEntity(jo.toString());
        } catch (JSONException | UnsupportedEncodingException e1) {
            Utils.LOGE(TAG, "logOut: " + e1);
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                viewer.showLoading("");
                super.onStart();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                viewer.hideLoading();
                logUtil.appendLogAction("logout success");
                gotoLogin();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                viewer.hideLoading();
            }
        });
    }

    protected void gotoLogin() {
//        boolean isAutoPrintReceipt = DataStoreApp.getInstance().getIsAutoPrintReceipt();
//        int numPrintReceipt = DataStoreApp.getInstance().getPrintMoreReceipt();
//        String dockAddress = PrefLibTV.getInstance(context).getBluetoothAddressPAX();
//        String ssIdbase = PrefLibTV.getInstance(context).getSSIDPAX();
//        String userName = PrefLibTV.getInstance(context).getUserId();
//        boolean isDisableNavbar = DataStoreApp.getInstance().getIsDisableNavbar();
//        boolean isDisableStatusBar = DataStoreApp.getInstance().getIsDisableStatusBar();
//        boolean isShowConfirmVoidTcp = DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowConfirmVoidTcp, Boolean.class);
//        boolean isOnlyShowUiWaitOrder = DataStoreApp.getInstance().getIsOnlyShowUIWaitOrderTCP();
//        int numSkipUpdate = getNumSkipUpdate();
//
        PrefLibTV.getInstance(context).clearDataAuto();
//        DataStoreApp.getInstance().clearData();
        MyApplication.self().clearMposSkd();
//
//        PrefLibTV.getInstance(context).createBluetoothAddressPAX(dockAddress);
//        PrefLibTV.getInstance(context).createSSIDPAX(ssIdbase);
//        PrefLibTV.getInstance(context).setUserId(userName);
//        setNumSkipUpdate(numSkipUpdate);
//        DataStoreApp.getInstance().setIsAutoPrintReceipt(isAutoPrintReceipt);
//        DataStoreApp.getInstance().setIsDisableNavbar(isDisableNavbar);
//        DataStoreApp.getInstance().setIsDisableStatusBar(isDisableStatusBar);
//        DataStoreApp.getInstance().setPrintMoreReceipt(numPrintReceipt);
//        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.isShowConfirmVoidTcp, isShowConfirmVoidTcp);
//        DataStoreApp.getInstance().setIsOnlyShowUIWaitOrderTCP(isOnlyShowUiWaitOrder);

        enableButtonHomeRecent();

        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        context.startActivity(intent);
        ((Activity) context).finish();
    }

    private void enableButtonHomeRecent() {
        if (DevicesUtil.isSP02() || DevicesUtil.isPax()) {
            boolean isDisableStatusBar = DataStoreApp.getInstance().getIsDisableStatusBar();
            boolean isDisableNavbar = DataStoreApp.getInstance().getIsDisableNavbar();
            if (isDisableNavbar) {
                MposUtil.getInstance().handlerActionDisplayNavbar(context, true);
            }
            if (isDisableStatusBar) {
                MposUtil.getInstance().handlerActionDisableStatusBar(context, false);
            }
        }
    }

    protected boolean checkIsFinishPayment() {
        Utils.LOGD(TAG, "checkIsFinishPayment: actionPayment=" + actionPayment);
        logUtil.appendLogAction("actionPayment: " + actionPayment);
        return actionPayment.equals("") || actionPayment.equals(com.mpos.sdk.util.Constants.AP_ACTION_END);
    }

    protected void handleActionPayment(String action) {
        actionPayment = action;
        if (action.equals(com.mpos.sdk.util.Constants.AP_ACTION_END)) {
            permitGotoInputAmount = true;
        }
    }

    protected void handleActionCardData(Intent intent) {
        maskPan = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
        logUtil.appendLogAction("handleActionCardData: " + maskPan);
    }

    protected void handleActionCancelOrder(Intent intent, String action) {
        actionPayment = action;
        logUtil.appendLogAction("handleActionCancelOrder: ");
        Utils.LOGD(TAG, "handleActionCancelOrder");
        String contentResult = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
        boolean isCancel = intent.getBooleanExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, false);
        ResultPayWrapper resultWrapper = MyGson.parseJson(contentResult, ResultPayWrapper.class);
        this.resultPay = resultWrapper;
        if (resultPay.getResult() != null) {
            dataPrePayEMart = convertToDataPrePayEMart(resultPay);
            handleCancelOrder(isCancel);
        }
    }

    protected void handleResultPay(ResultPayWrapper resultPay, boolean isDataBeforeSignature) {
        logUtil.appendLogAction("handlerResultPay: isDataBeforeSignature=" + isDataBeforeSignature);
        Utils.LOGD(TAG, "handlerResultPay: isDataBeforeSignature=" + isDataBeforeSignature + " -- method=" + resultPay.getResult().method);

        this.resultPay = resultPay;
        if (resultPay.getResult() != null) {
            dataPrePayEMart = convertToDataPrePayEMart(resultPay);
            dataPay = convertToDataPay(resultPay);

            String txId = dataPay.getTxId();
            logUtil.appendLogAction("result status= " + resultPay.getResult().status+" txId=" + txId);
            if (MposIntegrationHelper.TRANS_STATUS_APPROVED.equals(resultPay.getResult().status)) {
                handleSuccessTransaction(dataPay, resultPay, resultPay.getResult().method, isDataBeforeSignature);
            }
            else if (MposIntegrationHelper.TRANS_STATUS_CANCEL.equals(resultPay.getResult().status)) {
                handleCancelTransaction();
            }
            else if (MposIntegrationHelper.TRANS_STATUS_ERROR.equals(resultPay.getResult().status)) {
                handleFailTransaction(resultPay.getResult().error);
            }
            else if (MposIntegrationHelper.TRANS_STATUS_UNSIGN.equals(resultPay.getResult().status)) {
                handleUnsignTransaction(resultPay.getResult().transStatus);
            } else {
                logUtil.appendLogAction("TRANS_STATUS_FAIL");
                handleFailTransaction(new DataError(ORDER_CODE_FAIL));
            }
        }
    }

    private void handleUnsignTransaction(int errCode) {
        appendLog("handleUnsignTransaction");
        logUtil.saveLog();
        logUtil.pushLog();

        Utils.LOGD(TAG, "isCallbackDataToClient= " + isCallbackDataToClient);
        if (!isCallbackDataToClient) {
            String content = createDataPrePayEMartFail(new DataError(errCode, context.getString(R.string.msg_sign_fail)));
            callbackTrans(content);
        }

//        dialogErr = MyUtils.initDialogGeneralError(
//                context,
//                errCode,
//                msg,
//                ActivityPrePayment.class.getName());
//        dialogErr.setButtonCloseLabel(context.getString(R.string.history));
//        dialogErr.setOnClickListenerDialogClose(v -> {
//            dialogErr.dismiss();
//            gotoHistory();
//        });
//        if (ScreenUtils.canShowDialog(context)) {
//            dialogErr.show();
//        }

        MyDialogShow.showDialogCancelAndClick("", context.getString(R.string.msg_sign_fail), context.getString(R.string.history), context, new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                gotoHistory();
            }
        }, true, true);

    }

//    private void dissmissDialogErr() {
//        if (dialogErr != null) {
//            dialogErr.dismiss();
//        }
//    }

    /*  Handler data */

    private String buildDataErr(String orderId) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_CANCEL_TAKA);
        }else {
            DataPrePayEMart dataPrePayEMart = new DataPrePayEMart();
            dataPrePayEMart.setServiceName(UPDATE_TRANS);
            dataPrePayEMart.setStatus(FAIL);
            dataPrePayEMart.setResponseCode(ORDER_CODE_CANCEL);
            dataPrePayEMart.setOrderId(orderId);
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    private String buildDataErrNotFinishPreviousPayment(String orderId) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_NOT_FINISH_PREVIOUS_PAYMENT_TAKA);
        } else {
            DataPrePayEMart dataPrePayEMart = new DataPrePayEMart();
            dataPrePayEMart.setServiceName(UPDATE_TRANS);
            dataPrePayEMart.setStatus(FAIL);
            dataPrePayEMart.setResponseCode(ORDER_CODE_NOT_FINISH_PREVIOUS_PAYMENT);
            dataPrePayEMart.setOrderId(orderId);
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    private void handlerDataVoidErr(DataError dataError, boolean isShowDlgVoidResult) {
        MyDialogShow.dismissCurrDialog();
        updateStatusTrans(TransStatus.END_TRANS);
        String result = buildDataVoidErr(dataError);
        callbackTrans(result);

        if (isShowDlgVoidResult && (DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowConfirmVoidTcp, Boolean.class) || (dataVoidPayment.getConfirmVoid() == 1))) {
            MyDialogShow.showDialogError(context.getString(R.string.dialog_error_title, dataError.getErrorCode()), context.getString(R.string.msg_void_trans_fail), context, true);
        }
    }

    private String buildDataVoidErr(DataError dataError) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_CANCEL_TAKA);
        }else {
            dataVoidPayment.setResponseCode(String.valueOf(dataError.getErrorCode()));
            return MyGson.getGson().toJson(dataVoidPayment);
        }
    }

    private void handlerDataVoidSuccess(WfDetailRes detailRes) {
        actionPrintVoidReceipt();
        updateStatusTrans(TransStatus.END_TRANS);
        String result = buildDataVoidSuccess(detailRes);
        callbackTrans(result);
        if (MyUtils.isTypeAppTakashimaya() || (dataVoidPayment.getConfirmVoid() == 1) || DataStoreApp.getInstance().getDataByKey(DataStoreApp.isShowConfirmVoidTcp, Boolean.class)) {
            MyDialogShow.showDialogSuccess(context, context.getString(R.string.msg_void_trans_success), true);
        }
    }

    private void actionPrintVoidReceipt() {
        if ((dataVoidPayment.getPermitPrintReceipt() == 1) && isRunMacq) {
            downloadReceiptMA(dataVoidPayment.getTransCode());
        }
    }

    private void downloadReceiptMA(String txid) {
        viewer.showLoading("");
        ReceiptSend receiptSend = new ReceiptSend(txid, null);
        receiptSend.setFontBold(true);
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(receiptSend));
        MposRestClient.getInstance(context).post(context, ApiMultiAcquirerInterface.URL_GET_RECEIPT, entity, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG,  "downloadReceiptMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                viewer.hideLoading();
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));
                MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG,  "downloadReceiptMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                viewer.hideLoading();
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                DownloadReceiptRes receiptRes = MyGson.parseJson(clearData, DownloadReceiptRes.class);
                printReceiptBase64(receiptRes.getReceiptBase64());
            }
        });
    }

    private String buildDataVoidSuccess(WfDetailRes detailRes) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataVoidSuccess(dataVoidPayment.getTransCode(), detailRes);
        }else {
            dataVoidPayment.setResponseCode(ORDER_CODE_SUCCESS);
            return MyGson.getGson().toJson(dataVoidPayment);
        }
    }

    private DataPrePayEMart convertToDataPrePayEMart(ResultPayWrapper resultPay) {
        dataPay = convertToDataPay(resultPay);
        UserCard userCard = resultPay.getUserCard();
        DataPrePayEMart dataPrePayEMart = new DataPrePayEMart(String.valueOf(userCard.amountAuthorized), resultPay.getResult().paymentIdentifier);
//        dataPrePayEMart.setAuthCode(userCard.authCode);

        if (!TextUtils.isEmpty(dataPay.getLabel()) && (dataPay.getLabel().equals(EMartPresenter.EMART_VOUCHER_CARD) || dataPay.getLabel().equals(Constants.TYPE_PAY_QR_VNPAY)) ) {
            dataPrePayEMart.setAuthCode(userCard.authCode);
        } else if (resultPay.getWfInfo() != null) {
            dataPrePayEMart.setAuthCode(convertAuthCode(resultPay.getWfInfo().getRrn()));
        }

        dataPrePayEMart.setPan(userCard.pan);
        dataPrePayEMart.setCardHolderName(userCard.cardHolderName);
        dataPrePayEMart.setTransDate(userCard.transactionDate);
        dataPrePayEMart.setTransCode(resultPay.getResult().transId);

        String orderId = "";
        if ((userCard.applicationLabel != null) && (userCard.applicationLabel.equals(Constants.TYPE_PAY_QR_VNPAY) || userCard.applicationLabel.equals(Constants.TYPE_PAY_QR_MOMO))) {
            orderId = resultPay.getResult().paymentIdentifier.split(ConstantsPay.PREFIX_UDID_QR_NL)[1].split("\\|")[0];
        } else {
            dataPrePayEMart.setIssuerCode(userCard.applicationLabel);
            try {
                logUtil.appendLogAction("orderId: " + resultPay.getResult().paymentIdentifier);
                orderId = resultPay.getResult().paymentIdentifier.split("-")[1].split("-")[0];
                Utils.LOGD(TAG, "orderId:: " + orderId);
            } catch (Exception e) {
                logUtil.appendLogException("err get orderID " + e);
            }
        }
        dataPrePayEMart.setOrderId(orderId);

        return dataPrePayEMart;
    }

    private String convertAuthCode(String rrn) {
        if (TextUtils.isEmpty(rrn) || (rrn.length() < 6)) {
            return "";
        }
        Utils.LOGD(TAG, "refNo=== convertAuthCode " + rrn);
        logUtil.appendLogAction("Ref No= " + rrn);
        return rrn.substring(rrn.length()-6);
    }

    private void handleCancelTransaction() {
        String content = createDataCancel();
        callbackTrans(content);
    }

    private void handleCancelOrder(boolean isCancel) {
        String content = createDataCancelOrder(isCancel);
        callbackCancelOrder(content);
    }

    private String createDataCancel() {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_CANCEL_TAKA);
        }else {
            dataPrePayEMart.setServiceName(UPDATE_TRANS);
            dataPrePayEMart.setStatus(CANCEL);
            dataPrePayEMart.setResponseCode(ORDER_CODE_CANCEL);
            dataPrePayEMart.setMuid(PrefLibTV.getInstance(context).getUserId());
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    private String createDataCancelOrder(boolean isCancel) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_CANCEL_TAKA);
        }else {
            dataPrePayEMart.setServiceName(CANCEL_ORDER);
            if (isCancel) {
                dataPrePayEMart.setStatus(CANCEL);
            } else {
                dataPrePayEMart.setStatus(UNABLE_CANCEL);
            }
            dataPrePayEMart.setResponseCode(ORDER_CODE_CANCEL);
            dataPrePayEMart.setMuid(PrefLibTV.getInstance(context).getUserId());
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    private void handleFailTransaction(DataError dataError) {
        String content = createDataPrePayEMartFail(dataError);
        callbackTrans(content);
    }

    private String createDataPrePayEMartFail(DataError dataError) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_FAIL_TAKA);
        }else {
            if (dataPrePayEMart == null) {
                dataPrePayEMart = new DataPrePayEMart();
            }
            dataPrePayEMart.setServiceName(UPDATE_TRANS);
            dataPrePayEMart.setStatus(FAIL);
            dataPrePayEMart.setResponseCode(String.valueOf(dataError.getErrorCode()));
            dataPrePayEMart.setMuid(PrefLibTV.getInstance(context).getUserId());
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    public void handleSuccessTransaction(DataPay dataPay, ResultPayWrapper resultPay, String method, boolean isDataBeforeSignature) {
        Utils.LOGD(TAG, "handlerSuccessTransaction method= " + method);
        logUtil.appendLogAction("handlerSuccessTransaction method= " + method);
        String content;
        saveOrderSuccess();

        if ((method != null) && (method.equals(METHOD_RESIGN))) {
            content = createDataprePayEMartSuccess(RESIGN, resultPay);

            if (dataOrderPresent != null) {
                String builderAmount = Utils.zenMoney(dataOrderPresent.totalAmount) + ConstantsPay.CURRENCY_SPACE_PRE;
                MyDialogShow.showDialogCancelAndClick("", context.getString(R.string.continue_trans, builderAmount), context.getString(R.string.yes), context,
                        view -> handlerRevicedOrder(dataOrderPresent),
                        view -> callbackTrans(content),
                        true, false);
            } else {
                callbackTrans(content);
            }

        } else {
            if (!isDataBeforeSignature) {
                showDialogSuccess(dataPay);
            }
            content = createDataprePayEMartSuccess(APPROVED, resultPay);
        }

        if ((dataPrePayEMart.getIssuerCode() != null) && dataPrePayEMart.getIssuerCode().equals(EMART_VOUCHER_CARD)) {
            Utils.LOGD(TAG, "EMART VOUCHER CARD==> = ");
            callbackTrans(content);
        } else if (isDataBeforeSignature) {  // isDataBeforeSignature => haveSendApproved: can callback - reset connect
            callbackTrans(content);
        } else {
            updateStatusTrans(TransStatus.END_TRANS);
        }
    }

    private String createDataprePayEMartSuccess(String status, ResultPayWrapper resultPay) {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataSuccess(resultPay, maskPan);
        }else {
            dataPrePayEMart.setServiceName(UPDATE_TRANS);
            dataPrePayEMart.setStatus(status);
            dataPrePayEMart.setResponseCode(ORDER_CODE_SUCCESS);
            dataPrePayEMart.setMuid(PrefLibTV.getInstance(context).getUserId());
            return MyGson.getGson().toJson(dataPrePayEMart);
        }
    }

    private DataPay convertToDataPay(ResultPayWrapper resultPayWrapper) {
        UserCard userCard = resultPayWrapper.getUserCard();
        DataPay dataPay = new DataPay(String.valueOf(userCard.amountAuthorized), "", resultPayWrapper.getResult().paymentIdentifier);
        dataPay.setAuthCode(userCard.authCode);
        dataPay.setPan(userCard.pan);
        dataPay.setName(userCard.cardHolderName);
        dataPay.setTrId(resultPayWrapper.getResult().trId);
        dataPay.setTxId(resultPayWrapper.getResult().transId);
        dataPay.setUdid(resultPayWrapper.getResult().paymentIdentifier);
        dataPay.setSignatureBase64(resultPayWrapper.getUserCard().userSignature);
        dataPay.setLabel(resultPayWrapper.getUserCard().getApplicationLabel());
        dataPay.setTransactionDate(userCard.getTransactionDate());

        dataPay.setWfDetailRes(resultPayWrapper.getWfInfo());
        return dataPay;
    }

    private void saveOrderSuccess() {
        logUtil.appendLogAction("saveOrderSuccess");
        String orderId;
        if ((dataPrePayEMart.getIssuerCode() != null)) {
            if (dataPrePayEMart.getIssuerCode().equals(EMART_VOUCHER_CARD)) {
                orderId = resultPay.getResult().paymentIdentifier.split(PrefLibTV.getInstance(context).getUserId())[1];
            } else if (dataPrePayEMart.getIssuerCode().equals(Constants.TYPE_PAY_QR_VNPAY)) {
                orderId = resultPay.getResult().paymentIdentifier;
            } else {
                orderId = resultPay.getResult().paymentIdentifier.split("-")[1].split("-")[0];
            }
        } else {
            orderId = resultPay.getResult().paymentIdentifier;
        }

        Log.d(TAG, "saveOrderSuccess: " + orderId + "amount= " + Long.parseLong(resultPay.getResult().amount));
        dataCacheOrders.add(new DataStatusOrder(new DataOrder(ADD_ORDER, orderId, Long.parseLong(resultPay.getResult().amount), 0,"", null), resultPay));
        Gson gson = new Gson();
        String jsonText = gson.toJson(dataCacheOrders);

        DataStoreApp.getInstance().saveDateEmart(Utils.convertTimestamp(System.currentTimeMillis(), 2));
        DataStoreApp.getInstance().saveDataEmart(jsonText);
    }

    private void showDialogSuccess(DataPay dataPay) {
        Utils.LOGD(TAG, "showDialogSuccess");
        activity.setResult(RESULT_OK);

        if (dataOrderPresent != null && (dataOrderPresent.getAutoDismissDlgTimer() == -1)) {
            logUtil.saveLog();
            return;
        }

        dialogResult = DialogResult.newInstance(DialogResult.RESULT_SUCCESS, "", dataPay, null);
        dialogResult.setHomeEmart(true);
        dialogResult.setCancelable(false);

        if (dataPay.getLabel().equals(EMartPresenter.EMART_VOUCHER_CARD)) {
            dialogResult.setCallback(type -> printReceiptGiftCardOffline(dataPay));
        } else {
            dialogResult.setCallback(type -> printReceiptMaOffline(dataPay));
        }

        Utils.LOGD(TAG, "isStateSaved=" + activity.getSupportFragmentManager().isStateSaved());

        if (ScreenUtils.canShowDialog(context) && !activity.getSupportFragmentManager().isStateSaved()) {
            Utils.LOGD(TAG, "showDialogSuccess: --->show");
            dialogResult.show(activity.getSupportFragmentManager(), DialogResult.class.getName());
            if (dataOrderPresent != null) {
                dialogResult.startCoundDismissDialog(dataOrderPresent.getAutoDismissDlgTimer());
            }
            if (MyUtils.isTypeAppTakashimaya()) {
                dialogResult.startCoundDismissDialog(15);
            }
        } else {
            if (!DataStoreApp.getInstance().getDataByKey(DataStoreApp.enableRethink, Boolean.class)) {
                Utils.LOGD(TAG, "showDialogSuccess: --> delay");
                handler.removeCallbacksAndMessages(null);
                handler.postDelayed(() -> showDialogSuccess(dataPay), 300);
            }
        }

        logUtil.saveLog();
    }

    @Override
    public void handleOnResume() {
        isGotoHomeEnter = false;
        startFetchStatusSocket();
        checkOnlyShowUIWaitTCP();
    }

    private void checkOnlyShowUIWaitTCP() {
        if (DataStoreApp.getInstance().getIsOnlyShowUIWaitOrderTCP()) {
            viewer.onlyShowUIWaitOrder();
        } else {
            viewer.showFullUI();
        }
    }

    public void startFetchStatusSocket() {
        Utils.LOGD(TAG, "fetchStatusServer: ");

        cancelTimer();

        final Handler handler = new Handler();
        TimerTask timertask = new TimerTask() {
            @Override
            public void run() {
                handler.post(() -> {
                    String ipAddress = loadIpSocketServer();
                    if (TextUtils.isEmpty(ipAddress)) {
                        updateStateSocket(SocketState.OFFLINE, ipAddress);
                    } else {
                        updateStateSocket(SocketState.ONLINE, ipAddress);
                    }
                });
            }
        };
        timerCheckNetwork = new Timer(); //This is new
        timerCheckNetwork.schedule(timertask, 0, 5000); // execute in every 15sec
    }

    String ipWifiAddress;
    private void updateIpAddress(String ipAddress) {
        if (TextUtils.isEmpty(ipWifiAddress) || !ipWifiAddress.equals(ipAddress)) {
            ipWifiAddress = ipAddress;
            viewer.updateIpAddress(ipWifiAddress);
        }
    }

    public void cancelTimer() {
        if (timerCheckNetwork != null) {
            Utils.LOGD(TAG, "cancelTimer");
            timerCheckNetwork.cancel();
        }
    }

    public void dismissDialogResult() {
        if (dialogResult != null) {
            Utils.LOGD(TAG, "dismissDialogResult");
            appendLog("dismissDialogResult");
            dialogResult.dismissDialogFormMart();
        }
    }

//    @Override
//    public void resetSocket() {
//        Utils.LOGD(TAG, "resetSocket");
//        logUtil.appendLogAction("func resetSocket");
//        logUtil.saveLog();
//        logUtil.pushLog();
//        closeSocketServer();
//        startServiceSocket();
//    }

    private void startServiceSocket() {
//        closeSocketServer();
//        closeServiceSocketRunning(SocketService.class);
        Class<?> serviceClass;
        if (MyUtils.isTypeAppTakashimaya()) {
            serviceClass = TakashimayaSocketService.class;
        } else {
            serviceClass = EmartSocketService.class;
        }

        intentServiceSocket = new Intent(context, serviceClass);
        context.bindService(intentServiceSocket, mServiceConnection, Context.BIND_ABOVE_CLIENT);
        context.startService(intentServiceSocket);
    }

    private void updateStateSocket(SocketState state, String ipLAn) {
        updateIpAddress(ipLAn);
        if (stateSocket != state) {
            stateSocket = state;
            updateIpGateWay();
            viewer.updateStateSocket(state, ipLAn);
            logUtil.appendLogAction("Ip Socket Server= " + ipLAn);
        }
    }

    private void updateIpGateWay() {
        if (stateSocket == SocketState.ONLINE) {
//            final WifiManager manager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
            final DhcpInfo dhcp = wifiManager.getDhcpInfo();
            final String address = Formatter.formatIpAddress(dhcp.gateway);
            Utils.LOGD(TAG, "ipGateWay= " + address);
            logUtil.appendLogAction("IpGateWay= " + address);

            //update emart don't show ip gateway bc security
//            viewer.updateIPGateWay(address);
        }
    }

    private void updateStatusTrans(TransStatus status) {
        viewer.updateStatus(status);
        if (status == TransStatus.END_TRANS) {
            Handler handler = new Handler();
            handler.postDelayed(() -> viewer.updateStatus(TransStatus.WAIT_TRANS), 2000);
        }
    }

    /**
     * ==================== START PRINTER ======================
     */
    private void printReceiptGiftCardOffline(DataPay dataPay) {
        if (libPrinter == null) {
            libPrinter = new LibPrinterMpos(context, MyApplication.self().getSaveLogController());
        }
        if (dataPay != null) {
            libPrinter.actionPrintGiftCardEmart(dataPay);
        }
    }

    private void printReceiptMaOffline(DataPay dataPay) {
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            logUtil.appendLogException("hava data print");
            if (libPrinter == null) {
                libPrinter = new LibPrinterMpos(context, MyApplication.self().getSaveLogController());
            }
            if (dataPay != null && dataPay.getWfDetailRes() != null) {
                libPrinter.printReceiptMaOfflineByLayout(dataPay);
            }
        } else {
            try {
                logUtil.appendLogException("can not print: dataPay=>" +
                        (dataPay == null ? "null" : (dataPay.getWfDetailRes() == null ? "WfDetailRes = null" : "WfDetailRes != null")));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void printReceiptBase64(String data) {
        if (libPrinter == null) {
            libPrinter = new LibPrinterMpos(context, MyApplication.self().getSaveLogController());
        }
        libPrinter.actionPrintImageBase64(data);
    }

    /**
     * ==================== END PRINTER ======================
     */

    public interface ItfProcessMsgSocket {      // Nhận order để payment
        void addOrder(DataOrder dataOrder, ItfSendResultToClient in);

        void doCancelOrder(ItfSendResultToClient in);

        void doVoid(DataVoid dataVoid, ItfSendResultToClient in);

        void doErrSocket(String msg);
    }

    public interface ItfProcessMsgSocketTakashimaya {      // Nhận order để payment
        void addOrder(DataOrderTakashimaya dataOrder, ItfSendResultToClient in);

        void doCancelOrder(ItfSendResultToClient in);

        void doErrSocket(String msg);
    }

    public interface ItfClientConnect {
        void clientConnected(boolean isConnect, String ipClient);
    }

    public interface ItfSendResultToClient {       // payment xong callback lại socket server
        void onSendResultToClient(String msg);
    }

    public enum TransStatus {
        WAIT_TRANS,
        START_TRANS,
        VOID_TRANS,
        END_TRANS,
    }

    public enum SocketState {
        ONLINE,
        OFFLINE,
    }

    public void gotoHistory() {
        Utils.LOGD(TAG, "gotoHistory: " + PrefLibTV.getInstance(context).getBankName());
        Intent i = new Intent(context, ActivityPaymentHistory.class);
        context.startActivity(i);
    }

    @Override
    public void gotoDeposit() {
        logUtil.appendLogAction("onClick gotoDeposit ===>");
        isGotoHomeEnter = true;
        Intent intent = new Intent(context, FlutterTransferActivity.class);
        intent.putExtra("route", ROUTE_DEPOSIT);
        context.startActivity(intent);
    }

    @Override
    public void gotoPaymentEnterAmount() {
        Utils.LOGD(TAG, "permitGotoInputAmount: " + permitGotoInputAmount);
        logUtil.appendLogAction("permitGotoInputAmount/actionPayment: " + permitGotoInputAmount + "/"+ actionPayment);
        if (permitGotoInputAmount) {
            isGotoHomeEnter = true;
            logUtil.appendLogAction("goto payment Enter Amount ===>");
            Intent intent = new Intent(context, ActivityHomeEnter.class);
            intent.putExtra(Constants.EXTRA_INTENT, Constants.CARD_PAYMENT);
            context.startActivity(intent);
        }
    }

    // auto inject
    protected LibInjectKey libInjectKey;
    private String serialNumber;
    private String readerInjected;
    private boolean isMutilAcquire;
    private String currBankName = "";

    private void checkNeedAutoInjectKey() {
        serialNumber = PrefLibTV.getInstance(context).getSerialNumber();
        currBankName = PrefLibTV.getInstance(context).getBankName();
        isMutilAcquire = currBankName.equals(ConstantsPay.MPOS_MULTI_ACQUIRER);
        readerInjected = PrefLibTV.getInstance(context).get(PrefLibTV.readersInjected, String.class, "");
        try {
            logUtil.appendLogAction("injected: " + readerInjected);
            String lastBankInject = "";
            if (!TextUtils.isEmpty(readerInjected)) {
                JSONObject jRoot = new JSONObject(readerInjected);
                Iterator<String> keys = jRoot.keys();
                while (keys.hasNext()) {
                    String snInjected = keys.next();
                    if (snInjected.equals(serialNumber)) {
                        lastBankInject = jRoot.getString(snInjected);
                    }
                }
            }
            logUtil.appendLogAction("isMutilAcquire=" + isMutilAcquire + " lastBankInjected=" + lastBankInject + " currBank=" + currBankName);
            if (isMutilAcquire && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(ConstantsPay.MPOS_MULTI_ACQUIRER))) {
                processInjectMA();
            } else if (!isMutilAcquire && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(currBankName))) {
                processInjectBank();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void saveInjected() {
        try {
            JSONObject jRoot;
            if (TextUtils.isEmpty(readerInjected)) {
                jRoot = new JSONObject();
            } else {
                jRoot = new JSONObject(readerInjected);
            }
            jRoot.put(serialNumber, currBankName);
            logUtil.appendLogAction("save injected: " + jRoot);
            PrefLibTV.getInstance(context).put(PrefLibTV.readersInjected, jRoot.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void processInjectBank() {
        libInjectKey = new LibInjectKey(context, ConstantsPay.DEVICE_PAX, PrefLibTV.getInstance(context).getFlagServer(), serialNumber);
        libInjectKey.setCbLog((typeLog, s) -> logUtil.appendLogAction(s));
        if (DevicesUtil.isPax()) {
            libInjectKey.setLibPax(MyApplication.self().getLibPax());
        }
        Utils.LOGD(TAG, "processInjectBank: -->" + ConstantsPay.DEVICE_PAX);
        libInjectKey.setCallback((i, i1, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type=" + i + " res=" + i1 + " ->" + s);
            if (i == LibInjectKey.TYPE_KEY_END) {
                saveInjected();
            }
        });
        processGetIpekFromBank();
    }

    private void processGetIpekFromBank() {
        logUtil.appendLogAction("start get Ipek bank");
        if (libInjectKey != null) {
            libInjectKey.processInjectKey();
        }
    }

    private void processInjectMA() {
        libInjectKey = new LibInjectKey(context, deviceType, ConstantsPay.SERVER_MPOS_ACQUIRER, serialNumber);
        if (DevicesUtil.isPax()) {
            libInjectKey.setLibPax(MyApplication.self().getLibPax());
        }
        libInjectKey.setCbLog((typeLog, s) -> logUtil.appendLogAction(s));
        Utils.LOGD(TAG, "processInjectMA: -->" + deviceType);
        libInjectKey.setCallback((i, i1, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type=" + i + " res=" + i1 + " ->" + s);
            if (i == LibInjectKey.TYPE_KEY_END) {
                saveInjected();
            }
        });
        processGetIpekFromMA();
    }

    private void processGetIpekFromMA() {
        logUtil.appendLogAction("start get Ipek MA");
        if (libInjectKey != null) {
            libInjectKey.getIpekFromMA();
        }
    }

    private void registerReceiver() {
        Utils.LOGD(TAG, "registerReceiver");
        logUtil.appendLogException("registerReceiver ");
        IntentFilter filter = new IntentFilter();
        filter.addAction(nameFilterActionPayment);

        myReceiver = new MyReceiver(this);
        try {
            //Register or UnRegister your broadcast receiver here
            context.registerReceiver(myReceiver, filter);
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException " + e.getMessage());
            e.printStackTrace();
        }

        MyApplication.self().getMposSdk().setHandlerActionPayment(nameFilterActionPayment);

        ReceiverManagerFinish.getInstance().registerBroadcast(context);
    }

    public void unregisterReceiver(){
        Utils.LOGD(TAG, "unregisterReceiver ");
        logUtil.appendLogException("unregisterReceiver ");
        if (myReceiver != null) {
            context.unregisterReceiver(myReceiver);
        }

        ReceiverManagerFinish.getInstance().unregisterBroadcast(context);
    }

    private void sendEventCloseOtherActivities() {
        Utils.LOGD(TAG, "sendEventCloseOtherActivities");
        Intent intentState = new Intent(ReceiverManagerFinish.FILTER_ACTION_FINISH);
        intentState.putExtra(ReceiverManagerFinish.EXTRA_NAME_IS_FINISH, true);
        context.sendBroadcast(intentState);
    }

    public static class MyReceiver extends BroadcastReceiver {
        EMartPresenter presenter;

        public MyReceiver(EMartPresenter presenter) {
            this.presenter = presenter;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            String actionPayment = intent.getStringExtra(Intents.EXTRA_DATA_BC_ACTION);
            Utils.LOGD(TAG, "onReceive: actionPayment=" + actionPayment);
            presenter.appendLog("MyReceiver: action=" + actionPayment);
            switch (actionPayment) {
                case com.mpos.sdk.util.Constants.AP_ACTION_START:
                case com.mpos.sdk.util.Constants.AP_ACTION_END:
                    presenter.handleActionPayment(actionPayment);
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_CANCEL_ORDER:
                    presenter.handleActionCancelOrder(intent, actionPayment);
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_CALLBACK_CARD_DATA:
                    presenter.handleActionCardData(intent);
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_SEND_MSG_TO_CLIENT:
                    presenter.handlerSendMsgToClient(intent);
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_BEFORE_SIGNATURE:
                case com.mpos.sdk.util.Constants.AP_ACTION_CALLBACK_DATA:
                    if (!presenter.isGotoHomeEnter) {
                        Utils.LOGD(TAG, "AP_ACTION_CALLBACK_DATA handleActionResultPayment");
                        handleActionResultPayment(intent, actionPayment);
                    }
                    break;
            }
        }

        private void handleActionResultPayment(Intent intent, String action) {
            String contentResult = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
            ResultPayWrapper resultWrapper = MyGson.parseJson(contentResult, ResultPayWrapper.class);
            boolean isDataBeforeSignature = action.equals(com.mpos.sdk.util.Constants.AP_ACTION_BEFORE_SIGNATURE);
            presenter.handleResultPay(resultWrapper, isDataBeforeSignature);
        }
    }

    private void handlerSendMsgToClient(Intent intent) {
        String contentResult = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
        if (!isCallbackDataToClient) {
            callbackTrans(contentResult);
        }
    }

    @Override
    public void startProcessReadGiftCard() {
        isShowScreenGiftCard = true;
        if (DevicesUtil.isPax()) {
            startSeachCardForPax();
        } else if (DevicesUtil.isSP02P8()) {
            startSeachGiftCardForP8();
        }
    }

    private void startSeachGiftCardForP8() {
        Utils.LOGD(TAG, "startSeachGiftCardForP8");
        appendLog("startSeachGiftCardForP8");
    }

    private void startSeachCardForPax() {
        Utils.LOGD(TAG, "startSearchGiftCardForPax");
        logUtil.appendLogAction("startSearchGiftCardForPax");
        LibPax libPax = MyApplication.self().getLibPax();
        libPax.startDetectGiftCard(new LibPax.ItfCbGiftCardResult() {
            @Override
            public void onResult(GiftCardInfor data) {
                appendLog("GiftCardInfor= " + data.getResultCode());
                if (data.getResultCode().equals(SUCCESS)) {
                    viewer.updateViewInfoCard(data);
                } else {
                    runOnUIThread(() -> {
                        String msgErr;
                        if (data.getResultCode().equals(INVALID_CARD_NUMBER)) {
                            msgErr = context.getString(R.string.tv_card_not_exits);
                        } else {
                            msgErr = data.getResultMessage() + "(" + data.getResultCode() + ")";
                        }
                        MyDialogShow.showDialogError(msgErr, context);
                        viewer.errGetInforCard();
                    });
                }
            }

            @Override
            public void onErrDetectCard(int errorCode) {
                Utils.LOGD(TAG, "onErrDetectCard = " + errorCode);
                appendLog("GiftCardInfor onErrDetectCard = " + errorCode);
                runOnUIThread(() -> {
                    viewer.errGetInforCard();
                    if (errorCode == CardReaderHelper.TIMEOUT) {
                        MyDialogShow.showDialogError(getString(R.string.error_timeout_wait_giftcard), context);
                    } else if (errorCode == -2) {
                        MyDialogShow.showDialogError(context.getString(R.string.msg_not_giftcard), context);
                    } else {
                        MyDialogShow.showDialogError(getString(R.string.error_read_gift_card), context);
                    }
                });
            }

            @Override
            public void onErr(DataError dataError) {
                Utils.LOGD(TAG, "errCode = " + dataError.getErrorCode());
                appendLog("GiftCardInfor err = " + dataError.getErrorCode());
                runOnUIThread(() -> {
                    viewer.errGetInforCard();
                    MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), context, true);
                });
            }

            @Override
            public void onState(int i) {
                viewer.onStateCardInfo(i);
            }
        });
    }

    @Override
    public void closeSearchGiftCard() {
        appendLog("closeSearchGiftCard");
        try {
            if (DevicesUtil.isPax()) {
                MyApplication.self().getLibPax().closeSearchGiftCard();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void hideScreenGiftCard() {
        if (isShowScreenGiftCard) {
            viewer.hideViewGiftCard();
            closeSearchGiftCard();
        }
    }

    protected void appendLog(String log) {
        if (logUtil != null) {
            logUtil.appendLogAction(log);
        }
    }

    Handler handler = new Handler();
    private void runOnUIThread(Runnable runnable) {
        AppExecutors.getInstance().mainThread().execute(runnable);
    }
}
