package com.mpos.screen.mart;

import android.graphics.BitmapFactory;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.EncodeHintType;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogResult;
import com.mpos.customview.ViewToolBar;
import com.mpos.delegates.Singleton;
import com.mpos.models.BaseResponseQrNl;
import com.mpos.models.DataQrPay;
import com.mpos.models.DataTransStatusQrNl;
import com.mpos.screen.ActivityPaymentInfo;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.*;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.screen.MposPaymentActivity;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.*;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import butterknife.BindView;
import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import vn.mpos.R;

import static com.mpos.screen.mart.EMartPresenter.*;

/**
 * Create by ufo.thuan on 09/1/2023
 */

public class ActivityQrEmart extends AppCompatActivity {

    private static final String TAG = ActivityQrEmart.class.getSimpleName();

    private String amount;
    private String typeQR;
    private String orderCode;
    private String bankCode = Constants.BANK_CODE_QR_NL;
//    private String bankCode = "NGANLUONG";
    private String phone;
    private String email;
//    private String DOMAIN_TEST = "https://vcb-uat-checkout.nganluong.vn/api/web/checkout/version_1_0";
//    private String DOMAIN_TEST = "https://vietcombank.nganluong.vn/api/web/checkout/version_1_0/";

    private static String urlNLGatewaySanbox = "https://paygate-mpos-api-sandbox.nganluong.vn/api/web/checkout/version_1_0";
    private static String urlNLGateway = "https://paygate-mpos-api.nganluong.vn/api/web/checkout/version_1_0";


    private String orderID = "";

    @BindView(R.id.ll_qr_nl)
    LinearLayout view_qr;

    @BindView(R.id.txtAmountQr)
    TextView tvAmount;

    @BindView(R.id.img_qr)
    ImageView imgQr;

    @BindView(R.id.btn_check_status_trans)
    Button btnCheckStatus;

    @BindView(R.id.webview_qr_emart)
    WebView webviewQr;

    @BindView(R.id.progressBar1)	protected ProgressBar pgb;
    protected SaveLogController logUtil;
    ViewToolBar viewToolBar;

    // statusCode QR
    private static final int SUCCESS_PAYMENT = 3;
    DialogResult dialogResult;
    Handler handler = new Handler();
    protected MyProgressDialog mPgdl;

    private MposIntegrationHelper mposIntegrationHelper;

    DataPrePay dataPrePay;

    Intent intentBroadcastAction;
    private static final int MAX_RETRY_COUNT = 5;
    private int retryCount = 0;
    private String token;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qr_emart);
        ButterKnife.bind(this);

        mPgdl = new MyProgressDialog(this);
        logUtil = MyApplication.self().getSaveLogController();
        mposIntegrationHelper = new MposIntegrationHelper();

        initView();

        Intent intent = getIntent();
        if (intent != null) {
            amount = intent.getStringExtra(Constants.AMOUNT_QR);
            typeQR = intent.getStringExtra(Constants.TYPE_QR_NL);
            phone = intent.getStringExtra(Constants.TYPE_PHONE);
            email = intent.getStringExtra(Constants.TYPE_EMAIL);
            orderID = intent.getStringExtra(Constants.ORDER_CODE);

            if (MyUtils.isTypeAppTakashimaya()) {
                String builderAmount = Utils.zenMoney(amount)+ ConstantsPay.CURRENCY_SPACE_PRE;
                tvAmount.setText(builderAmount);
            }

            if (TextUtils.isEmpty(phone)) {
                phone = "**********";
            }
            if (TextUtils.isEmpty(email)) {
                email = "<EMAIL>";
            }
            if (!TextUtils.isEmpty(typeQR)) {
                if (typeQR.equals(Constants.TYPE_PAY_QR_VNPAY)) {
                    bankCode = Constants.BANK_CODE_QR_NL;
                } else if (typeQR.equals(Constants.TYPE_PAY_QR_MOMO)) {
                    bankCode = Constants.BANK_CODE_QR_MOMO_NL;
                } else if (typeQR.equals(Constants.TYPE_PAY_QR_ZALOPAY_NL)) {
                    bankCode = Constants.BANK_CODE_QR_ZALO_NL;
                } 
            }

        }

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            sendActionToEmartByBroadcast(com.mpos.sdk.util.Constants.AP_ACTION_START);
            registerReceiverCancelOrder();
        }

        dataPrePay = createDataPrePay(orderID, Long.parseLong(amount), "", email);
        createQrOrder(phone, amount, email, "VND", "macq",
                "HN", "vi", Constants.URL_CALLBACK_ERR_QR, Constants.URL_CALLBACK_ERR_NOTIFY);
    }

    private DataPrePay createDataPrePay(String orderId, long amount, String desc, String email) {
        return createDataPrePay(orderId, amount, 0, 0, desc, email, null);
    }

    private DataPrePay createDataPrePay(String orderId, long amount, long amountDomestic, long amountInternational, String desc, String email, String udid) {
        DataPrePay dataPrePay = new DataPrePay(amount, desc, email, PrefLibTV.getInstance(ActivityQrEmart.this).getUserId());
        dataPrePay.setOrderId(orderId);
        if (amountDomestic > 0 && amountInternational > 0) {
            dataPrePay.setAmountDomestic(amountDomestic);
            dataPrePay.setAmountInternational(amountInternational);
            dataPrePay.setTrxType(ConstantsPay.TRX_TYPE_SERVICE);
        }
        return dataPrePay;
    }

    private void initView() {
        viewToolBar = new ViewToolBar(this, findViewById(R.id.container));
        viewToolBar.showTextTitle(getString(R.string.title_scan_qr));
        viewToolBar.showButtonBack(true, view -> showDialogCancelTrans());

        if (MyUtils.isTypeAppTakashimaya()) {
            webviewQr.setVisibility(View.GONE);
            view_qr.setVisibility(View.VISIBLE);

            btnCheckStatus.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    autoCheckStatusQr();
                }
            });
        } else {
            pgb.setVisibility(View.VISIBLE);
            webviewQr.setVisibility(View.VISIBLE);
            view_qr.setVisibility(View.GONE);
            webviewQr.getSettings().setJavaScriptEnabled(true);
            webviewQr.setWebViewClient(new WebViewClient() {
                @Override
                public void onPageStarted(WebView view, String url, Bitmap favicon) {
                    Utils.LOGD(TAG, "onPageStarted= " + url);
                    if (!TextUtils.isEmpty(url) && url.equals(Constants.URL_CALLBACK_SUCCESSS_QR)) {
                        Utils.LOGD(TAG, "success= " + url);
                        logUtil.appendLogAction("payment qr success = ");
                        webviewQr.stopLoading();
                        // show success
                        callbackResultToEmart(TRANS_TYPE.SUCCESS);
                        showDialogSuccess();
                    } else if (!TextUtils.isEmpty(url) && url.equals(Constants.URL_CALLBACK_ERR_QR)) {
                        logUtil.appendLogAction("payment qr fail, url= " + url);
                        callbackResultToEmart(TRANS_TYPE.FAIL);
                        showDialogFail();
                    }
                }

                @Override
                public void onPageFinished(WebView view, String url) {
                    super.onPageFinished(view, url);
                    Utils.LOGD(TAG, "onPageFinished= " + url);
                    pgb.setVisibility(View.GONE);
                }

                @Override
                public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                    super.onReceivedError(view, request, error);
                    Utils.LOGD(TAG, "onReceivedError= " + error.getErrorCode());
                    logUtil.appendLogAction("onReceivedError qr code= " + error.getErrorCode());
                    pgb.setVisibility(View.GONE);
                    //retry
                }
            });

        }
    }

    private void createQrOrder(String phone, String amount, String email, String currency, String buyer_fullname,
                               String buyer_address, String language, String url_callback_err, String url_callback_notify) {
        logUtil.appendLogAction("createQrOrder with QR===>");
        mPgdl.showLoading();
        orderCode = ConstantsPay.PREFIX_UDID_QR_NL + (TextUtils.isEmpty(orderID) ? "" : orderID) + Utils.zenUdid() + Constants.CHAR_REPLACE_SPACE_OF_UDID + getTransCode();

//        String orderCode = getRandomString();
        Utils.LOGD(TAG, "orderCode==== " + orderCode);

        String checkSum = buildCheckSumCreateOrder(orderCode, PrefLibTV.getInstance(ActivityQrEmart.this).getUserId(), phone, amount, email, currency, buyer_fullname, buyer_address, language, Constants.URL_CALLBACK_SUCCESSS_QR, url_callback_err, url_callback_notify);
        RequestParams params = new RequestParams();
        params.put("function", Constants.FUNCTION_CREATE_ORDER_QR);
        params.put("merchant_site_code", DataStoreApp.getInstance().getQrNlSiteCode());
        params.put("order_code", orderCode);
        params.put("order_description", PrefLibTV.getInstance(ActivityQrEmart.this).getUserId());
        params.put("amount", amount);
        params.put("currency", currency);
        params.put("buyer_fullname", buyer_fullname);
        params.put("buyer_email", email);
        params.put("buyer_mobile", phone);
        params.put("buyer_address", buyer_address);
        if (MyUtils.isTypeAppTakashimaya()) {
            params.put("version", "2.0");
        }else {
            params.put("version", "1.0");
        }
        params.put("return_url", Constants.URL_CALLBACK_SUCCESSS_QR);
        params.put("cancel_url", url_callback_err);
        params.put("notify_url", url_callback_notify);
        params.put("language", language);
        params.put("payment_method_code", Constants.METHOD_CODE_QR);
        if (vn.mpos.BuildConfig.DEBUG) {
            params.put("bank_code", "NGANLUONG");
        }else {
            params.put("bank_code", bankCode);
        }
        params.put("checksum", checkSum);

        logUtil.appendLogAction("createQrOrder = " + params.toString());
        Utils.LOGD("Data: ", params.toString());

        String url;
        if (MyUtils.isTypeAppTakashimaya()) {
            url = BuildConfig.URL_SERVER_QR_NL_DEV_NEW;
        } else {
            url = BuildConfig.URL_SERVER_QR_NL;
        }
        MposRestClient.getInstance(ActivityQrEmart.this).post(ActivityQrEmart.this, url, params, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    mPgdl.hideLoading();
                    String contentResponse = new String(arg2);
                    JSONObject jRoot = new JSONObject(contentResponse);

                    Utils.LOGD(TAG, "onSuccess createQrOrder: " + contentResponse);
                    logUtil.appendLogAction("onSuccess createQrOrder:");

                    Type type = new TypeToken<BaseResponseQrNl>() {
                    }.getType();
                    BaseResponseQrNl baseResponseQrNl = new Gson().fromJson(jRoot.toString(), type);

                    if ((baseResponseQrNl != null) && baseResponseQrNl.getResultCode().equals(SUCCESS)) {
                        logUtil.appendLogAction("load QrOrder:");
                        if (MyUtils.isTypeAppTakashimaya()) {
                            token = baseResponseQrNl.getResultData().getTokenCode();
//                            _getQrCode(baseResponseQrNl.getResultData().checkoutUrl);
                            if (!baseResponseQrNl.getResultData().getDatQRr().isEmpty()) {
                                // Decode base64 to byte[]
                                byte[] decodedBytes = Base64.decode(baseResponseQrNl.getResultData().getDatQRr(), Base64.DEFAULT);
//                                // Convert to Bitmap
                                Bitmap qrBitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
//                                // Hiển thị lên ImageView
                                imgQr.setImageBitmap(qrBitmap);

                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        autoCheckStatusQr();
                                    }
                                }, 5000); // 5000 milliseconds = 5 seconds
                            }
                        } else {
                            webviewQr.loadUrl(baseResponseQrNl.getResultData().checkoutUrl);
                        }
                    } else {
                        logUtil.appendLogAction("load fail QrOrder:");
                        callbackResultToEmart(TRANS_TYPE.FAIL);
                        MyDialogShow.showDialogError("", getString(R.string.err_create_qr, baseResponseQrNl.getResultCode()), ActivityQrEmart.this, true, new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                cancelAndExits();
                            }
                        });
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(TAG, "onFailure createQrOrder: " + arg3.getMessage());
                logUtil.appendLogAction( "onFailure: createQrOrder " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                mPgdl.hideLoading();
                callbackResultToEmart(TRANS_TYPE.FAIL);
                MyDialogShow.showDialogError("", getString(R.string.err_create_qr), ActivityQrEmart.this, true, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        cancelAndExits();
                    }
                });
            }
        });
    }

    private void autoCheckStatusQr() {
        retryCount = 0;
        btnCheckStatus.setVisibility(View.INVISIBLE);
        checkStatusQrWithRetry(token);
    }

    private void checkStatusQrWithRetry(String token) {
        logUtil.appendLogAction("createQrOrder with QR===> Lần " + (retryCount + 1));
        String checkSum = buildCheckSumFetchStatusQr(token);
        Utils.LOGD(TAG, "checkSum= " + checkSum);
        RequestParams params = new RequestParams();
        params.put("function", Constants.FUNCTION_CHECK_ORDER_QR);
        params.put("merchant_site_code", DataStoreApp.getInstance().getQrNlSiteCode());
        params.put("token_code", token);
        params.put("checksum", checkSum);

        Utils.LOGD("updateStatusQrNl Data: ", params.toString());
        logUtil.appendLogAction("updateStatusQrNl merchant_site_code= " + DataStoreApp.getInstance().getQrNlSiteCode());

        String url;
        if (MyUtils.isTypeAppTakashimaya()) {
            url = BuildConfig.URL_SERVER_QR_NL_DEV_NEW;
        } else {
            url = BuildConfig.URL_SERVER_QR_NL;
        }
        MposRestClient.getInstance(ActivityQrEmart.this).post(ActivityQrEmart.this, url, params, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, Header[] headers, byte[] responseBody) {
                try {
                    String contentResponse = new String(responseBody);
                    JSONObject jRoot = new JSONObject(contentResponse);
                    Utils.LOGD(TAG, "onSuccess loadSalesHistoryQrNl: " + contentResponse);
                    logUtil.appendLogAction("onSuccess loadSalesHistoryQrNl: ");

                    Type listType = new TypeToken<DataTransStatusQrNl>() {}.getType();
                    DataTransStatusQrNl dataTransStatusQrNl = new Gson().fromJson(jRoot.toString(), listType);

                    boolean isSuccess = dataTransStatusQrNl.getResultCode().equals(SUCCESS)
                            && dataTransStatusQrNl.getResultData().getStatus() == 3;

                    if (isSuccess) {
                        callbackResultToEmart(TRANS_TYPE.SUCCESS);
                        showDialogSuccess();
                    } else {
                        retryCount++;
                        if (retryCount < MAX_RETRY_COUNT) {
                            // Gọi lại sau 1 giây (tuỳ chỉnh nếu muốn delay)
                            new Handler(Looper.getMainLooper()).postDelayed(() -> checkStatusQrWithRetry(token), 2000);
                        } else {
                            Utils.LOGD(TAG, "Đã kiểm tra đủ " + MAX_RETRY_COUNT + " lần, dừng lại.");
                            logUtil.appendLogAction("Đã kiểm tra đủ " + MAX_RETRY_COUNT + " lần, dừng lại.");
                            btnCheckStatus.setVisibility(View.VISIBLE);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int statusCode, Header[] headers, byte[] responseBody, Throwable error) {
                Utils.LOGD(TAG, "onFailure loadSalesHistoryQrNl: " + error.getMessage());
                logUtil.appendLogAction("loadSalesHistoryQrNl onFailure: " +
                        MposRestClient.buildMsgErrorOnFail(statusCode, headers, responseBody, error));
                retryCount++;
                if (retryCount < MAX_RETRY_COUNT) {
                    new Handler(Looper.getMainLooper()).postDelayed(() -> checkStatusQrWithRetry(token), 1000);
                } else {
                    Utils.LOGD(TAG, "Đã thử lại đủ " + MAX_RETRY_COUNT + " lần sau lỗi, dừng lại.");
                    logUtil.appendLogAction("Đã thử lại đủ " + MAX_RETRY_COUNT + " lần sau lỗi, dừng lại.");
                }
            }
        });
    }
    private static String getTransCode()
    {
        final Random random=new Random();
        final StringBuilder sb=new StringBuilder(); //size = 6
        for(int i=0;i<6;++i)
            sb.append(random.nextInt(9));
        return sb.toString();
    }
    
    /*
    buildCheckSumCreateOrder:
        merchant_site_code + '|' + order_code + '|' + order_description + '|' + amount + '|' + currency + '|' + buyer_fullname + '|' + buyer_email + '|' +
            buyer_mobile + '|' + buyer_address + '|' + return_url +
            '|' + cancel_url + '|' + notify_url + '|' + language + '|' +
            merchant_passcode)
     */
    private String  buildCheckSumCreateOrder(String udid, String orderDescription, String phone, String amount, String email, String currency,
                                            String buyer_fullname, String buyer_address, String language, String returnUrl, String url_callback_err, String url_callback_notify) {
        Utils.LOGD(TAG, "buildCheckSumCreateOrder checkSum ===== " + DataStoreApp.getInstance().getQrNlSiteCode() + '|' + udid + '|' +
                orderDescription + '|' + amount + '|' + currency + '|' +
                buyer_fullname + '|' + email + '|' +
                phone + '|' + buyer_address + '|' + returnUrl +
                '|' + url_callback_err + '|' + url_callback_notify + '|' + language + '|' +
                DataStoreApp.getInstance().getQrNlPassCode());
        return Utils.md5(DataStoreApp.getInstance().getQrNlSiteCode() + '|' + udid + '|' +
                orderDescription + '|' + amount + '|' + currency + '|' +
                buyer_fullname + '|' + email + '|' +
                phone + '|' + buyer_address + '|' + returnUrl +
                '|' + url_callback_err + '|' + url_callback_notify + '|' + language + '|' +
                DataStoreApp.getInstance().getQrNlPassCode());
    }

    private String buildCheckSumFetchStatusQr(String token_code) {
        Utils.LOGD(TAG, "buildCheckSumFetchStatusQr checkSum ===== " +
                DataStoreApp.getInstance().getQrNlSiteCode() + '|' + token_code + '|' + DataStoreApp.getInstance().getQrNlPassCode());

        String checkSum = DataStoreApp.getInstance().getQrNlSiteCode() + '|' + token_code + '|' + DataStoreApp.getInstance().getQrNlPassCode();
        return Utils.md5(checkSum);
//        return Utils.sha256(checkSum);
    }

    private void callbackResultToEmart(TRANS_TYPE type) {
        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            switch (type) {
                case SUCCESS:
                    callbackResult(buildDataSuccess());
                    break;
                case FAIL:
                    callbackResult(buildDataFail());
                    break;
                case CANCEL:
                    callbackResult(buildDataCancel());
                    break;
                default:
                    break;
            }
        }
    }

    private String buildDataSuccess() {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataSuccessQR(amount, orderID, orderCode);
        }else {
            DataPay dataPay = new DataPay(amount, "", orderCode);
            dataPay.setResign(false);
            dataPay.setWfDetailRes(new WfDetailRes());
            dataPay.setLabel(typeQR);
            dataPay.setAuthCode(orderCode.substring(orderCode.length() - 6));          // 6 số cuối
            return mposIntegrationHelper.buildDataSuccess(dataPrePay, dataPay);
        }
    }

    private void cancelTransAndExits() {
        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            callbackResultToEmart(TRANS_TYPE.CANCEL);
        }
        cancelAndExits();
    }

    private void cancelAndExits() {
        logUtil.appendLogAction("callbackResultToEmart");
        ActivityQrEmart.super.onBackPressed();
        finish();
    }

    private String buildDataCancel() {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_CANCEL_TAKA);
        }else {
            return mposIntegrationHelper.buildDataCancel(dataPrePay, null);
        }
    }

    private String buildDataFail() {
        if (MyUtils.isTypeAppTakashimaya()) {
            return PacketCryptoTakashimaya.buildDataFail(ORDER_CODE_FAIL_TAKA);
        }else {
            return mposIntegrationHelper.buildDataFail(dataPrePay, null, new DataError(-1));
        }
    }


    private void callbackResult(String result) {
        logUtil.appendLogAction("Callback data result QR");
        //build data callback
        intentBroadcastAction = new Intent(nameFilterActionPayment);
//        intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, com.mpos.sdk.util.Constants.AP_ACTION_CALLBACK_DATA);
        if (MyUtils.isTypeAppTakashimaya()) {
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, com.mpos.sdk.util.Constants.AP_ACTION_SEND_MSG_TO_CLIENT);
        }else {
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, com.mpos.sdk.util.Constants.AP_ACTION_BEFORE_SIGNATURE);
        }
        intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_RESULT, result);
        sendBroadcast(intentBroadcastAction);
    }

    @Override
    public void onBackPressed() {
        showDialogCancelTrans();
    }

    private void showDialogCancelTrans() {
        if (ScreenUtils.canShowDialog(this)) {
            MyDialogShow.showDialogCancelAndClick("", getString(R.string.msg_warning_back_qr_screen), getString(R.string.yes), ActivityQrEmart.this, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    cancelTransAndExits();
                }
            }, true, true);
        }
    }

    private void showDialogFail() {
        logUtil.appendLogAction("showDialogFail");
        DataQrPay dataQrPay = new DataQrPay();
        dataQrPay.setSoTienThanhToan(amount);
        dataQrPay.setPaymentUnit(bankCode);           //todo payment unit
        dialogResult = DialogResult.newInstanceForQrNl(DialogResult.RESULT_FAIL, dataQrPay);
        dialogResult.setCancelable(false);

        Utils.LOGD(TAG, "isStateSaved= " + ActivityQrEmart.this.getSupportFragmentManager().isStateSaved());
        if (ScreenUtils.canShowDialog(ActivityQrEmart.this) && !ActivityQrEmart.this.getSupportFragmentManager().isStateSaved()) {
            Utils.LOGD(TAG, "showDialogSuccess: --->show");
            dialogResult.show(ActivityQrEmart.this.getSupportFragmentManager(), DialogResult.class.getName());
        }
        else {
            Utils.LOGD(TAG, "showDialogFail: --> delay");
            handler.removeCallbacksAndMessages(null);
            handler.postDelayed(() -> showDialogSuccess(), 300);
        }
    }

    private void showDialogSuccess() {
        logUtil.appendLogAction("showDialogSuccess");
        DataQrPay dataQrPay = new DataQrPay();
        dataQrPay.setSoTienThanhToan(amount);
        dataQrPay.setPaymentUnit(bankCode);           //todo payment unit
        if (!TextUtils.isEmpty(orderCode)) {
            dataQrPay.setMaGiaoDich(orderCode.substring(orderCode.length() - 6));                  // todo authCode or refNo (QR not return authCode and Refno)
        }

        Utils.LOGD(TAG, "showDialogSuccess");
//        activity.setResult(RESULT_OK);
        dialogResult = DialogResult.newInstanceForQrNl(DialogResult.RESULT_SUCCESS, dataQrPay);
        dialogResult.setCancelable(false);

        // Payment QR
//        dialogResult.setCallback(type -> printReceiptQROffline(null));

        Utils.LOGD(TAG, "isStateSaved= " + ActivityQrEmart.this.getSupportFragmentManager().isStateSaved());
        if (ScreenUtils.canShowDialog(ActivityQrEmart.this) && !ActivityQrEmart.this.getSupportFragmentManager().isStateSaved()) {
            Utils.LOGD(TAG, "showDialogSuccess: --->show");
            dialogResult.show(ActivityQrEmart.this.getSupportFragmentManager(), DialogResult.class.getName());
            dialogResult.startCoundDismissDialog(10000);
        }
        else {
            Utils.LOGD(TAG, "showDialogSuccess: --> delay");
            handler.removeCallbacksAndMessages(null);
            handler.postDelayed(() -> showDialogSuccess(), 300);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (webviewQr != null) {
            webviewQr.onPause();
            webviewQr.removeAllViews();
            webviewQr.destroyDrawingCache();
            webviewQr.destroy();
        }
        logUtil.saveLog();
        logUtil.pushLog();
        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            sendActionToEmartByBroadcast(com.mpos.sdk.util.Constants.AP_ACTION_END);
        }
        unregisterTCPReceiver();
    }

    private void sendActionToEmartByBroadcast(String action) {
        intentBroadcastAction = new Intent(nameFilterActionPayment);
        intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, action);
        sendBroadcast(intentBroadcastAction);
    }

    ReceiverTCPCancelOrder receiverTCPCancelOrder;
    private void registerReceiverCancelOrder() {
        Utils.LOGD(TAG, "registerReceiverCancelOrder");

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intents.nameFilterActionCancelOrder);

        receiverTCPCancelOrder = new ReceiverTCPCancelOrder();
        try {
            //Register or UnRegister your broadcast receiver here
            registerReceiver(receiverTCPCancelOrder, filter);
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException " + e.getMessage());
            e.printStackTrace();
        }
    }
    private void unregisterTCPReceiver() {
        if (receiverTCPCancelOrder != null) {
            unregisterReceiver(receiverTCPCancelOrder);
        }
    }
    public class ReceiverTCPCancelOrder extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Utils.LOGD(TAG, "ReceiverTCPCancelOrder onReceive" + intent.getAction());
            logUtil.appendLogAction("ReceiverTCPCancelOrder onReceive");
            if (intent.getBooleanExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, false)) {
                String dataCancelOrder = mposIntegrationHelper.buildDataCancel(dataPrePay, null);
                if (!TextUtils.isEmpty(dataCancelOrder)) {
                    cancelTransAndExits();
                    if (PrefLibTV.getInstance(context).getPermitSocket()) {
                        cancelTransAndExits();
                    }
                }
            }
        }
    }


    enum TRANS_TYPE {
        SUCCESS,
        CANCEL,
        FAIL
    }
}