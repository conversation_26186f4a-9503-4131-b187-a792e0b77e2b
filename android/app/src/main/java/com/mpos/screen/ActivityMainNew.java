package com.mpos.screen;

import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.IntentSender;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

//import com.datecs.pinpad.Pinpad;
//import com.datecs.pinpad.PinpadException;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.ResolvableApiException;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.LocationSettingsStatusCodes;
import com.google.android.gms.location.SettingsClient;
import com.loopj.android.http.AsyncHttpResponseHandler;
//import com.mpos.audioreaderemv.inject.InjectKey;
import com.mpos.common.CheckCodePTIController;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.customview.DialogDetailReversal;
import com.mpos.customview.MposDialog;
import com.mpos.dspread.DeviceListActivity;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataFromPartner;
//import com.mpos.pinpad.PinpadHelper;
//import com.mpos.pinpad.PinpadManager;
//import com.mpos.pinpad.TransactionException;
import com.mpos.screen.mart.ActivityHomeMart;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.DownloadConfigReader;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.LibInjectKey;
import com.mpos.sdk.core.control.LibKozenP5;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.LibReportError;
import com.mpos.sdk.core.control.LibSignature;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.BluetoothReaderPair;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.EmvConfigSp01;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.HexUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MyOnClickListenerView;
import com.mpos.sdk.util.PayUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.ConfigIntegrated;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;
import com.whty.smartpos.tysmartposapi.pos.PosConstrants;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Locale;

import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class ActivityMainNew extends AppCompatActivity implements FragmentHomeNew.ItfActionHomeScreen {// implements FragmentAccount.ItfHandlerClickInAccount

    String TAG = "ActivityMainNew";

    final int RQ_PERMISSION_LOCATION = 15;
    final int RQ_PERMISSION_STORAGE = 16;
    /**
     * Constant used in the location settings dialog.
     */
    private static final int REQUEST_CHECK_SETTINGS = 11;
    private static final int REQUEST_UPDATE_READER = 12;

    private MyProgressDialog mPgdl;
    private ToastUtil	mToast;
    private SaveLogController logUtil;

//    private PinpadManager mPinpadManager;


    boolean mFlagEMV = false;
    boolean mFlagUpdateFW = false;

    private int deviceType;
    private long mStartPressBack = 0;

    private String currBankName = "";
    private String serialNumber = "";

    private LibDspreadReader dspreadManager;
    private LibReportError libReportError;
    protected LibInjectKey libInjectKey;

    private Intent intentServiceLocation;
    private int countRequestPermissionLocation = 0;
    private int countRequestPermissionStorage = 0;

    /**
     * The desired interval for location updates. Inexact. Updates may be more or less frequent.
     */
    private static final long UPDATE_INTERVAL_IN_MILLISECONDS = 10000;

    /**
     * The fastest rate for active location updates. Exact. Updates will never be more frequent
     * than this value.
     */
    private static final long FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS = UPDATE_INTERVAL_IN_MILLISECONDS / 2;

    /**
     * Provides access to the Location Settings API.
     */
    private SettingsClient mSettingsClient;

    /**
     * Stores parameters for requests to the FusedLocationProviderApi.
     */
    private LocationRequest mLocationRequest;

    /**
     * Stores the types of location services the client is interested in using. Used for checking
     * settings to determine if the device has optimal location settings.
     */
    private LocationSettingsRequest mLocationSettingsRequest;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
//        setContentView(R.layout.activity_main_new);

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        ButterKnife.bind(this);

        mPgdl = new MyProgressDialog(this);
        mToast = new ToastUtil(this);
        logUtil = MyApplication.self().getSaveLogController();

        serialNumber = PrefLibTV.getInstance(this).getSerialNumber();

//        boolean isDisableCheckGps = DataStoreApp.getInstance().getIsDisableCheckGps();
//        if (!isDisableCheckGps) {
//            checkPermissionLocation();
//        }

        checkPermissions();

//        initView();
        FragmentManager fragmentManager = getSupportFragmentManager();
        Fragment fragment = fragmentManager.findFragmentByTag(FragmentHomeNew.class.getSimpleName());
        FragmentHomeNew fragmentHomeNew;
        if (fragment == null) {
            fragmentHomeNew = new FragmentHomeNew();
        }
        else {
            fragmentHomeNew = (FragmentHomeNew) fragment;
        }
        fragmentHomeNew.setItfActionHomeScreen(this);
        fragmentManager.beginTransaction()
                .replace(R.id.container, fragmentHomeNew, FragmentHomeNew.class.getSimpleName())
                .commit();

        initData();

    }

    private void checkPermissions() {
        boolean isDisableCheckGps = DataStoreApp.getInstance().getIsDisableCheckGps();
        Utils.LOGD(TAG, "isDisableCheckGps= " + isDisableCheckGps);
        logUtil.appendLogAction("isDisableCheckGps= " + isDisableCheckGps);
        if (!isDisableCheckGps) {
            checkPermissionLocation();
        } else if (DevicesUtil.isPax()) {
            checkPermissionStorage();
        }
    }

    private void checkPermissionStorage() {
        if (MyUtils.checkHaveStoragePermission(getApplicationContext())) {      //auto run with pax
            Utils.LOGD(TAG, "Have Storage Permission");
            logUtil.appendLogAction("Have Storage Permission");
            checkUpgradeEmvConfigPax();
        } else {
            Utils.LOGD(TAG, "Have not Storage Permission");
            logUtil.appendLogAction("Have not Storage Permission");
//            Toast.makeText(this, getString(R.string.need_allow_access_storage), Toast.LENGTH_LONG).show();
            if (countRequestPermissionStorage < 3) {
                countRequestPermissionStorage++;
                MyUtils.requestStoragePermission(this, RQ_PERMISSION_STORAGE);
            }
        }
    }

    public FragmentTransaction getMyFragmentManager() {
        return this.getSupportFragmentManager().beginTransaction();
    }

    // check emv config with pax
    public void checkUpgradeEmvConfigPax() {
        boolean needUpdateConfig = PrefLibTV.getInstance(ActivityMainNew.this).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE);

        if (needUpdateConfig) {
            logUtil.appendLogAction("not exist emvConfig or upgradeEMVConfig");
            Utils.LOGD(TAG, "not exist Emv Config or need upgradeEMVConfig");
//            showDialogWarningUpgradeEmvConfig();

            int numSkipUpdate = getNumSkipUpdate();
            boolean hideCancel = numSkipUpdate <= 0;
            String nameBtnClose = numSkipUpdate > 0 ? String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSkipUpdate) : "";
            String msg = getString(R.string.warning_have_upgrade_evm_config);

            showDialogWarningUpdateReader(msg, hideCancel, nameBtnClose, view -> {
                fetchEmvConfigPax();
            }, view -> {
                setNumSkipUpdate(numSkipUpdate - 1);
            });
        }
    }

    private void setNumSkipUpdate(int data) {
        PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.numSaleRemain, data);
    }

    private void checkPermissionLocation() {
        Utils.LOGD(TAG, "checkPermissionLocation: ---->");
        if (MyUtils.checkHaveLocationPermission(getApplicationContext())) {
//            if (!DevicesUtil.isP20L()) {
//                intentServiceLocation = new Intent(this, MyServiceLocation.class);
//                startLocationService(intentServiceLocation);
//            }

            if (DevicesUtil.isP20L()) {
                if (checkHavePlayServices()) {
                    requestLocation();
                }
            }
            else {
                String lat = PrefLibTV.getInstance(ActivityMainNew.this).getLatitude();
                String lng = PrefLibTV.getInstance(ActivityMainNew.this).getLongtitude();
                if (TextUtils.isEmpty(lat) || TextUtils.isEmpty(lng) || lat.equals(Constants.SVALUE_0) || lng.equals(Constants.SVALUE_0) ) {
                    processGetLocation();
                }
            }

            if (DevicesUtil.isPax()) {
                checkPermissionStorage();
            }

        }
        else {
            mToast.showToast(getString(R.string.need_allow_access_location));
            if (countRequestPermissionLocation >= 3) {
                finish();
            }
            else {
                countRequestPermissionLocation++;
                MyUtils.requestLocationPermission(this, RQ_PERMISSION_LOCATION);
            }
        }
    }

    private boolean checkHavePlayServices() {
        int result = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(getApplicationContext());
        Utils.LOGD(TAG, "checkHavePlayServices: "+GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(getApplicationContext()));
        return result == ConnectionResult.SUCCESS;
    }

    private void processGetLocation() {
//        LocationManagerMp locationManagerMp = new LocationManagerMp(ActivityMainNew.this);
//        locationManagerMp.setCbLocation(location -> {
//            locationManagerMp.stopGetLocation();
//        });
//        locationManagerMp.startGetLocation();
    }

//    private void startLocationService(Intent intentServiceLocation) {
//        Log.d(TAG, "startLocationService: ===>>");
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            startForegroundService(intentServiceLocation);
//        }
//        else {
//            startService(intentServiceLocation);
//        }
//    }

    protected void requestLocation() {
        Log.d(TAG, "requestLocation: ");
        appendLogEnableGps("request location");
        createLocationRequest();
        buildLocationSettingsRequest();
        mSettingsClient = LocationServices.getSettingsClient(this);
        startLocationUpdates();
    }

    private void createLocationRequest() {
        mLocationRequest = new LocationRequest();

        // Sets the desired interval for active location updates. This interval is
        // inexact. You may not receive updates at all if no location sources are available, or
        // you may receive them slower than requested. You may also receive updates faster than
        // requested if other applications are requesting location at a faster interval.
        mLocationRequest.setInterval(UPDATE_INTERVAL_IN_MILLISECONDS);

        // Sets the fastest rate for active location updates. This interval is exact, and your
        // application will never receive updates faster than this value.
        mLocationRequest.setFastestInterval(FASTEST_UPDATE_INTERVAL_IN_MILLISECONDS);

        //mLocationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        mLocationRequest.setPriority(LocationRequest.PRIORITY_LOW_POWER);
    }

    private void buildLocationSettingsRequest() {
        LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder();
        builder.addLocationRequest(mLocationRequest);
        mLocationSettingsRequest = builder.build();
    }

    private void startLocationUpdates() {
        appendLogEnableGps("start location update");
        // Begin by checking if the device has the necessary location settings.
        mSettingsClient.checkLocationSettings(mLocationSettingsRequest)
                .addOnSuccessListener(this, locationSettingsResponse -> {
                    appendLogEnableGps("success");
                })
                .addOnFailureListener(this, e -> {
                    int statusCode = ((ApiException) e).getStatusCode();
                    appendLogEnableGps("fail: code=" + statusCode);
                    switch (statusCode) {
                        case LocationSettingsStatusCodes.RESOLUTION_REQUIRED:
                            Log.i(TAG, "Location settings are not satisfied. Attempting to upgrade location settings ");
                            try {
                                // Show the dialog by calling startResolutionForResult(), and check the
                                // result in onActivityResult().
//                                ResolvableApiException rae = (ResolvableApiException) e;
                                ResolvableApiException rae = new ResolvableApiException(((ApiException) e).getStatus());

                                rae.startResolutionForResult(ActivityMainNew.this, REQUEST_CHECK_SETTINGS);
                            } catch (IntentSender.SendIntentException sie) {
                                Log.i(TAG, "PendingIntent unable to execute request.");
                            } catch (Exception e1) {
                                Log.e(TAG, "startLocationUpdates: ", e1);
                            }
                            break;
                        case LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE:
                            String errorMessage = "Location settings are inadequate, and cannot be fixed here. Fix in Settings.";
                            Log.e(TAG, errorMessage);
                            Toast.makeText(ActivityMainNew.this, errorMessage, Toast.LENGTH_LONG).show();
//                                mRequestingLocationUpdates = false;
                    }
                });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (DevicesUtil.isP20L() && DataStoreApp.getInstance().getIsDissableButton()) {
            try {
                MyApplication.self().getLibP20L().getSmartPosApi().disableExpand(new int[]{PosConstrants.DISABLE_NONE});
                DataStoreApp.getInstance().setIsDisableButton(false);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        checkHaveTransWaitSignatureMacq();

        if (libReportError == null) {
            libReportError = new LibReportError(this);
        }
        libReportError.processSendCacheData();
    }

    @Override
    public void onBackPressed() {
        Utils.LOGD(TAG, "onbackpress: getFragmentManager().getBackStackEntryCount()="+getFragmentManager().getBackStackEntryCount());
//        testAddview();
        if (getSupportFragmentManager().getBackStackEntryCount() > 0) {
            getSupportFragmentManager().popBackStack();
        }
        else if (System.currentTimeMillis() - mStartPressBack < 1500) {
            super.onBackPressed();
        }
        else {
            mStartPressBack = System.currentTimeMillis();
            mToast.showToast(getString(R.string.alert_confirm_exit_app));
        }
    }

    private void initData() {
        MyApplication.self().initMposSdk();

        runOnUiThread(this::checkUpdateCaPulicKey);

        boolean hasWaitSignature = checkHaveTransWaitSignatureBank();
        currBankName = PrefLibTV.getInstance(this).getBankName();
        if (!hasWaitSignature) {
            deviceType = PrefLibTV.getInstance(this).getFlagDevices();
            if (!checkAndShowUpgradeFWPr02() && !checkAndShowUpdateConfig()) {
                if (deviceType == ConstantsPay.DEVICE_AUDIO || deviceType == ConstantsPay.DEVICE_PINPAD
                        || deviceType == ConstantsPay.DEVICE_NONE
                        || !checkNeedAutoInjectKey()) {
                    checkHaveDataIntegrated();
                }
            }
        }
        checkVersionBinLocal();
    }

    private void checkVersionBinLocal() {
        int currVerBinLocal = PrefLibTV.getInstance(this).get(PrefLibTV.currVerBinLocal, Integer.class, 0);
        int serverVerBinLocal = PrefLibTV.getInstance(this).get(PrefLibTV.serverVerBinLocal, Integer.class, 0);
        if (currVerBinLocal < serverVerBinLocal) {
            startLoadListBinLocal();
        }
    }

    private void gotoHistoryMacq() {
        Intent i = new Intent(this, ActivityPaymentHistory.class);
        startActivity(i);
    }

    private void gotoHomeEnter(DataFromPartner dataFromPartner) {
        Intent intentType1 = new Intent(this, ActivityHomeEnter.class);
        intentType1.putExtra(Intents.EXTRA_DATA_PARTNER, dataFromPartner);
        startActivity(intentType1);
        finish();
    }

    protected void gotoLogin(){
        PrefLibTV.getInstance(this).clearDataAuto();
        DataStoreApp.getInstance().clearData();

        MyDialogShow.gotoReLogin(this);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (intentServiceLocation != null) {
            stopService(intentServiceLocation);
        }
        if (logUtil != null) {
            logUtil.saveLog();
        }
    }

    /**
     * CHECK WAIT SIGNATURE TRANSACTION
     */

    private boolean checkHaveTransWaitSignatureBank() {

        final DataReversalLogin dataReversal = (DataReversalLogin) getIntent().getSerializableExtra("dataReversalLogin");
        if (dataReversal != null) {
            PayUtils payUtils = new PayUtils();
            if (payUtils.checkSkipSignature(getApplicationContext(), dataReversal.amount)) {
                runSkipSignature(dataReversal);
            } else {
                logUtil.appendLogAction("- show DialogDetailReversal");
                Utils.LOGD(TAG, "show DialogDetailReversal");
                showDialogReversalTrans(dataReversal);
            }
            return true;
        }

        return false;
    }

    private void checkHaveTransWaitSignatureMacq() {
        if (MyApplication.self().isRunMA()) {
            if(DataStoreApp.getInstance().getDataByKey(DataStoreApp.hasWaitSignatureMacq, Boolean.class, false)){
                MyDialogShow.showDialogContinueCancel(getString(R.string.alert_have_reversal_trans_ma),
                        this, false, new MyOnClickListenerView(view -> gotoHistoryMacq()));
            }
        }
    }

    private boolean checkAndShowUpgradeFWPr02() {
        boolean result = false;
        if (deviceType == ConstantsPay.DEVICE_DSPREAD && checkHaveUpgradeFwPr02()) {
            result = true;
            showDialogWarningUpgradeFwPr02();
        }
        return result;
    }

    private boolean checkAndShowUpdateConfig() {
        boolean result = false;
        if (deviceType == ConstantsPay.DEVICE_P20L && checkHaveUpgradeEmvConfigSP01()) {
            result = true;
            showDialogWarningUpgradeEmvConfig();
        }
        else if (deviceType == ConstantsPay.DEVICE_DSPREAD && checkHaveUpgradeEmvConfigPr02()) {
            result = true;
            showDialogWarningUpgradeEmvConfig();
        }
        return result;
    }


    private void runSkipSignature(DataReversalLogin dataReversal) {
        final DataPay dataPay = new DataPay(dataReversal);
        LibSignature libSignature = new LibSignature(this, dataPay, new LibSignature.ItfResultSignature() {
            @Override
            public void appendLogSignature(String s) {
                logUtil.appendLogAction(s);
            }

            @Override
            public void onFailureSignature(int typeFail, DataError dataError, int typeVoidFail, boolean requestLogin) {
//                returnFailurePayment(dataPay, dataError, typeFail, typeVoidFail, requestLogin);
                logUtil.appendLogAction("-----Fail auto reSignature------");
            }

            @Override
            public void onSuccessSignature() {
                logUtil.appendLogAction("-----Success auto reSignature------");
            }

//            @Override
//            public void onSuccessSignature(WfDetailRes wfDetailRes) {
//                logUtil.appendLogAction("-----Success auto reSignature------");
//            }
        });
        libSignature.confirmPayment(null, 1);
    }

    private void showDialogReversalTrans(final DataReversalLogin dataReversal) {
        DialogDetailReversal mdialog = new DialogDetailReversal();
        mdialog.setCancelable(false);
        mdialog.initVariable(dataReversal.transactionDate, dataReversal.pan,
                dataReversal.amount, dataReversal.cardholderName, dataReversal.itemDesc);

        mdialog.setClickListener(new DialogDetailReversal.OnMyClickListener() {
            @Override
            public void clickOk(DialogFragment d, String email) {
                logUtil.appendLogAction("- select continue");
                logUtil.saveLog();
                gotoSignature(dataReversal, email);
                d.dismiss();
            }

            @Override
            public void clickCancel(DialogFragment d) {
                logUtil.appendLogAction("- select cancel");
                logIn(d, dataReversal.pw);
            }
        });
        mdialog.show(getSupportFragmentManager(), DialogDetailReversal.class.getName());
    }

    private void gotoSignature(DataReversalLogin dataReversal, String email){
        Intent i = new Intent(this, ActivityPrePayment.class);
//        Intent i = new Intent(this, ActivityInsertCard.class);

        DataPay dataPay = new DataPay(dataReversal);
        dataPay.setEmail(email);

        i.putExtra(Intents.EXTRA_DATA_PAY_MP, dataPay);

        if (dataPay.getUdid().startsWith(ConstantsPay.PREFIX_UDID_MERCHANT_INTEGRATED)) {
            String orderId = dataPay.getUdid().substring(ConstantsPay.PREFIX_UDID_MERCHANT_INTEGRATED.length() + 1);
            orderId = orderId.substring(0, orderId.indexOf("-"));
            Utils.LOGD(TAG, "gotoSignature: orderId="+orderId);
            DataFromPartner dataPartner = checkOrderIdReSignWithOrderIdReceiver(orderId);//new DataFromPartner()
            if (dataPartner == null) {
                dataPartner = new DataFromPartner();
                dataPartner.setOrderId(orderId);
            }
            i.putExtra(Intents.EXTRA_DATA_PARTNER, dataPartner);
        }
        startActivity(i);
    }

    public void logIn(final DialogFragment dialog, final String pin) {
        logUtil.appendLogRequestApi("LOGIN "+TAG);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.LOGIN);
            jo.put("readerSerialNo", serialNumber);
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(this).getUserId());
            jo.put("userPIN", pin);
            jo.put("appId", "mpos.vn");
            jo.put("forceReversal", 1); // true

            Utils.LOGD("Data: ", jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(this).getSessionKey()));
        }
        catch (Exception e) {
            Utils.LOGE(TAG, "logIn: ", e);
        }

        MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this), entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        mPgdl.showLoading("");
                        super.onStart();
                    }
                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        String msg = "";
                        try {
                            JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityMainNew.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityMainNew.this).setSessionKey( jRoot.getString("sessionKey"));
                            mPgdl.hideLoading();
                            Utils.LOGD("Login: ", jRoot.toString());
                            if (jRoot.has("error")) {
                                try {
                                    final JSONObject jError = jRoot.getJSONObject("error");
                                    int errCode =  jError.getInt("code");
                                    msg = String.format("%s %02d: %s", getString(R.string.error), errCode, LibError.getErrorMsg(errCode, ActivityMainNew.this));
//                                    msg = getString(R.string.error) + " " + String.format("%02d", jError.getInt("code"))
//                                            + ": " + LibError.getErrorMsg(jError.getInt("code"), ActivityMainNew.this);
                                } catch (JSONException e) {
                                    Utils.LOGE(TAG, "login JSONException: ", e);
                                    msg = getString(R.string.error_try_again);
                                }
                            } else {
                                dialog.dismiss();
                                MyDialogShow.showDialogError(getString(R.string.VOID_PAYMENT_SUCCESS), ActivityMainNew.this);
                                logUtil.appendLogRequestApi("LOGIN(ActivityMainNew) success");
                                logUtil.saveLog();
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "login Exception: ", e);
                            msg = getString(R.string.error_try_again);
                        }
                        if(!TextUtils.isEmpty(msg)){
                            logUtil.appendLogRequestApi("LOGIN(ActivityMainNew) Exception(timeout|parse json)");
                            logUtil.saveLog();
                            MyDialogShow.showDialogRetryCancelFinish("",msg, ActivityMainNew.this, v -> logIn(dialog, pin), true);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        logUtil.appendLogRequestApiFail("LOGIN(ActivityMainNew) onFailure", arg2);
                        logUtil.saveLog();
                        Utils.LOGE(TAG, "Login onFailure: ", arg3);
                        mPgdl.hideLoading();
                        MyDialogShow.showDialogRetryCancelFinish("",getString(R.string.error_onfaild_request), ActivityMainNew.this, v -> logIn(dialog, pin), true);
                    }
                });
    }


    private void checkHaveDataIntegrated() {
        Utils.LOGD(TAG, ">> checkHaveDataIntegrated <<");
//		DataIntegrated dataIntegrated = myAppData.getDataIntegrated();
        DataFromPartner dataFromPartner = (DataFromPartner) getIntent().getSerializableExtra(Intents.EXTRA_DATA_PARTNER);
        String connectType = DataStoreApp.getInstance().getConnectType();
        if (dataFromPartner != null) {
            //note: TYPE NULL
            if (connectType.equals(""))
            {
                //note: On 11/16/17, at 5:19 PM, AnhPT-MPOS wrote:
                //note: > a check luôn TH MC ko có tích hợp thanh toán, mà click từ app/web của MC link sang mpos thì chặn luôn ko cho vào thanh toán nhé
                //note: > ĐVCNT chưa tích hợp thanh toán nên không thể tiếp tục thanh toán bằng hình thức này. Vui lòng sử dụng chức năng “Thanh toán thường”.

                final MposDialog mposDialogError = MyUtils.initDialogGeneralError(ActivityMainNew.this, 0,
                        getString(R.string.txt_DVCNT_chuatichhop), ActivityMainNew.class.getName());
                mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
                mposDialogError.show();
            }
            else {
                /*==================================================================================
                //      note:        DANG NHAP BANG READER
                //      note:        DANG NHAP BANG READER
                //      note:        DANG NHAP BANG READER
                //      note:        DANG NHAP BANG READER
                //      note:        DANG NHAP BANG READER
                //==================================================================================*/
                if (DataStoreApp.getInstance().isUseReader()) {
                    Utils.LOGD(TAG, "DANG NHAP BANG THIET BI MPOS, ConnectType == "+connectType);
                    switch (connectType)
                    {
						 /* connectType == 1 */
                        case ConfigIntegrated.TYPE_APP1_APP2_APP1:
                            gotoHomeEnter(dataFromPartner);
                            break;

						/* connectType == 2 */
						/*====== case ConfigIntegrated.TYPE_APP2_SER2_SER2:
						Intent intent = new Intent(this, ActivityMainNew.class);
						startActivity(intent);
						finish();
						break;*/

						/* connectType == 3 */
                        case ConfigIntegrated.TYPE_APP1_SER2_SER2_APP1:
                            checkOrderCodePTI(dataFromPartner);
                            break;
                    }
                }
                /*==============================================================================
                //    note:   DANG NHAP BANG EMAIL
                //==============================================================================*/
                else
                {
                    Utils.LOGD(TAG, "DANG NHAP BANG EMAIL, ConnectType == "+connectType);
                    if (!DataStoreApp.getInstance().isMerchantRegistedQR() && !connectType.equals(ConfigIntegrated.TYPE_APP1_SER2_SER2_APP1))
                    {
                        //note: this merchant NOT register use mVISA
                        //note: ĐVCNT chưa đăng ký thanh toán MPOS QR. Vui lòng liên hệ hotline &hot_line; để đăng ký hoặc đăng nhập lại với thiết bị đọc thẻ để thanh toán
                        final MposDialog mposDialogError = MyUtils.initDialogGeneralError(ActivityMainNew.this, 0,
                                getString(R.string.txt_login_email_and_not_accept_mvisa), ActivityMainNew.class.getName());
                        mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
                        mposDialogError.show();
                    } else {
                        switch (connectType)
                        {
							/* connectType == 1 */
                            case ConfigIntegrated.TYPE_APP1_APP2_APP1:
                                gotoHomeEnter(dataFromPartner);
                                break;

							/* connectType == 2 */
							/*======case ConfigIntegrated.TYPE_APP2_SER2_SER2:
								Intent intent = new Intent(this, ActivityMainNew.class);
								startActivity(intent);
								finish();
								break;*/

							/* connectType == 3 */
                            case ConfigIntegrated.TYPE_APP1_SER2_SER2_APP1:
                                checkOrderCodePTI(dataFromPartner);
                                break;
                        }
                        Utils.LOGD(TAG, "-----------------------------------------------------------------");
                    }
                }
            }
        }
    }


    private DataFromPartner checkOrderIdReSignWithOrderIdReceiver(String orderId) {
        DataFromPartner tempData = (DataFromPartner) getIntent().getSerializableExtra(Intents.EXTRA_DATA_PARTNER);
        logUtil.appendLogAction("check equal OrderId: reSign=" + orderId + " recei:" + (tempData == null ? "null" : tempData.getOrderId()));
        if (tempData != null && !TextUtils.isEmpty(orderId) && orderId.equalsIgnoreCase(tempData.getOrderId())) {

            return tempData;
        }
        return null;
    }

    private void checkOrderCodePTI(final DataFromPartner dataFromPartner) {
        final String udid = MposUtil.getInstance().createUdidForIntegration(dataFromPartner.getOrderId(), Utils.zenUdid());

        CheckCodePTIController ptiController = new CheckCodePTIController(this, true, false,
                new CheckCodePTIController.ResultCheckCodePTI() {
                    @Override
                    public void onResultCheckCodePTI(DataFromPartner dataPartner) {
                        if (dataPartner.isHaveError()) {
                            DataError dataError = new DataError();
                            dataError.setMsg(dataPartner.getMsgError());
                            dataError.setErrorCode(-1);

                            try {
                                MposUtil.getInstance().callbackFailToPartner(ActivityMainNew.this, dataError, dataFromPartner);
                            } catch (Exception e) {
                                e.printStackTrace();
                                MyDialogShow.showDialogCancelAndClick("", e.getMessage(), getString(R.string.ALERT_BTN_OK), ActivityMainNew.this, v -> finish(), false, true);
                            }
                        } else {
                            Intent intent = new Intent(getApplicationContext(), ActivityHomeEnter.class);
                            intent.putExtra(Intents.EXTRA_DATA_PARTNER, dataPartner);
                            intent.putExtra(IntentsMP.EXTRA_PARTNER_IS_INPUT_CODE, true);
                            intent.putExtra(IntentsMP.EXTRA_IS_GO_SWIPE_CARD, true);
                            intent.putExtra(IntentsMP.EXTRA_PARTNER_UDID, udid);
                            startActivity(intent);
                            finish();
                        }
                    }

                    @Override
                    public void onResultGotoMVisaWithQRID(DataFromPartner dataPartner, String qrid) {
                        gotoPaymentMVisa(dataPartner, qrid, udid);
                    }
                });
        ptiController.getInfoOrderId(dataFromPartner.getOrderId(), udid, dataFromPartner);
    }


    private void gotoPaymentMVisa(DataFromPartner dataPartner, String qrid, String udid) {
        Utils.LOGD(TAG, "---------------------");
        Utils.LOGD(TAG, ">>> gotoPaymentMVisa");
        Intent i = new Intent(getApplicationContext(), ActivityScanQRCode.class);

        Bundle bundle = new Bundle();
        bundle.putString(Constants.KEY_MVISA_AMOUNT, dataPartner.getAmount());
        bundle.putString(Constants.KEY_MVISA_NOTE,   dataPartner.getDescription());
        bundle.putString(Constants.KEY_MVISA_EMAIL_CUSTOMER, dataPartner.getEmailReceipt());
        bundle.putString(Constants.KEY_MVISA_QRID, qrid);
        bundle.putString(Constants.KEY_MVISA_QRCODE, dataPartner.getQrCode());
        bundle.putString(Constants.KEY_MVISA_UDID, udid);

        if (!TextUtils.isEmpty(dataPartner.getQrType())) {
            bundle.putString(Constants.KEY_MVISA_TYPE, dataPartner.getQrType());
        }

        i.putExtra(Constants.KEY_EXTRA_MVISA, bundle);
        Utils.LOGD(TAG, "qrid = "+qrid+" amount = "+dataPartner.getAmount()+ " email = "+dataPartner.getEmailReceipt() +" descMVisa = "+dataPartner.getDescription());
        logUtil.appendLogAction("qrid = "+qrid+" amount = "+dataPartner.getAmount()+ " email = "+dataPartner.getEmailReceipt() +" descMVisa = "+dataPartner.getDescription());
        logUtil.saveLog();

        startActivity(i);
        finish();
    }

    /**
     * start CAPK
     */
    private void checkUpdateCaPulicKey() {
        Utils.LOGD(TAG, "checkUpdateCaPulicKey: ----->>");
        logUtil.appendLogAction(" checkUpdateCaPulicKey ");
        //Check update config
//        switch (getIntent().getIntExtra("flag_devices", 0)) {
//            case 1 :
//                try {
//                    JSONObject jo = new JSONObject(getIntent().getStringExtra("config"));
//                    if (jo.has("emv_version") && jo.getBoolean("emv_version")) {
//                        mFlagEMV = true;
//                    }
//
//                    if (!jo.getString("fw_url").equals("")) {
//                        mFlagUpdateFW = true;
//                    }
//
//                    if (mFlagUpdateFW || mFlagEMV) {
//                        Intent i = new Intent(this, InjectKey.class);
//                        i.putExtra("emv_flag", mFlagEMV);
//                        i.putExtra("emv_config", jo.getString("emv_config"));
//
//                        i.putExtra("fw_flag", mFlagUpdateFW);
//                        i.putExtra("fw_url", jo.getString("fw_url"));
//                        startActivity(i);
//                    }
//                } catch (JSONException e) {
//                    Utils.LOGE(TAG, "checkUpdateCaPulicKey JSONException:", e);
//                }
//                break;
//            case 2 :
//                try {
//                    if (PrefLibTV.getInstance(ActivityMainNew.this).getCAKey()) {
//                        try {
//                            mPinpadManager = PinpadManager.getInstance(this);
//                            loadKeys();
//                        } catch (Exception e) {
//                            Utils.LOGE(TAG, "update CAPublicKey Exception", e);
//                        }
//                    }
//                } catch (Exception e) {
//                    Utils.LOGE(TAG, "checkUpdateCaPulicKey Exception:", e);
//                }
//                break;
//
//            default :
//                break;
//        }
    }

//    private void loadKeys() {
//        logUtil.appendLogAction(" loadKeys ");
//        invokeHelper(pinpad -> {
//            PinpadHelper.showBusy(pinpad);
//            // Load CA keys.
//            int idx = 0;
//            try {
//                JSONArray ja = new JSONArray(PrefLibTV.getInstance(ActivityMainNew.this).getCAData());
////					Utils.LOGD(TAG, "invoke: caData="+ja.toString());
//                JSONObject jo;
//                for (int i = 0, length = ja.length(); i < length; i++) {
//                    jo = ja.getJSONObject(i);
//                    String ridi = jo.getString("rid")+" "+jo.getString("index");
//                    String exponent = jo.getString("exponent");
//                    String modulus = jo.getString("modulus");
//
////						Utils.LOGD(TAG, "invoke: idx="+idx+" ridi="+ridi+" exponect="+exponent+" modulus="+modulus);
//                    logUtil.appendLogAction("invoke: idx="+idx+" ridi="+ridi+" exponect="+exponent);
//                    pinpad.caImportKey(idx++, decode(ridi), decode(exponent), decode(modulus));
////						pinpad.caImportKey(idx++, decode(jo.getString("rid")+" "+jo.getString("index")), decode(jo.getString("index")), decode(jo.getString("modulus")));
//                }
//            }
//            catch (Exception e) {
//                logUtil.appendLogException(" loadkeys error: "+e.getMessage());
//                Utils.LOGE(TAG, "load keys Exception", e);
//            }
//
//            Utils.LOGD(TAG, "invoke: after run update CA public key");
//
//            // Write CA keys to flash.
//            pinpad.caWriteKeysToFlash();
//            PinpadHelper.initScreen(pinpad);
//
//            runOnUiThread(this::updateCAKey);
//
////				getCA(pinpad);
//        });
//    }


    private byte[] decode(String s) {
        return HexUtil.hexStringToByteArray(s, ' ');
    }

    public void updateCAKey() {

        logUtil.appendLogRequestApi(Config.UPDATE_CA_PUBLIC_KEY);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPDATE_CA_PUBLIC_KEY);
            jo.put("readerSerialNo", serialNumber);
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityMainNew.this).getUserId());
            jo.put("sessionKey", PrefLibTV.getInstance(ActivityMainNew.this).getSessionKey());
            Utils.LOGD("Data: ", jo.toString());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), PrefLibTV.getInstance(ActivityMainNew.this).getSessionKey()));
        } catch (Exception e1) {
            Utils.LOGE(TAG, "updateCAKey: "+ e1.getMessage());
        }

        MposRestClient.getInstance(this).post(ActivityMainNew.this, ConstantsPay.getUrlServer(this), entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        mPgdl.showLoading("");
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        try {
                            mPgdl.hideLoading();
                            JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(ActivityMainNew.this).getSessionKey()));
                            PrefLibTV.getInstance(ActivityMainNew.this).setSessionKey( response.getString("sessionKey"));
                            Utils.LOGD("Update ca key: ", response.toString());
                            if (response.has("error")) {
                                try {
                                    final JSONObject jo = response.getJSONObject("error");
                                    String msg = getString(R.string.error) + " " + jo.getInt("code")
                                            + ": " + LibError.getErrorMsg(jo.getInt("code"), ActivityMainNew.this);
                                    MyDialogShow.showDialogErrorFinish(msg, ActivityMainNew.this);
                                    logUtil.appendLogRequestApi(Config.UPDATE_CA_PUBLIC_KEY +" error from server:"+msg);
                                } catch (JSONException e) {
                                    Utils.LOGE(TAG, "Exception", e);
                                    MyDialogShow.showDialogErrorFinish(getString(R.string.error_default), ActivityMainNew.this);
                                }
                            } else {
                                logUtil.appendLogRequestApi(Config.UPDATE_CA_PUBLIC_KEY+" success");
                                mToast.showToast(getString(R.string.msg_success_update_ca_public_key));
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "Exception", e);
                            logUtil.appendLogRequestApi(Config.UPDATE_CA_PUBLIC_KEY +" error:"+e.getMessage());
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityMainNew.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        mPgdl.hideLoading();
                        Utils.LOGE(TAG, "confirm payment Error: ", arg3);
                        logUtil.appendLogRequestApi(Config.UPDATE_CA_PUBLIC_KEY+" error: request time out");
                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityMainNew.this,
                                v -> updateCAKey(), true);
                    }
                });
    }

    @Override
    public void onSelectUpdateConfigReader(int typeUpdate) {
        switch (typeUpdate) {
            case FragmentHomeNew.ItfActionHomeScreen.TYPE_FW:
                showDialogWarningUpgradeFwPr02();
                break;
            case FragmentHomeNew.ItfActionHomeScreen.TYPE_CONFIG:
                showDialogWarningUpgradeEmvConfig();
                break;
        }
    }

//    private interface MethodInvoker {
//        void invoke(Pinpad pinpad) throws IOException, TransactionException;
//    }
//
//    private void invokeHelper(final ActivityMainNew.MethodInvoker invoker) {
//
//        final Pinpad pinpad = mPinpadManager.getPinpad();
//        if (pinpad == null) {
//            logUtil.appendLogAction(" not connect pinpad -> relogin");
//            MyDialogShow.showDialogCancelAndClick("", getString(R.string.msg_request_relogin_for_update_capk), getString(R.string.txt_go_login), this,
//                    v -> gotoLogin(), false, true);
//            return;
//        }
//
//        final ProgressDialog dialog = new ProgressDialog(this);
//        dialog.setCancelable(false);
//        dialog.setCanceledOnTouchOutside(false);
//        dialog.setMessage(getString(R.string.msg_please_wait));
//        dialog.setOnKeyListener((dialog1, keyCode, event) -> true);
//
//        dialog.show();
//
//        final Thread t = new Thread(() -> {
//            try {
//                invoker.invoke(pinpad);
//            } catch (PinpadException e) { // Non critical exception
//                Utils.LOGE(TAG, "Exception", e);
//                StringWriter sw = new StringWriter();
//                e.printStackTrace(new PrintWriter(sw));
////					String stacktrace = sw.toString();
//            } catch (IOException e) { // Critical exception
//                Utils.LOGE(TAG, "Exception", e);
//                mPinpadManager.disconnect();
//            } catch (TransactionException e) { // Non critical exception
//                Utils.LOGE(TAG, "Exception", e);
//            } finally {
//                dialog.dismiss();
//            }
//        });
//        t.start();
//    }

    private void appendLogEnableGps(String log) {
        logUtil.appendLogAction("Enable gps: " + log );
    }
    private void appendLogEmvConfig(String log) {
        logUtil.appendLogAction("U_EMV_CONFIG " + log );
    }

    private void appendLogInjectKey(String log) {
        Utils.LOGD(TAG, "appendLogInjectKey: " + log);
        logUtil.appendLogAction("IJ:  " + log);
    }

    private boolean checkHaveUpgradeFwPr02() {
        return PrefLibTV.getInstance(this).get(PrefLibTV.upgradeFw, Boolean.class, Boolean.FALSE);
    }
    private boolean checkHaveUpgradeEmvConfigPr02() {
        boolean result = false;
        if (PrefLibTV.getInstance(this).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE)) {
            appendLogEmvConfig("PR02 need update Emv Config");
            result = true;
        }
        return result;
    }
    private boolean checkHaveUpgradeEmvConfigSP01() {
        boolean result = false;
        if (PrefLibTV.getInstance(this).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE)) {
            appendLogEmvConfig("SP01 need update Emv Config");
            result = true;
        }
        return result;
    }

    private void showDialogWarningUpgradeFwPr02() {
        int numSkipUpdate = getNumSkipUpdate();

        String msg = DevicesUtil.isP20L() ? getString(R.string.p20l_warning_upgrade_fw) : getString(R.string.warning_upgrade_fw);
        boolean hideCancel = numSkipUpdate > 0;
        String nameBtnClose = numSkipUpdate > 0 ? String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSkipUpdate) : "";

//        com.mpos.sdk.util.MyDialogShow.showDialogWarning(this,
//                msg,
//                getString(R.string.BTN_UPGRADE),
//                numSkipUpdate > 0 ? String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSkipUpdate) : "",
//                view -> callMposSdkUpgradeFw(),
//                numSkipUpdate > 0 ? view -> appendLogEmvConfig("cancel upgrade fw") : null,
//                false);

        showDialogWarningUpdateReader(msg, hideCancel, nameBtnClose, view -> callMposSdkUpgradeFw(),null);
    }

    private void callMposSdkUpgradeFw() {
        Utils.LOGD(TAG, "callMposSdkUpgradeFw: ====>>");
        try {
            MyApplication.self().getMposSdk().updatePOSConfig(this, REQUEST_UPDATE_READER, false);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void showDialogWarningUpgradeEmvConfig() {

        String msg = getString(R.string.warning_have_upgrade_evm_config);

        boolean hideCancel;
        if (deviceType == ConstantsPay.DEVICE_P20L) {
            hideCancel = true;
        } else {
            int numSaleRemain = getNumSkipUpdate();
            hideCancel = numSaleRemain <= 0;
        }

        showDialogWarningUpdateReader(msg, hideCancel, null, view -> {
            if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
                downloadEmvConfig();}
            else {
                fetchEmvConfigSmartpos();
            }
        },null);
    }

    private void showDialogWarningUpdateReader(String msg, boolean hideCancel, String nameBtnClose, View.OnClickListener onClickOk, View.OnClickListener onClickCancel) {

        Dialog dialog = new Dialog(this, R.style.SpecialDialog);
        LayoutInflater inflater = (LayoutInflater) getSystemService(LAYOUT_INFLATER_SERVICE);
        View dialogLayout = inflater.inflate(R.layout.dialog_upgrade_fw_reader, null);

//        Utils.LOGD(TAG, "showDialogWarningUpdateReader: msg=" + msg);
        ((TextView) dialogLayout.findViewById(R.id.tv_description)).setText(msg);

        dialogLayout.findViewById(R.id.btn_update_now).setOnClickListener(v -> {
            if (onClickOk != null) {
                onClickOk.onClick(v);
            }
            dialog.dismiss();
        });


        Button btnClose = dialogLayout.findViewById(R.id.btn_close);
        if (hideCancel) {
            btnClose.setVisibility(View.GONE);
        }
        else {
            btnClose.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(nameBtnClose)) {
                btnClose.setText(nameBtnClose);
            }
            btnClose.setOnClickListener(v -> {
                if (onClickCancel != null) {
                    onClickCancel.onClick(v);
                }
                dialog.dismiss();
            });
        }

        dialog.setCancelable(false);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;

        dialog.setCanceledOnTouchOutside(false);
        dialog.setContentView(dialogLayout);
        dialog.show();
    }

    private int getNumSkipUpdate() {
        return PrefLibTV.getInstance(this).get(PrefLibTV.numSaleRemain, Integer.class, com.mpos.sdk.util.Constants.NUM_SALE_REMAIN_DEFAULT);
    }


    DownloadConfigReader downloadConfigReader;
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.d(TAG, "onRequestPermissionsResult() called with: requestCode = [" + requestCode + "], permissions = [" + Arrays.toString(permissions) + "], grantResults = [" + Arrays.toString(grantResults) + "]");
        switch (requestCode) {
            case RQ_PERMISSION_LOCATION:
                checkPermissionLocation();
                break;
            case RQ_PERMISSION_STORAGE:
                checkPermissionStorage();
                break;
//            case DownloadConfigReader.RQ_PERMISSION_STORAGE:
//                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//                    if (downloadConfigReader != null) {
//                        downloadConfigReader.processDownloadConfig();
//                    }
//                }
//                break;
        }
    }

    private void downloadEmvConfig() {
        appendLogEmvConfig("start check Url");
        String urlApp = PrefLibTV.getInstance(this).get(PrefLibTV.urlEmvApp, String.class, "");
        String urlCapk = PrefLibTV.getInstance(this).get(PrefLibTV.urlEmvCapk, String.class, "");
        if (!TextUtils.isEmpty(urlApp) && !TextUtils.isEmpty(urlCapk)) {

            if (downloadConfigReader == null) {
                downloadConfigReader = new DownloadConfigReader(this, urlApp, urlCapk, new DownloadConfigReader.ItfProcessDownloadConfig() {
                    @Override
                    public void appendLog(String log) {
                        appendLogEmvConfig(log);
                    }

                    @Override
                    public void showLoading(boolean show, String msg) {
                        if (show) {
                            mPgdl.showLoading(msg);
                        }
                        else {
                            mPgdl.hideLoading();
                        }
                    }

                    @Override
                    public void onSuccessDownloadConfig(String emvConfigApp, String emvConfigCapk) {
                        connectToPr02UpdateConfig(emvConfigApp, emvConfigCapk);
                    }

                    @Override
                    public void onErrorDownloadConfig(int result, String msg) {
                        MyDialogShow.showDialogError(msg, ActivityMainNew.this);
                    }
                });
            }
            downloadConfigReader.processDownloadConfig();

        }
        else {
            appendLogEmvConfig("not found url:"+(TextUtils.isEmpty(urlApp)?"EmvApp ":" ") + (TextUtils.isEmpty(urlCapk)?"Capk":""));
        }
    }

    private void fetchEmvConfigPax() {
        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), this, v -> fetchEmvConfigPax());
            return;
        }
        logUtil.appendLogRequestApi("fetchEmvConfigPax");
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(this, new DownloadConfigReader.ItfProcessDownloadConfigPax() {
                @Override
                public void showLoading(boolean show) {
                    ActivityMainNew.this.showLoading(show);
                }

                @Override
                public void onErrorDownloadConfig(String msg) {
                    Utils.LOGD(TAG, "onFailure: " + msg);
                    logUtil.appendLogRequestApi("fetchEmvConfigPax onFailure--" +msg);
                    hideLoading();
                    MyDialogShow.showDialogError(getString(R.string.msg_error_load_data_no_param), ActivityMainNew.this);
                }

                @Override
                public void onSuccessDownloadConfigPax(String config) {
                    logUtil.appendLogAction("onSuccessDownloadConfigPax: " + deviceType);
                    Utils.LOGD(TAG, "onSuccessDownloadConfigPax= " + config);
                    PrefLibTV.getInstance(ActivityMainNew.this).saveEmvConfigStorage(config);
                    PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.upgradeEMVConfig, false);
                    tickUpdateEmvConfigSuccess();
                }

                @Override
                public void appendLog(String log) {
                    appendLogEmvConfig(log);
                }
            });
        }
        downloadConfigReader.processDownloadConfig();
    }

    private void fetchEmvConfigSmartpos() {
        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), this, v -> fetchEmvConfigSmartpos());
            return;
        }
        logUtil.appendLogRequestApi("fetchEmvConfigSmartpos");
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(this, new DownloadConfigReader.ItfProcessDownloadConfigSp01() {
                @Override
                public void appendLog(String log) {
                    appendLogEmvConfig(log);
                }

                @Override
                public void showLoading(boolean show) {
                    ActivityMainNew.this.showLoading(show);
                }

                @Override
                public void onErrorDownloadConfig(String msg) {
                    Utils.LOGD(TAG, "onFailure: " + msg);

                    logUtil.appendLogRequestApi("fetchEmvConfigSp01 onFailure--" +msg);
                    hideLoading();
                    MyDialogShow.showDialogError(getString(R.string.msg_error_load_data_no_param), ActivityMainNew.this);

                }

                @Override
                public void onSuccessDownloadConfigSp01(String config) {
                    logUtil.appendLogAction("onSuccessDownloadConfigSmartpos: " + deviceType);
                    if (deviceType == ConstantsPay.ReaderType.SP02.getReaderType()) {
                        handlerDataEmvConfigSp02(config);
                    }
                    else {
                        handlerDataEmvConfigSp01(config);
                    }
                }
            });
        }
        downloadConfigReader.processDownloadConfig();
    }

    private void handlerDataEmvConfigSp02(String content) {
        EmvConfigSp01 emvConfigSp02 = MyGson.parseJson(content, EmvConfigSp01.class);
        if (emvConfigSp02 != null) {
            LibKozenP5 libKozenP5 = new LibKozenP5(this);
            libKozenP5.setCallBackSaveLog((typeLog, s) -> appendLogEmvConfig(s));
            libKozenP5.processUpdateEmvConfig(emvConfigSp02);
            tickUpdateEmvConfigSuccess();
        }
    }

    private void handlerDataEmvConfigSp01(String content) {
//        content = validateContentEmvConfig(content);
        EmvConfigSp01 emvConfigSp01 = MyGson.parseJson(content, EmvConfigSp01.class);
        if (emvConfigSp01 != null) {
//            processUpdateConfigSp01(emvConfigSp01);
            LibP20L libP20L = new LibP20L(this);
            libP20L.setCallBackSaveLog((typeLog, s) -> appendLogEmvConfig(s));
            libP20L.processUpdateConfigSp01(emvConfigSp01);
            tickUpdateEmvConfigSuccess();
//            MyDialogShow.showDialogInfo(this, getString(R.string.alert_update_emv_config_sp01_success));
        }
    }

    private final int ACT_TYPE_UPDATE_CONFIG    = 0;
    private final int ACT_TYPE_INJECT_KEY       = 1;
    private void connectToPr02UpdateConfig(String emvConfigApp, String emvConfigCapk) {
        connectToPr02ActionByType(ACT_TYPE_UPDATE_CONFIG, emvConfigApp, emvConfigCapk);
    }

    private void connectToPr02InjectKey() {
        connectToPr02ActionByType(ACT_TYPE_INJECT_KEY, null, null);
    }

    private void connectToPr02ActionByType(int type, String emvConfigApp, String emvConfigCapk) {
        appendLogEmvConfig("connect to reader: "+type);
        showLoading(getString(R.string.msg_connecting_device_please_wait, serialNumber));
        if (dspreadManager == null) {
            dspreadManager = new LibDspreadReader(this);
            dspreadManager.setTypeController(LibDspreadReader.TYPE_CONTROLLER_GET_SERIAL_NUMBER);
            if (type == ACT_TYPE_UPDATE_CONFIG) {
                dspreadManager.setCbUpdateConfig((isSuccess, result) -> {
                    appendLogEmvConfig("result updateEmvConfig=" + isSuccess + " ->" + result);
                    hideLoading();
                    if (isSuccess) {
                        tickUpdateEmvConfigSuccess();
                    } else {
                        MyDialogShow.showDialogRetryCancel(getString(R.string.update_config_reader_fail), this,
                                view -> downloadEmvConfig(), true);
                    }
                });
            }
            dspreadManager.setCallBackConnectDevice(new LibDspreadReader.ResultConnectDspread() {
                @Override
                public void onSuccessConnectDevice(BluetoothReaderPair dspreadDevicePair) {
                    if (type == ACT_TYPE_UPDATE_CONFIG) {
                        showLoading(getString(R.string.updating_config_reader));
                        appendLogEmvConfig("start update emv config");
                        dspreadManager.getPos().updateEmvConfig(emvConfigApp, emvConfigCapk);
                    }
                    else {
                        hideLoading();
                        appendLogInjectKey("connected PR02");
                        if (MyApplication.self().isRunMA()) {
                            processGetIpekFromMA();
                        }
                        else {
                            processGetIpekFromBank();
                        }
                    }
                }

                @Override
                public void onFailConnectDevice(BluetoothReaderPair dspreadDevicePair) {
                    Utils.LOGD(TAG, "onFailConnectDevice: ");
                    mPgdl.hideLoading();
                    appendLogEmvConfig("fail connect device");
                    MyDialogShow.showDialogRetry(getString(R.string.error_not_connect_reader),
                            ActivityMainNew.this, view -> connectToPr02ActionByType(type, emvConfigApp, emvConfigCapk));
                }

                @Override
                public void foundDevices(ArrayList<BluetoothDevice> arrayList) {

                }
            });
        }
        dspreadManager.initPinpadStartTransaction();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode) {
            case LibReaderController.REQUEST_ENABLE_BT: {
                if (resultCode == RESULT_OK) {
                    dspreadManager.checkAutoConnectReader();
                } else {
                    finish();
                }
                break;
            }
            case LibReaderController.REQUEST_DEVICE_DSPREAD: {
                if (resultCode == RESULT_OK && data != null && data.getExtras() != null) {
                    String address = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
                    String serialnumber = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_SERIALNUMBER);
                    dspreadManager.connectToDeviceByBluetoothAddress(new BluetoothReaderPair(serialnumber, address));
                } else {
                    finish();
                }
            }
            case REQUEST_CHECK_SETTINGS:
                switch (resultCode) {
                    case Activity.RESULT_OK:
                        Utils.LOGD(TAG, "User agreed to make required location settings changes.");
                        // Nothing to do. startLocationupdates() gets called in onResume again.
                        break;
                    case Activity.RESULT_CANCELED:
                        Utils.LOGD(TAG, "User chose not to make required location settings changes.");
//                        mRequestingLocationUpdates = false;
                        showDialogMustEnableGps();
                        break;
                }
                break;
            default:
                break;
        }
    }

    private void hideLoading() {
        showLoading(false);
    }
    private void showLoading(boolean show) {
        showLoading(show, null);
    }
    private void showLoading(String msg) {
        showLoading(true, msg);
    }
    private void showLoading(boolean show, String msg) {
        if (show) {
            mPgdl.showLoading(msg);
        }
        else {
            mPgdl.hideLoading();
        }
    }

    void showDialogMustEnableGps() {
        MyDialogShow.showDialogCancelAndClick("", getString(R.string.ALERT_LOCATION_SERVICE_ANDROID_MSG), getString(R.string.ok), this,
                view -> startLocationUpdates(), true, true);
    }

    public void tickUpdateEmvConfigSuccess() {

        logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS+" sn="+serialNumber);
        PrefLibTV.getInstance(this).put(PrefLibTV.upgradeEMVConfig, false);

        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.UPGRADE_EMV_CONFIG_SUCCESS);
            jo.put("readerSerial", serialNumber);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", Config.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(ActivityMainNew.this).getUserId());
            Utils.LOGD("Data: ", jo.toString());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "updateCAKey: "+ e1.getMessage());
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        mPgdl.showLoading();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        mPgdl.hideLoading();
                        try {
                            JsonParser jsonParser = new JsonParser();
                            BaseObjJson errorBean = new BaseObjJson();
                            JSONObject jRoot = new JSONObject(new String(arg2));
                            jsonParser.checkHaveError(jRoot, errorBean);
                            Utils.LOGD(TAG, "onSuccess: "+ jRoot);
                            if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                                handleTickUpdateSuccess();
                            }
                        } catch (Exception e) {
                            Utils.LOGE(TAG, "Exception", e);
                            logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS +" error:"+e.getMessage());
                            MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), ActivityMainNew.this);
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        mPgdl.hideLoading();
                        Utils.LOGE(TAG, "confirm payment Error: ", arg3);
                        logUtil.appendLogRequestApi(Config.UPGRADE_EMV_CONFIG_SUCCESS+" error: request time out");
                        MyDialogShow.showDialogRetryCancelFinish("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), ActivityMainNew.this,
                                v -> tickUpdateEmvConfigSuccess(), true);
                    }
                });
    }

    private void handleTickUpdateSuccess() {
        PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.upgradeEMVConfig, false);
        MyApplication.self().clearCacheLoginInSDK();

        if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
            PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.numSaleRemain, com.mpos.sdk.util.Constants.NUM_SALE_REMAIN_DEFAULT);
            mToast.showToast(getString(R.string.status_successful));
        }
        else {
            MyDialogShow.showDialogInfo(ActivityMainNew.this, getString(R.string.update_config_reader_success));
        }
        // hide view update in FragmentHome
        try {
            FragmentHomeNew fragmentHomeNew = (FragmentHomeNew) getSupportFragmentManager().findFragmentByTag(FragmentHomeNew.class.getSimpleName());
            if (fragmentHomeNew != null) {
                fragmentHomeNew.checkShowViewUpgrade();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startLoadListBinLocal() {
        loadListBinLocal(1);
    }
    private void loadListBinLocal(int counter) {

        logUtil.appendLogRequestApi(ConstantsPay.GET_BIN_LOCAL + "->"+counter);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.GET_BIN_LOCAL);
            Utils.LOGD(TAG, "Data: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "loadListBinLocal: "+ e1.getMessage());
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY, entity,
                Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        String content = new String(arg2);
                        Utils.LOGD(TAG, "loadListBinLocal onSuccess: "+content);
                        PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.listBinLocal, content);
                        PrefLibTV.getInstance(ActivityMainNew.this).put(PrefLibTV.currVerBinLocal,
                                PrefLibTV.getInstance(ActivityMainNew.this).get(PrefLibTV.serverVerBinLocal, Integer.class, 0));
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        Utils.LOGE(TAG, "loadListBinLocal Error: ", arg3);
                        logUtil.appendLogRequestApi(ConstantsPay.GET_BIN_LOCAL+" error: request time out"+(counter));
                        if (counter < 3) {
                            loadListBinLocal(counter + 1);
                        }
                        else {
                            mToast.showToast("can't load list BinLocal");
                        }
                    }
                });
    }

    // auto inject
    private String readerInjected;
    private boolean checkNeedAutoInjectKey() {
        readerInjected = PrefLibTV.getInstance(this).get(PrefLibTV.readersInjected, String.class, "");
        try {
            appendLogInjectKey("injected: " + readerInjected);
            String lastBankInject = "";
            if (!TextUtils.isEmpty(readerInjected)) {
                JSONObject jRoot = new JSONObject(readerInjected);
                Iterator<String> keys = jRoot.keys();
                while (keys.hasNext()) {
                    String snInjected = keys.next();
                    if (snInjected.equals(serialNumber)) {
                        lastBankInject = jRoot.getString(snInjected);
                    }
                }
            }
            appendLogInjectKey("isMutilAcquire=" + MyApplication.self().isRunMA() + " lastBankInjected=" + lastBankInject + " currBank=" + currBankName);
            if (MyApplication.self().isRunMA() && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(ConstantsPay.MPOS_MULTI_ACQUIRER))) {
                AppExecutors.getInstance().mainThread().execute(this::processInjectMA);
//                processInjectMA();
                return true;
            }
            else if (!MyApplication.self().isRunMA() && (TextUtils.isEmpty(lastBankInject) || !lastBankInject.equals(currBankName))) {
                AppExecutors.getInstance().mainThread().execute(this::processInjectBank);
//                processInjectBank();
                return true;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return false;
    }

    private void saveInjected() {
        try {
            JSONObject jRoot;
            if (TextUtils.isEmpty(readerInjected)) {
                jRoot = new JSONObject();
            }
            else {
                jRoot = new JSONObject(readerInjected);
            }
            jRoot.put(serialNumber, currBankName);
            appendLogInjectKey("save injected: " + jRoot);
            PrefLibTV.getInstance(this).put(PrefLibTV.readersInjected, jRoot.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void processInjectBank() {
        libInjectKey = new LibInjectKey(this, deviceType, PrefLibTV.getInstance(this).getFlagServer(), serialNumber);
        libInjectKey.setCbLog((typeLog, s) -> logUtil.appendLogAction(s));
        Utils.LOGD(TAG, "processInjectBank: -->" + deviceType);
        libInjectKey.setCallback((i, i1, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type="+i+" res="+i1+" ->"+s);
            if (i == LibInjectKey.TYPE_KEY_END) {
                saveInjected();
            }
        });
        libInjectKey.setCallbackShowLoading(this::showLoading);
        if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
            connectToPr02InjectKey();
            libInjectKey.setDspreadControl(dspreadManager);
        }
        else {
            processGetIpekFromBank();
        }
    }

    private void processGetIpekFromBank() {
        appendLogInjectKey("start get Ipek bank");
        if (libInjectKey != null) {
            libInjectKey.processInjectKey();
        }
    }

    private void processInjectMA() {
        libInjectKey = new LibInjectKey(this, deviceType, ConstantsPay.SERVER_MPOS_ACQUIRER, serialNumber);
        libInjectKey.setCbLog((typeLog, s) -> logUtil.appendLogAction(s));
        Utils.LOGD(TAG, "processInjectMA: -->" + deviceType);
        libInjectKey.setCallback((i, i1, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type="+i+" res="+i1+" ->"+s);
            if (i == LibInjectKey.TYPE_KEY_END) {
                saveInjected();
            }
        });
        libInjectKey.setCallbackShowLoading(this::showLoading);
        if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
            connectToPr02InjectKey();
            libInjectKey.setDspreadControl(dspreadManager);
        }
        else {
            processGetIpekFromMA();
        }
    }

    private void processGetIpekFromMA() {
        appendLogInjectKey("start get Ipek MA");
        if (libInjectKey != null) {
            libInjectKey.getIpekFromMA();
        }
    }

}
