package com.mpos.screen.login;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mpos.delegates.Singleton;
import com.mpos.screen.HomeNotify;
import com.mpos.utils.MposUtil;

import java.util.concurrent.Executor;

import vn.mpos.R;

public class LoginActivity extends HomeNotify {

    private static final String TAG = "LoginActivity";

    private LoginPresenter loginPresenter;

    private Executor executor;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // ngăn việc app khởi động lại khi click icon
//        if (!isTaskRoot()
//                && getIntent().hasCategory(Intent.CATEGORY_LAUNCHER)
//                && getIntent().getAction() != null
//                && getIntent().getAction().equals(Intent.ACTION_MAIN)) {
//            finish();
//            return;
//        }

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

//        checkUpdateByPax();

        LoginView loginView = new LoginView(this);
        loginPresenter = new LoginPresenter(this, loginView, this);
        loginPresenter.setIntent(getIntent());
        loginView.start();

        finishScreenInsertCardIfExit();
        getSizeDevice(this);
    }

//    private void checkUpdateByPax() {
//        executor = Executors.newSingleThreadExecutor();
//        executor.execute(() -> {
//            StoreDownloadManager.getInstance().checkUpdate(new StoreCheckUpdateListener() {
//                @Override
//                public boolean onCheckResVersion(String resName, String version) {
//                    Utils.LOGD(TAG, "onCheckResVersion name= " + resName + " ver= " + version);
//                    return true;
//                }
//
//                @Override
//                public void onProgress(int progress) {
//                    Utils.LOGD(TAG, "progesss= " + progress);
////                    runOnUiThread(() -> binding.tvContent.setText(progress + "%"));
//                }
//
//                @Override
//                public boolean onPermitInstall() {
//                    Utils.LOGD(TAG, "onPermitInstall= ");
//                    return true;
//                }
//
//                @Override
//                public void onComplete(boolean needInstall, List<ResourceFile> resourceFiles) {
//                    StringBuilder stringBuilder = new StringBuilder();
//                    if (resourceFiles != null && resourceFiles.size() > 0) {
//                        //SharedPreferences.Editor editor = sp.edit();
//                        for (ResourceFile resourceFile : resourceFiles) {
//                            Utils.LOGD(TAG, "getResName= " + resourceFile.getResName() + " ver = " + resourceFile.getVersion());
////                            if (!sp.getString(resourceFile.getResName(), "").equals(resourceFile.getVersion())) {
////                                // need update resource
////                                File resFile = new File(resourceFile.getResFilePath());
////                                // TODO parse and save resource
////
////                                // save resource version
//////                                editor.putString(resourceFile.getResName(), resourceFile.getVersion());
////
////
////                                stringBuilder.append("[")
////                                        .append(resourceFile.getResName())
////                                        .append(":")
////                                        .append(resourceFile.getVersion())
////                                        .append("]\n");
////                            }
//                        }
//                        //editor.apply();
//                    }
//                    runOnUiThread(() -> Utils.LOGD(TAG, "Check update completed."
//                            + "\nNeed install: " + needInstall
//                            + "\nRes files: " + stringBuilder.toString()));
//                }
//
//                @Override
//                public void onError(int code, String msg) {
//                    runOnUiThread(() -> Utils.LOGD(TAG, "Check update error\ncode:" + code + "\nmsg:" + msg));
//                }
//            });
//        });
//    }

    private void finishScreenInsertCardIfExit() {
        MposUtil.getInstance().sendMessageFinishToScreenInsertCard(this);
    }

    private void getSizeDevice(Activity activity) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        Singleton.getInstance().setWidthScreen(displayMetrics.widthPixels);
        Singleton.getInstance().setHeightScreen(displayMetrics.heightPixels);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (loginPresenter != null) {
            loginPresenter.setIntent(intent);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        loginPresenter.handlerActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        loginPresenter.handleRequestPermission(requestCode, permissions, grantResults);
    }


}
