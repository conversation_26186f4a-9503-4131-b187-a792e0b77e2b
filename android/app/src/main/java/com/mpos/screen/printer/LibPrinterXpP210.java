package com.mpos.screen.printer;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Bitmap;
import android.os.IBinder;
import android.util.Log;

import com.mpos.sdk.core.control.LibPrinterBluetooth;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.Utils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;

import net.posprinter.posprinterface.IMyBinder;
import net.posprinter.posprinterface.UiExecute;
import net.posprinter.service.PosprinterService;
import net.posprinter.utils.BitmapToByteData;
import net.posprinter.utils.DataForSendToPrinterPos80;
import net.posprinter.utils.DataForSendToPrinterTSC;

import java.util.ArrayList;

import vn.mpos.R;

/**
 * Create by anhnguyen on 29/08/2022
 */
public class LibPrinterXpP210 extends LibPrinterBluetooth {

    private static final String TAG = "LibPrinterXpP210";

    public static final String NAME_XPRINTER = "XP-P210";

    //IMyBinder interface，All methods that can be invoked to connect and send data are encapsulated within this interface
    public static IMyBinder binder;

    //bindService connection
    ServiceConnection conn = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            //Bind successfully
            binder = (IMyBinder) iBinder;
            Log.e(TAG, "P210 binder connected");
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
            Log.e(TAG, "P210 disbinder disconnected");
        }
    };

    public static boolean ISCONNECT;
    MyProgressDialog pgdl;

    public LibPrinterXpP210(Context context) {
        super(context);
        Utils.LOGD(TAG, "LibPrinterXpP210: bindService--->");
        Intent intent = new Intent(context, PosprinterService.class);
        context.bindService(intent, conn, Context.BIND_AUTO_CREATE);
        pgdl = new MyProgressDialog(context);
    }

    public boolean isConnected() {
        return ISCONNECT;
    }

    public void connect2BlueToothDevice() {
        if (binder == null) {
            // wait 3s to bin services
            try {
                Utils.LOGD(TAG, "connect2BlueToothDevice: wait 3s");
                Thread.sleep(2000);
                if (binder == null) {
                    Utils.LOGD(TAG, "connect2BlueToothDevice: binder still null");
                    return;
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        showHideLoading(true);
        Utils.LOGD(TAG, "connect2BlueToothDevice: devicesAddress=" + devicesAddress);
        updateStageProcessing(PRINT_CONNECTTING);
        binder.connectBtPort(devicesAddress, new UiExecute() {
            @Override
            public void onsucess() {
                showHideLoading(false);
                Utils.LOGD(TAG, "onsucess: connect XP-P210 bitmapWait="+(bitmapPrintAfterConnected==null?"null":"exit"));
                ISCONNECT = true;
                showToast(getString(R.string.xpp210_con_success));
//                BTCon.setText(getString(R.string.con_success));
                PrefLibTV.getInstance(context).createBluetoothAddressPrinter(devicesAddress, NAME_XPRINTER);

                if (bitmapPrintAfterConnected != null) {
                    printBitmap(bitmapPrintAfterConnected);
                }
                binder.write(DataForSendToPrinterPos80.openOrCloseAutoReturnPrintState(0x1f), new UiExecute() {
                    @Override
                    public void onsucess() {
                        binder.acceptdatafromprinter(new UiExecute() {
                            @Override
                            public void onsucess() {

                            }

                            @Override
                            public void onfailed() {
                                ISCONNECT = false;
                                showToast(getString(R.string.xpp210_con_has_discon));
                            }
                        });
                    }

                    @Override
                    public void onfailed() {

                    }
                });
            }

            @Override
            public void onfailed() {
                showHideLoading(false);
                Utils.LOGD(TAG, "onfailed: connect XP-P210");
                ISCONNECT = false;
                showToast(getString(R.string.xpp210_con_failed));
            }
        });
    }

    void printBitmap(Bitmap bitmapOriginal) {
        if (binder == null || bitmapOriginal == null) {
            Utils.LOGD(TAG, "printBitmap: binder || bitmap is null");
            return;
        }
        showHideLoading(true);
        Utils.LOGD(TAG, "printBitmap: ==> w=" + bitmapOriginal.getWidth() + " h=" + bitmapOriginal.getHeight());
        binder.clearBuffer();
        binder.writeDataByYouself(new UiExecute() {
            @Override
            public void onsucess() {
                Utils.LOGD(TAG, "onsucess: printBitmap");
//                relativeLayout.setVisibility(View.VISIBLE);
//                imageView.setImageBitmap(b);
                showHideLoading(false);
                showToast(getString(R.string.xpp210_notice_printer_after_5s));
            }

            @Override
            public void onfailed() {
                Utils.LOGD(TAG, "onfailed: printBitmap");
                showHideLoading(false);
            }
        }, () -> {
            ArrayList<byte[]> list = new ArrayList<>();
            list.add(DataForSendToPrinterTSC.cls());

//            list.add(DataForSendToPrinterTSC.sizeBydot(bitmap.getWidth(), bitmap.getHeight()+70));
            list.add(DataForSendToPrinterTSC.sizeBymm(58, 57.0 * bitmapOriginal.getHeight() / bitmapOriginal.getWidth()));
//            list.add(DataForSendToPrinterTSC.sizeBymm(58,210));
//            list.add(DataForSendToPrinterTSC.gapBymm(1, 0));
            list.add(DataForSendToPrinterTSC.direction(1));
//            list.add(DataForSendToPrinterTSC.cls());
            // X - Specify the x-coordinate
            // Y Specify the y-coordinate width Image width (in bytes) height Image height (in dots)
            // mode Graphic modes listed below: 0: OVERWRITE 1: OR 2: XOR
            // bitmap data Bitmap data
//            list.add(DataForSendToPrinterTSC.bitmap(100, 100, 0, bitmap, BitmapToByteData.BmpType.Threshold));
            list.add(DataForSendToPrinterTSC.bitmap(0, 0, 0, bitmapOriginal, BitmapToByteData.BmpType.Dithering));

            // split bitmap and add to list
//            Bitmap bm1 = Bitmap.createBitmap(bitmapOriginal, 0, 0, bitmapOriginal.getWidth(), (bitmapOriginal.getHeight() / 2));
//            Bitmap bm2 = Bitmap.createBitmap(bitmapOriginal, 0, (bitmapOriginal.getHeight() / 2), bitmapOriginal.getWidth(), (bitmapOriginal.getHeight() / 2));
//
//            list.add(DataForSendToPrinterTSC.bitmap(0, 0, 0, bm1, BitmapToByteData.BmpType.Dithering));
//            list.add(DataForSendToPrinterTSC.bitmap(0, (bitmapOriginal.getHeight() / 2), 0, bm2, BitmapToByteData.BmpType.Dithering));

            list.add(DataForSendToPrinterTSC.print(1));


            // split bitmap and print 2 turn
//            list.add(DataForSendToPrinterTSC.bitmap(0, 0, 0, bm1, BitmapToByteData.BmpType.Dithering));
//            list.add(DataForSendToPrinterTSC.print(1));
//
//            list.add(DataForSendToPrinterTSC.bitmap(0, 0, 0, bm2, BitmapToByteData.BmpType.Dithering));
//            list.add(DataForSendToPrinterTSC.print(1));

            return list;
        });
    }

    void disconnectPrinter() {
        if (ISCONNECT) {
            binder.disconnectCurrentPort(new UiExecute() {
                @Override
                public void onsucess() {
                    Utils.LOGD(TAG, "onsucess: disconnect Printer p210");
//                    showSnackbar(getString(R.string.toast_discon_success));
//                    showET.setText("");
//                    BTCon.setText(getString(R.string.connect));
                }

                @Override
                public void onfailed() {
                    Utils.LOGD(TAG, "onfailed: disconnect Printer p210");
//                    showSnackbar(getString(R.string.toast_discon_faile));

                }
            });
        }
    }

    String getString(int resId) {
        if (context == null) {
            return "";
        }
        return context.getString(resId);
    }

    ToastUtil toastUtil;

    void showToast(String msg) {
        try {
            if (toastUtil == null) {
                toastUtil = new ToastUtil(context);
            }
            toastUtil.showToast(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void showHideLoading(boolean show) {
        if (pgdl != null) {
            if (show) {
                pgdl.showLoading();
            }
            else {
                pgdl.hideLoading();
            }
        }
    }
}
