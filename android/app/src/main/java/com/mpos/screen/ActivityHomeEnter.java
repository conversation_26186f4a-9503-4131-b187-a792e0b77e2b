package com.mpos.screen;

import static com.mpos.screen.mart.EMartPresenter.SUCCESS;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.mpos.common.CheckCodePTIController;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.customview.DialogMethodQrPay;
import com.mpos.customview.ViewToolBar;
import com.mpos.dialogs.CashbackDialogFragment;
import com.mpos.models.AmountLimit;
import com.mpos.models.BaseObjJson;
import com.mpos.models.ConfigConvertUserPromotion;
import com.mpos.models.DataFromPartner;
import com.mpos.models.QrPayResponse;
//import com.mpos.rnmodules.MyReactActivity;
import com.mpos.screen.mart.ActivityQrEmart;
import com.mpos.screen.mart.ReceiverManagerFinish;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.LibPrinterS85;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.MyOnClickListenerView;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.mpos.utils.Config;
import com.mpos.utils.ConfigIntegrated;
import com.mpos.utils.Constants;
import com.mpos.utils.IntentsMP;
import com.mpos.utils.LibErrorMpos;
import com.mpos.utils.MposUtil;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyTextUtils;
import com.mpos.utils.MyUtils;
import com.pps.core.DataUtils;
import com.pps.core.MyProgressDialog;

import net.yslibrary.android.keyboardvisibilityevent.util.UIUtil;

import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import butterknife.ButterKnife;
import butterknife.OnClick;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.BuildConfig;
import vn.mpos.R;
import vn.mpos.databinding.ActivityHomeEnterBinding;

public class ActivityHomeEnter extends ActivityPrePayment implements CashbackDialogFragment.OnClickSeeDetailListener, ActivityPrePayment.ItfShowScreenLoading { //HomeView,

    String TAG = this.getClass().getSimpleName();

    final byte requestCodeLocation = 1;
    final byte requestCodePayment = 2;
    final byte requestCodeEnableGps = 3;

    List<TextView> arrViewSgAmount;
    ActivityHomeEnterBinding binding;

    private ConfigConvertUserPromotion configConvertUserPromotion;

    public final byte SELECTED_MODE_NORMAL_PAYMENT = 1;
    public final byte SELECTED_MODE_QRCODE_PAYMENT = 2;
    public final byte SELECTED_MODE_QRCODE_DOMESTIC_PAYMENT = 3;
    private byte currentTypePayment = 0;
    private String content = "";

    private boolean payPartnerInputCode = false;
    private boolean payCashBack = false;
    private boolean payIntegrated = false;
    private boolean isDisableCheckGps = false;
    private boolean isMerchantEnableMVisa = false;
    private boolean useReaderPay;
    private boolean selectPaymentBaseOnQRCode = false;
    private String email;
    private String phone;
    private String qrTypeCode = "";
    private String transactionType;
    private String typePayByPartner;
    private String descMVisa;
    private String dataMerchant;
    private QrPayResponse qrPayRes = null;
    private SelectedQrPayReceiver selectedQrPayReceiver = null;

    ViewToolBar viewToolBar;

    String orderId;

    private String payType = "";
    private final HashMap<String, Boolean> hmSuggestAmount = new HashMap<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityHomeEnterBinding.inflate(getLayoutInflater());

        setContentView(binding.getRoot());

//        setContentView(R.layout.activity_home_enter);
        ButterKnife.bind(this);

        setItfShowScreenLoading(this);

        viewToolBar = new ViewToolBar(this, binding.container);
        viewToolBar.showTextTitle(getToolbarTitle());
        viewToolBar.showButtonBack(true);
        initCashback();
        dataMerchant = PrefLibTV.getInstance(context).getDataLoginMerchant();

        MyUtils.setRequestedOrientation(this, DataStoreApp.getInstance().getIsLandscape());

        isDisableCheckGps = DataStoreApp.getInstance().getIsDisableCheckGps();
        isMerchantEnableMVisa = DataStoreApp.getInstance().isMerchantRegistedQR();
        useReaderPay = DataStoreApp.getInstance().isUseReader();
        Utils.LOGD(TAG, "isDisableCheckGps: " + isDisableCheckGps + " isMerchantEnableMVisa: " + isMerchantEnableMVisa + " useReaderPay: " + useReaderPay);

        boolean canEditAmount = true;
        try {
            payPartnerInputCode = getIntent().getBooleanExtra(IntentsMP.EXTRA_PARTNER_IS_INPUT_CODE, false);
            dataFromPartner = (DataFromPartner) getIntent().getSerializableExtra(Intents.EXTRA_DATA_PARTNER);
            if (dataFromPartner != null) {
                if (!TextUtils.isEmpty(dataFromPartner.getAmount()) && TextUtils.isDigitsOnly(dataFromPartner.getAmount())) {
                    content = dataFromPartner.getAmount();
                    String mContent;
                    if (content.length() > 3) {
                        mContent = Utils.zenMoney(content);
                    } else {
                        mContent = content;
                    }
                    binding.edtMoney.setText(mContent);
                    canEditAmount = false;
                }

                orderId = dataFromPartner.getOrderId();
                if (!TextUtils.isEmpty(orderId)) {
                    binding.edtDescription.setText(orderId);
                    binding.edtDescription.setEnabled(false);
                }

                if (!TextUtils.isEmpty(dataFromPartner.getDescription())) {
                    binding.edtDescription.setText(binding.edtDescription.getText().toString() + "-" + dataFromPartner.getDescription());
                }

                if (!TextUtils.isEmpty(dataFromPartner.getEmailReceipt())) {
                    binding.edtEmail.setText(dataFromPartner.getEmailReceipt());
                    binding.edtEmail.setEnabled(false);
                }

                if (!TextUtils.isEmpty(dataFromPartner.getOrderType())) {
                    transactionType = dataFromPartner.getOrderType();
                    if (!ConstantsPay.TRX_TYPE_SERVICE.equals(transactionType)) {
                        transactionType = null;
                    }
                }

                typePayByPartner = dataFromPartner.getTypePay();
                if (ConfigIntegrated.TP_QR_CODE.equals(typePayByPartner)) {
                    selectTypePaymentBaseOn(SELECTED_MODE_QRCODE_PAYMENT);
                } else if (ConfigIntegrated.TP_NORMAL.equals(typePayByPartner)) {
                    selectTypePaymentBaseOn(SELECTED_MODE_NORMAL_PAYMENT);
                }

                // from select pay card in dialog integration
                if (getIntent().getBooleanExtra(IntentsMP.EXTRA_IS_GO_SWIPE_CARD, false)) {
                    selectTypePaymentBaseOn(SELECTED_MODE_NORMAL_PAYMENT);
                }

                payIntegrated = true;
                Utils.LOGD(TAG, "--dataPartner:" + content + " typePayByPartner=" + typePayByPartner);
            }
        } catch (Exception e) {
            Utils.LOGE("Exception", "parse data integrated: ", e);
        }

        initUiByTypePay();

        if (canEditAmount) {
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
            initCurrTypePay();
            binding.edtMoney.requestFocus();
            if (DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) {
                initViewSuggestAmount();
            }
            setupEdtAmount();
        } else {
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
            binding.edtMoney.setEnabled(false);
            checkContinuePayNow();
        }

        if (payPartnerInputCode) {
            payIntegrated = true;
        } else {
            payCashBack = DataStoreApp.getInstance().getHaveCashback();
            if (payCashBack) {
                handlerInputEmailReceiptToAccGowow();
            }
        }
        checkConstraintSet();

        isPermitQrNl();

//-----------------created by NHANLX----------------------------
        initBroadCast();
        onClickNextPayment(payType);
        onHideKeyboard();

        onSetupEdDescription();
        onSetupEdEmail();
        onSetupEdPhone();

        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            ReceiverManagerFinish.getInstance().pushActivityNeedFinish(this);
        }
    }

    private void checkConstraintSet() {
        if (DevicesUtil.isP20L() || DevicesUtil.isSP02()) {
            ConstraintSet constraintSet = new ConstraintSet();
            constraintSet.clone(binding.container);
            constraintSet.clear(R.id.v_bottom, ConstraintSet.TOP);
            constraintSet.connect(R.id.home_enter_nestedScroll, ConstraintSet.BOTTOM, R.id.v_bottom, ConstraintSet.TOP);
            constraintSet.applyTo(binding.container);
        }
    }

    private void initCashback() {
        if (context != null && Constants.CARD_PAYMENT.equals(payType)) {
            if (DataStoreApp.getInstance().getDataConvertVimo() == 1) {
                loadConfig(context);
                binding.ivCashbackButton.setVisibility(View.VISIBLE);
            } else {
                binding.ivCashbackButton.setVisibility(View.GONE);
            }
        }
    }

    private void initViewSuggestAmount() {
        binding.vSuggestAmount.setVisibility(View.VISIBLE);

        arrViewSgAmount = new ArrayList<>();
        arrViewSgAmount.add(binding.tvAmountSg1);
        arrViewSgAmount.add(binding.tvAmountSg2);
        arrViewSgAmount.add(binding.tvAmountSg3);

        for (int i = 0; i < arrViewSgAmount.size(); i++) {
            final int index = i;
            arrViewSgAmount.get(i).setOnClickListener(view -> binding.edtMoney.setText(arrViewSgAmount.get(index).getText()));
        }

        String amountPaid = DataStoreApp.getInstance().getAmountsSave();
        Utils.LOGD(TAG, "initViewSuggestAmount: "+amountPaid);
        if (!TextUtils.isEmpty(amountPaid)) {
            String[] arrAmountPaid = amountPaid.split(Constants.CHAR_SPLIT_PHONE);

            ArrayList<Long> alAmountPaid = new ArrayList<>();
            for (String s : arrAmountPaid) {
                try {
                    hmSuggestAmount.put(s, true);
                    alAmountPaid.add(Long.parseLong(s));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
            Utils.LOGD(TAG, "initViewSuggestAmount: size="+alAmountPaid.size());
            if (alAmountPaid.size() > 0) {
                showSuggestAmount(alAmountPaid);
            }
        }
    }

    private void createSuggestAmount(String amount) {
        amount = amount.replaceAll("[\\D]", "");
        if (TextUtils.isEmpty(amount)) {
            return;
        }
        long originAmount = Long.parseLong(amount);
        ArrayList<Long> arrAmount = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            long amountTemp = (long) (originAmount * (100 * Math.pow(10, i)));
            if (amountTemp < 10000000000L) {
                arrAmount.add(amountTemp);
            }
            else {
                break;
            }
        }
        showSuggestAmount(arrAmount);
    }

    private void showSuggestAmount(ArrayList<Long> arrAmount) {
        for (int i = 0; i < arrViewSgAmount.size(); i++) {
            if (i < arrAmount.size()) {
                arrViewSgAmount.get(i).setVisibility(View.VISIBLE);
                arrViewSgAmount.get(i).setText(Utils.zenMoney(arrAmount.get(i)));
            }
            else {
                arrViewSgAmount.get(i).setVisibility(View.GONE);
            }
        }
    }


    public void loadConfig(Context context) {
        mPgdl = new MyProgressDialog(context);
        Utils.LOGD(TAG, "run load file config");
        MposRestClient.getInstance(context).get(Config.URL_CONFIG, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                super.onStart();
                mPgdl.showLoading();
            }

            @Override
            public void onSuccess(int i, Header[] headers, byte[] bytes) {
                mPgdl.hideLoading();
                String content = new String(bytes);
                Utils.LOGD(TAG, "onSuccess: content=" + content);

                try {
                    JSONObject jRoot = new JSONObject(content);
                    String config = JsonParser.getDataJson(jRoot, "convertUserPromotion");

                    configConvertUserPromotion = MyGson.parseJson(config, ConfigConvertUserPromotion.class);
                    if (!TextUtils.isEmpty(configConvertUserPromotion.getIconUrl())) {
                        Glide.with(context).load(configConvertUserPromotion.getIconUrl()).into(binding.ivCashbackButton);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int i, Header[] headers, byte[] bytes, Throwable throwable) {
                mPgdl.hideLoading();
                Utils.LOGD(TAG, "onFailure: loadconfig----");
            }
        });
    }

    @OnClick({R.id.iv_cashback_button})
    protected void onClickCashback(View v) {
        if (configConvertUserPromotion != null) {
            CashbackDialogFragment cashbackDialogFragment = new CashbackDialogFragment();
            cashbackDialogFragment.setOnClickSeeDetailListener(this);
            Bundle bundle = new Bundle();
            bundle.putString("DATA_DIALOG", new Gson().toJson(configConvertUserPromotion));
            cashbackDialogFragment.setArguments(bundle);
            cashbackDialogFragment.show(getSupportFragmentManager(), "DIALOG_CASHBACK");
        }
    }

    @Override
    public void onClickSeeDetail(String url, String title) {
        if (!TextUtils.isEmpty(url)) {
            Intent intent = new Intent(this, ActivityNewsDetail.class);
            intent.putExtra(Constants.LINK_WEBVIEW, url);
            intent.putExtra(Constants.TITLE_WEBVIEW, title);
            startActivity(intent);
        }
    }

    protected String getToolbarTitle() {
        payType = getIntent().getStringExtra(Constants.EXTRA_INTENT);
        Utils.LOGD(TAG, "getToolbarTitle: payType=" + payType);
        if (Constants.QR_PAYMENT.equals(payType)) {
            return getString(R.string.pay_by_qr);
        } else if (Constants.CARD_PAYMENT.equals(payType)) {
            return getString(R.string.pay_by_card);
        } else if (Constants.CARD_ENTER_PAYMENT.equals(payType)) {
            return getString(R.string.pay_by_card_enter);
        } else {
            return getString(R.string.pay_action_normal);
        }
    }

    private void initCurrTypePay() {
        currentTypePayment = useReaderPay ? SELECTED_MODE_NORMAL_PAYMENT : SELECTED_MODE_QRCODE_PAYMENT;
    }

    // toto check is merchant emart use QR code vcb (isContinue = false) || check flag permitQRvcb && payType == QR_PAYMENT && emart ---> thanh toán QR vcb

    private void checkContinuePayNow() {
        boolean isContinue = false;

        //dang su dung thiet bi MPOS
        if (useReaderPay) {
            if (isMerchantEnableMVisa) {
                isContinue = true;
                if (Constants.QR_PAYMENT.equals(payType)) {
                    currentTypePayment = SELECTED_MODE_QRCODE_PAYMENT;
                } else if (Constants.CARD_PAYMENT.equals(payType)) {
                    currentTypePayment = SELECTED_MODE_NORMAL_PAYMENT;
                }
            } else {
                isContinue = true;
                currentTypePayment = SELECTED_MODE_NORMAL_PAYMENT;
            }
        } else {
            if (isMerchantEnableMVisa) {
                currentTypePayment = SELECTED_MODE_QRCODE_PAYMENT;
            }
        }
        if (isContinue) {
            attemptPayNow(false);
        }
    }

    private void initUiByTypePay() {
        if (DevicesUtil.isPax()) {
            binding.homeEnterLabelBillInfo.setVisibility(View.GONE);
        }
        Utils.LOGD(TAG, "initUiByTypePay: useReaderPay=" + useReaderPay + " isMerchantEnableMVisa=" + isMerchantEnableMVisa);
        if (Constants.CARD_PAYMENT.equals(payType) || Constants.CARD_ENTER_PAYMENT.equals(payType) || Constants.QR_PAYMENT.equals(payType)) {
            binding.btnNext.setVisibility(View.VISIBLE);
        }
        binding.btnPayNow.setVisibility(View.GONE);

        if (Constants.CARD_ENTER_PAYMENT.equals(payType)) {
            String sDataFee = DataStoreApp.getInstance().getDataExchangeLinkInfo();
            if (!TextUtils.isEmpty(sDataFee)) {
                try {
                    JSONArray jsaDataFee = new JSONArray(sDataFee);
                    if (jsaDataFee.length() > 0) {
                        StringBuilder feeString = new StringBuilder();
                        for (int i = 0; i < jsaDataFee.length(); i++) {
                            JSONObject jsData = jsaDataFee.getJSONObject(i);
                            if (jsData != null) {
                                if (jsData.has("issuerCode")) {
                                    String issuerCode = jsData.getString("issuerCode");
                                    if ("MASTER_LOCAL".equals(issuerCode) || "CUP_LOCAL".equals(issuerCode) || "JCB_LOCAL".equals(issuerCode) || "VISA_LOCAL".equals(issuerCode)) {
                                        switch (jsData.getString("issuerCode")) {
                                            case "MASTER_LOCAL":
                                                feeString.append(" MasterCard ");
                                                break;
                                            case "CUP_LOCAL":
                                                feeString.append(" Union Pay ");
                                                break;
                                            case "JCB_LOCAL":
                                                feeString.append(" JCB ");
                                                break;
                                            case "VISA_LOCAL":
                                                feeString.append(" Visa ");
                                                break;
                                        }
                                        if (jsData.has("fee")) {
                                            feeString.append(jsData.getDouble("fee")).append("%,");
                                        }
                                    }
                                }
                            }
                        }
                        if (!TextUtils.isEmpty(feeString)) {
                            String textHint = getString(R.string.content_link_payment_fee) + "<b>" + feeString.substring(0, feeString.length() - 1) + " / " + getString(R.string.transaction) + "</b>";
                            binding.tvFeeHint.setText(Html.fromHtml(textHint));
                            binding.tvFeeHint.setVisibility(View.VISIBLE);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            try {
                JSONObject jRoot = new JSONObject(dataMerchant);
                String createLinkPaymentFlag = JsonParser.getDataJson(jRoot, "createLinkPaymentFlag");
                boolean isNormalPayLink = DataStoreApp.getInstance().getIsNormalPayLink();
                if ("1".equals(createLinkPaymentFlag) && isNormalPayLink) {
//                    binding.llLinkHint.setVisibility(View.VISIBLE);
                    binding.btnNext.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.bg_button_blue));
//                    binding.btnNext.setBackgroundDrawable(getResources().getDrawable(R.drawable.bg_button_blue));
                    binding.btnNext.setText(getResources().getText(R.string.button_enter_card));
//                    showBtnLink();
                } else if ("1".equals(createLinkPaymentFlag)) {
                    binding.btnNext.setVisibility(View.GONE);
                }
                binding.llLinkHint.setVisibility(View.VISIBLE);
                showBtnLink();
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        if (Constants.CARD_PAYMENT.equals(payType)) {
            String serviceCodeInput = getIntent().getStringExtra(Constants.EXTRA_SERVICE_CODE);
            JSONObject jsonObjectConfig = getConfigService(serviceCodeInput);
            String amountThreshold = JsonParser.getDataJson(jsonObjectConfig, "amountThreshold");
            if (!TextUtils.isEmpty(amountThreshold)) {
                int iAmountThreshold = Integer.parseInt(amountThreshold);
                binding.edtMoney.addTextChangedListener(new TextWatcher() {
                    String stringAmount;
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        stringAmount = DataUtils.getTextInEdt(binding.edtMoney).replaceAll("[\\D]", "");
                        if (!TextUtils.isEmpty(stringAmount) && (Integer.parseInt(stringAmount) >= iAmountThreshold)) {
                            binding.tvWarnAmount.setVisibility(View.VISIBLE);
                        } else {
                            binding.tvWarnAmount.setVisibility(View.GONE);
                        }
                        binding.edtPhone.setError(null);
                        binding.edtDescription.setError(null);
                        binding.edtEmail.setError(null);
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                    }
                });
            }
        }
    }

    private void showBtnLink() {
        binding.btnLink.setVisibility(View.VISIBLE);
        binding.btnLink.setOnClickListener(v -> {
            String phone = binding.edtPhone.getMyText().trim();
            String amount = binding.edtMoney.getMyText().trim();
            String email = binding.edtEmail.getText().toString().trim();
            String desc = binding.edtDescription.getText().toString().trim();
            if (validateDataEnterCard(phone, amount, email, desc)) {
                requestEnterCardPayment(true);
            }
        });
    }

    private JSONObject getConfigService(String serviceCode) {
        if (!TextUtils.isEmpty(serviceCode)) {
            try {
                JSONObject jRoot = new JSONObject(dataMerchant);
                String configParamRequires = JsonParser.getDataJson(jRoot, "configParamRequires");
                if (!TextUtils.isEmpty(configParamRequires)) {
                    JSONArray jsonArrayMenuHome = new JSONArray(configParamRequires);
                    for (int i = 0; i < jsonArrayMenuHome.length(); i++) {
                        JSONObject item = jsonArrayMenuHome.getJSONObject(i);
                        String itemServiceCode = JsonParser.getDataJson(item, "serviceCode");
                        if (serviceCode.equals(itemServiceCode)) {
                            return jsonArrayMenuHome.getJSONObject(i);
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private void handlerInputEmailReceiptToAccGowow() {
        binding.edtEmail.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    private void onClickNextPayment(String payType) {
        binding.btnNext.setOnClickListener(new MyOnClickListenerView(view -> {
            if (Constants.QR_PAYMENT.equals(payType)) {
                if (DataStoreApp.getInstance().getListQrPay() != null && DataStoreApp.getInstance().getListQrPay().size() > 0) {
                    String phone = binding.edtPhone.getMyText().trim();
                    String amount = binding.edtMoney.getMyText().trim();
                    String email = binding.edtEmail.getText().toString().trim();
                    if (validateData(phone, amount, email)) {
                        showDialogListQrPay();
                    }
                } else {
                    MyDialogShow.showDialogInfo(this, getString(R.string.txt_login_email_and_not_accept_mvisa), false);
                }
            } else if (Constants.CARD_ENTER_PAYMENT.equals(payType)) {
                String phone = binding.edtPhone.getMyText().trim();
                String amount = binding.edtMoney.getMyText().trim();
                String email = binding.edtEmail.getText().toString().trim();
                String desc = binding.edtDescription.getText().toString().trim();
                if (validateDataEnterCard(phone, amount, email, desc)) {
                    requestEnterCardPayment(false);
                }
            } else {
                String serviceCodeInput = getIntent().getStringExtra(Constants.EXTRA_SERVICE_CODE);
                JSONObject jsonObjectConfig = getConfigService(serviceCodeInput);
                if (jsonObjectConfig != null) {
                    String stringAmount = DataUtils.getTextInEdt(binding.edtMoney).replaceAll(",", "");
                    String amountThreshold = JsonParser.getDataJson(jsonObjectConfig, "amountThreshold");
                    if (!TextUtils.isEmpty(stringAmount) && !TextUtils.isEmpty(amountThreshold) && (Integer.parseInt(stringAmount) >= Integer.parseInt(amountThreshold))) {
                        boolean requiredEmail = "Y".equals(JsonParser.getDataJson(jsonObjectConfig, "emailParam"));
                        boolean requiredPhone = "Y".equals(JsonParser.getDataJson(jsonObjectConfig, "mobileNoParam"));
                        boolean requiredDesc = "Y".equals(JsonParser.getDataJson(jsonObjectConfig, "descriptionParam"));
                        if (validateDataWithRequired(requiredPhone, requiredEmail, requiredDesc)) {
                            selectTypePaymentBaseOn(SELECTED_MODE_NORMAL_PAYMENT);
                            attemptPayNow(true);
                        }
                    } else {
                        selectTypePaymentBaseOn(SELECTED_MODE_NORMAL_PAYMENT);
                        attemptPayNow(true);
                    }
                } else {
                    selectTypePaymentBaseOn(SELECTED_MODE_NORMAL_PAYMENT);
                    attemptPayNow(true);
                }
            }
        }));

        binding.btnCreateQrNl.setOnClickListener(new MyOnClickListenerView(view -> {
            Utils.LOGD(TAG, "QR_PAYMENT_NL");
            String phone = binding.edtPhone.getMyText().trim();
            String amount = binding.edtMoney.getMyText().trim();
            String email = binding.edtEmail.getText().toString().trim();
            if (validateDataQrNl(phone, amount, email, Constants.TYPE_PAY_QR_VNPAY)) {
                gotoActivityScanQr(amount, phone, email, Constants.TYPE_PAY_QR_VNPAY);
            }
        }));

        binding.btnCreateMomoQrNl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Utils.LOGD(TAG, "VIETQR_PAYMENT_NL");
                String phone = binding.edtPhone.getMyText().trim();
                String amount = binding.edtMoney.getMyText().trim();
                String email = binding.edtEmail.getText().toString().trim();
                if (validateDataQrNl(phone, amount, email, Constants.TYPE_PAY_QR_MOMO)) {
                    gotoActivityScanQr(amount, phone, email, Constants.TYPE_PAY_QR_MOMO);
                }
            }
        });

        binding.btnCreateQrZaloNl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Utils.LOGD(TAG, "VIETQR_PAYMENT_NL");
                String phone = binding.edtPhone.getMyText().trim();
                String amount = binding.edtMoney.getMyText().trim();
                String email = binding.edtEmail.getText().toString().trim();
                if (validateDataQrNl(phone, amount, email, Constants.TYPE_PAY_QR_ZALOPAY_NL)) {
                    gotoActivityScanQr(amount, phone, email, Constants.TYPE_PAY_QR_ZALOPAY_NL);
                }
            }
        });

    }

    private void gotoActivityScanQr(String amount, String phone, String email, String typeQR) {
        hideKeyboard(ActivityHomeEnter.this);
        logUtil.appendLogAction("gotoActivityScanQr: " + typeQR);
        Intent intent = new Intent(ActivityHomeEnter.this, ActivityQrEmart.class);
        intent.putExtra(Constants.TYPE_QR_NL, typeQR);
        intent.putExtra(Constants.AMOUNT_QR, amount);
        intent.putExtra(Constants.TYPE_PHONE, phone);
        intent.putExtra(Constants.TYPE_EMAIL, email);
        startActivity(intent);
        finish();
    }

    public boolean validateDataQrNl(String phone, String amount, String email, String typeQR) {
        boolean isResult = true;
        int limitAmountQR = 5000;
        if (typeQR.equals(Constants.TYPE_PAY_QR_ZALOPAY_NL) || typeQR.equals(Constants.TYPE_PAY_QR_MOMO)) {
            limitAmountQR = 2000;
        }
        if (TextUtils.isEmpty(amount) || Integer.parseInt(amount) < limitAmountQR) {
            binding.edtMoney.setError(getString(R.string.msg_alert_invalid_amount_qr_nl, String.valueOf(limitAmountQR)));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(email) && !DataUtils.validateEmail(email)) {
            binding.edtEmail.setError(getString(R.string.error_wrong_email));
            binding.edtEmail.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(phone) && !MyUtils.validPhone(phone)) {
            binding.edtPhone.setError(getString(R.string.error_wrong_mobile));
            binding.edtPhone.requestFocus();
            isResult = false;
        }

        return isResult;
    }

    public boolean validateData(String phone, String amount, String email) {
        boolean isResult = true;
        if (TextUtils.isEmpty(amount) || Integer.parseInt(amount) <= 0) {
            binding.edtMoney.setError(getString(R.string.ALERT_PAYMENT_MISSING_AMOUNT_MSG));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(email) && !DataUtils.validateEmail(email)) {
            binding.edtEmail.setError(getString(R.string.error_wrong_email));
            binding.edtEmail.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(phone) && !MyUtils.validPhone(phone)) {
            binding.edtPhone.setError(getString(R.string.error_wrong_mobile));
            binding.edtPhone.requestFocus();
            isResult = false;
        }
        return isResult;
    }

    public boolean validateDataEnterCard(String phone, String amount, String email, String desc) {
        boolean isResult = true;
        if (TextUtils.isEmpty(amount) || Integer.parseInt(amount) <= 0) {
            binding.edtMoney.setError(getString(R.string.ALERT_PAYMENT_MISSING_AMOUNT_MSG));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (Integer.parseInt(amount) < 1000) {
            binding.edtMoney.setError(getString(R.string.error_min_amount));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(desc) && (desc.length() < 5 || desc.length() > 255)) {
            binding.edtDescription.setError(getString(R.string.error_desc_length));
            binding.edtDescription.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(phone) && !MyUtils.validPhone(phone)) {
            binding.edtPhone.setError(getString(R.string.error_wrong_mobile));
            binding.edtPhone.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(email) && !DataUtils.validateEmail(email)) {
            binding.edtEmail.setError(getString(R.string.error_wrong_email));
            binding.edtEmail.requestFocus();
            isResult = false;
        }
        return isResult;
    }

    public boolean validateDataWithRequired(boolean requiredPhone, boolean requiredEmail, boolean requiredDesc) {
        boolean isResult = true;
        String phone = binding.edtPhone.getMyText().trim();
        String amount = binding.edtMoney.getMyText().trim();
        String email = binding.edtEmail.getText().toString().trim();
        String desc = binding.edtDescription.getText().toString().trim();

        DecimalFormat formatter = new DecimalFormat("#,###,###");
        int minAmountValue = Utils.checkTypeBuildIsCertify() ? 1 : 1000;
        try {
//            String stringDataMC = DataStoreApp.getInstance().getDataLoginMerchant();
            JSONObject jsonDataMC = new JSONObject(dataMerchant);
            String minAmount = JsonParser.getDataJson(jsonDataMC, "minAmount");
            if (!TextUtils.isEmpty(minAmount)) {
                minAmountValue = Integer.parseInt(minAmount);
            }
            if (Utils.checkTypeBuildIsCertify()) {
                minAmountValue = 1;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (TextUtils.isEmpty(amount) || Integer.parseInt(amount) <= 0) {
            binding.edtMoney.setError(getString(R.string.ALERT_PAYMENT_MISSING_AMOUNT_MSG));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (Integer.parseInt(amount) < minAmountValue) {
            binding.edtMoney.setError(getString(R.string.error_min_amount2, formatter.format(minAmountValue)));
            binding.edtMoney.requestFocus();
            isResult = false;
        } else if (requiredDesc && TextUtils.isEmpty(desc)) {
            binding.edtDescription.setError(getString(R.string.error_empty_desc));
            binding.edtDescription.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(desc) && (desc.length() < 5 || desc.length() > 255)) {
            binding.edtDescription.setError(getString(R.string.error_desc_length));
            binding.edtDescription.requestFocus();
            isResult = false;
        } else if (requiredPhone && TextUtils.isEmpty(phone)) {
            binding.edtPhone.setError(getString(R.string.error_empty_phone));
            binding.edtPhone.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(phone) && !MyUtils.validPhone(phone)) {
            binding.edtPhone.setError(getString(R.string.error_wrong_mobile));
            binding.edtPhone.requestFocus();
            isResult = false;
        } else if (requiredEmail && TextUtils.isEmpty(email)) {
            binding.edtEmail.setError(getString(R.string.error_empty_email));
            binding.edtEmail.requestFocus();
            isResult = false;
        } else if (!TextUtils.isEmpty(email) && !DataUtils.validateEmail(email)) {
            binding.edtEmail.setError(getString(R.string.error_wrong_email));
            binding.edtEmail.requestFocus();
            isResult = false;
        }
        return isResult;
    }

    private void showDialogListQrPay() {
        Utils.LOGD(TAG, "showDialogListQrPay: ====>>");
        List<QrPayResponse> listQrPayResponse = DataStoreApp.getInstance().getListQrPay();
        DialogMethodQrPay dialogMethodQrPay = new DialogMethodQrPay();
        dialogMethodQrPay.setCancelable(true);
        dialogMethodQrPay.initVariable(listQrPayResponse);
        dialogMethodQrPay.setClickListener(qrPayResponse -> {
            qrTypeCode = qrPayResponse.getQrType();
            qrPayRes = qrPayResponse;
            if (qrTypeCode != null && !qrTypeCode.isEmpty()) {
                selectTypePaymentBaseOn(SELECTED_MODE_QRCODE_PAYMENT);
                attemptPayNow(true);
            } else {
                showDialogErrorQrCode();
            }
        });
        dialogMethodQrPay.show(getSupportFragmentManager(), ActivityHomeEnter.class.getName());
    }

    private void showDialogErrorQrCode() {
        MyDialogShow.showDialogError(getString(R.string.cannot_qrtype), this);
    }

    private void selectTypePaymentBaseOn(byte type) {
        currentTypePayment = type;
    }

    private void checkTypePayIsQR() {
        selectPaymentBaseOnQRCode = (isMerchantEnableMVisa &&
                (currentTypePayment == SELECTED_MODE_QRCODE_PAYMENT || currentTypePayment == SELECTED_MODE_QRCODE_DOMESTIC_PAYMENT)
        );
        Utils.LOGD(TAG, "selectPaymentBaseOnQRCode =" + selectPaymentBaseOnQRCode + ", isMerchantEnableMVisa= " + isMerchantEnableMVisa);
    }

    /**
     * attempt call pay
     *
     * @param validateEmail have validate email
     */
    public void attemptPayNow(boolean validateEmail) {
        Utils.LOGD(TAG, "attemptPayNow: currentTypePayment=" + currentTypePayment);
        if (currentTypePayment == 0) {
            initCurrTypePay();
            return;
        }
        content = binding.edtMoney.getMyText();
        phone = DataUtils.getTextInEdt(binding.edtPhone);
        if (TextUtils.isEmpty(content) || Constants.SVALUE_0.equals(content)) {
            binding.edtMoney.setError(getString(R.string.ALERT_PAYMENT_MISSING_AMOUNT_MSG));
            binding.edtMoney.requestFocus();
        }
        else if (!TextUtils.isEmpty(phone) && !MyUtils.validPhone(phone)) {
            binding.edtPhone.setError(getString(R.string.error_wrong_mobile));
            binding.edtPhone.requestFocus();
        }
        else {
            String desc = DataUtils.getTextInEdt(binding.edtDescription);
            email = DataUtils.getTextInEdt(binding.edtEmail);

            checkTypePayIsQR();
            if (validateEmail && !TextUtils.isEmpty(email) && !DataUtils.validateEmail(email)) {
                binding.edtEmail.setError(getString(R.string.error_wrong_email));
                binding.edtEmail.requestFocus();
            }
            else if (useReaderPay && !selectPaymentBaseOnQRCode && !isDisableCheckGps && !MyUtils.checkHaveLocationPermission(getApplicationContext())) {
                MyUtils.requestLocationPermission(this, requestCodeLocation);
            }
            else if (useReaderPay && !selectPaymentBaseOnQRCode && !isDisableCheckGps && !UtilsSystem.checkIsEnableGps(this)) {
                UtilsSystem.buildAlertMessageNoGps(this, getString(R.string.ALERT_LOCATION_SERVICE_ANDROID_MSG), requestCodeEnableGps);
            }
            else {
                payment(desc);
            }
        }
    }

    private void payment(String desc) {
        hideKeyboard(this);
        String udid = Constants.CHAR_SPLIT_DESC_UDID + Utils.zenUdid();

        if (!TextUtils.isEmpty(desc)) {
            MyTextUtils myTextUtils = new MyTextUtils();
            desc = myTextUtils.convertString(desc);
        }

        Utils.LOGD(TAG, "attemptPayNow: selectPaymentBaseOnQRCode=" + selectPaymentBaseOnQRCode + "payIntegrated=" + payIntegrated
                + " udid=" + udid);
        if (payCashBack) {
            udid = ConstantsPay.PREFIX_UDID_CASHBACK + udid;
            checkInfoInstallmentOrMvisa(desc, udid);
        } else {
            //==============================================================================
            //                               SCAN QR CODE
            //==============================================================================
            if (selectPaymentBaseOnQRCode) {
                // call to mpos get QR_ID
                // if integrated && have orderId: send orderId to service MERCHANT_INTEGRATED_CHECK_PREPAY
                if (payIntegrated && !TextUtils.isEmpty(orderId)) {
                    final String udidCheck = udid;
                    //noe: continue, add param for not show showDialogResultCheckOrderId
                    CheckCodePTIController ptiController = new CheckCodePTIController(this, false,
                            true, new CheckCodePTIController.ResultCheckCodePTI() {
                        @Override
                        public void onResultCheckCodePTI(DataFromPartner dataPartner) {
                        }

                        @Override
                        public void onResultGotoMVisaWithQRID(DataFromPartner dataPartner, String qridTemp) {
                            if (!TextUtils.isEmpty(qridTemp)) {
//                                        qrid = qridTemp;
                                binding.btnPayNow.setText(getString(R.string.txt_create_square_code_mpos));
                                gotoPaymentMVisa(qridTemp, null, udidCheck);
                            }
                        }
                    });
                    ptiController.getInfoOrderId(dataFromPartner.getOrderId(), udid, dataFromPartner);
                } else {
                    checkInfoInstallmentOrMvisa(desc, udid);
                }
            }
            //==========================================================================
            //                        THANH TOAN VOI READER
            //==========================================================================
            else {
                if (payIntegrated) {
                    String udidRecei = getIntent().getStringExtra(IntentsMP.EXTRA_PARTNER_UDID);
                    if (TextUtils.isEmpty(udidRecei)) {
                        String orderId = dataFromPartner != null ? dataFromPartner.getOrderId() : "";
                        udid = MposUtil.getInstance().createUdidForIntegration(orderId, udid);
                    } else {
                        udid = udidRecei;
                    }
                    logUtil.appendLogAction("udid-integrated = " + udid);
                    Utils.LOGD(TAG, "attemptPayNow: udid-integrated = " + udid);
//                          Ex: attemptPayNow: udid-integrated=MERCHANT_INTEGRATED--596-Thanh_toan_hoa_don/_Pay_order||b9c57cab-d562-410b-aa1b-16c95ef8283c
                    binding.btnPayNow.setText(getString(R.string.PAYMENT_BTN_PAY_NOW));
                }

                checkPrePay(desc, udid);
            }
        }
    }

    private void requestEnterCardPayment(boolean isCreateLink) {
        String udid = "NORMAL-" + Utils.zenUdid();
        StringEntity entity = buildEntityEnterCardPayment(isCreateLink, udid);

        fetchPrepareTrans(entity, jResponseRoot -> {
            String linkCheckout = JsonParser.getDataJson(jResponseRoot, "linkCheckout");
            if (!TextUtils.isEmpty(linkCheckout)) {
                if (isCreateLink) {
//                    String minuteExpired = JsonParser.getDataJson(jResponseRoot, "minuteExpired");
//                    Intent intent = new Intent(ActivityHomeEnter.this, MyReactActivity.class);
//                    intent.putExtra("RN_ROUTE_NAME", "PaymentLinkContainer");
//                    intent.putExtra("RN_LINK_PAYMENT", linkCheckout);
//                    intent.putExtra("RN_MINUTE_EXPIRED", minuteExpired);
//                    intent.putExtra("RN_LINK_PAYMENT_AMOUNT", DataUtils.getTextInEdt(binding.edtMoney).replaceAll(",", ""));
//                    startActivity(intent);
                } else {
                    Intent intent = new Intent(ActivityHomeEnter.this, ActivityEnterCardWebView.class);
                    intent.putExtra(Constants.LINK_WEBVIEW, linkCheckout);
                    intent.putExtra(Constants.KEY_MVISA_UDID, udid);
                    startActivityForResult(intent, 1000);
                }
            }
        });
    }

    private StringEntity buildEntityEnterCardPayment(boolean isCreateLink, String udid) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.PREPARE_TRANSACTION);
            jo.put("paymentMethod", "LINKCARD");
            jo.put("udid", udid);
            jo.put("merchantId", DataStoreApp.getInstance().getmerchantId());
            jo.put("customerEmail", DataUtils.getTextInEdt(binding.edtEmail));
            jo.put("customerMobile", DataUtils.getTextInEdt(binding.edtPhone).replaceAll("-", ""));
            jo.put("description", getAndFormatDescInput());
            jo.put("amount", DataUtils.getTextInEdt(binding.edtMoney).replaceAll(",", ""));
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            if (isCreateLink) {
                jo.put("transLinkType", "CREATE_LINK");
            }

            Utils.LOGD(TAG, "PREPARE_TRANSACTION | REQ: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }
        return entity;
    }

    private void checkInfoInstallmentOrMvisa(final String desc, final String udid) {
        StringEntity entity = buildEntityInstallmentOrMvisa(udid);

        fetchPrepareTrans(entity, jResponseRoot -> {

            // check pay by type
            if (selectPaymentBaseOnQRCode) {
                String qridTemp = JsonParser.getDataJson(jResponseRoot, "qrid");
                String qrCode = JsonParser.getDataJson(jResponseRoot, "qrCode");
                if (!TextUtils.isEmpty(qrCode)) {
//                                        qrid = qridTemp;
                    gotoPaymentMVisa(qridTemp, qrCode, udid);
                } else {
                    MyDialogShow.showDialogError(getString(R.string.error_qrCode_empty), ActivityHomeEnter.this);
                }
            }
            // type = normal pay
            else {
                if (payCashBack) {
                    try {
                        JSONObject joCashBackInfo = jResponseRoot.getJSONObject("cashbackInfo");
                        gotoInsertCard(desc, udid, joCashBackInfo, null);
                    } catch (JSONException e) {
                        e.printStackTrace();
                        MyDialogShow.showDialogError(getString(R.string.error_cashback_empty), ActivityHomeEnter.this);
                    }
                } else {
                    gotoInsertCard(desc, udid);
                }
            }
        });

        /*MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.PREPARE_TRANSACTION + " onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.error_onfaild_request), ActivityHomeEnter.this, v -> {
                    checkInfoInstallmentOrMvisa(desc, udid);
//                                checkInfoInstallmentOrMvisa(installmentOutId, desc, udid);
                }, true);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                BaseObjJson errorBean = null;
                try {
                    JsonParser jsonParser = new JsonParser();
                    errorBean = new BaseObjJson();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(TAG, "-PREPARE_TRANSACTION:" + jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " success");

                    } else {
                        msgError = LibErrorMpos.getErrorMsg(ActivityHomeEnter.this, errorBean.code);
                        if (TextUtils.isEmpty(msgError)) {
                            msgError = TextUtils.isEmpty(errorBean.message) ? (payCashBack ? getString(R.string.error_get_info_cash_back) : getString(R.string.error_get_info_installment))
                                    : errorBean.message;
                        }
                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(TAG, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    if (errorBean != null && ("55000".equals(errorBean.code) || "1004".equals(errorBean.code))) {
                        MyDialogShow.showDialogError("", msgError, ActivityHomeEnter.this, false, v -> {

                        });
                    } else {
                        MyDialogShow.showDialogRetryCancel("", msgError, ActivityHomeEnter.this, v -> {
                            checkInfoInstallmentOrMvisa(desc, udid);
                        }, true);
                    }
                }
            }
        });*/
    }

    private StringEntity buildEntityInstallmentOrMvisa(String udid) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.PREPARE_TRANSACTION);
            jo.put("udid", udid);

            // note: NEW long, 5:24 PM
            // note: NEW lúc tạo prepare_ bên app truyền cho em cái deviceIdentifier nữa nhé, e sửa notify chỉ cho device đó
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());

            if (payCashBack) {
                JSONObject joCashBack = new JSONObject();
                joCashBack.put("cashbackProgramId", DataStoreApp.getInstance().getCashBackId());
                joCashBack.put("originalAmount", content);
                joCashBack.put("username", "");

                jo.put("cashbackInfo", joCashBack);
            }
            // note: NORMAL payment QR
            else {
//            else if (TextUtils.isEmpty(installmentOutId)) {
                jo.put("merchantId", DataStoreApp.getInstance().getmerchantId());
                jo.put("paymentMethod", "QR");
                jo.put("customerEmail", email);
                jo.put("customerMobile", DataUtils.getTextInEdt(binding.edtPhone).replaceAll("-", ""));
                jo.put("description", getAndFormatDescInput());
                jo.put("qrType", qrTypeCode);

                if (!TextUtils.isEmpty(content)) {
                    jo.put("amount", content);
                }
            }

            Utils.LOGD(TAG, "PREPARE_TRANSACTION | REQ: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }
        return entity;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NotNull String[] permissions, @NotNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Utils.LOGD(TAG, "onRequestPermissionsResult() called with: requestCode = [" + requestCode
                + "], grantResults.length = [" + grantResults.length + "], grantResults = [" + grantResults[0] + "]");

        if (requestCode == requestCodeLocation) {
            if (grantResults[0] != PackageManager.PERMISSION_GRANTED) { //grantResults.length == 1 &&
                mToast.showToast(getString(R.string.need_allow_access_location));
            } else {
                attemptPayNow(true);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        Utils.LOGD(TAG, "onActivityResult: requestCode=" + requestCode + " resultCode=" + resultCode + " payIntegrated=" + payIntegrated);
        if (requestCode == RC_PAY) {
            super.onActivityResult(requestCode, resultCode, data);
        }

        setResult(RESULT_OK);
        if (requestCodePayment == requestCode) {
            if (resultCode == RESULT_OK || resultCode == Constants.RESULT_NOT_HANDLER || payIntegrated) {
                finish();
            }
        }
        else if (requestCode == requestCodeEnableGps && resultCode == RESULT_OK) {
            attemptPayNow(true);
        }
        else if (requestCode == 1000 && resultCode == RESULT_OK) {
            // back tu man hinh webview enter card
            finish();
        } else if (requestCode == LibPrinterS85.REQUEST_ENABLE_BT || requestCode == LibPrinterS85.REQUEST_CONNECT_DEVICE) {
            // check BT khi in S85
            if (libPrinter != null) {
                libPrinter.handlerResultSelectBluetoothPrinter(requestCode, resultCode, data);
            }
        }
    }

    private void gotoInsertCard(String desc, String udid) {
        gotoInsertCard(desc, udid, null, null);
    }


    private boolean validateMVisa() {
        boolean isOK = true;
        if (TextUtils.isEmpty(content)) {
            MyDialogShow.showDialogError(getString(R.string.ALERT_PAYMENT_MISSING_AMOUNT_MSG), this);
            isOK = false;
        } else {
            descMVisa = DataUtils.getTextInEdt(binding.edtDescription);
            email = DataUtils.getTextInEdt(binding.edtEmail);
            if (!TextUtils.isEmpty(email)) {
                if (!DataUtils.validateEmail(email)) {
                    binding.edtEmail.setError(getString(R.string.error_wrong_email));
                    binding.edtEmail.requestFocus();
                    isOK = false;
                }
            }
        }

        return isOK;
    }

    //note: PAYMENT mVISA
    private void gotoPaymentMVisa(String qrid, String qrCode, String udid) {
        Utils.LOGD(TAG, ">>> gotoPaymentMVisa()");
        if (validateMVisa()) {
            Intent i = new Intent(this, ActivityScanQRCode.class);
            Bundle bundle = new Bundle();
            bundle.putString(Constants.KEY_MVISA_AMOUNT, content);
            bundle.putString(Constants.KEY_MVISA_NOTE, descMVisa);
            bundle.putString(Constants.KEY_MVISA_EMAIL_CUSTOMER, email);
            bundle.putString(Constants.KEY_MVISA_QRID, qrid);
            bundle.putString(Constants.KEY_MVISA_QRCODE, qrCode);
            bundle.putString(Constants.KEY_MVISA_UDID, udid);
            bundle.putSerializable(Constants.EXTRA_INTENT, qrPayRes);
            if (currentTypePayment == SELECTED_MODE_QRCODE_PAYMENT) {
                bundle.putString(Constants.KEY_MVISA_TYPE, Constants.TYPE_QR_MVISA);
            } else if (currentTypePayment == SELECTED_MODE_QRCODE_DOMESTIC_PAYMENT) {
                bundle.putString(Constants.KEY_MVISA_TYPE, Constants.TYPE_QR_VIMO);
            }

            i.putExtra(Constants.KEY_EXTRA_MVISA, bundle);
            logUtil.appendLogAction("mVisa --- amount=" + content + " email=" + email + " descMVisa=" + descMVisa);
            logUtil.saveLog();

            startActivityForResult(i, requestCodePayment);
        }
    }


    //note: PAYMENT NORMAL
    private void checkPrePay(String desc, final String udid) {
        if (DataStoreApp.getInstance().isMandatoryCheckLimit()) {
            fetchLimitAmount(desc, udid);
        }

        else {
            gotoInsertCard(desc, udid, null, null);
        }
    }

    private void fetchLimitAmount(String desc, final String udid) {
        logUtil.appendLogRequestApi(Config.GET_LIMID_BY_MUID);
        if (!GetData.CheckInternet(this)) {
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), this, v -> fetchLimitAmount(desc, udid));
            return;
        }
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.GET_LIMID_BY_MUID);

            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            jo.put("mid", PrefLibTV.getInstance(context).getMId());

            Utils.LOGD(TAG, "GET_LIMID_BY_MUID | REQ: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.GET_LIMID_BY_MUID + " onFailure", arg2);
                mPgdl.hideLoading();
                handleFailCheckLimit(desc, udid, getString(R.string.error_onfaild_request));
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                try {
                    String data = new String(arg2);
                    Utils.LOGD(TAG, "GET_LIMID_BY_MUID onSuccess: " + data);
                    AmountLimit amountLimit = MyGson.parseJson(data, AmountLimit.class);

                    if (Config.CODE_REQUEST_SUCCESS.equals(amountLimit.getError().code)) {
                        logUtil.appendLogRequestApi(Config.GET_LIMID_BY_MUID + " success");

                        gotoInsertCard(desc, udid, null, amountLimit);
                    } else {
                        msgError = LibErrorMpos.getErrorMsg(ActivityHomeEnter.this, amountLimit.getError().code);
                        if (TextUtils.isEmpty(msgError)) {
                            msgError = getString(R.string.error_get_amount_limit);
                        }
                        logUtil.appendLogRequestApi(Config.GET_LIMID_BY_MUID + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.GET_LIMID_BY_MUID + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(TAG, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    handleFailCheckLimit(desc, udid, msgError);
                }
            }
        });
    }

    private void handleFailCheckLimit(String desc, final String udid, String msg) {
        logUtil.appendLogAction("check limit amount fail: " + msg);
        gotoInsertCard(desc, udid, null, null);
    }

    /**
     * @param desc           converted to vietnamese
     * @param udid           udid
     *                       //     * @param installmentOutId id installment
     * @param joCashBackInfo info cashback
     */
    private void gotoInsertCard(String desc, final String udid, JSONObject joCashBackInfo, AmountLimit amountLimit) {

        Intent i = new Intent(this, ActivityPrePayment.class);

        String amountPaySend;
        if (payCashBack) {
            String amountDiscount = JsonParser.getDataJson(joCashBackInfo, "discountAmount");
            String amountPay = JsonParser.getDataJson(joCashBackInfo, "payAmount");

            if (validateAmountGetInServer(amountPay)) {
                amountPaySend = amountPay;
            }
            else {
                return;
            }

            long lAmountDiscount = 0;
            try {
                lAmountDiscount = Long.parseLong(amountDiscount);
            } catch (NumberFormatException e) {
                Utils.LOGE(TAG, "gotoInsertCard: parse discount ", e);
            }

            if (lAmountDiscount > 0) {
                i.putExtra(IntentsMP.EXTRA_AMOUNT_DISCOUNT, amountDiscount);
                i.putExtra(IntentsMP.EXTRA_IS_PAY_CASHBACK, payCashBack);
                desc = ConstantsPay.PREFIX_DESCRIPTION_CASHBACK + desc;
            }

            logUtil.appendLogAction(desc);
        }
        else {
            amountPaySend = content;
        }

        logUtil.appendLogAction(" a=" + amountPaySend + " udid=" + udid + " transactionType=" + transactionType);
        Utils.LOGD(TAG, "---desc convert:" + desc);//+" installmentOutId="+installmentOutId);

        if (amountLimit != null && checkAmountIsLimit(amountPaySend, amountLimit)) {
            logUtil.appendLogAction("limit amount -->> stop pay");
            return;
        }

        if (!TextUtils.isEmpty(phone)) {
            desc = String.format("M-%s|%s", phone, desc);
        }

        i.putExtra(IntentsMP.EXTRA_AMOUNT_ORIGIN, content);
        i.putExtra(Intents.EXTRA_V_AMOUNT, amountPaySend);
        i.putExtra(Intents.EXTRA_V_UDID, udid);
        i.putExtra(Intents.EXTRA_V_DESC, desc);
        i.putExtra(Intents.EXTRA_V_EMAIL, email);
        i.putExtra(Intents.EXTRA_DATA_PARTNER, dataFromPartner);

        if (!TextUtils.isEmpty(transactionType)) {
            i.putExtra(Intents.EXTRA_V_TRX_TYPE, transactionType);
        }

        StringEntity entity = buildEntityPrepareTransNormalSwipeCard(udid);

        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.hasErrorCodePrepay, Boolean.class, false)) {
            fetchPrepareTrans(true, entity,
                    // success
                    jSuccessResponseRoot -> {
                        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasErrorCodePrepay, false);
                        startPayCard(i);
                    },
                    // hasError
                    errorBean -> {
                        DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasErrorCodePrepay, true);
                        MyDialogShow.showDialogError(errorBean.message, ActivityHomeEnter.this);
                    },
                    // timeout
                    () -> startPayCard(i));
        }
        else {
            fetchPrepareTrans(false, entity,
                    // success
                    jSuccessResponseRoot -> DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasErrorCodePrepay, false),
                    // hasError
                    errorBean -> DataStoreApp.getInstance().saveDataByKey(DataStoreApp.hasErrorCodePrepay, true),
                    // timeout
                    ()->{});

            startPayCard(i);
        }
        processSaveMount(amountPaySend);
    }

    private void processSaveMount(String amountPaySend) {
        if (!hmSuggestAmount.containsKey(amountPaySend)) {
            DataStoreApp.getInstance().processSaveAmountPaid(amountPaySend);
        }
    }

    private void fetchPrepareTrans(StringEntity entity, @NonNull ItfHandleSuccessPrepare callbackSuccess) {
        fetchPrepareTrans(true, entity, callbackSuccess, null,null);
    }

    private void fetchPrepareTrans(boolean showLoading, StringEntity entity, @NonNull ItfHandleSuccessPrepare callbackSuccess,
                                   ItfHandleErrorPrepare callbackError, ItfHandleTimeoutPrepare callbackTimeout) {
        if (showLoading) {
            mPgdl.showLoading();
        }
        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION);

        MposRestClient.getInstance(this).post(this, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                logUtil.appendLogRequestApiFail(Config.PREPARE_TRANSACTION + " onFailure", arg2);
                if (showLoading) {
                    mPgdl.hideLoading();
                }
                if (callbackTimeout == null) {
                    MyDialogShow.showDialogError(getString(R.string.error_onfaild_request), ActivityHomeEnter.this
//                            ,v -> fetchPrepareTrans(entity, callbackSuccess), true
                    );
                }
                else {
                    callbackTimeout.onHandleTimeoutPrepare();
                }
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                if (showLoading) {
                    mPgdl.hideLoading();
                }
                String msgError = null;
                BaseObjJson errorBean = new BaseObjJson();
                try {
                    JsonParser jsonParser = new JsonParser();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(TAG, "-PREPARE_TRANSACTION:" + jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " success");

                        callbackSuccess.onHandleSuccessPrepare(jRoot);
                    }
                    else {
                        msgError = LibErrorMpos.getErrorMsg(ActivityHomeEnter.this, errorBean.code);
                        if (TextUtils.isEmpty(msgError)) {
                            msgError = TextUtils.isEmpty(errorBean.message) ? (payCashBack ? getString(R.string.error_get_info_cash_back) : getString(R.string.error_get_info_installment))
                                    : errorBean.message;
                        }
                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " error:" + msgError);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(TAG, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    if (callbackError == null) {
                        MyDialogShow.showDialogError(msgError, ActivityHomeEnter.this);
                    }
                    else {
                        errorBean.message = msgError;
                        callbackError.onHandleErrorPrepare(errorBean);
                    }
                }
            }
        });
    }

    private StringEntity buildEntityPrepareTransNormalSwipeCard(String udid) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.PREPARE_TRANSACTION);
            jo.put("udid", udid);
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            jo.put("customerMobile", DataUtils.getTextInEdt(binding.edtPhone).replaceAll("-", ""));
            jo.put("customerEmail", DataUtils.getTextInEdt(binding.edtEmail));
            jo.put("merchantId", DataStoreApp.getInstance().getmerchantId());
            jo.put("paymentMethod", "CARD");
            jo.put("description", getAndFormatDescInput());

            Utils.LOGD(TAG, "PREPARE_TRANSACTION | REQ: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }
        return entity;
    }

    private String getAndFormatDescInput() {
        return new String(DataUtils.getTextInEdt(binding.edtDescription).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
    }

    private void startPayCard(Intent intent) {
        logUtil.saveLog();
        callSdkPayment(intent);
    }

    private void showDialogWarningLimitAmount(String msg) {
        MyDialogShow.showDialogWarning(this, msg, false);
    }

    private boolean validateAmountGetInServer(String amount) {
        String msgError = "";
        if (TextUtils.isEmpty(amount)) {
            msgError = getString(R.string.error_wrong_amount);
        }
        return TextUtils.isEmpty(msgError);
    }

    private boolean checkAmountIsLimit(String amountPay, AmountLimit amountLimit) {

        long lAmountPay = Long.parseLong(amountPay);
        if (isRunMultiAcquirer) {
            if (amountLimit.getMidMaxAmountPerTransation()>0 && lAmountPay > amountLimit.getMidMaxAmountPerTransation()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_trans,
                        Utils.zenMoney(amountLimit.getMidMaxAmountPerTransation()) + ConstantsPay.CURRENCY_SPACE_PRE));
                return true;
            }
            else if (lAmountPay + amountLimit.getMidTotalAmountDay() > amountLimit.getMidMaxAmountPerDay()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_day));
                return true;
            }
            else if (lAmountPay + amountLimit.getMidTotalAmountMonth() > amountLimit.getMidMaxAmountPerMonth()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_month));
                return true;
            }
        }
        else {
            if (amountLimit.getMaxAmountPerTransation()>0 && lAmountPay > amountLimit.getMaxAmountPerTransation()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_trans, Utils.zenMoney(amountLimit.getMaxAmountPerTransation()) + ConstantsPay.CURRENCY_SPACE_PRE));
                return true;
            }
            else if (lAmountPay + amountLimit.getTotalAmountDay() > amountLimit.getMaxAmountPerDay()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_day));
                return true;
            }
            else if (lAmountPay + amountLimit.getTotalAmountMonth() > amountLimit.getMaxAmountPerMonth()) {
                showDialogWarningLimitAmount(getString(R.string.warning_limit_amount_month));
                return true;
            }
        }

        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (selectedQrPayReceiver != null) {
            unregisterReceiver(selectedQrPayReceiver);
        }

//        if (libPrinterS85 != null) {
//            libPrinterS85.disconnectPrinter();
//        }
//        // view leak
//        RefWatcher refWatcher = MyApplication.getRefWatcher(this);
//        refWatcher.watch(this);
    }

    //    @Override
    public void onScanQrCode(boolean isValidateEmail, QrPayResponse qrPayResponse) {
        selectTypePaymentBaseOn(SELECTED_MODE_QRCODE_PAYMENT);
        hideKeyboard(this);
        if (useReaderPay) {
            attemptPayNow(true);
        } else {
            if (qrPayResponse != null) {
                this.qrTypeCode = qrPayResponse.getQrType();
                this.qrPayRes = qrPayResponse;
                attemptPayNow(isValidateEmail);
            } else {
                showDialogErrorQrCode();
            }
        }
        onHideKeyboard();
    }

    private void onHideKeyboard() {
        binding.container.setOnClickListener(v -> hideKeyboard(this));
        binding.homeEnterLabelBillInfo.setOnClickListener(view -> hideKeyboard(this));
        binding.homeEnterLabelCustomerInfo.setOnClickListener(view -> hideKeyboard(this));
    }

    private void hideKeyboard(ActivityHomeEnter activityHomeEnter) {
        UIUtil.hideKeyboard(activityHomeEnter);
    }


    private void initBroadCast() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Constants.BROADCAST);
        selectedQrPayReceiver = new SelectedQrPayReceiver();
        registerReceiver(selectedQrPayReceiver, filter);
    }

    @Override
    public void onShowScreenLoadingAfterPayment() {
        binding.vSplash.getRoot().setVisibility(View.VISIBLE);
    }

    public class SelectedQrPayReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            qrPayRes = (QrPayResponse) intent.getSerializableExtra(Constants.EXTRA_INTENT);
            if (qrPayRes == null) return;
            qrTypeCode = qrPayRes.getQrType();
            if (qrTypeCode != null && !qrTypeCode.isEmpty()) {
                selectTypePaymentBaseOn(SELECTED_MODE_QRCODE_PAYMENT);
                attemptPayNow(true);
            } else {
                showDialogErrorQrCode();
            }
        }
    }

    private void onSetupEdPhone() {
        binding.edtPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable != null && editable.toString().length() > 0) {
                    binding.iconRemovePhone.setVisibility(View.VISIBLE);
                } else {
                    binding.iconRemovePhone.setVisibility(View.GONE);
                }
            }
        });
        binding.iconRemovePhone.setOnClickListener(view -> binding.edtPhone.setText(""));
    }

    private void onSetupEdDescription() {
        binding.edtDescription.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable != null && editable.toString().length() > 0) {
                    binding.imgRemoveDescription.setVisibility(View.VISIBLE);
                } else {
                    binding.imgRemoveDescription.setVisibility(View.GONE);
                }
            }
        });
        binding.imgRemoveDescription.setOnClickListener(view -> binding.edtDescription.setText(""));
    }

    private void onSetupEdEmail() {
        binding.edtEmail.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable != null && editable.toString().length() > 0) {
                    binding.imgRemoveEmail.setVisibility(View.VISIBLE);
                } else {
                    binding.imgRemoveEmail.setVisibility(View.GONE);
                }
            }
        });
        binding.imgRemoveEmail.setOnClickListener(view -> binding.edtEmail.setText(""));
    }

    private void setupEdtAmount() {
        binding.edtMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (editable != null && editable.toString().length() > 0) {
                    binding.imgRemoveAmount.setVisibility(View.VISIBLE);
                } else {
                    binding.imgRemoveAmount.setVisibility(View.GONE);
                }
                if (DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) {
                    if (editable != null && editable.toString().length() > 0) {
                        createSuggestAmount(editable.toString());
                    }
                }
            }
        });
        binding.imgRemoveAmount.setOnClickListener(view -> binding.edtMoney.setText(""));
    }


    /**
     *
     * Ufo thuan: QR ngan luong
     *
     */

    private void isPermitQrNl() {
//        String permitQr = DataStoreApp.getInstance().getPermitQrNl();
//        if (!TextUtils.isEmpty(permitQr) && permitQr.equals(Constants.SVALUE_1)) {
        if (DataStoreApp.getInstance().getPermitQrNl().equals(Constants.SVALUE_1)) {
            binding.btnCreateQrNl.setVisibility(View.VISIBLE);
            binding.btnNext.setText(getString(R.string.txt_swipe_card));
            viewToolBar.showTextTitle(getString(R.string.txt_title_payment));
        }

        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.permitQrMomoNl ,String.class).equals(Constants.SVALUE_1)) {
            binding.btnCreateMomoQrNl.setVisibility(View.VISIBLE);
            binding.homeEnterLabelCustomerInfo.setVisibility(View.GONE);
//            binding.btnCreateQrZalo.setVisibility(View.VISIBLE);
            binding.btnNext.setText(getString(R.string.txt_swipe_card));
            viewToolBar.showTextTitle(getString(R.string.txt_title_payment));
        }

        if (DataStoreApp.getInstance().getDataByKey(DataStoreApp.permitQrZaloNl, String.class).equals(Constants.SVALUE_1)) {
            binding.btnCreateQrZaloNl.setVisibility(View.VISIBLE);
            binding.homeEnterLabelCustomerInfo.setVisibility(View.GONE);
            binding.btnNext.setText(getString(R.string.txt_swipe_card));
            viewToolBar.showTextTitle(getString(R.string.txt_title_payment));
        }
    }

    private interface ItfHandleSuccessPrepare{
        void onHandleSuccessPrepare(JSONObject jResponseRoot);
    }

    private interface ItfHandleTimeoutPrepare{
        void onHandleTimeoutPrepare();
    }
    private interface ItfHandleErrorPrepare{
        void onHandleErrorPrepare(BaseObjJson errorBean);
    }

}
