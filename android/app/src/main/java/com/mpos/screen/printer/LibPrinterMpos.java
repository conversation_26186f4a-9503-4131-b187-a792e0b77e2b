package com.mpos.screen.printer;

import static com.mpos.sdk.core.control.LibPrinterPax.PRINT_BUSY;
import static com.mpos.sdk.core.control.LibPrinterPax.PRINT_OUT_OF_PAPER;
import static com.mpos.sdk.core.control.LibPrinterPax.PRINT_OVER_HEATS;
import static com.mpos.sdk.core.control.LibPrinterPax.PRINT_SUCCESS;
import static com.mpos.sdk.core.control.LibPrinterPax.PRINT_VOLTAGE_LOW;

import android.app.ActionBar;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.text.TextUtils;

import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.constraintlayout.widget.Constraints;
import androidx.core.content.ContextCompat;

import com.mpos.common.DataStoreApp;
import com.mpos.common.MyP20LPrinterListener;
import com.mpos.models.DataSummaryDeposit;
import com.mpos.models.DetailItemPaymentEmart;
import com.mpos.models.StringUtil;
import com.mpos.sdk.core.control.LibPrinterBluetooth;
import com.mpos.sdk.core.control.LibPrinterP8;
import com.mpos.sdk.core.control.LibPrinterPax;
import com.mpos.sdk.core.control.LibPrinterS85;
import com.mpos.sdk.core.control.LibPrinterSp01;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.PrefLibTV;

import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.BluetoothDeviceList;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyTextUtils;
import com.pos.sdk.printer.POIPrinterManager;
import com.pos.sdk.printer.models.BitmapPrintLine;
import com.pos.sdk.printer.models.PrintLine;
import com.pos.sdk.printer.models.TextPrintLine;

import vn.mpos.R;
import vn.mpos.databinding.LayoutPrintFormatBinding;
import vn.mpos.databinding.LayoutPrintFormatGiftcardBinding;
import vn.mpos.databinding.LayoutPrintFormatSummaryBinding;

/**
 * Create by anhnguyen on 07/07/2022
 */
public class LibPrinterMpos {

    private static final String TAG = LibPrinterMpos.class.getSimpleName();

    // printer
    private LibPrinterSp01 libPrinter;
    private LibPrinterBluetooth libPrinterBluetooth;
    private LibPrinterS85 libPrinterS85;
    private LibPrinterXpP210 libPrinterXpP210;
    private LibPrinterP8 libPrinterP8;
    protected LibPrinterPax libPrinterPax;

    POIPrinterManager printerManager;
    private POIPrinterManager.IPrinterListener listenerPrinterP8;

    MyP20LPrinterListener myP20LPrinterListener;


    Context context;

    long startTime = 0;
    SaveLogController logController;

    public LibPrinterMpos(@NonNull Context context, SaveLogController logController) {
        this(context, logController, true);
    }
    public LibPrinterMpos(@NonNull Context context, SaveLogController logController, boolean needInitPrinter) {
        this.context = context;
        this.logController = logController;
        if (needInitPrinter) {
            initPrinterMpos();
        }
    }

    public void initPrinterMpos(){
        if (DevicesUtil.isP20L()) {
            myP20LPrinterListener = new MyP20LPrinterListener(context);
            if (libPrinter == null) {
                try {
                    libPrinter = new LibPrinterSp01(context, myP20LPrinterListener);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        else if (DevicesUtil.isSP02P8()) {
            if (libPrinterP8 == null) {
                try {
                    libPrinterP8 = new LibPrinterP8(context);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        else if (DevicesUtil.isSP02()) {
//            initPrinterS85();
            String addressBluetooth = PrefLibTV.getInstance(context).getBluetoothAddressPrinter();
            String nameBluetooth = PrefLibTV.getInstance(context).getBluetoothNamePrinter();

            if (!TextUtils.isEmpty(addressBluetooth) && !TextUtils.isEmpty(addressBluetooth)) {
                if (nameBluetooth.equalsIgnoreCase("S85")) {
                    initPrinterS85();
//                    libPrinterS85.connect2BlueToothDevice();
                } else if (nameBluetooth.equalsIgnoreCase(LibPrinterXpP210.NAME_XPRINTER)) {
                    initPrinterXPP210();
//                    libPrinterXpP210.connect2BlueToothDevice();
                }
            }
            else if (libPrinterBluetooth == null) {
                PrefLibTV.getInstance(context).createBluetoothAddressPrinter("", "");
                libPrinterBluetooth = new LibPrinterXpP210(context);
            }
        } else if (DevicesUtil.isPax()) {
            try {
                libPrinterPax = new LibPrinterPax(context);
                libPrinterPax.setItfResultPrintPax(itfResultPrintPax);
            } catch (Exception e) {
                Utils.LOGE(TAG, "initPrinterMpos pax err: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void initPrinterS85() {
        if (libPrinterS85 == null) {
            libPrinterS85 = new LibPrinterS85(context);
        }
    }

    private void initPrinterXPP210() {
        if (libPrinterXpP210 == null) {
            libPrinterXpP210 = new LibPrinterXpP210(context);
        }
    }

    private void initListenerP8() {
        if (listenerPrinterP8 == null) {
            listenerPrinterP8 = new POIPrinterManager.IPrinterListener() {
                @Override
                public void onStart() {
                    Utils.LOGD(TAG, "onStart: --->");
                }

                @Override
                public void onFinish() {
                    Utils.LOGD(TAG, "onFinish: ---->");
                    closePrinter();
                }

                @Override
                public void onError(int errorCode, String s) {
                    MyDialogShow.dismissCurrDialog();
                    Utils.LOGD(TAG, "onError() called with: i = [" + errorCode + "], s = [" + s + "]");
                    closePrinter();
                    // errorCode in POIPrinterManager.
                    String msgError;
                    switch (errorCode) {
                        case POIPrinterManager.ERROR_NO_PAPER:
                            msgError = getString(R.string.error_printer_out_of_paper);
                            break;
                        case POIPrinterManager.ERROR_OVERHEAT:
                            msgError = getString(R.string.error_printer_p8_overheat);
                            break;
                        default:
                            msgError = getString(R.string.error_printer_default);
                            break;
                    }
                    MyDialogShow.showDialogError(msgError, context);
                }

                public void closePrinter() {
                    printerManager.close();
                }
            };
        }
    }

    public void actionPrintImageBase64(String imageBase64) {
        Utils.LOGD(TAG, "actionPrint: ");
        startTime = System.currentTimeMillis();

//        showImageBase64(imageBase64);

        MyTextUtils myTextUtils = new MyTextUtils();
        Bitmap bitmap = myTextUtils.convertTextBase64ToBitmap(imageBase64);

        long timeConvertBitmap = System.currentTimeMillis() - startTime;
        Utils.LOGD(TAG, "actionPrint: timeConvertBitmap=" + timeConvertBitmap);
        appendLog("actionPrint: timeConvertBitmap=" + timeConvertBitmap);

        actionPrintBitmap(bitmap);
    }

    public void actionPrintBitmap(@NonNull Bitmap bitmap) {
        if (bitmap == null) {
            return;
        }
        if (DevicesUtil.isP20L()) {
            actionPrintP20L(bitmap);
        }
        else if (DevicesUtil.isSP02P8()) {
            actionPrintP8(bitmap);
        }
        else if (DevicesUtil.isSP02()) {
            if (libPrinterS85 != null) {
                actionPrintS85(bitmap);
            }
            else if (libPrinterXpP210 != null) {
                actionPrintXpP210(bitmap);
            }
            else if (libPrinterBluetooth != null) {
                libPrinterBluetooth.checkBluetooth();
            }
        }
        else if (DevicesUtil.isPax()) {
            actionPrintPax(bitmap);
        }
    }

    public void actionPrintGiftCardEmart(DataPay dataPay) {
        if (dataPay != null) {
            String authCode = TextUtils.isEmpty(dataPay.getAuthCode()) ? "000000" : dataPay.getAuthCode();
            String amount = dataPay.getAmount();
            String date = dataPay.getTransactionDate();
            String pan = dataPay.getPan();
            Bitmap bitmap = buildBitmapGiftCardOfflineByLayout(amount, authCode, date, pan);
            actionPrintBitmap(bitmap);
        }
    }

    public void actionPrintGiftCardEmart(DetailItemPaymentEmart itemPaymentEmartDetail) {
        if (itemPaymentEmartDetail != null) {
            String authCode = (TextUtils.isEmpty(itemPaymentEmartDetail.getData().getAuthCode()) ? "000000" : itemPaymentEmartDetail.getData().getAuthCode());
            String amount = itemPaymentEmartDetail.getData().getAmount();
            String date = Utils.convertTimestamp(Long.parseLong(itemPaymentEmartDetail.getData().getTransactionDate()), 4);
            String pan = CardUtils.getMaskedPan(itemPaymentEmartDetail.getData().getPan());
            Bitmap bitmap = buildBitmapGiftCardOfflineByLayout(amount, authCode, date, pan);
            actionPrintBitmap(bitmap);
        }
    }

    private void actionPrintS85(Bitmap bitmap) {
        if (libPrinterS85 == null) {
            appendLog("libPrinter not init");
            return;
        }

        libPrinterS85.setTypePrint(LibPrinterS85.TYPE_PRINT_RECEIPT);
        libPrinterS85.setBitmapPrintAfterConnected(bitmap);

        if (libPrinterS85.isConnected()) {
            Utils.LOGD(TAG, "actionPrint: is connected ");

            startTime = System.currentTimeMillis();

            libPrinterS85.printReceipt(libPrinterS85.getmPrinter(), bitmap);

            long timePrint = System.currentTimeMillis() - startTime;
            Utils.LOGD(TAG, "actionPrint: timePrint=" + timePrint);
            appendLog( "actionPrint: timePrint=" + timePrint);
            return;
        }
        libPrinterS85.checkBluetooth();
    }

    private void actionPrintXpP210(Bitmap bitmap) {
        libPrinterXpP210.setBitmapPrintAfterConnected(bitmap);

        if (libPrinterXpP210.isConnected()) {

            libPrinterXpP210.printBitmap(bitmap);
            return;
        }

        libPrinterXpP210.checkBluetooth();
    }

    private void actionPrintP20L(Bitmap bitmap) {
        Utils.LOGD(TAG, "actionPrint: ");
        if (libPrinter == null) {
            appendLog("libPrinter not init");
            return;
        }
        myP20LPrinterListener.resetHaveShowError();

        startTime = System.currentTimeMillis();

        libPrinter.printBitmap(bitmap);
        libPrinter.pushPage(4);

        long timePrint = System.currentTimeMillis() - startTime;
        Utils.LOGD(TAG, "actionPrint: timePrint=" + timePrint);
        appendLog( "actionPrint: timePrint=" + timePrint);
    }

    private void actionPrintP8(Bitmap bitmap) {
        if (libPrinterP8 == null) {
            appendLog("libPrinter not init");
            return;
        }
        if (printerManager == null) {
            printerManager = libPrinterP8.getPrinterManager();
        }
        initListenerP8();
        printerManager.cleanCache();
        printerManager.open();
        printerManager.addPrintLine(new BitmapPrintLine(bitmap, PrintLine.CENTER));
        printerManager.addPrintLine(new TextPrintLine("\n"));
        printerManager.addPrintLine(new TextPrintLine("\n"));
        printerManager.beginPrint(listenerPrinterP8);
    }

    private void actionPrintPax(Bitmap bitmap) {
        if (libPrinterPax == null) {
            appendLog("libPrinter not init");
            return;
        }

        startTime = System.currentTimeMillis();

        libPrinterPax.printBitmap(bitmap);
        libPrinterPax.printStr("\n\n\n\n\n\n\n\n", null);

        libPrinterPax.start();
        libPrinterPax.disconnectPrinter();
        long timePrint = System.currentTimeMillis() - startTime;
        Utils.LOGD(TAG, "actionPrint: timePrint=" + timePrint);
        appendLog( "actionPrint: timePrint=" + timePrint);
    }

    public void handlerResultSelectBluetoothPrinter(int requestCode, int resultCode, @Nullable Intent data) {
        if (requestCode == LibPrinterS85.REQUEST_ENABLE_BT) {
            if (libPrinterBluetooth != null) {
                libPrinterBluetooth.showDeviceList();
            }
        }
        else if (resultCode == Activity.RESULT_OK && requestCode == LibPrinterS85.REQUEST_CONNECT_DEVICE) {
            String devicesName = data.getExtras().getString(BluetoothDeviceList.EXTRA_DEVICE_NAME);

            if (devicesName.equalsIgnoreCase("S85")) {
                initPrinterS85();
                handleResultSelectS85(data);
            }
            else if (devicesName.equalsIgnoreCase(LibPrinterXpP210.NAME_XPRINTER)) {
                initPrinterXPP210();
                handleResultSelectXPP210(data);
            }
        }
    }

    protected void handleResultSelectS85(Intent data) {
        // connect S85 sau khi nhận devicesAddress s85
        libPrinterS85.handlerDeviceSelected(data);
    }
    protected void handleResultSelectXPP210(Intent data) {
        // connect XP-P210 sau khi nhận devicesAddress
        libPrinterXpP210.handlerDeviceSelected(data);
    }

    public void disconnectPrinter() {
        if (libPrinter != null) {
            libPrinter.disconnectPrinter();
        }

        if (libPrinterS85 != null) {
            libPrinterS85.disconnectPrinter();
        }

        if (libPrinterXpP210 != null) {
            libPrinterXpP210.disconnectPrinter();
        }

        if (libPrinterP8 != null) {
            libPrinterP8.disconnectPrinter();
        }

        if (libPrinterPax != null) {
            libPrinterPax.disconnectPrinter();
        }
    }

    public void printReceiptMaOfflineByLayout(DataPay dataPay) {

        Bitmap bitmap = buildBitmapMacqOfflineByLayout(dataPay);

        actionPrintBitmap(bitmap);
    }

    public Bitmap buildBitmapMacqOfflineByLayout(DataPay dataPay) {
        LayoutPrintFormatBinding binding = LayoutPrintFormatBinding.inflate(LayoutInflater.from(context));
//        View layout = LayoutInflater.from(context).inflate(R.layout.layout_print_format, null, false);
        putDataToView(dataPay, binding);

        return getBitmapFromView(binding.getRoot());
    }

    public Bitmap buildBitmapGiftCardOfflineByLayout(String amount, String authCode, String date, String pan) {
        LayoutPrintFormatGiftcardBinding binding = LayoutPrintFormatGiftcardBinding.inflate(LayoutInflater.from(context));
        putDataGiftCardToView(amount, authCode, date, pan, binding);

        return getBitmapFromView(binding.getRoot());
    }

    private void putDataGiftCardToView(String amount, String authCode, String date, String pan, LayoutPrintFormatGiftcardBinding binding) {
//        String dateTime = "";
//        try {
//            dateTime = Utils.convertTimestamp(Long.parseLong(Utils.buildDataReceipt(detailRes.getTransactionDate())), 3);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        binding.tvTime.setText(date);

        binding.tvAppcode.setText(Utils.buildDataReceipt(authCode));
        binding.tvPan.setText(Utils.buildDataReceipt(pan));
        binding.tvType.setText("GIFT CARD");
        binding.tvDesc.setText(Utils.buildDataReceipt(""));

        binding.tvAmount.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(amount))));
    }

    private void putDataToView(DataPay dataPay, LayoutPrintFormatBinding binding) {
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            WfDetailRes detailRes = dataPay.getWfDetailRes();
            
            binding.tvMcName.setText(Utils.buildDataReceipt(detailRes.getMerchantName()));
            binding.tvMcAddress.setText(Utils.buildDataReceipt(detailRes.getMerchantAddress()));

            binding.tvMid.setText(Utils.buildDataReceipt(detailRes.getMid()));
            binding.tvTid.setText(Utils.buildDataReceipt(detailRes.getTid()));

            String dateTime = "";
            try {
                dateTime = Utils.convertTimestamp(Long.parseLong(Utils.buildDataReceipt(detailRes.getTransactionDate())), 3);
            } catch (Exception e) {
                e.printStackTrace();
            }
            binding.tvTime.setText(dateTime);

            String latLng = StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLatitude()))
                    + "/" + StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLongitude()));
            binding.tvLatlng.setText(latLng);
            binding.tvBatch.setText(Utils.buildDataReceipt(detailRes.getBatchNo()));
            binding.tvInvoice.setText(Utils.buildDataReceipt(detailRes.getInvoiceNo()));
            binding.tvRef.setText(Utils.buildDataReceipt(detailRes.getRrn()));
            binding.tvAppcode.setText(Utils.buildDataReceipt(detailRes.getAuthCode()));
            binding.tvPan.setText(Utils.buildDataReceipt(detailRes.getPan()));
            binding.tvCardHoldername.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            binding.tvType.setText(Utils.buildDataReceipt(detailRes.getIssuerCode()));
            binding.tvAppl.setText(Utils.buildDataReceipt(detailRes.getAppl()));
            binding.tvAid.setText(Utils.buildDataReceipt(detailRes.getAid()));
            binding.tvArqc.setText(Utils.buildDataReceipt(detailRes.getArqc()));

            if (detailRes.getTrxType().equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
                binding.tvAmountDeposit.setVisibility(View.VISIBLE);
                binding.tvTitleAmountDeposit.setVisibility(View.VISIBLE);
                binding.tvTitleIdDeposit.setVisibility(View.VISIBLE);
                binding.tvIdDeposit.setVisibility(View.VISIBLE);

                binding.tvAmountDeposit.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(detailRes.getAmountDeposit()))));
                binding.tvIdDeposit.setText(Utils.buildDataReceipt(detailRes.getDepositRefCode()));
            }

            binding.tvDesc.setText(Utils.buildDataReceipt(detailRes.getDescription()));

            binding.tvAmount.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(detailRes.getAmount()))));

            binding.tvCardHoldernameBottom.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));

            if (detailRes.isFlagNoSignature()) {
                binding.tvNoRequiredSignature.setVisibility(View.VISIBLE);
                binding.tvTitleSignature.setVisibility(View.GONE);
                binding.imvSignature.setVisibility(View.GONE);
                binding.tvCardHoldernameBottom.setVisibility(View.GONE);
            }
            else {
                MyTextUtils myTextUtils = new MyTextUtils();
                Bitmap bitmap = myTextUtils.convertTextBase64ToBitmap(dataPay.getSignatureBase64());
                binding.imvSignature.setImageBitmap(bitmap);
                binding.tvCardHoldernameBottom.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            }

            binding.tvTitleBill.setText(getDetailByStatus(detailRes, binding));
        }
    }

    private String getDetailByStatus(WfDetailRes detailRes, LayoutPrintFormatBinding binding) {
        String title = context.getString(R.string.title_sale_receipt);
        if ((detailRes.getStatus() != null)) {
            switch (detailRes.getStatus()) {
                case Constants.TRANS_STATUS_APPROVED:
                case Constants.TRANS_STATUS_PENDING_TC:
                case Constants.TRANS_STATUS_SETTLE:
                case Constants.TRANS_STATUS_SETTLED:
                    if ((detailRes.getTrxType() != null) && detailRes.getTrxType().equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
                        title = context.getString(R.string.title_deposit_receipt);
                    } else {
                        title = context.getString(R.string.title_sale_receipt);
                    }
                    break;
                case Constants.TRANS_STATUS_VOIDED:
                    binding.tvNoRequiredSignature.setVisibility(View.GONE);
                    if ((detailRes.getTrxType() != null) && detailRes.getTrxType().equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
                        title = context.getString(R.string.title_deposit_void_receipt);
                    } else {
                        title = context.getString(R.string.title_void_receipt);
                    }
                    break;
                case Constants.TRANS_STATUS_REVERSAL:
                    if ((detailRes.getTrxType() != null) && detailRes.getTrxType().equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
                        title = context.getString(R.string.title_deposit_reversal_receipt);
                    } else {
                        title = context.getString(R.string.title_reversal_receipt);
                    }
                    break;
                default:
                    break;
            }
        }

        return title;
    }

    public void printReceiptOfflineByLayout(DataPay dataPay) {

        Bitmap bitmap = buildBitmapOfflineByLayout(dataPay);

        actionPrintBitmap(bitmap);
    }

    public Bitmap buildBitmapOfflineByLayout(DataPay dataPay) {
        LayoutPrintFormatBinding binding = LayoutPrintFormatBinding.inflate(LayoutInflater.from(context));

        if (dataPay.getTransactionDetail() != null) {
            putDataBankToView(dataPay, binding);
        }
        else if (dataPay.getWfDetailRes() != null) {
            putDataMacqToView(dataPay, binding);
        }
        else {
            Utils.LOGD(TAG, "buildBitmapOfflineByLayout: no data print");
            appendLog("buildBitmapOfflineByLayout: no data print");
        }

        return getBitmapFromView(binding.getRoot());
    }

    private void putDataBankToView(DataPay dataPay, LayoutPrintFormatBinding binding) {
        Utils.LOGD(TAG, "putDataBankToView: --->flagServer=" + PrefLibTV.getInstance(context).getFlagServer());
        if (dataPay != null && dataPay.getTransactionDetail() != null) {
            // logo by bank
            if (PrefLibTV.getInstance(context).getFlagServer() == ConstantsPay.SERVER_SCB) {
                Utils.LOGD(TAG, "=> set logo printer: STB + mpos");
                binding.imvLogo.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.logo_stb_mpos_printer));
                ViewGroup.LayoutParams layoutParams = binding.imvLogo.getLayoutParams();
                layoutParams.width = 600;
                binding.imvLogo.setLayoutParams(layoutParams);

                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) binding.imvLogo.getLayoutParams();
                params.setMarginStart(0);
                params.setMarginEnd(0);
                binding.imvLogo.setLayoutParams(params);
            }

            TransItem.TransactionDetail detailTrans = dataPay.getTransactionDetail();

            binding.tvMcName.setText(Utils.buildDataReceipt(PrefLibTV.getInstance(context).get(PrefLibTV.BUSINESS_NAME, String.class)));
            binding.tvMcAddress.setText(Utils.buildDataReceipt(PrefLibTV.getInstance(context).get(PrefLibTV.BUSINESS_ADDRESS, String.class)));
            binding.tvUsername.setText(Utils.buildDataReceipt(PrefLibTV.getInstance(context).getUserId()));


            binding.tvMid.setText(Utils.buildDataReceipt(detailTrans.getApplication().getMID()));
            binding.tvTid.setText(Utils.buildDataReceipt(detailTrans.getApplication().getTID()));

            String dateTime = "";
            try {
                dateTime = Utils.convertTimestamp(detailTrans.getTransactionDate(), 3);
            } catch (Exception e) {
                e.printStackTrace();
            }
            binding.tvTime.setText(dateTime);

            String latLng = StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailTrans.getLat()))
                    + "/" + StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailTrans.getLng()));
            binding.tvLatlng.setText(latLng);
            binding.tvBatch.setText(Utils.buildDataReceipt(detailTrans.getBatchNo()));
            binding.tvInvoice.setText(Utils.buildDataReceipt(detailTrans.getInvoiceNumber()));
            binding.tvRef.setText(Utils.buildDataReceipt(detailTrans.getRREFNo()));
            binding.tvAppcode.setText(Utils.buildDataReceipt(detailTrans.getApprovalCode()));
            binding.tvPan.setText(Utils.buildDataReceipt(detailTrans.getMaskedPAN()));
            binding.tvCardHoldername.setText(Utils.buildDataReceipt(detailTrans.getCardHolderName()));

            String issuerCode = new CardUtils().getCardType(dataPay.getPan());
            binding.tvType.setText(Utils.buildDataReceipt(issuerCode));

            binding.tvAppl.setText(Utils.buildDataReceipt(detailTrans.getApplicationLabel()));
            binding.tvAid.setText(Utils.buildDataReceipt(detailTrans.getAIDICC()));

            binding.tvArqc.setVisibility(View.GONE);
            binding.tvTitleArqc.setVisibility(View.GONE);
//            binding.tvArqc.setText(Utils.buildDataReceipt(detailTrans.getArqc()));

            binding.tvDesc.setText(Utils.buildDataReceipt(detailTrans.getItemDescription()));

            binding.tvAmount.setText(String.format("%s VND", Utils.buildDataReceipt(detailTrans.getAmountAuthorized())));

            MyTextUtils myTextUtils = new MyTextUtils();
            Bitmap bitmap = myTextUtils.convertTextBase64ToBitmap(dataPay.getSignatureBase64());

            binding.imvSignature.setImageBitmap(bitmap);

            binding.tvCardHoldernameBottom.setText(Utils.buildDataReceipt(detailTrans.getCardHolderName()));

        }
    }

    private void putDataMacqToView(DataPay dataPay, LayoutPrintFormatBinding binding) {
        Utils.LOGD(TAG, "putDataMacqToView: ====>");
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            WfDetailRes detailRes = dataPay.getWfDetailRes();

            binding.tvMcName.setText(Utils.buildDataReceipt(detailRes.getMerchantName()));
            binding.tvMcAddress.setText(Utils.buildDataReceipt(detailRes.getMerchantAddress()));
            binding.tvUsername.setText(Utils.buildDataReceipt(PrefLibTV.getInstance(context).getUserId()));

            binding.tvMid.setText(Utils.buildDataReceipt(detailRes.getMid()));
            binding.tvTid.setText(Utils.buildDataReceipt(detailRes.getTid()));

            String dateTime = "";
            try {
                dateTime = Utils.convertTimestamp(Long.parseLong(Utils.buildDataReceipt(detailRes.getTransactionDate())), 3);
            } catch (Exception e) {
                e.printStackTrace();
            }
            binding.tvTime.setText(dateTime);

            String latLng = StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLatitude()))
                    + "/" + StringUtil.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLongitude()));
            binding.tvLatlng.setText(latLng);
            binding.tvBatch.setText(Utils.buildDataReceipt(detailRes.getBatchNo()));
            binding.tvInvoice.setText(Utils.buildDataReceipt(detailRes.getInvoiceNo()));
            binding.tvRef.setText(Utils.buildDataReceipt(detailRes.getRrn()));
            binding.tvAppcode.setText(Utils.buildDataReceipt(detailRes.getAuthCode()));
            binding.tvPan.setText(Utils.buildDataReceipt(detailRes.getPan()));
            binding.tvCardHoldername.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            binding.tvType.setText(Utils.buildDataReceipt(detailRes.getIssuerCode()));
            binding.tvAppl.setText(Utils.buildDataReceipt(detailRes.getAppl()));
            binding.tvAid.setText(Utils.buildDataReceipt(detailRes.getAid()));
            binding.tvArqc.setText(Utils.buildDataReceipt(detailRes.getArqc()));
            binding.tvDesc.setText(Utils.buildDataReceipt(detailRes.getDescription()));

            binding.tvAmount.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(detailRes.getAmount()))));

            if (detailRes.isFlagNoSignature()) {
                binding.tvNoRequiredSignature.setVisibility(View.VISIBLE);
                binding.tvTitleSignature.setVisibility(View.GONE);
                binding.imvSignature.setVisibility(View.GONE);
                binding.tvCardHoldernameBottom.setVisibility(View.GONE);
            }
            else {
                MyTextUtils myTextUtils = new MyTextUtils();
                Bitmap bitmap = myTextUtils.convertTextBase64ToBitmap(dataPay.getSignatureBase64());

                binding.imvSignature.setImageBitmap(bitmap);
                binding.tvCardHoldernameBottom.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            }

            binding.tvTitleBill.setText(getDetailByStatus(detailRes, binding));
        }
    }

    public Bitmap getBitmapFromView(View view) {
        view.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        Bitmap bitmap = Bitmap.createBitmap(view.getMeasuredWidth(), view.getMeasuredHeight(),
                Bitmap.Config.ARGB_8888);

        Canvas canvas = new Canvas(bitmap);
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        view.draw(canvas);

        int widthPaper = 380;
        int heightPaper = widthPaper * bitmap.getHeight() / bitmap.getWidth();
        bitmap = Bitmap.createScaledBitmap(bitmap, widthPaper, heightPaper, true);

        return bitmap;
    }


    public static int convertDpToPixel(int dp, Context context){
        return dp * ((int) context.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    private String getString(int stringId) {
        return context.getString(stringId);
    }

    private void appendLog(String msg) {
        if (logController != null) {
            logController.appendLogAction(msg);
        }
    }

    LibPrinterPax.ItfResultPrintPax itfResultPrintPax = new LibPrinterPax.ItfResultPrintPax() {
        @Override
        public void result(int i) {
            MyDialogShow.dismissCurrDialog();
            switch (i) {
                case PRINT_SUCCESS:
//                printerListener.onPrinterEnd();
                    break;
                case PRINT_BUSY:
//                printerListener.onPrinterError(context.getString(R.string.print_busy));
                    MyDialogShow.showDialogError(context.getString(R.string.print_busy), context);

                    break;
                case PRINT_VOLTAGE_LOW:
//                printerListener.onPrinterError(context.getString(R.string.print_voltage_low));
                    MyDialogShow.showDialogError(context.getString(R.string.print_voltage_low), context);

                    break;
                case PRINT_OUT_OF_PAPER:
//                printerListener.onPrinterOutOfPaper();
                    MyDialogShow.showDialogError(context.getString(R.string.error_printer_out_of_paper), context);

                    break;
                case PRINT_OVER_HEATS:
//                printerListener.onPrinterError(context.getString(R.string.print_over_heats));
                    MyDialogShow.showDialogError(context.getString(R.string.print_over_heats), context);
                    break;
                default:
                    MyDialogShow.showDialogError(context.getString(R.string.error_printer_default), context);
                    break;
            }
        }
    };

    public void printReceiptSummaryOfflineByLayout(DataSummaryDeposit dataSummaryDeposit) {
        Bitmap bitmap = buildBitmapSummaryOfflineByLayout(dataSummaryDeposit);

        actionPrintBitmap(bitmap);
    }

    public Bitmap buildBitmapSummaryOfflineByLayout(DataSummaryDeposit dataSummaryDeposit) {
        LayoutPrintFormatSummaryBinding binding = LayoutPrintFormatSummaryBinding.inflate(LayoutInflater.from(context));
        putDataSummaryDepositToView(dataSummaryDeposit, binding);

        return getBitmapFromView(binding.getRoot());
    }

    private void putDataSummaryDepositToView(DataSummaryDeposit dataPay, LayoutPrintFormatSummaryBinding binding) {
        binding.tvUsername.setText(PrefLibTV.getInstance(context).getUserId());
        binding.tvMcName.setText(DataStoreApp.getInstance().getMerchantName());

        binding.tvStartTime.setText(dataPay.timeFrom);
        binding.tvEndTime.setText(dataPay.timeTo);

        if (!TextUtils.isEmpty(dataPay.totalTransApproved)) {
            binding.tvSummaryTotalTransApprove.setText(dataPay.totalTransApproved);
        }

        if (!TextUtils.isEmpty(dataPay.amountTransApproved)) {
            binding.tvSummaryTotalAmountApprove.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransApproved))));
        }

        if (!TextUtils.isEmpty(dataPay.totalTransSettled)) {
            binding.tvSummaryTotalTransSettle.setText(dataPay.totalTransSettled);
        }
        if (!TextUtils.isEmpty(dataPay.amountTransSettled)) {
            binding.tvSummaryTotalAmountSettle.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransSettled))));
        }
        if (!TextUtils.isEmpty(dataPay.totalTransVoid)) {
            binding.tvSummaryTotalTransVoid.setText(dataPay.totalTransVoid);
        }
        if (!TextUtils.isEmpty(dataPay.amountTransVoid)) {
            binding.tvSummaryTotalAmountVoid.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransVoid))));
        }
    }

}
