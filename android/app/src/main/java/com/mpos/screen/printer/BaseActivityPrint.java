package com.mpos.screen.printer;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.CryptoInterface;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.LibPrinterS85;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.DataBaseObj;
import com.mpos.sdk.core.modelma.DownloadReceiptRes;
import com.mpos.sdk.core.modelma.ReceiptSend;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyTextUtils;
import com.pps.core.MyProgressDialog;

import org.json.JSONException;
import org.json.JSONObject;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

@SuppressLint("Registered")
public abstract class BaseActivityPrint extends AppCompatActivity {

    String TAG = this.getClass().getSimpleName();

    public static final int PRINT_END_TRANSACTION   = 1;
    public static final int PRINT_IN_SEND_EMAIL     = 2;

    // printer
    protected LibPrinterMpos libPrinter;

    private String receiptDownloaded;
    protected DataPay dataPayUsePrint;

    protected MyProgressDialog mPgdl;
    private ItfCallbackPrinter callbackPrinter;

    public abstract StringEntity buildStringEntityReceipt();

    protected boolean isRunMultiAcquirer = false;

    protected String txId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPgdl = new MyProgressDialog(this);

        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(this).getBankName())) {
            isRunMultiAcquirer = true;
            txId = getIntent().getStringExtra("txid");
            if (MyTextUtils.isEmpty(txId)) {
                txId = getIntent().getStringExtra("tid");
            }
        }
    }

    protected void initPrinter() {
        if (libPrinter == null) {
            libPrinter = new LibPrinterMpos(this, MyApplication.self().getSaveLogController());
        }
    }

    public void setCallbackPrinter(ItfCallbackPrinter callbackPrinter) {
        this.callbackPrinter = callbackPrinter;
    }

    protected void actionPrint(String msg) {
        Utils.LOGD(TAG, "actionPrint: ");

        libPrinter.actionPrintImageBase64(msg);
    }


    protected synchronized void processPrintReceipt(int typeCallPrint) {
        // show message only print in S85 in SP02
        if (DevicesUtil.isSP02() && !DevicesUtil.isSP02P8()) {
            String bluetoothAddress = PrefLibTV.getInstance(this).getBluetoothAddressPrinter();
            if (MyTextUtils.isEmpty(bluetoothAddress)) {
                MyDialogShow.showDialogCancelAndClick("", getString(R.string.msg_connect_printer_s85), getString(R.string.txt_yes), this, new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        startProcessPrintReceipt(typeCallPrint);
                    }
                }, true, false);
            }
            else {
                startProcessPrintReceipt(typeCallPrint);
            }
        }
        else {
            startProcessPrintReceipt(typeCallPrint);
        }
    }

    private void startProcessPrintReceipt(int typeCallPrint) {
        if (typeCallPrint == PRINT_END_TRANSACTION && canPrintOffline()) {
            initPrinter();
            new Handler().postDelayed(() -> printReceiptOffline(dataPayUsePrint), 500);
            return;
        }
        if (TextUtils.isEmpty(receiptDownloaded)) {
            appendLog("process print-> download");
            mPgdl.showLoading(getString(R.string.downloading_receipt));
            initPrinter();
            if (isRunMultiAcquirer) {
                downloadReceiptMA(txId);
            }
            else {
                downloadReceiptCustomer();
            }
        }
        else {
            appendLog("process print-> reprint");
            initPrinter();
            actionPrint(receiptDownloaded);
        }
    }

    private boolean canPrintOffline() {
        if (dataPayUsePrint != null && !DataStoreApp.getInstance().isReceiptFromServer()) {
            if (isRunMultiAcquirer) {
                return dataPayUsePrint.getWfDetailRes() != null;
            }
            else {
                return dataPayUsePrint.getTransactionDetail() != null;
            }
        }
        return false;
    }

    protected void sleepWaitInitPrinter() {
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void printReceiptOffline(DataPay dataPay) {
        libPrinter.printReceiptOfflineByLayout(dataPay);
    }

    long startTime = 0;
    private void downloadReceiptCustomer() {
        mPgdl.showLoading();
        StringEntity entity = buildStringEntityReceipt();
        Utils.LOGD(TAG, "downloadReceiptCustomer: url=" + ConstantsPay.getUrlServer(this));
        MposRestClient.getInstance(this).post(this, ConstantsPay.getUrlServer(this), entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                super.onStart();
                startTime = System.currentTimeMillis();
                Utils.LOGD(TAG, "onStart: "+startTime);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                try {
//                    Utils.LOGD(TAG, "onSuccess: download receipt ssK=" + PrefLibTV.getInstance(BaseActivityPrint.this).getSessionKey());
                    JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(getApplicationContext()).getSessionKey()));
                    PrefLibTV.getInstance(getApplicationContext()).setSessionKey(response.getString("sessionKey"));
                    Utils.LOGD(TAG, "Img receipt: " + response);
                    if (response.has("error")) {
                        try {
                            final JSONObject jo = response.getJSONObject("error");
                            int code = jo.getInt("code");
                            String msg = getString(R.string.error) + " " + code
                                    + ": " + LibError.getErrorMsg(code, BaseActivityPrint.this);
                            appendLog("load fail: code=" + code + " msg=" + msg);
                            if (code == ConstantsPay.CODE_REQUEST_TIMEOUT || code == 14004) {
                                MyDialogShow.showDialogErrorReLogin(msg, BaseActivityPrint.this);
                            }
                            else {
                                MyDialogShow.showDialogError(msg, BaseActivityPrint.this);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                    else {
                        long timeDownload = System.currentTimeMillis() - startTime;
                        startTime = System.currentTimeMillis();
                        Utils.LOGD(TAG, "onSuccess: timeDownload=" + timeDownload);
                        appendLog("onSuccess: timeDownload=" + timeDownload);

                        receiptDownloaded = response.getString("receipt");
                        actionPrint(receiptDownloaded);
                        processCallback();
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    appendLog("load fail: " + e.getMessage());
                    MyDialogShow.showDialogErrorReLogin(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2), BaseActivityPrint.this);
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                appendLog("onFailure: "+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));

                Utils.LOGE(TAG, "download receipt Error: "+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));

                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT),
                        BaseActivityPrint.this, v -> downloadReceiptCustomer(), true);
            }
        });
    }

    private void downloadReceiptMA(String txid) {
        ReceiptSend receiptSend = new ReceiptSend(txid, null);
        if (DevicesUtil.isP20L() || DevicesUtil.isSP02P8() || DevicesUtil.isPax()) {
            receiptSend.setFontBold(true);
        }
        if (DevicesUtil.isPax()) {
            receiptSend.setPrinterType(4);
        }
        Utils.LOGD(TAG, "downloadReceiptMA= " + MyGson.getGson().toJson(receiptSend));
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(receiptSend));

        MposRestClient.getInstance(this).post(this, ApiMultiAcquirerInterface.URL_GET_RECEIPT, entity, new MyTextHttpResponseHandler(this) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG,  "downloadReceiptMA onFailure() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                mPgdl.hideLoading();
                appendLog("error ->statusCode=" + statusCode + " throwable=" + (throwable == null ? "" : throwable.toString()));
                DataError dataError = new DataError();
                dataError.build(statusCode, rawJsonData, getString(R.string.error_ma_default, String.valueOf(statusCode)));

//                boolean checkMart = DataStoreApp.getInstance().isMerchantEMart();
                if (PrefLibTV.getInstance(BaseActivityPrint.this).getPermitSocket()) {
                    MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), BaseActivityPrint.this, false);
                } else {
                   MyDialogShow.showDialogError(getString(R.string.dialog_error_title, dataError.getErrorCode()), dataError.getMsg(), BaseActivityPrint.this, true);
                }
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG,  "downloadReceiptMA onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + headers + "], rawJsonResponse = [" + rawJsonResponse + "]");
                mPgdl.hideLoading();
                appendLog("download receipt success, lengthData=" + rawJsonResponse.length());
                DataBaseObj data = MyGson.parseJson(rawJsonResponse, DataBaseObj.class);
                String clearData = CryptoInterface.getInstance().decryptData(data.getData());

                DownloadReceiptRes receiptRes = MyGson.parseJson(clearData, DownloadReceiptRes.class);
                receiptDownloaded = receiptRes.getReceiptBase64();
                actionPrint(receiptDownloaded);
                processCallback();
            }
        });
    }

//    private void showReceiptDownload(String content) {
//        int length = 100;
//        Log.d(TAG, "showReceiptDownload: ==>");
//        for (int i = 0; i * 100 < receiptDownloaded.length(); i++) {
//            int end = (i + 1) * length;
//            Log.d(TAG, content.substring(i * length, Math.min(end, receiptDownloaded.length())));
//        }
//        Log.d(TAG, "showReceiptDownload: <==");
//    }

    private void appendLog(String log) {
        MyApplication.self().getSaveLogController().appendLogAction(log);
    }

    private void processCallback() {
        if (callbackPrinter != null) {
            callbackPrinter.onAfterCallPrint();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == LibPrinterS85.REQUEST_ENABLE_BT || requestCode == LibPrinterS85.REQUEST_CONNECT_DEVICE) {
                libPrinter.handlerResultSelectBluetoothPrinter(requestCode, resultCode, data);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (libPrinter != null) {
            libPrinter.disconnectPrinter();
        }
    }

    public interface ItfCallbackPrinter{
        void onAfterCallPrint();
    }

}
