package com.mpos.common;

import android.text.TextUtils;

import com.mpos.models.BaseObjJson;
import com.mpos.models.BaseTrustWorldObjJson;
import com.mpos.sdk.util.Utils;

import org.json.JSONException;
import org.json.JSONObject;

public class JsonParser {

    static final String TAG = "JsonParser";

    public void checkHaveError(JSONObject jObj, BaseObjJson errorBean) {
        if (jObj != null && jObj.has("error")) {
            try {
                JSONObject jError = jObj.getJSONObject("error");
                errorBean.code = getDataJson(jError, "code");
                errorBean.message = getDataJson(jError, "message");
            } catch (JSONException e) {
                Utils.LOGE(TAG, "checkHaveError json exception: ", e);
            }
        }
    }

    public BaseTrustWorldObjJson checkStatusRequestTW(JSONObject jObj) {
        String status = getDataJson(jObj, "status");
        String msg = "";
        try {
            msg = jObj.has("data") && jObj.get("data") instanceof JSONObject ?
                    getDataJson(jObj.getJSONObject("data"), "message") : "";
        } catch (JSONException e) {
            Utils.LOGE(TAG, "checkStatusRequestTW json exception: ", e);
        }
        return new BaseTrustWorldObjJson(status, msg);
    }

    public static String getDataJson(JSONObject jRoot, String element) {
        /*if (!TextUtils.isEmpty(element) && jRoot != null && jRoot.has(element)) {
            try {
                return "null".equals(jRoot.getString(element)) ? "" : jRoot.getString(element);
            } catch (JSONException e) {
                Utils.LOGE(TAG, "getDataJson json exception: ", e);
                return "";
            }
        }
        return "";*/
        return getDataJson(jRoot, element, "");
    }

    public static String getDataJson(JSONObject jRoot, String element, String defObj) {
        if (!TextUtils.isEmpty(element) && jRoot != null && jRoot.has(element)) {
            try {
                return "null".equals(jRoot.getString(element)) ? "" : jRoot.getString(element);
            } catch (JSONException e) {
                Utils.LOGE(TAG, "getDataJson json exception: ", e);
                return defObj;
            }
        }
        return defObj;
    }

/*
    public ArrayList<BankInstallmentObj> parseInstallment(String content) {
        ArrayList<BankInstallmentObj> listBank = new ArrayList<>();
        try {
            JSONArray jArr = new JSONArray(content);
            if (jArr.length() > 0) {
                for (int i = 0; i < jArr.length(); i++) {
                    JSONObject jItem = jArr.getJSONObject(i);
                    BankInstallmentObj itemBank = parseItemBank(jItem);
                    listBank.add(itemBank);
                }
            }
        } catch (JSONException e) {
            Utils.LOGE(TAG, "init data mapBank: ", e);
        }
        return listBank;
    }

    private BankInstallmentObj parseItemBank(JSONObject jItem) {
        //NOTE: new code
        BankInstallmentObj objBank = new BankInstallmentObj();
        objBank.bankId           = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "bankId");
        objBank.bankName         = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "bankName");
        objBank.bankLongName     = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "bankLongName");
        objBank.bankLogo         = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "logo");
        objBank.bankIdCardNumber = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "idCardNumber");
        objBank.minAmount        = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "minAmount");
        objBank.policy           = com.mpos.sdk.core.control.JsonParser.getDataJson(jItem, "policy");

        try {
            //listPeriod
            JSONArray jsonArrayPeriod = jItem.getJSONArray("listPeriod");
            JSONObject jsonObject;
            BankPeriodObject bankPeriodObject;
            ArrayList<BankPeriodObject> arrBankPeriod = new ArrayList<>();

            for (int i = 0; i < jsonArrayPeriod.length(); i++) {
                jsonObject = jsonArrayPeriod.getJSONObject(i);

                bankPeriodObject = new BankPeriodObject();
                bankPeriodObject.installmentOutId  = com.mpos.sdk.core.control.JsonParser.getDataJson(jsonObject, "installmentOutId");
                bankPeriodObject.period  = com.mpos.sdk.core.control.JsonParser.getDataJson(jsonObject, "period");
                bankPeriodObject.rate  = com.mpos.sdk.core.control.JsonParser.getDataJson(jsonObject, "rate");

                arrBankPeriod.add(bankPeriodObject);
            }

            objBank.bankPeriodObjectArrayList = arrBankPeriod;
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (jItem.has("binList")) {
            objBank.binList = new ArrayList<>();

            try {
                JSONArray jBinList = jItem.getJSONArray("binList");
                for (int i = 0; i < jBinList.length(); i++) {
                    objBank.binList.add(jBinList.getString(i));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Utils.LOGD(TAG, "parseItemBank: size binList="+objBank.binList.size());
        }

        return objBank;
    }*/
}
