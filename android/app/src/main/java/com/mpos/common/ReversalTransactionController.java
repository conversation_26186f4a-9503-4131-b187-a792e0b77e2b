package com.mpos.common;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.EditText;
import android.widget.LinearLayout;

import androidx.fragment.app.DialogFragment;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.customview.ViewToolBar;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataReversalTrans;
import com.mpos.screen.BaseDialogFragment;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.pps.core.DataUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

public class ReversalTransactionController {

	Context context;

	public ReversalTransactionController(Context c) {
		context = c;
	}
	
//	public void showDialogErrorAndInputMobile(final DataReversalTrans data) {
//		if(!ScreenUtils.canShowDialog(context)){
//			return;
//		}
//		DialogReversal dialogReversal = DialogReversal.newInstance(data);
//		dialogReversal.setCancelable(false);
//		dialogReversal.show(((AppCompatActivity) context).getSupportFragmentManager(), DialogResult.class.getName());
//	}
	
	public static class DialogReversal extends BaseDialogFragment {

        ToastUtil mToast;
        MyProgressDialog pgdl;

		DataReversalTrans data;

        public static DialogReversal newInstance(DataReversalTrans d) {
            DialogReversal dialogReversal = new DialogReversal();
            Bundle bundle = new Bundle();
            bundle.putSerializable("dataReversal",d);
            dialogReversal.setArguments(bundle);
            return dialogReversal;
        }

        @Override
		public void onCreate(Bundle savedInstanceState) {
			super.onCreate(savedInstanceState);

			setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
		}

		@Override
		public Dialog onCreateDialog(Bundle savedInstanceState) {

            Bundle bundle = getArguments();
            if (bundle != null) {
                data = (DataReversalTrans) bundle.getSerializable("dataReversal");
            }

			Dialog dialog = super.onCreateDialog(savedInstanceState);
			dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
			dialog.getWindow().getAttributes().windowAnimations = R.style.updownDialog;
			dialog.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT,
	                LinearLayout.LayoutParams.MATCH_PARENT);

			return dialog;
		}

		@Override
		public View onCreateView(LayoutInflater inflater, ViewGroup container,
				Bundle savedInstanceState) {
			View view = inflater.inflate(R.layout.dialog_invalid_transaction, container);

            pgdl = new MyProgressDialog(context);

			ViewToolBar vToolBarDialog = new ViewToolBar(context, view);
			vToolBarDialog.showTextTitle(PrefLibTV.getInstance(context).getShopName());

			final EditText edtPhone = view.findViewById(R.id.phone);

			view.findViewById(R.id.v_continue).setOnClickListener(v -> {
                if(TextUtils.isEmpty(edtPhone.getText().toString())){
                    showToast(context.getString(R.string.error_no_input_mobile));
                }
                else if(!DataUtils.validateMobile(edtPhone.getText().toString())){
                    showToast(context.getString(R.string.error_wrong_mobile));
                }
                else{
                    data.setMobile(edtPhone.getText().toString());
                    reversalTransaction(data, DialogReversal.this);
                }
            });

			return view;
		}

        private void showToast(String msg) {
            if(mToast==null) {
                mToast = new ToastUtil(context);
            }
            mToast.showToast(msg);
        }

        private void reversalTransaction(final DataReversalTrans data, final DialogFragment mdialog){
            Utils.LOGD("Data: ", " call reversalTransaction=");
            StringEntity entity = null;
            try {
                JSONObject jo = new JSONObject();
                jo.put(Constants.STR_SERVICE_NAME, Config.API_REVERSAL_TRANSACTION);
                jo.put("platform", Config.PLATFORM);
                jo.put("versionNo", android.os.Build.VERSION.RELEASE);

                if(!TextUtils.isEmpty(data.getTxId())){
                    jo.put("txId", data.getTxId());
                }

                jo.put("mid", PrefLibTV.getInstance(context).getMId());
                jo.put("tid", PrefLibTV.getInstance(context).getTId());

                jo.put("amount", data.getAmount());
                if(!TextUtils.isEmpty(data.getApprovalCode())) {
                    jo.put("approvalCode", data.getApprovalCode());
                }


                if(!TextUtils.isEmpty(data.getPan())) {
                    jo.put("pan", data.getPan());
                }
                if(!TextUtils.isEmpty(data.getCardHolder())) {
                    jo.put("cardHolder", data.getCardHolder());
                }

                jo.put("udid", data.getUdid());
                jo.put("username", PrefLibTV.getInstance(context).getUserId());
                if(!TextUtils.isEmpty(data.getEmail())) {
                    jo.put("passengerEmail", data.getEmail());
                }
                jo.put("passengerMobile", data.getMobile());
                jo.put("muid", PrefLibTV.getInstance(context).getUserId());
                Utils.LOGD("Data: ", jo.toString());
                entity = new StringEntity(jo.toString());
            } catch (JSONException | UnsupportedEncodingException e) {
                Utils.LOGE("ReversalTransaction", "reversalTransaction: ", e);
            }
            MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                @Override
                public void onStart() {
                    super.onStart();
                    pgdl.showLoading();
                }

                @Override
                public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                    pgdl.hideLoading();
                    Utils.LOGE("reversalTransaction: ", new String(arg2));
                    try {
                        JSONObject jRoot = new JSONObject(new String(arg2));
                        BaseObjJson errorBean = new BaseObjJson();
                        JsonParser jsonParser = new JsonParser();
                        jsonParser.checkHaveError(jRoot, errorBean);

                        if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code) || !jRoot.has("error")) {
                            showToast(context.getString(R.string.alert_contact_soon));
                            mdialog.dismiss();
                            MyDialogShow.gotoReLogin(context);
                            ((Activity) context).finish();
                        } else {
                            showDialogErrorRetry(TextUtils.isEmpty(errorBean.message) ?
                                    context.getString(R.string.error_try_again) : errorBean.message, data, mdialog);
                        }
                    } catch (Exception e) {
                        Utils.LOGE("ReversalTransController", "reversalTransaction", e);
                        showDialogErrorRetry(context.getString(R.string.error_try_again), data, mdialog);
                    }
                }

                @Override
                public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                    pgdl.hideLoading();
                    Utils.LOGE("reversalTransaction Error: ", "onFailure: ", arg3);
                    showDialogErrorRetry(context.getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), data, mdialog);
                }
            });
        }

        private void showDialogErrorRetry(String msg, final DataReversalTrans data, final DialogFragment mdialog) {
            MyDialogShow.showDialogRetryCancel("", msg, context, v -> reversalTransaction(data, mdialog), true);
        }
	}

	
}
