package com.mpos.common;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TableLayout;
import android.widget.TextView;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.customview.MposDialog;
import com.mpos.models.BaseObjJson;
import com.mpos.models.DataFromPartner;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.MyProgressDialog;
import com.mpos.utils.Config;
import com.mpos.utils.ConfigIntegrated;
import com.mpos.utils.Constants;
import com.mpos.utils.IntegrationUtils;
import com.mpos.utils.LibErrorMpos;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

/**
 * Created by AnhNT on 6/2/16
 */
public class CheckCodePTIController {
    public static final String tag = CheckCodePTIController.class.getSimpleName();

    private final Context context;
    private final MyProgressDialog mPgdl;
    private final ResultCheckCodePTI callBack;
    private String orderId;
    private String udid;
    private DataFromPartner dataFromPartner;
    private final boolean isMerchantUseMVisa;
    private final boolean isUseReader;

    String qrid;

    //noe: continue, add param for showDialogResultCheckOrderId or not
    private boolean isShowDialogResultCheckOrderId;
    private final boolean flagParamsForTypeMVisa;

    private final AsyncHttpResponseHandler responseHandler = new AsyncHttpResponseHandler() {
        @Override
        public void onStart() {
            mPgdl.showLoading("");
            super.onStart();
        }

        @Override
        public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
            mPgdl.hideLoading();
            MyDialogShow.showDialogRetryCancel("", context.getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), context,
                    v -> getInfoOrderId(orderId, udid, dataFromPartner), true);
        }

        @Override
        public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
            mPgdl.hideLoading();
            String msgError = "";
            String title = null;
            try {
                JsonParser jsonParser = new JsonParser();
                BaseObjJson errorBean = new BaseObjJson();
                JSONObject jRoot = new JSONObject(new String(arg2));
                Utils.LOGD(tag, "RESPONSE getInfoOrderId: "+ jRoot);
                jsonParser.checkHaveError(jRoot, errorBean);
                if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {

                    String desc = JsonParser.getDataJson(jRoot, "description");
                    String info = JsonParser.getDataJson(jRoot, "info");
                    String amount = JsonParser.getDataJson(jRoot, "amount");
                    String status = JsonParser.getDataJson(jRoot, "status");

                    boolean flag = jRoot.has("flag") && !jRoot.isNull("flag") && jRoot.getBoolean("flag");
                    if (!TextUtils.isEmpty(info)) {
                        info = info.replace("|", "\n");
                    }
                    qrid  = JsonParser.getDataJson(jRoot, "qrid");

                    if (dataFromPartner != null) {
                        String typePayByPartner = dataFromPartner.getTypePay();
                        if (ConfigIntegrated.NOT_SHOW_INFO_ORDER.equals(dataFromPartner.getShowInfoOrder())
                                && ConfigIntegrated.TP_QR_CODE.equals(typePayByPartner)) {
                            isShowDialogResultCheckOrderId = false;
                            if (checkCanSale(status)) {
                                if (callBack != null && !TextUtils.isEmpty(qrid)) {
                                    callBack.onResultGotoMVisaWithQRID(dataFromPartner, qrid);
                                }
                            } else {
                                callbackCantPay();
                            }
                        }
                        else if (ConfigIntegrated.NOT_SHOW_INFO_ORDER.equals(dataFromPartner.getShowInfoOrder())
                                && ConfigIntegrated.TP_NORMAL.equals(typePayByPartner)) {
                            isShowDialogResultCheckOrderId = false;
                            if (checkCanSale(status)) {
                                if (callBack != null) {
                                    callBack.onResultCheckCodePTI(dataFromPartner);
                                }
                            } else {
                                callbackCantPay();
                            }
                        }
                    }

                    //noe: continue, check param for show or return callback
                    if (isShowDialogResultCheckOrderId){
                        showDialogResultCheckOrderId(orderId, status, amount, desc, info, flag, jRoot);
                    }

//                    if (!TextUtils.isEmpty(qrid)) {
                    if (DataStoreApp.getInstance().getConnectType().equals(ConfigIntegrated.TYPE_APP1_APP2_APP1) && !TextUtils.isEmpty(qrid)) {
                        if (callBack != null) {
                            callBack.onResultGotoMVisaWithQRID(dataFromPartner, qrid);
                        }
                    }
                }
                else if ("7000".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7000);
                }
                else if ("7001".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7001);
                }
                else if ("7002".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7002);
                }
                else if ("7003".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7003);
                }
                else if ("7004".equals(errorBean.code)) {
                    msgError = String.format(context.getString(R.string.error_7004_order_id_not_found), orderId);
                }
                else if ("7006".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7006);
                }
                else if ("7007".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7007);
                }
                else if ("7008".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_7008);
                }
                else if ("7005".equals(errorBean.code)) {
                    msgError = errorBean.message;
//                    msgError = String.format(context.getString(R.string.error_7005);
                }
                else if ("14000".equals(errorBean.code)) {
                    msgError = context.getString(R.string.error_14000);
                }
                else {
                    msgError = context.getString(R.string.error_get_info_merchant) + "(" +context.getString(R.string.error) + " " +errorBean.code + ")";
                }
                title = context.getString(R.string.error) + " " +errorBean.code;
            } catch (JSONException e) {
                msgError = context.getString(R.string.error_get_info_merchant);
                Utils.LOGE("Exception", "parse json: ", e);
            }
            if (!TextUtils.isEmpty(msgError)) {
                MyDialogShow.showDialogRetryCancel(title, msgError, context, v -> getInfoOrderId(orderId, udid, dataFromPartner), true);
            }
        }

        private void showDialogResultCheckOrderId(final String code, final String status,
                                                  final String amount, String desc, String info, final boolean flag, JSONObject jRoot) {

            final Dialog dialogResultCheckOrderId = new Dialog(context, android.R.style.Theme_Translucent);
//            final Dialog dialog = new Dialog(context, R.style.SpecialDialog);
            final LayoutInflater inflater = (LayoutInflater) context.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
            View dialogLayout = inflater.inflate(R.layout.dialog_ket_qua_nhap_code, null);

            ((TextView) dialogLayout.findViewById(R.id.tv_order_code)).setText(code);

            LinearLayout vAmount = dialogLayout.findViewById(R.id.v_amount);
            TableLayout vTransDetails = dialogLayout.findViewById(R.id.v_trans_info);
            TextView tvInfo = dialogLayout.findViewById(R.id.tv_info);
            TextView tvDesc = dialogLayout.findViewById(R.id.tv_desc);
            TextView tvStatus = dialogLayout.findViewById(R.id.tv_status);

//          note: normal
            LinearLayout llParentNormal = dialogLayout.findViewById(R.id.linearLayoutNormal);
            Button btnOk = dialogLayout.findViewById(R.id.ok);
            Button btnCancel = dialogLayout.findViewById(R.id.cancel);

//          note: mvisa
            LinearLayout llParentMVisa  = dialogLayout.findViewById(R.id.linearLayoutForMVISA);
            View llSelectPaymentType  = dialogLayout.findViewById(R.id.select_type_pay_layout_method);
            TextView tvBaseOnSwipeCard  = dialogLayout.findViewById(R.id.tvPaymentBaseOnSwipeCard);
            TextView tvBaseOnMVisa      = dialogLayout.findViewById(R.id.tvPaymentBaseOnMVisa);
            Button btnCancelMVisa = dialogLayout.findViewById(R.id.cancelMVISA);

            Utils.LOGD(tag, "showDialogResultCheckOrderId: isMerchantUseMVisa="+isMerchantUseMVisa);

            if (isMerchantUseMVisa) {
                llParentMVisa.setVisibility(View.VISIBLE);
                llParentNormal.setVisibility(View.GONE);
            } else {
                llParentMVisa.setVisibility(View.GONE);
                llParentNormal.setVisibility(View.VISIBLE);
            }

            final String descUse;
            if (!TextUtils.isEmpty(desc)) {
                descUse = desc;
            } else if (dataFromPartner!=null && !TextUtils.isEmpty(dataFromPartner.getDescription())) {
                descUse = dataFromPartner.getDescription();
            } else {
                descUse = "";
            }

            tvDesc.setText(descUse);

            if (!TextUtils.isEmpty(amount)) {
                ((TextView) dialogLayout.findViewById(R.id.tv_amount)).setText(Utils.zenMoney(amount)
                        + ConstantsPay.CURRENCY_SPACE_PRE);
                tvInfo.setText(info);
            }

            View.OnClickListener onClickCancel = v -> dialogResultCheckOrderId.dismiss();

            if (checkCanSale(status)) {
                if (TextUtils.isEmpty(amount)) {
                    vAmount.setVisibility(View.GONE);
                    tvInfo.setText(context.getString(R.string.not_found_order_select_continue));

                    if (isMerchantUseMVisa) {
                        llSelectPaymentType.setVisibility(View.VISIBLE);
                        btnCancelMVisa.setVisibility(View.VISIBLE);
                    } else {
                        btnOk.setVisibility(View.VISIBLE);
                        btnCancel.setVisibility(View.VISIBLE);
                    }

                    dialogLayout.findViewById(R.id.v_status).setVisibility(View.GONE);
                } else {
                    tvStatus.setText(context.getString(ConfigIntegrated.STATUS_ACTIVE.equals(status) ? R.string.not_paid : R.string.can_paid));
                }
            } else {
                tvStatus.setText(context.getString(R.string.order_cannot_pay));
                if (isMerchantUseMVisa) {
                    llSelectPaymentType.setVisibility(View.GONE);
                    btnCancelMVisa.setVisibility(View.VISIBLE);
                } else {
                    btnOk.setVisibility(View.GONE);
                    btnCancel.setVisibility(View.VISIBLE);
                }

                final DataPay dataPay = new DataPay("",
                        JsonParser.getDataJson(jRoot, "txid"),
                        amount,
                        JsonParser.getDataJson(jRoot, "pan"),
                        "",
                        JsonParser.getDataJson(jRoot, "cardHolderName"),
                        "", "", "", ""
                );

                if (!TextUtils.isEmpty(dataPay.getTxId())) {
                    vTransDetails.setVisibility(View.VISIBLE);

                    TextView tvHolderName   = dialogLayout.findViewById(R.id.tv_holder_name);
                    TextView tvPan          = dialogLayout.findViewById(R.id.tv_pan);
                    TextView tvTransID      = dialogLayout.findViewById(R.id.tv_trans_id);
                    TextView tvTime         = dialogLayout.findViewById(R.id.tv_time);


                    tvHolderName.setText(dataPay.getName());//.getDataJson(jRoot, "cardHolderName")
                    tvPan.setText(dataPay.getPan());        //.getDataJson(jRoot, "pan")
                    tvTransID.setText(dataPay.getTxId());   //JsonParser.getDataJson(jRoot, "txid")


                    try {
                        tvTime.setText(Utils.convertTimestamp(Long.parseLong(JsonParser.getDataJson(jRoot, "createdDate"))));
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                        tvTime.setVisibility(View.GONE);
                    }
                    onClickCancel = v -> {
                        if (dataFromPartner != null) {
                            IntegrationUtils integrationUtils = new IntegrationUtils();
                            integrationUtils.callbackToPartner(context, dataFromPartner, dataPay);
                        } else {
//                              note: On 11/21/17, at 9:48 PM, Tap Ngo Minh wrote:
//                              note: > App Android, Trường hợp tích hợp API (connectType = 2) khi nhập mã hóa đơn đã thanh toán,
//                              note: ra màn hình (như hình) click vào quay lại ứng dụng tích hợp, TH này khi click vòa nút này thì back về màn hình chính (Home).
                            dialogResultCheckOrderId.dismiss();
                        }
                    };
                    if (isMerchantUseMVisa) {
                        btnCancelMVisa.setText(context.getString(R.string.go_back_integration));
                    } else {
                        btnCancel.setText(context.getString(R.string.go_back_integration));
                    }
                }

            }

            View.OnClickListener listenerPayment = v -> {
                if (callBack != null) {
                    if (isUseReader) {
                        dialogResultCheckOrderId.dismiss();
                        callBack.onResultCheckCodePTI(createDataPartner(code, amount, descUse, flag));
                    } else {
                        if (isMerchantUseMVisa) {
                            //note: Để thực hiện giao dịch QUẸT THẺ bạn cần đăng nhập với thiết bị đọc thẻ, vui lòng đăng nhập lại
                            Utils.LOGD(tag, "DIALOG-----SU DUNG QRCODE");
                            showDialogBeforeSwipeCard();
                        } else {
                            Utils.LOGD(tag, "DIALOG-----KHONG SU DUNG QRCODE");
                            //note: DVCNT chua dang ky thanh toan MPOS QR. Vui long lien he hotline de dang ky hoac dang nhap lai voi thiet bi doc the de thanh toan
                            showDialogBeforeScanQR();
                        }
                    }
                }
            };

            if (isMerchantUseMVisa) {
                tvBaseOnSwipeCard.setOnClickListener(listenerPayment);
                btnCancelMVisa.setOnClickListener(onClickCancel);
                tvBaseOnMVisa.setOnClickListener(v -> {
                    checkPrepareTransQR(Constants.TYPE_QR_MVISA, udid, createDataPartner(code, amount, descUse, flag));
//                    checkPrepareTransQR(Constants.TYPE_QR_MVISA, udid, dataFromPartner);
                });
            } else {
                btnOk.setOnClickListener(listenerPayment);
                btnCancel.setOnClickListener(onClickCancel);
            }

            dialogResultCheckOrderId.requestWindowFeature(Window.FEATURE_NO_TITLE);
            if (dialogResultCheckOrderId.getWindow() != null) {
                dialogResultCheckOrderId.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
            }
            dialogResultCheckOrderId.setCanceledOnTouchOutside(false);
            dialogResultCheckOrderId.setContentView(dialogLayout);
            dialogResultCheckOrderId.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);

            dialogResultCheckOrderId.show();
        }

    };


    private DataFromPartner createDataPartner(final String code, final String amount, final String desc, final boolean flag) {
        Utils.LOGD(tag, "createDataPartner");
        if (dataFromPartner == null) {
            DataFromPartner dataPartner = new DataFromPartner();
            dataPartner.setAmount(amount);
            dataPartner.setOrderId(code);
            dataPartner.setDescription(desc);
            dataPartner.setFlag(flag);

            dataPartner.setManual(!TextUtils.isEmpty(amount));
            return dataPartner;
        }
        else {
            dataFromPartner.setAmount(amount);
            dataFromPartner.setOrderId(code);
            dataFromPartner.setDescription(desc);
            dataFromPartner.setFlag(flag);
            dataFromPartner.setManual(!TextUtils.isEmpty(amount));

            return  dataFromPartner;
        }
    }

    private void callbackCantPay() {
        if (callBack != null) {
            dataFromPartner.setMsgError(context.getString(R.string.integration_cant_pay));
            callBack.onResultCheckCodePTI(dataFromPartner);
        }
    }

    private boolean checkCanSale(String status) {
        return ConfigIntegrated.STATUS_ACTIVE.equals(status) || ConfigIntegrated.STATUS_PROCESSING.equals(status);
    }

    public CheckCodePTIController(Context context, boolean isShowDialogResultCheckOrderId, boolean flagParamsForTypeMVisa, ResultCheckCodePTI cb) {
        this.context = context;
        this.isShowDialogResultCheckOrderId = isShowDialogResultCheckOrderId;
        this.flagParamsForTypeMVisa = flagParamsForTypeMVisa;
        this.callBack = cb;
        mPgdl = new MyProgressDialog(context);

        isMerchantUseMVisa = DataStoreApp.getInstance().isMerchantRegistedQR();
        isUseReader     = DataStoreApp.getInstance().isUseReader();
    }

    public void getInfoOrderId(final String orderId, String udid) {
        getInfoOrderId(orderId, udid, null);
    }

    public void getInfoOrderId(final String orderId, final String udid, DataFromPartner dataFromPartner) {
        Utils.LOGD(tag, "--------------------------------");
        Utils.LOGD(tag, "getInfoOrderId");
        StringEntity entity = null;
        this.orderId = orderId;
        this.udid = udid;
        this.dataFromPartner = dataFromPartner;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.MERCHANT_INTEGRATED_CHECK_PREPAY);
            jo.put("orderId", orderId);
            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            if (!TextUtils.isEmpty(udid)) {
                jo.put("udid", udid);
            }
            if (dataFromPartner!=null && !TextUtils.isEmpty(dataFromPartner.getAmount())) {
                jo.put("amount", dataFromPartner.getAmount());
            }
            if (dataFromPartner!=null && !TextUtils.isEmpty(dataFromPartner.getExtParam())) {
                jo.put("extParam", dataFromPartner.getExtParam());
            }

            //noe: continue, add more param for case mvisa
            if (flagParamsForTypeMVisa){
                if (dataFromPartner != null) {
                    jo.put("customerEmail",  dataFromPartner.getEmailReceipt());
                }
                jo.put("customerMobile", "");
                jo.put("paymentMethod", "QR");
            }

            Utils.LOGD(tag, "REQUEST: "+ jo);

            entity = new StringEntity(jo.toString());
        } catch (UnsupportedEncodingException | JSONException e) {
            Log.e("CheckCodePTIController", "123", e);
            Utils.LOGE("CheckCodePTIController", "getInfoOrderId: ", e);
        }

        MposRestClient.getInstance(context).post(context, Config.URL_INTERGRATED, entity,
                Config.CONTENT_TYPE, responseHandler);
    }

    private void checkPrepareTransQR(final String qrType, final String udid, final DataFromPartner dataPartner) {
//        checkTypePayIsQR();
//        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION);
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.PREPARE_TRANSACTION);
            jo.put("udid", udid);

            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());

            jo.put("merchantId",    DataStoreApp.getInstance().getmerchantId());
            jo.put("paymentMethod", "QR");
            jo.put("customerEmail", dataPartner.getEmailReceipt());

            jo.put("qrType", qrType);   //Constants.TYPE_QR_MVISA:Constants.TYPE_QR_VIMO

            jo.put("amount", dataPartner.getAmount());


            Utils.LOGD(tag,"checkInfoInstallmentOrMvisa | REQ: "+ jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(tag, "Exception", e);
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading();
                super.onStart();
            }
            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//                logUtil.appendLogRequestApiFail(Config.PREPARE_TRANSACTION+" onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", context.getString(R.string.error_onfaild_request), context, v -> checkPrepareTransQR(qrType, udid, dataPartner), true);
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                String msgError = null;
                try {
                    JsonParser jsonParser = new JsonParser();
                    BaseObjJson errorBean = new BaseObjJson();
                    JSONObject jRoot = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "-checkInfoInstallmentOrMvisa:"+ jRoot);
                    jsonParser.checkHaveError(jRoot, errorBean);
                    if (Config.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
//                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION+" success");

                        String qridTemp = JsonParser.getDataJson(jRoot, "qrid");
                        String qrCode = JsonParser.getDataJson(jRoot, "qrCode");
                        if (!TextUtils.isEmpty(qridTemp)) {
//                            gotoPaymentMVisa(qridTemp, qrCode);
                            if (callBack != null) {
                                DataFromPartner dataFromPartner = createDataPartner(orderId, dataPartner.getAmount(), dataPartner.getDescription(),
                                        dataPartner.isFlag());
                                dataFromPartner.setQrCode(qrCode);
                                dataFromPartner.setQrType(qrType);
                                callBack.onResultGotoMVisaWithQRID(dataFromPartner, qrid);
                            }
                        }
                    }
                    else{
                        msgError = LibErrorMpos.getErrorMsg(context, errorBean.code);
                        if (TextUtils.isEmpty(msgError)) {
                            msgError = TextUtils.isEmpty(errorBean.message)? context.getString(R.string.error_default)
                                    :errorBean.message;
                        }
//                        logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION+" error:"+msgError);
                    }
                } catch (Exception e) {
//                    logUtil.appendLogRequestApi(Config.PREPARE_TRANSACTION+" Exception:"+e.getMessage());
                    msgError = context.getString(R.string.error_try_again);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    MyDialogShow.showDialogRetryCancel("", msgError, context, v -> checkPrepareTransQR(qrType, udid, dataPartner), true);
                }
            }
        });
    }

    /**
     * NOTICE: Để thực hiện giao dịch QUET THE bạn cần đăng nhập với thiết bị đọc thẻ, vui lòng đăng nhập lại
     */
    private void showDialogBeforeSwipeCard() {
        String positiveText = context.getString(R.string.txt_yes);
        String negativeText = context.getString(R.string.txt_no);
        new AlertDialog.Builder(context)
                .setTitle(context.getString(R.string.txt_title_notice))
                .setMessage(context.getString(R.string.txt_guide_step_swipe_card))
                .setPositiveButton(positiveText, (dialog, which) -> logOut())
                .setNegativeButton(negativeText, (dialog, which) -> dialog.dismiss())
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    /**
     * NOTICE: DVCNT chua dang ky thanh toan MPOS QR. Vui long lien he hotline de dang ky hoac dang nhap lai voi thiet bi doc the de thanh toan
     */
    private void showDialogBeforeScanQR(){
        final MposDialog mposDialogError = MyUtils.initDialogGeneralError(context, 0, context.getString(R.string.txt_login_email_and_not_accept_mvisa), CheckCodePTIController.class.getName());
        mposDialogError.setOnClickListenerDialogClose(v -> mposDialogError.dismiss());
        mposDialogError.show();
    }

    public interface ResultCheckCodePTI {
        void onResultCheckCodePTI(DataFromPartner dataPartner);
        void onResultGotoMVisaWithQRID(DataFromPartner dataPartner, String qrid);
    }

    private void logOut() {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.GATEWAY_MERCHANT_LOGOUT);
            jo.put("readerSerial", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("os", "Android");
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            Utils.LOGD("Data: ", jo.toString());
            Utils.LOGD(tag,"ChechCodePTI: "+DataStoreApp.getInstance().getRegisterId());
            entity = new StringEntity(jo.toString());
        } catch (JSONException | UnsupportedEncodingException e1) {
            Utils.LOGE(tag, "logOut: "+ e1);
        }

        MposRestClient.getInstance(context).post(context, Config.URL_GATEWAY, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading("");
                super.onStart();
            }
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                mPgdl.hideLoading();
                gotoLogin();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                mPgdl.hideLoading();
            }
        });
    }

    protected void gotoLogin(){
        PrefLibTV.getInstance(context).clearDataAuto();
        DataStoreApp.getInstance().clearData();
        MyApplication.self().clearMposSkd();
        MyDialogShow.gotoReLogin(context);
//        Intent intent = new Intent(context, ActivityLogin.class);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//        context.startActivity(intent);
        ((Activity)context).finish();
    }
}
