package com.mpos.common;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;

import com.mpos.models.QrPayResponse;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.utils.Constants;
import com.mpos.utils.SharedPrefs;

import java.util.List;

public class DataStoreApp {

    private final String showUserGuide = "SHOW_USER_GUIDE";
    private final String regId = "REG_ID";
    private final String logData = "LOG_DATA";
    private final String merchantName = "MERCHANT_NAME";
    private final String isLandscape = "IS_LANDSCAPE";
    private final String canSaleService = "CAN_SALE_SERVICES";

    // Integrate
    private final String integrateLogoUrl = "INTEGRATE_LOGO_URL";
    private final String integrateSecretKey = "INTEGRATE_SECRET_KEY";
    private final String integratePreUrl = "INTEGRATE_PRE_URL";
    private final String integratePostUrl = "INTEGRATE_POST_URL";


    // config cash_back + installment
    private final String haveInstallment = "HAVE_INSTALLMENT";
//    private final String dataInstallment = "DATA_INSTALLMENT";
    private final String dataExchangeInfo = "DATA_exchangeInfo";
    private final String dataExchangeLinkInfo = "DATA_exchangeLinkInfo";
    private final String dataCheckBinInstallment = "DATA_CheckBinInstallment";
    private final String dataConvertVimo = "DATA_ConvertVimo";
    private final String dataConvertVimoAmount = "DATA_ConvertVimoAmount";
    private final String dataBanner = "DATA_Banner";
    private final String dataCheckAffiliate = "DATA_CheckAffiliate";
    private final String dataLinkAffiliateCover = "DATA_LinkAffiliateCover";
    private final String dataLinkAffiliateEvent = "DATA_LinkAffiliateEvent";
    private final String dataCheckIsPayLink = "DATA_dataCheckIsPayLink";
    private final String dataCheckIsPayCard = "DATA_dataCheckIsPayCard";

    private final String enableNormalPayment = "enableNormalPayment";
    private final String isDisableCheckGps = "IS_DISABLE_CHECK_GPS";
    private final String haveCashBack = "HAVE_CASH_BACK";
    private final String cashBackId = "CASH_BACK_ID";
    private final String cashBackName = "CASH_BACK_NAME";
    private final String isShowNew = "IS_SHOW_NEW";
    private final String isShowChangePassword = "IS_SHOW_CHANGE_PASS";
    private final String isShowInstallmentPayment = "IS_SHOW_INSTALLMENT";
    public static String isShowPromotion = "IS_SHOW_PROMOTION";
    private final String isShowNormalPayment = "IS_SHOW_NORMAL_PAYMENT";
    private final String isShowOTA = "IS_SHOW_OTA_PAYMENT";
    private final String isShowUnivestLink = "IS_SHOW_UNIVEST_LUNK";
    private final String isAutoSelectApp = "IS_AUTO_SELECT_APP";
    private final String dataVaymuonInstallment = "DATA_VAYMUON_INSTALLMENT";
    private final String dataVaymuonExchange = "DATA_VAYMUON_EXCHANGE";

//    private final String phonesSave = "PHONES_SAVE";
    private final String amountsSave = "AMOUNTS_SAVE";

    private final String enableQuickWithdrawal = "KEY_ENABLE_QUICK_WITHDRAWAL";
    //    private String vatQuickWithdrawal = "KEY_VAT_QUICK_WITHDRAWAL";
//    private String percentQuickWithdrawal = "KEY_PERCENT_QUICK_WITHDRAWAL";
    private final String amountMinQuick = "KEY_AMOUNT_MIN";
    private final String amountMaxQuick = "KEY_AMOUNT_MAX";

    private final String cacheSerialBank = "CACHE_SERIAL_BANK";

    private final String merchantInfo = "merchantInfo";
    private final String merchantCategoryCode = "mVisaMerchantCategoryCode";
    private final String merchantNameShortcut = "mvisaMerchantName";
    private final String mvisaMidMaster = "mvisaMidMaster";
    private final String mvisaMid = "mvisaMid";
    private final String merchantId = "merchantId";
    private final String emailMerchant = "emailMerchant";
    private final String terminalId = "terminalId";
    private final String isMerchantUseMVisa = "isMerchantRegistedQR";
    private final String isUseReader = "isUseReader";
    private final String isShowVimoQR = "isShowVimoQR";
    private final String isNormalPayLink = "isNormalPayLink";

    private final String orderIdFromPartner = "orderIdFromPartner";
    private final String urlCallbackPartner = "urlCallbackPartner";
    private final String extParamPartner = "extParamPartner";
    private final String connectType = "connectType";
    private final String quickWithdrawList = "jsonQuickWithdrawList";

    private final String categoryGCM = "categoryGCM";
//    private String userLogin = "userLogin";

    private final String checkFeeChange = "checkFeeChange";
    private final String checkFeeInstallment = "checkFeeInstallment";
    private final String checkFeeTrans = "checkFeeTrans";
    private final String mandatoryCheckLimit = "mandatoryCheckLimit";

    //    private String configSupport = "configSupport";
//    private String configAfterSale = "configAfterSale";
    private final String phoneInTime = "phoneInTime";
    private final String phoneOutTime = "phoneOutTime";

//    private final String sendEmailMerchant = "sendEmailMerchant";
    private final String canFeedback = "canFeedback";
    private final String feedbackAmountMin = "feedbackAmountMin";
    private final String mobileUserPhone = "mobileUserPhone";
    private final String listQrPay = "listQrPay";
    private final String haveListQrSource = "haveListQrSource";
    private final String haveListQrInternational = "haveListQrInternational";
    private final String haveQrInternational = "haveQrInternational";

//    private final String upgradeEMVConfig = "upgradeEMVConfig";
//    private final String urlEmvApp = "urlEmvApp";
//    private final String urlEmvCapk = "urlEmvCapk";

    private final String dataLoginMerchant = "dataLoginMerchant";

//    data last time show home popup
    private final String dataLTSHP = "dataLTSHP";

    public static final String lastTimeLoginLevel2        = "lastTimeLoginLevel2";
    public static final String havePrintWhenPaySuccess    = "havePrintWhenPaySuccess";
//    public static final String readersInjected = "readersInjected";
    public static final String receiptFromServer = "skipSignature";  // value=1: must download receipt from server -> not accept print offline

    // rethink
    public static final String enableRethink        = "enableRethink";
    public static final String rethinkHostName      = "rethinkHostName";
    public static final String rethinkPort          = "rethinkPort";
    public static final String rethinkDbName        = "rethinkDbName";
    public static final String rethinkUsername      = "rethinkUsername";
    public static final String rethinkUserPassword  = "rethinkUserPassword";
    public static final String rethinkAutoPay       = "rethinkAutoPay";

    public static final String isShowTransactionHistory = "isShowTransactionHistory";
    public static final String isConfirmPassword        = "isConfirmPassword";
    public static final String isDisableButtonSP01      = "isDisableButtonSP01";

    public static final String isAutoPrintReceipt      = "isAutoPrintSp01";    // preference to file xml/settings_reference.xml

    public static final String isPayMotoMacq      = "isPayMotoMacq";
    public static final String hasErrorCodePrepay      = "hasErrorCodePrepay";

    public static final String dataEmart            = "dataEmart";
    public static final String dateCacheEmart       = "dateCacheEmart";
    public static final String permitVoidSocket       = "permitVoidSocket";
    public static final String isOnlyShowUIWaitOrderTCP       = "isOnlyShowUIWaitOrderTCP";

    // QR nl
    public static final String permitQrNl   = "permitQrNl";
    public static final String permitQrMomoNl   = "permitQrMomoNl";
    public static final String permitQrZaloNl   = "permitQrZaloNl";
    public static final String qrNlSiteCode = "qrNlSiteCode";
    public static final String qrNlPassCode = "qrNlPassCode";

    public static final String hasWaitSignatureMacq      = "hasWaitSignatureMacq";
    public static final String isAutoGotoCashierActivity      = "isAutoGotoCashierActivity";

    private static DataStoreApp dataStoreApp;

    //Setting
    public static final String isDisableStatusBar      = "isDisableStatusBar";
    public static final String isDisableNavbar      = "isDisableNavbar";
    public static final String printMoreReceipt      = "printMoreReceipt";
    public static final String isShowConfirmVoidTcp      = "isShowConfirmVoidTcp";      // todo use for Emart

    public static DataStoreApp getInstance() {
        if (dataStoreApp == null) {
            dataStoreApp = new DataStoreApp();
        }
        return dataStoreApp;
    }

    // Data Log
    public void saveListQrPay(List<QrPayResponse> data) {
        SharedPrefs.getInstance().put(listQrPay, data);
    }

    public List<QrPayResponse> getListQrPay() {
        return SharedPrefs.getInstance().getListQrPay(listQrPay);
    }

    public void saveHaveListQrSource(boolean check) {
        SharedPrefs.getInstance().put(haveListQrSource, check);
    }

    public boolean getHaveListQrSource() {
        return SharedPrefs.getInstance().get(haveListQrSource, Boolean.class, false);
    }

    public void saveHaveListQrInternational(boolean check) {
        SharedPrefs.getInstance().put(haveListQrInternational, check);
    }

    public boolean getHaveListQrInternational() {
        return SharedPrefs.getInstance().get(haveListQrInternational, Boolean.class, false);
    }


//    public void saveHaveQrInternational(List<QrPayResponse> data) {
//        boolean have = false;
//        if (data != null && data.size() > 0) {
//            for (QrPayResponse item : data) {
//                if (item != null && Constants.TYPE_QR_MVISA.equals(item.getQrType())) {
//                    have = true;
//                    break;
//                }
//            }
//        }
//        SharedPrefs.getInstance().put(haveQrInternational, have);
//    }
//
//    public boolean haveQrInternational() {
//        return SharedPrefs.getInstance().get(haveQrInternational, Boolean.class, false);
//    }

    public <T> T getDataByKey(String key, Class<T> anonymousClass) {
        return SharedPrefs.getInstance().get(key, anonymousClass);
    }
    public <T> T getDataByKey(String key, Class<T> anonymousClass, Object defaultObject) {
        return SharedPrefs.getInstance().get(key, anonymousClass, defaultObject);
    }

    public void saveDataByKey(String key, Object data) {
        SharedPrefs.getInstance().put(key, data);
    }

    // MY_LANGUAGE
    public void createShowUserGuide(boolean show) {
        SharedPrefs.getInstance().put(showUserGuide, show);
    }

    public boolean getShowUserGuide() {
        return SharedPrefs.getInstance().get(showUserGuide, Boolean.class);
    }

    // gcm register ID
    public void createRegisterId(String rId) {
        SharedPrefs.getInstance().put(regId, rId);
    }

    public String getRegisterId() {
        return SharedPrefs.getInstance().get(regId, String.class);
    }

    // Data Log
    public void saveLogData(String data) {
        SharedPrefs.getInstance().put(logData, data);
    }

    public String getLogData() {
        return SharedPrefs.getInstance().get(logData, String.class);
    }

    // Merchant Name
    public void createMerchantName(String data) {
        SharedPrefs.getInstance().put(merchantName, data);
    }

    public String getMerchantName() {
        return SharedPrefs.getInstance().get(merchantName, String.class);
    }

    // Integrate Pre Url
    public void createIntergratPreUrl(String data) {
        SharedPrefs.getInstance().put(integratePreUrl, data);
    }

    public String getIntergratPreUrl() {
        return SharedPrefs.getInstance().get(integratePreUrl, String.class);
    }

    // Integrate Post Url
    public void createIntergratPostUrl(String data) {
        SharedPrefs.getInstance().put(integratePostUrl, data);
    }

    public String getIntergratPostUrl() {
        return SharedPrefs.getInstance().get(integratePostUrl, String.class);
    }

    // Integrate logo url
    public void createIntergratLogoUrl(String data) {
        SharedPrefs.getInstance().put(integrateLogoUrl, data);
    }

    public String getIntergratLogoUrl() {
        return SharedPrefs.getInstance().get(integrateLogoUrl, String.class);
    }

    // Integrate secrect key
    public void createIntergratSecrectKey(String data) {
        SharedPrefs.getInstance().put(integrateSecretKey, data);
    }

    public String getIntergratSecrectKey() {
        return SharedPrefs.getInstance().get(integrateSecretKey, String.class);
    }

    // screen orientation
    public void createIsLandscape(boolean isLands) {
        SharedPrefs.getInstance().put(isLandscape, isLands);
    }

    public boolean getIsLandscape() {
        return SharedPrefs.getInstance().get(isLandscape, Boolean.class);
    }

    // can sale service
    public void createCanSaleService(boolean canSale) {
        SharedPrefs.getInstance().put(canSaleService, canSale);
    }

    public boolean getCanSaleService() {
        return SharedPrefs.getInstance().get(canSaleService, Boolean.class);
    }

    // is disable gps
    public void createDisableCheckGps(boolean disableGps) {
        SharedPrefs.getInstance().put(isDisableCheckGps, disableGps);
    }

    public boolean getIsDisableCheckGps() {
        return SharedPrefs.getInstance().get(isDisableCheckGps, Boolean.class);
    }

    // is show new
    public void createShowNew(boolean disableGps) {
        SharedPrefs.getInstance().put(isShowNew, disableGps);
    }

    public boolean getIsShowNew() {
        return SharedPrefs.getInstance().get(isShowNew, Boolean.class, true);
    }

    // is show change pass
    public void createShowChangePass(boolean disableGps) {
        SharedPrefs.getInstance().put(isShowChangePassword, disableGps);
    }

    public boolean getIsShowChangePass() {
        return SharedPrefs.getInstance().get(isShowChangePassword, Boolean.class, true);
    }

    // is show installment
    public void createShowInstallment(boolean disableGps) {
        SharedPrefs.getInstance().put(isShowInstallmentPayment, disableGps);
    }

    public boolean getIsShowInstallment() {
        return SharedPrefs.getInstance().get(isShowInstallmentPayment, Boolean.class, true);
    }

    // data vaymuon installment
    public void createDataVaymuonInstallment(String dataVaymuon) {
        SharedPrefs.getInstance().put(dataVaymuonInstallment, dataVaymuon);
    }

    public String getDataVaymuonInstallment() {
        return SharedPrefs.getInstance().get(dataVaymuonInstallment, String.class, "");
    }

    // data vaymuon exchange
    public void createDataVaymuonExchange(String dataVaymuonExchange) {
        SharedPrefs.getInstance().put(dataVaymuonExchange, dataVaymuonExchange);
    }

    public String getDataVaymuonExchange() {
        return SharedPrefs.getInstance().get(dataVaymuonExchange, String.class, "");
    }

    // is show normal payment
    public void createShowNormalPayment(boolean disableGps) {
        SharedPrefs.getInstance().put(isShowNormalPayment, disableGps);
    }

    public boolean getIsShowNormalPayment() {
        return SharedPrefs.getInstance().get(isShowNormalPayment, Boolean.class, true);
    }

    // is show normal payment
    public void createAutoSelectApp(boolean disableGps) {
        SharedPrefs.getInstance().put(isAutoSelectApp, disableGps);
    }

    public boolean getIsAutoSelectApp() {
        return SharedPrefs.getInstance().get(isAutoSelectApp, Boolean.class, true);
    }

    // is show OTA payment
    public void createShowOTA(boolean show) {
        SharedPrefs.getInstance().put(isShowOTA, show);
    }

    public boolean getIsShowOTA() {
        return SharedPrefs.getInstance().get(isShowOTA, Boolean.class, true);
    }

    // is show Univest Link
    public void createShowUnivestLink(boolean show) {
        SharedPrefs.getInstance().put(isShowUnivestLink, show);
    }

    public boolean getIsShowUnivestLink() {
        return SharedPrefs.getInstance().get(isShowUnivestLink, Boolean.class, true);
    }

    // installment
    public void createHaveInstallment(boolean isInstallment) {
        SharedPrefs.getInstance().put(haveInstallment, isInstallment);
    }

    public boolean getHaveInstallment() {
        return SharedPrefs.getInstance().get(haveInstallment, Boolean.class);
    }

    // data ExchangeInfo
    public void createDataExchangeInfo(String data) {
        SharedPrefs.getInstance().put(dataExchangeInfo, data);
    }

    public String getDataExchangeInfo() {
        return SharedPrefs.getInstance().get(dataExchangeInfo, String.class);
    }

    // data ExchangeLinkInfo
    public void createDataExchangeLinkInfo(String data) {
        SharedPrefs.getInstance().put(dataExchangeLinkInfo, data);
    }

    public String getDataExchangeLinkInfo() {
        return SharedPrefs.getInstance().get(dataExchangeLinkInfo, String.class);
    }

    // data CheckIsPayLink
    public void createDataCheckIsPayLink(int data) {
        SharedPrefs.getInstance().put(dataCheckIsPayLink, data);
    }

    public int getDataCheckIsPayLink() {
        return SharedPrefs.getInstance().get(dataCheckIsPayLink, Integer.class);
    }

    // data CheckIsPayCard
    public void createDataCheckIsPayCard(int data) {
        SharedPrefs.getInstance().put(dataCheckIsPayCard, data);
    }

    public int getDataCheckIsPayCard() {
        return SharedPrefs.getInstance().get(dataCheckIsPayCard, Integer.class);
    }

    // data CheckBinInstallment
    public void createDataCheckBinInstallment(int data) {
        SharedPrefs.getInstance().put(dataCheckBinInstallment, data);
    }

    public int getDataCheckBinInstallment() {
        return SharedPrefs.getInstance().get(dataCheckBinInstallment, Integer.class);
    }

    // data CheckAffiliate
    public void createDataCheckAffiliate(int data) {
        SharedPrefs.getInstance().put(dataCheckAffiliate, data);
    }

    public int getDataCheckAffiliate() {
        return SharedPrefs.getInstance().get(dataCheckAffiliate, Integer.class);
    }

    // data LinkAffiliateCover
    public void createDataLinkAffiliateCover(String data) {
        SharedPrefs.getInstance().put(dataLinkAffiliateCover, data);
    }

    public String getDataLinkAffiliateCover() {
        return SharedPrefs.getInstance().get(dataLinkAffiliateCover, String.class);
    }

    // data LinkAffiliateEvent
    public void createDataLinkAffiliateEvent(String data) {
        SharedPrefs.getInstance().put(dataLinkAffiliateEvent, data);
    }

    public String getDataLinkAffiliateEvent() {
        return SharedPrefs.getInstance().get(dataLinkAffiliateEvent, String.class);
    }

    // data ConvertVimo
    public void createDataConvertVimo(int data) {
        SharedPrefs.getInstance().put(dataConvertVimo, data);
    }

    public int getDataConvertVimo() {
        return SharedPrefs.getInstance().get(dataConvertVimo, Integer.class);
    }

    // data ConvertVimoAmount
    public void createDataConvertVimoAmount(double data) {
        SharedPrefs.getInstance().put(dataConvertVimoAmount, data);
    }

    public double getDataConvertVimoAmount() {
        return SharedPrefs.getInstance().get(dataConvertVimoAmount, Double.class);
    }

    // data Banner
    public void createDataBanner(String data) {
        SharedPrefs.getInstance().put(dataBanner, data);
    }

    public String getDataBanner() {
        return SharedPrefs.getInstance().get(dataBanner, String.class);
    }

    // cash_back
    public void createHaveCashback(boolean isCashback) {
        SharedPrefs.getInstance().put(haveCashBack, isCashback);
    }

    public boolean getHaveCashback() {
        return SharedPrefs.getInstance().get(haveCashBack, Boolean.class);
    }

    public void createCashBackId(String cashBackIdSave) {
        SharedPrefs.getInstance().put(cashBackId, cashBackIdSave);
    }

    public String getCashBackId() {
        return SharedPrefs.getInstance().get(cashBackId, String.class);
    }

    public void createCashBackName(String cashBackNameSave) {
        SharedPrefs.getInstance().put(cashBackName, cashBackNameSave);
    }

    public String getCashBackName() {
        return SharedPrefs.getInstance().get(cashBackName, String.class);
    }

    public void processSaveAmountPaid(String amountsSave) {
        if (TextUtils.isEmpty(amountsSave)) {
            return;
        }
        amountsSave = amountsSave.replaceAll("[\\D]", "");

        String curr = getAmountsSave();
        if (TextUtils.isEmpty(curr)) {
            curr = amountsSave;
        }
        else {
            curr = amountsSave + Constants.CHAR_SPLIT_PHONE + curr;
        }
        String[] arr = curr.split(Constants.CHAR_SPLIT_PHONE);
        if (arr.length > 3) {
            curr = curr.substring(0, curr.lastIndexOf(Constants.CHAR_SPLIT_PHONE));
        }
        createAmountsSave(curr);
    }

    public void createAmountsSave(String amount) {
        SharedPrefs.getInstance().put(amountsSave, amount);
    }

    public String getAmountsSave() {
        return SharedPrefs.getInstance().get(amountsSave, String.class);
    }

    public void createCacheSerialBank(String cache) {
        SharedPrefs.getInstance().put(cacheSerialBank, cache);
    }

    public String getCacheSerialBank() {
        return SharedPrefs.getInstance().get(cacheSerialBank, String.class);
    }

    /**
     * clear data integration save
     */
    public void clearIntegrateData() {
        SharedPrefs.getInstance().put(integratePreUrl, "");
        SharedPrefs.getInstance().put(integratePostUrl, "");
    }

    public void clearData() {
        boolean userGuild = getShowUserGuide();
        String regId = getRegisterId();
        String logData = getLogData();
        SharedPrefs.getInstance().clear();

        createRegisterId(regId);
        createShowUserGuide(userGuild);
        saveLogData(logData);
        createDataLTSHP("");
    }

    public void saveEnableQuickDrawal(boolean enable) {
        SharedPrefs.getInstance().put(enableQuickWithdrawal, enable);
    }

    public boolean isEnableQuickDrawal() {
        return SharedPrefs.getInstance().get(enableQuickWithdrawal, Boolean.class);
    }


    /* note: MAX AMOUNT */
    public String getAmountMaxQuick() {
        return SharedPrefs.getInstance().get(amountMaxQuick, String.class);
    }

    public void saveAmountMaxQuick(String data) {
        SharedPrefs.getInstance().put(amountMaxQuick, data);
    }

    /* note: MIN AMOUNT */
    public String getAmountMinQuick() {
        return SharedPrefs.getInstance().get(amountMinQuick, String.class);
    }

    public void saveAmountMinQuick(String data) {
        SharedPrefs.getInstance().put(amountMinQuick, data);
    }


    //==============================================================================================
    //
    //                                          mVISA
    //
    //==============================================================================================

    /* note: mvisaMid */
    public String getmvisaMid() {
        return SharedPrefs.getInstance().get(mvisaMid, String.class);
    }

    public void saveMvisaMid(String data) {
        SharedPrefs.getInstance().put(mvisaMid, data);
    }

    /* note: mvisaMidMaster */
    public String getmvisaMidMaster() {
        return SharedPrefs.getInstance().get(mvisaMidMaster, String.class);
    }

    public void saveMvisaMidMaster(String data) {
        SharedPrefs.getInstance().put(mvisaMidMaster, data);
    }

    /* note: merchantId */
    public String getmerchantId() {
        return SharedPrefs.getInstance().get(merchantId, String.class);
    }

    public void saveMerchantId(String data) {
        SharedPrefs.getInstance().put(merchantId, data);
    }

    /* note: emailMerchant */
    public String getemailMerchant() {
        return SharedPrefs.getInstance().get(emailMerchant, String.class);
    }

    public void saveEmailMerchant(String data) {
        SharedPrefs.getInstance().put(emailMerchant, data);
    }

    /* note: terminalId */
    public String getTerminalId() {
        return SharedPrefs.getInstance().get(terminalId, String.class);
    }

    public void saveTerminalId(String data) {
        SharedPrefs.getInstance().put(terminalId, data);
    }

    /* note: isMerchantRegistedQR */
    public boolean isMerchantRegistedQR() {
        return SharedPrefs.getInstance().get(isMerchantUseMVisa, Boolean.class);
    }

    public void setIsMerchantRegistedQR(boolean status) {
        SharedPrefs.getInstance().put(isMerchantUseMVisa, status);
    }

    /* note: merchantNameShortcut */
    public String getMerchantNameShortcut() {
        return SharedPrefs.getInstance().get(merchantNameShortcut, String.class);
    }

    public void saveMerchantNameShortcut(String name) {
        SharedPrefs.getInstance().put(merchantNameShortcut, name);
    }

    /* note: MerchantCategoryCode */
    public String getMerchantCategoryCode() {
        return SharedPrefs.getInstance().get(merchantCategoryCode, String.class);
    }

    public void saveMerchantCategoryCode(String name) {
        SharedPrefs.getInstance().put(merchantCategoryCode, name);
    }

    /* note: merchantInfo */
    public String getMerchantInfo() {
        return SharedPrefs.getInstance().get(merchantInfo, String.class);
    }

    public void saveMerchantInfo(String name) {
        SharedPrefs.getInstance().put(merchantInfo, name);
    }

    /* note: isNormalPayLink */
    public boolean getIsNormalPayLink() {
        return SharedPrefs.getInstance().get(isNormalPayLink, Boolean.class);
    }

    public void setIsNormalPayLink(boolean status) {
        SharedPrefs.getInstance().put(isNormalPayLink, status);
    }

    //==============================================================================================
    //
    //                                          isUseReader
    //
    //==============================================================================================
    public boolean isUseReader() {
        return SharedPrefs.getInstance().get(isUseReader, Boolean.class);
    }

    public void setIsUseReader(boolean status) {
        SharedPrefs.getInstance().put(isUseReader, status);
    }

    public boolean isShowVimoQR() {
        return SharedPrefs.getInstance().get(isShowVimoQR, Boolean.class);
    }

    public void setIsShowVimoQR(boolean status) {
        SharedPrefs.getInstance().put(isShowVimoQR, status);
    }

    /* note: connectType */
    public String getConnectType() {
        return SharedPrefs.getInstance().get(connectType, String.class);
    }

    public void saveConnectType(String data) {
        SharedPrefs.getInstance().put(connectType, data);
    }

    /* note: emailReceiptFromPartner */
    public String getOrderIdFromPartner() {
        return SharedPrefs.getInstance().get(orderIdFromPartner, String.class);
    }

    public void saveOrderIdFromPartner(String data) {
        SharedPrefs.getInstance().put(orderIdFromPartner, data);
    }

    /* note: urlCallbackPartner */
    public String getUrlCallbackPartner() {
        return SharedPrefs.getInstance().get(urlCallbackPartner, String.class);
    }

    public void saveUrlCallbackPartner(String data) {
        SharedPrefs.getInstance().put(urlCallbackPartner, data);
    }

    /* note: urlCallbackPartner */
    public String getExtParamPartner() {
        return SharedPrefs.getInstance().get(extParamPartner, String.class);
    }

    public void saveExtParamPartner(String data) {
        SharedPrefs.getInstance().put(extParamPartner, data);
    }

    /* note: quickWithdrawList */
    public String getQuickWithdrawList() {
        return SharedPrefs.getInstance().get(quickWithdrawList, String.class);
    }

    public void saveQuickWithdrawList(String data) {
        SharedPrefs.getInstance().put(quickWithdrawList, data);
    }
    //==============================================================================================


    /* note: categoryGCM */
    public String getCategoryGCM() {
        return SharedPrefs.getInstance().get(categoryGCM, String.class);
    }

    public void saveCategoryGCM(String data) {
        SharedPrefs.getInstance().put(categoryGCM, data);
    }

    /* note: checkFeeChange allow change fee */
    public boolean getAllowChangeFee() {
        return SharedPrefs.getInstance().get(checkFeeChange, Boolean.class);
    }

    public void saveAllowChangeFee(String data) {
        SharedPrefs.getInstance().put(checkFeeChange, Constants.SVALUE_1.equals(data));
    }

    /* note: checkFeeInstallment enable fee installment */
    public boolean getEnalbeFeeInstallment() {
        return SharedPrefs.getInstance().get(checkFeeInstallment, Boolean.class);
    }

    public void saveFeeInstallment(String data) {
        SharedPrefs.getInstance().put(checkFeeInstallment, Constants.SVALUE_1.equals(data));
    }

    /* note: checkFeeTrans enalbe fee trans*/
    public boolean getEnalbeFeeTrans() {
        return SharedPrefs.getInstance().get(checkFeeTrans, Boolean.class);
    }

    public void saveFeeTrans(String data) {
        SharedPrefs.getInstance().put(checkFeeTrans, Constants.SVALUE_1.equals(data));
    }

    public boolean isReceiptFromServer() {
        return SharedPrefs.getInstance().get(receiptFromServer, Boolean.class);
    }

    public void saveIsReceiptFromServer(String data) {
        SharedPrefs.getInstance().put(receiptFromServer, Constants.SVALUE_1.equals(data));
    }

    /* note: mandatory check limit amount*/
    public boolean isMandatoryCheckLimit() {
        return SharedPrefs.getInstance().get(mandatoryCheckLimit, Boolean.class);
    }

    public void saveMandatoryCheckLimit(String value) {
        SharedPrefs.getInstance().put(mandatoryCheckLimit, Constants.SVALUE_1.equals(value));
    }

    /* note: phoneInTime */
    public String getPhoneInTime() {
        return SharedPrefs.getInstance().get(phoneInTime, String.class, "");
    }

    public void savePhoneInTime(String data) {
        SharedPrefs.getInstance().put(phoneInTime, data);
    }

    /* note: phoneOutTime */
    public String getPhoneOutTime() {
        return SharedPrefs.getInstance().get(phoneOutTime, String.class, "");
    }

    public void savePhoneOutTime(String data) {
        SharedPrefs.getInstance().put(phoneOutTime, data);
    }

    /* note: feedback */
    public boolean isCanFeedback() {
        return SharedPrefs.getInstance().get(canFeedback, Boolean.class, true);
    }

    public void saveCanFeedback(boolean data) {
        SharedPrefs.getInstance().put(canFeedback, data);
    }


    /* note: feedbackAmountMin */
    public String getFeedbackAmountMin() {
        return SharedPrefs.getInstance().get(feedbackAmountMin, String.class);
    }

    public void saveFeedbackAmountMin(String data) {
        SharedPrefs.getInstance().put(feedbackAmountMin, data);
    }

    /* note: mobileUserPhone */
    public String getMobileUserPhone() {
        return SharedPrefs.getInstance().get(mobileUserPhone, String.class);
    }

    public void saveMobileUserPhone(String data) {
        SharedPrefs.getInstance().put(mobileUserPhone, data);
    }

    /* note: EnableNormalPayment */
    public boolean isEnableNormalPayment() {
        return SharedPrefs.getInstance().get(enableNormalPayment, Boolean.class);
    }

    public void saveEnableNormalPayment(boolean data) {
        SharedPrefs.getInstance().put(enableNormalPayment, data);
    }

    // data last time show home popup
    public void createDataLTSHP(String data) {
        SharedPrefs.getInstance().put(dataLTSHP, data);
    }

    public String getDataLTSHP() {
        return SharedPrefs.getInstance().get(dataLTSHP, String.class);
    }

    public void setIsAutoPrintReceipt(boolean data) {
        SharedPrefs.getInstance().put(isAutoPrintReceipt, data);
    }

    public boolean getIsAutoPrintReceipt() {
        return SharedPrefs.getInstance().get(isAutoPrintReceipt, Boolean.class, true);
    }

    public boolean isAutoPrint(@NonNull Context context) {
        boolean isAutoPrint;
        // mini not auto print
        if (DevicesUtil.isSP02() && !DevicesUtil.isSP02P8()) {
            isAutoPrint = PreferenceManager.getDefaultSharedPreferences(context).getBoolean(DataStoreApp.isAutoPrintReceipt, false);
        } else if (DevicesUtil.isPax()) {
            isAutoPrint = getIsAutoPrintReceipt();
        } else  {
            isAutoPrint = PreferenceManager.getDefaultSharedPreferences(context).getBoolean(DataStoreApp.isAutoPrintReceipt, true);
        }
//        logUtil.appendLogAction("auto print="+isAutoPrint);
        return isAutoPrint;
    }

//    public boolean isMerchantEMart() {
//        String connectType = DataStoreApp.getInstance().getConnectType();
//        if (!TextUtils.isEmpty(connectType) && (Integer.parseInt(connectType) == 4)) {
//            return true;
//        }
//
//        return false;
//    }

    public void saveDateEmart(String date) {
        SharedPrefs.getInstance().put(dateCacheEmart, date);
    }

    public String getDateCacheEmart() {
        return SharedPrefs.getInstance().get(dateCacheEmart, String.class);
    }

    public void saveDataEmart(String data) {
        SharedPrefs.getInstance().put(dataEmart, data);
    }

    public String getDataEmart() {
        return SharedPrefs.getInstance().get(dataEmart, String.class);
    }

    public String getPermitVoidSocket() {
        return SharedPrefs.getInstance().get(permitVoidSocket, String.class);
    }

    public void savePermitVoidSocket(String data) {
        SharedPrefs.getInstance().put(permitVoidSocket, data);
    }


    public String getPermitQrNl() {
        return SharedPrefs.getInstance().get(permitQrNl, String.class);
    }

    public void savePermitQrNl(String data) {
        SharedPrefs.getInstance().put(permitQrNl, data);
    }

    public String getQrNlSiteCode() {
        return SharedPrefs.getInstance().get(qrNlSiteCode, String.class);
    }

    public void setQrNlSiteCode(String data) {
        SharedPrefs.getInstance().put(qrNlSiteCode, data);
    }

    public String getQrNlPassCode() {
        return SharedPrefs.getInstance().get(qrNlPassCode, String.class);
    }

    public void setQrNlPassCode(String data) {
        SharedPrefs.getInstance().put(qrNlPassCode, data);
    }



    public boolean getIsAutoGotoCashierActivity() {
        return SharedPrefs.getInstance().get(isAutoGotoCashierActivity, Boolean.class);
    }

    public void saveAutoGotoCashierActivity(boolean data) {
        SharedPrefs.getInstance().put(isAutoGotoCashierActivity, data);
    }

    public boolean getIsDissableButton() {
        return SharedPrefs.getInstance().get(isDisableButtonSP01, Boolean.class);
    }

    public void setIsDisableButton(boolean data) {
        SharedPrefs.getInstance().put(isDisableButtonSP01, data);
    }

    public void setIsDisableStatusBar(boolean data) {
        SharedPrefs.getInstance().put(isDisableStatusBar, data);
    }

    public boolean getIsDisableStatusBar() {
        return SharedPrefs.getInstance().get(isDisableStatusBar, Boolean.class);
    }

    public void setIsDisableNavbar(boolean data) {
        SharedPrefs.getInstance().put(isDisableNavbar, data);
    }

    public boolean getIsDisableNavbar() {
        return SharedPrefs.getInstance().get(isDisableNavbar, Boolean.class);
    }

    public void setPrintMoreReceipt(int index) {
        SharedPrefs.getInstance().put(printMoreReceipt, index);
    }

    public int getPrintMoreReceipt() {
        return SharedPrefs.getInstance().get(printMoreReceipt, Integer.class);
    }

    public void setIsOnlyShowUIWaitOrderTCP(boolean data) {
        SharedPrefs.getInstance().put(isOnlyShowUIWaitOrderTCP, data);
    }

    public boolean getIsOnlyShowUIWaitOrderTCP() {
        return SharedPrefs.getInstance().get(isOnlyShowUIWaitOrderTCP, Boolean.class, false);
    }
}
