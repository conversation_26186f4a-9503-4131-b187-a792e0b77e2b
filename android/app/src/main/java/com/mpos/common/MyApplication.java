package com.mpos.common;

import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import androidx.multidex.MultiDexApplication;

import com.mpos.models.DataIntegrated;
import com.mpos.sdk.core.control.LibKozenP5;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.LibPax;
import com.mpos.sdk.core.control.MposSdk;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataCache;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.vnt.vntstore.sdk.StoreDownloadManager;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Executor;

import vn.mpos.R;

public class MyApplication extends MultiDexApplication {

    private static final String TAG = "MyApplication";

    private boolean haveEvent = false;

    Map<String, List<String>> dataCardTypes;
    List<String> dataTelcos;

    DataIntegrated dataIntegrated;
    private static MyApplication mSelf;

    private MposSdk mposSdk;
    private SaveLogController saveLogController;
    private LibP20L libP20L;
    private LibPax libPax;
    private LibKozenP5 libSP02;

    private Boolean isRunMA = null;

    private SharedPreferences sp;
    private Executor executor;
    @Override
    public void onCreate() {
        super.onCreate();
        mSelf = this;

        createHandleUncaughtException();

		/*if (LeakCanary.isInAnalyzerProcess(this)) {
			// This process is dedicated to LeakCanary for heap analysis.
			// You should not init your app in this process.
			return;
		}
		refWatcher = LeakCanary.install(this);*/
//        retrofit = new Retrofit.Builder()
//                .baseUrl(BuildConfig.URL_MPOS)
//                .addConverterFactory(GsonConverterFactory.create())
//                .build();
    }


	/*public static RefWatcher getRefWatcher(Context context) {
		MyApplication application = (MyApplication) context.getApplicationContext();
		return application.refWatcher;
	}
	private RefWatcher refWatcher;*/

    public void setHaveEvent(boolean haveEvent) {
        this.haveEvent = haveEvent;
    }

    public boolean isHaveEvent() {
        return haveEvent;
    }

    public void setDataCardTypes(Map<String, List<String>> dataCardTypes) {
        this.dataCardTypes = dataCardTypes;
    }

    public Map<String, List<String>> getDataCardTypes() {
        return dataCardTypes;
    }

    public void setDataTelcos(List<String> dataTelcos) {
        this.dataTelcos = dataTelcos;
    }

    public List<String> getDataTelcos() {
        return dataTelcos;
    }

    public void setDataIntegrated(DataIntegrated dataIntegrated) {
        this.dataIntegrated = dataIntegrated;
    }

    public DataIntegrated getDataIntegrated() {
        return dataIntegrated;
    }

    private Thread.UncaughtExceptionHandler androidDefaultUEH;
    private void createHandleUncaughtException() {

        androidDefaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(handler);
    }

    private final Thread.UncaughtExceptionHandler handler = new Thread.UncaughtExceptionHandler() {
        public void uncaughtException(Thread thread, Throwable ex) {
//            Log.e("TestApplication", "Uncaught exception is: ", ex);
            // log it & phone home.
            handleUncaughtException(ex);
            androidDefaultUEH.uncaughtException(thread, ex);
        }
    };

    public void handleUncaughtException(Throwable ex) {
        // not all Android versions will print the stack trace automatically
        Utils.LOGD("MyApplication", "handleUncaughtException");
        String errorString = getStackTraceString(ex);
        Utils.LOGD("MyApplication", "handleUncaughtException:" + errorString);

        SaveLogController logUtils = SaveLogController.getInstance(getApplicationContext());
        logUtils.saveLog(Utils.convertTimestamp(System.currentTimeMillis(), 3) + SaveLogController.LOG_CRASH + errorString);

        logUtils.pushLog();
        Utils.LOGD(TAG, "handleUncaughtException: log->" + DataStoreApp.getInstance().getLogData());

    }

    public String getStackTraceString(Throwable e) {
        return getStackTraceString(e, "");
    }

    public String getStackTraceString(Throwable e, String indent) {
        StringBuilder sb = new StringBuilder();
        String log = Log.getStackTraceString(e);
        if (!TextUtils.isEmpty(log)) {
            return log;
        }
        sb.append(e.toString());
        sb.append("\n");

        StackTraceElement[] stack = e.getStackTrace();
        if (stack != null) {
            String tempStack;
            for (StackTraceElement stackTraceElement : stack) {
                sb.append(indent);
                sb.append("\tat ");
                tempStack = stackTraceElement.toString();
                if (tempStack.contains("Unknown Source")) {
                    sb.append("<clas>").append(stackTraceElement.getClassName())
                            .append(" <func>").append(stackTraceElement.getMethodName())
                            .append(" <line>").append(stackTraceElement.getLineNumber())
                            .append("\n");
                }
                sb.append(tempStack);
                sb.append("\n");
            }
        }

        Throwable[] suppressedExceptions = e.getSuppressed();
        // Print suppressed exceptions indented one level deeper.
        if (suppressedExceptions.length > 0) {
            for (Throwable throwable : suppressedExceptions) {
                sb.append(indent);
                sb.append("\tSuppressed: ");
                sb.append(getStackTraceString(throwable, indent + "\t"));
            }
        }

        Throwable cause = e.getCause();
        if (cause != null) {
            sb.append(indent);
            sb.append("Caused by: ");
            sb.append(getStackTraceString(cause, indent));
        }

        return sb.toString();
    }

    public static MyApplication self() {
        return mSelf;
    }

    public void initMposSdk() {
        mposSdk = new MposSdk(PrefLibTV.getInstance(getApplicationContext()).getUserId(), PrefLibTV.getInstance(getApplicationContext()).getPW(),
                ConstantsPay.getReaderTypeByDevice(PrefLibTV.getInstance(this).getFlagDevices()));

        MposCustom mposCustom = new MposCustom();
        mposCustom.setAutoConnectPr02(true);
        mposCustom.setCacheDeviceConnected(true);
        mposCustom.setShowToolbar(true);
        mposCustom.setContactEmail(Config.DEFAULT_EMAIL_SUPPORT);
        mposCustom.setHotline(getString(R.string.SUPPORT_HOTLINE_PHONE_NUMBER));
        String language = Locale.getDefault().getLanguage();
        mposCustom.setLanguage(language.equals(LanguageCode.LANGUAGE_VI.getLanguageCode()) ? LanguageCode.LANGUAGE_VI : LanguageCode.LANGUAGE_EN);
        mposCustom.setColorStatusBar("#"+Integer.toHexString(getResources().getColor(R.color.red)));
        mposSdk.setMposCustom(mposCustom);

        if (DevicesUtil.isP20L() || DevicesUtil.isPax()) {
            mposSdk.setVersionLite(true);
        }
    }

    public synchronized MposSdk getMposSdk() {
        if (mposSdk == null) {
            initMposSdk();
        }
        if (PrefLibTV.getInstance(this).getPermitSocket()) {
            mposSdk.setbName(PrefLibTV.getInstance(this).getBankName());
        }
        return mposSdk;
    }

    public void clearMposSkd() {
        mposSdk = null;
    }

    public void setRunMA(boolean runMA) {
        isRunMA = runMA;
    }

    public boolean isRunMA() {
        if (isRunMA == null) {
            isRunMA = ConstantsPay.MPOS_MULTI_ACQUIRER.equals(PrefLibTV.getInstance(this).getBankName());
        }
        return isRunMA;
    }

    public synchronized SaveLogController getSaveLogController() {
        if (saveLogController == null) {
            saveLogController = new SaveLogController(getApplicationContext());
        }
        return saveLogController;
    }

    public synchronized LibP20L getLibP20L() {
        if (libP20L == null) {
            libP20L = new LibP20L(getApplicationContext());
            libP20L.setCallBackSaveLog((typeLog, s) -> getSaveLogController().appendLog(typeLog, s));
        }
        return libP20L;
    }
    public synchronized LibPax getLibPax() {
        if (libPax == null) {
            libPax = new LibPax(getApplicationContext());
            libPax.setCallBackSaveLog((typeLog, s) -> getSaveLogController().appendLog(typeLog, s));
        }

        return libPax;
    }

    public synchronized LibKozenP5 getLibSP02() {
        if (libSP02 == null) {
            libSP02 = new LibKozenP5(getApplicationContext());
            libSP02.setCallBackSaveLog((typeLog, s) -> getSaveLogController().appendLog(typeLog, s));
        }
        return libSP02;
    }

    public void clearCacheLoginInSDK() {
        DataCache.getInstance().clearCache();
//        PrefLibTV.getInstance(this).put(PrefLibTV.DATA_MERCHANT_CONFIG_CACHE_SDK, "");
//        PrefLibTV.getInstance(this).setMcConfigCache("");

    }
}
