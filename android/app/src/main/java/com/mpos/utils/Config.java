package com.mpos.utils;

public class Config extends BuildConfig{

	public static final String URL_CONFIG = "https://mpos.vn/public/mpos/config.txt";
	public static final String ip_v2_mpos = "https://mpos.vn";

	public static final String URL_CHECK_STATUS_MVISA = URL_MPOS+"/push";
	public static final String URL_GATEWAY 		= URL_MPOS+"/gateway";
	public static final String URL_GATEWAY_API	= URL_MPOS+"/api";
	public static final String URL_INTERGRATED	= URL_MPOS+"/integrated/mpos";
	public static final String URL_GUIDE_INSTALLMENT	= "https://mpos.vn/public/installment-guide/%1$s.html";
	public static final String URL_ICON			= "https://mpos.vn/assets/qr/";

	public static final String URL_CONFIG_FEE   = URL_MPOS+"/assets/config_fee.txt";

	public static final String URL_MANUAL 	= "https://chuyendoiso.nextpay.vn/mpos-guide/";
	public static final String URL_TERM		= "http://www.mpos.vn/public/term_privacy_vi.html";
	public static final String URL_REGISTER	= "https://www.mpos.vn/registration/account?ref=home";
	public static final String URL_LANDING_INSTALLMENT	= "https://www.mpos.vn/tra-gop-khong-phan-tram";

    public static final String URL_PTI_OTO  = "https://mpos-m.epti.vn/dang-ky-bao-hiem-trach-nhiem-dan-su-chu-xe-oto.htm";
    public static final String URL_PTI_XEMAY= "https://mpos-m.epti.vn/dang-ky-bao-hiem-trach-nhiem-dan-su-chu-xe-may.htm";
    public static final String URL_PTI_OTO_SUCCESS      = "https://mpos-m.epti.vn/dang-ky-bao-hiem-trach-nhiem-dan-su-chu-xe-oto-o";
    public static final String URL_PTI_XEMAY_SUCCESS    = "https://mpos-m.epti.vn/dang-ky-bao-hiem-trach-nhiem-dan-su-chu-xe-may-o";

	public static final String URL_SERVER_QR_NL            	= "https://sandbox2.nganluong.vn/vietcombank-checkout/vcb/api/web/checkout/version_1_0";  // dev
//	public static final String URL_SERVER_QR_NL         	= "https://vietcombank.nganluong.vn/api/web/checkout/version_1_0/";  // live

	public static final String CONTENT_TYPE = "application/json; charset=utf-8";
	public static final String PLATFORM     = "ANDROID";

    public static final String LOGIN 					= "LOGIN_LEVEL_1";

    public static final String VIETINBANK 				= "VIETINBANK";
    public static final String SACOMBANK 				= "SACOMBANK";

	public static final String LOGIN_LEVEL_2 			= "LOGIN_LEVEL_2";
//	public static final String LOGOUT 					= "LOGOUT";
	public static final String GET_HISTORY 				= "SALES_HISTORY";
	public static final String SEND_RECEIPT 			= "SEND_RECEIPT";
	public static final String CONFIRM_VOID 			= "CONFIRM_VOID";
	public static final String CREDIT_SETTLEMENT 		= "CREDIT_SETTLEMENT";
	public static final String SALES_HISTORY_DETAIL 	= "SALES_HISTORY_DETAIL";
//	public static final String CONFIRM_PAYMENT 			= "CONFIRM_PAYMENT";
//	public static final String GET_EZPK 				= "GET_EZPK";
	public static final String CONFIRM_ACTIVATION 		= "CONFIRM_ACTIVATION";
//	public static final String TC_ADVICE 				= "TC_ADVICE";
	public static final String UPDATE_CA_PUBLIC_KEY 	= "UPDATE_CA_PUBLIC_KEY";
	public static final String UPGRADE_EMV_CONFIG_SUCCESS 	= "UPGRADE_EMV_CONFIG_SUCCESS";
	public static final String MARK_QUICK_WITHDRAWAL 	= "MARK_QUICK_WITHDRAW";
	public static final String FEEDBACK_TRANSACTION 	= "FEEDBACK_TRANSACTION";

	public static final String MPOS_API 				= "MPOS_API";
	public static final String DOWNLOAD_RECEIPT_IMAGE 	= "DOWNLOAD_RECEIPT_IMAGE";
	public static final String CHANGE_PASSWORD 			= "CHANGE_PASSWORD";
	public static final String TRANSACTION_PUSH_REFUND 			= "TRANSACTION_PUSH_REFUND";

    // service name of mpos
//    public static final String GATEWAY_MERCHANT_LOGIN               = "GATEWAY_MERCHANT_LOGIN";
    public static final String GATEWAY_MERCHANT_LOGIN_SDK           = "GATEWAY_MERCHANT_LOGIN_SDK";
    public static final String GATEWAY_MERCHANT_LOGOUT              = "GATEWAY_MERCHANT_LOGOUT";
    public static final String PAY_SERVICE_TRANSACTION              = "PAY_SERVICE_TRANSACTION";
    public static final String CHECK_TRANSACTION_STATUS             = "CHECK_TRANSACTION_STATUS";
    public static final String MERCHANT_INTEGRATED_CHECK_PREPAY     = "MERCHANT_INTEGRATED_CHECK_PREPAY";
    public static final String MERCHANT_INTEGRATED_CHECK_POSTPAY    = "MERCHANT_INTEGRATED_CHECK_POSTPAY";
    public static final String UPDATE_TRANSACTION_STATUS            = "UPDATE_TRANSACTION_STATUS";
    public static final String API_REVERSAL_TRANSACTION             = "API_REVERSAL_TRANSACTION";
    public static final String GET_TOKEN_TRANSACTION                = "GET_TOKEN_TRANSACTION";
    public static final String GET_ALL_CARD_TYPE                    = "GET_ALL_CARD_TYPE";
    public static final String PREPARE_TRANSACTION                  = "PREPARE_TRANSACTION";
    public static final String VETC_QUERY_ACCOUNT_INFO              = "VETC_QUERY_ACCOUNT_INFO";
    public static final String GET_LIMID_BY_MUID                    = "GET_LIMID_BY_MUID";
    public static final String CHECK_ACTIVE_VAS_WALLET              = "CHECK_ACTIVE_VAS_WALLET";
    public static final String UPDATE_WARRANTY_CODE_TRANSACTION_PROMOTION= "UPDATE_WARRANTY_CODE_TRANSACTION_PROMOTION";
//    public static final String TRANSACTION_LIST                     = "TRANSACTION_LIST";
    public static final String TRANSACTION_LIST_V2                     = "TRANSACTION_LIST_V2";

	public static final int CODE_REQUEST_TIMEOUT = 2002;

	public static final String CODE_REQUEST_SUCCESS = "1000";
	public static final String CODE_STATUS_SUCCESS 	= "SUCCESS";

	public static final String PRE_FIX_ORDER_CODE_TW   = "-OCTW-";


//    public static final String DEFAULT_PHONE_IN_WORKING             = "1900.63.64.88";
//    public static final String DEFAULT_PHONE_OUT_WORKING            = "1900.63.64.88";

	public static final String DEFAULT_PHONE_IN_WORKING             = "0339 506 286";
    public static final String DEFAULT_PHONE_OUT_WORKING            = "0339 506 286";
//    public static final String DEFAULT_PHONE_OUT_WORKING            = "0901.75.79.98";

    public static final String DEFAULT_EMAIL_SUPPORT            = "<EMAIL>";

	// Google Project Number
	public static final String GOOGLE_PROJ_ID = "954610956906";	// produce

	// <MERCHANT_INTEGRATED>-<OrderId>-Thanh_toan_hoa_don/_Pay_order|<UDIDPayment>
	public static final String UDID_INTEGRATED_FORMAT = "%s%s-Thanh_toan_hoa_don/_Pay_order|%s";

	public static final String DESC_INSTALLMENT_FORMAT = "%s Installment: %s Months/%s Tra gop: %s Thang-";
}
