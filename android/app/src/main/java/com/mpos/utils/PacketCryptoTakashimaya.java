package com.mpos.utils;

import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.mpos.screen.mart.EMartPresenter.ORDER_CODE_SUCCESS;

public class PacketCryptoTakashimaya {

    private static final String TAG = PacketCryptoTakashimaya.class.getSimpleName();

    /**
     * Giải mã cả gói <STX><MSGLEN><DATA><ETX><LRC> và trả về DATA gốc.
     *
     * @param packet  Mảng byte đầy đủ gói tin do buildEncryptedMessage tạo ra.
     * @param keyStr  Khóa 24 ký tự ASCII.
     * @return        Chuỗi DATA gốc (ASCII), đã trim zeros padding.
     */

    public static String PROC_CODE_SALE = "000000";
    public static String PROC_CODE_VOID = "020000";

    public static String buildAmount(String amountStr) {
        if (!amountStr.matches("\\d+")) {
            throw new IllegalArgumentException("Invalid amount: must be digits only");
        }
        return String.format("%012d", Long.parseLong(amountStr));
    }

    public static String buildDataSuccessQR(String amount, String orderId, String orderCode) {
        Map<String, String> data = new LinkedHashMap<>();
        data.put("APP", "MPOS");
        data.put("RESPONSE_CODE", ORDER_CODE_SUCCESS);
        data.put("BILL_ID", orderId);
        data.put("AMOUNT", buildAmount(amount));
        data.put("INVOICE", orderCode);
        data.put("SEND", "OK");
        return PacketCryptoTakashimaya.buildDataString(data);    }

    public static String buildDataSuccess(ResultPayWrapper resultPay, String maskPan) {
        String orderId = "";
        if ((resultPay.getUserCard().applicationLabel != null) && (resultPay.getUserCard().applicationLabel.equals(Constants.TYPE_PAY_QR_VNPAY) || resultPay.getUserCard().applicationLabel.equals(Constants.TYPE_PAY_QR_MOMO))) {
            orderId = resultPay.getResult().paymentIdentifier.split(ConstantsPay.PREFIX_UDID_QR_NL)[1].split("\\|")[0];
        } else {
            try {
                orderId = resultPay.getResult().paymentIdentifier.split("-")[1].split("-")[0];
                Utils.LOGD(TAG, "orderId:: " + orderId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String date = convertTimestamp(Long.parseLong(resultPay.getUserCard().getTransactionDate()), "MMdd");
        String time = convertTimestamp(Long.parseLong(resultPay.getUserCard().getTransactionDate()), "HHmmss");

        Map<String, String> data = new LinkedHashMap<>();
        data.put("APP", "MPOS");
        data.put("PROC_CODE ", PROC_CODE_SALE);
        data.put("DATE", date);
        data.put("TIME", time);
        data.put("REF_NO", resultPay.getWfInfo().getRrn());
        data.put("APPV_CODE ", resultPay.getWfInfo().getAuthCode());
        data.put("RESPONSE_CODE", ORDER_CODE_SUCCESS);
        data.put("TERMINAL_ID", resultPay.getWfInfo().getTid());
        data.put("MERCHANT_CODE", resultPay.getWfInfo().getMid());
        data.put("CARD_TYPE", resultPay.getUserCard().getApplicationLabel());
        data.put("PAN", maskPan);
        data.put("NAME", resultPay.getUserCard().getCardHolderName());
        data.put("INVOICE", resultPay.getWfInfo().getTxid()); // trả về txid để họ ko phải sửa code. -> cho nghiệp vụ void
        data.put("BILL_ID", orderId);
        data.put("AMOUNT", buildAmount(String.valueOf(resultPay.getUserCard().getAmountAuthorized())));
        data.put("SEND", "OK");
        return PacketCryptoTakashimaya.buildDataString(data);
    }

    public static String buildDataVoidSuccess(String invoice, WfDetailRes detailRes) {
        String date = convertTimestamp(Long.parseLong(detailRes.getCreatedTimestamp()), "MMDD");
        String time = convertTimestamp(Long.parseLong(detailRes.getCreatedTimestamp()), "HHmmss");

        Map<String, String> data = new LinkedHashMap<>();
        data.put("APP", "MPOS");
        data.put("PROC_CODE ", PROC_CODE_VOID);
        data.put("STAN ", "");
        data.put("DATE", date);
        data.put("TIME", time);
        data.put("REF_NO", detailRes.getRrn());
        data.put("APPV_CODE ", detailRes.getAuthCode());
        data.put("RESPONSE_CODE", ORDER_CODE_SUCCESS);
        data.put("TERMINAL_ID", detailRes.getTid());
        data.put("MERCHANT_CODE", detailRes.getMid());
        data.put("NAME", detailRes.getCardHolderName());
        data.put("PAN", "");
        data.put("INVOICE", invoice);
        data.put("AMOUNT", buildAmount(detailRes.getAmount()));
        data.put("SEND", "OK");
        return PacketCryptoTakashimaya.buildDataString(data);
    }

    public static String buildDataFail(String errCode) {
        Map<String, String> data = new LinkedHashMap<>();
        data.put("APP", "MPOS");
        data.put("RESPONSE_CODE", errCode);
        data.put("SEND", "OK");
        return PacketCryptoTakashimaya.buildDataString(data);
    }

    public static String buildDataString(Map<String, String> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) return "";

        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            builder.append(entry.getKey())
                    .append(":")
                    .append(entry.getValue())
                    .append(";");
        }
        return builder.toString();
    }

    public static byte[] buildEncryptedMessage(String msg, String keyStr) throws Exception {
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv = new byte[8]; // 8 bytes zero IV
        byte[] plainData = msg.getBytes(StandardCharsets.US_ASCII);

        // Encrypt DATA
        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        // Padding manually to make multiple of 8
        int padLen = 8 - (plainData.length % 8);
        byte[] paddedPlainData = Arrays.copyOf(plainData, plainData.length + padLen);

        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encryptedData = cipher.doFinal(paddedPlainData);

        // Message length
        int len = encryptedData.length;

        // Construct packet
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(0x02);                       // STX
        baos.write((len >> 8) & 0xFF);         // MSGLEN High byte
        baos.write(len & 0xFF);                // MSGLEN Low byte
        baos.write(encryptedData);             // Encrypted DATA
        baos.write(0x03);                      // ETX

        byte[] withoutLRC = baos.toByteArray();
        byte lrc = 0x00;
        for (int i = 1; i < withoutLRC.length; i++) {
            lrc ^= withoutLRC[i];              // LRC = XOR from MSGLEN to ETX
        }

        baos.write(lrc);                       // LRC
        return baos.toByteArray();
    }

    public static String decryptPacket(DataInputStream in, String keyStr) throws Exception {
//        DataInputStream in = new DataInputStream(new ByteArrayInputStream(packet));

        // 1. STX
        byte stx = in.readByte();
        if (stx != 0x02) {
            throw new IllegalArgumentException("Invalid STX: " + stx);
        }

        // 2. MSGLEN (2 bytes)
        int lenHigh = in.readUnsignedByte();
        int lenLow = in.readUnsignedByte();
        int dataLen = (lenHigh << 8) | lenLow;

        // 3. Đọc DATA đã mã hóa
        byte[] encryptedData = new byte[dataLen];
        in.readFully(encryptedData);

        // 4. ETX
        byte etx = in.readByte();
        if (etx != 0x03) {
            throw new IllegalArgumentException("Invalid ETX: " + etx);
        }

        // 5. LRC
        byte lrc = in.readByte();
        // Tính LRC
        byte calcLrc = 0x00;
        calcLrc ^= (byte) lenHigh;
        calcLrc ^= (byte) lenLow;
        for (byte b : encryptedData) {
            calcLrc ^= b;
        }
        calcLrc ^= etx;
        if (lrc != calcLrc) {
            throw new IllegalArgumentException(
                    String.format("Invalid LRC: expected 0x%02X but got 0x%02X", calcLrc, lrc)
            );
        }

        // 6. Khởi tạo Cipher DESede/CBC/NoPadding
        byte[] key = keyStr.getBytes(StandardCharsets.US_ASCII);
        byte[] iv  = new byte[8];

        Cipher cipher = Cipher.getInstance("DESede/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(key, "DESede");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

        // 7. Giải mã và loại bỏ zero-padding
        byte[] decryptedPadded = cipher.doFinal(encryptedData);
        // Tìm vị trí cuối cùng không phải zero
        int trimIndex = decryptedPadded.length;
        while (trimIndex > 0 && decryptedPadded[trimIndex - 1] == 0x00) {
            trimIndex--;
        }
        byte[] decrypted = Arrays.copyOf(decryptedPadded, trimIndex);

        return new String(decrypted, StandardCharsets.US_ASCII);
    }

    public static String convertTimestamp(long timeStamp, String format) {
        Calendar mydate = Calendar.getInstance();
        mydate.setTimeInMillis(timeStamp);

        try {
            return (new SimpleDateFormat(format)).format(mydate.getTime());
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
