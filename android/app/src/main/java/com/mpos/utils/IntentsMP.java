package com.mpos.utils;

/**
 * extra of intent in current mpos
 * Created by AnhNT on 7/14/16.
 */
public class IntentsMP {

    public static final String EXTRA_IS_GO_SWIPE_CARD       = "mp.it.pay_card_now";
    public static final String EXTRA_IS_PAY_INSTALLMENT      = "mp.it.pay_installment";
    public static final String EXTRA_IS_PAY_CASHBACK         = "mp.it.pay_cash_back";
    public static final String EXTRA_INSTALLMENT_BANK_OBJ    = "mp.it.installment_bank_obj";
    public static final String EXTRA_INSTALLMENT_OUT_ID      = "mp.it.installment_out_id";
//    public static final String EXTRA_INSTALLMENT_MIN_AMOUNT  = "mp.it.installment_min_amount";
//    public static final String EXTRA_INSTALLMENT_NAME_BANK   = "mp.it.installment_name_bank";
    public static final String EXTRA_INSTALLMENT_NUM_MONTH   = "mp.it.installment_num_month";
    public static final String EXTRA_HISTORY_CASHIER        = "mp.it.history_cashier";

    public static final String EXTRA_PARTNER_IS_INPUT_CODE   = "mp.it.partner_input_order_id";
    public static final String EXTRA_PARTNER_UDID            = "mp.it.partner_udid";

    public static final String EXTRA_AMOUNT_DISCOUNT         = "mp.it.amount_discount";
    public static final String EXTRA_AMOUNT_ORIGIN           = "mp.it.amount_origin";

    public static final String EXTRA_INSTALLMENT_PERIOD_OBJ  = "mp.it.pay_installment_period";
    public static final String EXTRA_INSTALLMENT_PROMOTION   = "mp.it.pay_installment_promotion";
    public static final String EXTRA_VETC_ACCOUNT_INFO       = "mp.it.vetc_acc_info";
    public static final String EXTRA_VETC_AMOUNT             = "mp.it.vetc_amount";

    public static final String EXTRA_PAYMENT_INFO            = "mp.it.payment_info";
    public static final String EXTRA_TRANS_INFO              = "mp.it.trans_info";
    public static final String EXTRA_TYPE_HISTORY            = "mp.it.type_history";
    public static final String EXTRA_TYPE_HISTORY_EMART      = "mp.it.type_history_emart";
    public static final String EXTRA_TYPE_HISTORY_QR_NL      = "mp.it.type_history_qr_nl";

    public static final String EXTRA_IS_DOMESTIC_IN_MVISA    = "mp.it.isDomesticInMVisa";


}
