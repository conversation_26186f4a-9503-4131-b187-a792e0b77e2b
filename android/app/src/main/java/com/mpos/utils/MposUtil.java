package com.mpos.utils;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.text.TextUtils;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.mpos.common.DataStoreApp;
import com.mpos.common.MyApplication;
import com.mpos.models.DataFromPartner;
import com.mpos.sdk.core.control.EncodeDecode;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;

import org.json.JSONObject;

import vn.mpos.R;

import static android.app.Activity.RESULT_CANCELED;

/**
 * Created by anhnguyen on 6/11/18.
 */

public class MposUtil {

    String tag = "MposUtil";

    final String NAME_BRC_INSERT_CARD = "vn.mpos.BRC_INSERT_CARD";

    private static MposUtil mposUtil;

    public static synchronized MposUtil getInstance() {
        if (mposUtil == null) {
            mposUtil = new MposUtil();
        }
        return mposUtil;
    }

    private MposUtil(){

    }

    public void callbackFailToPartner(Context context, DataError dataError, DataFromPartner dataFromPartner) throws Exception{
        callbackFailToPartner(context, dataError, dataFromPartner, null);
    }
    public void callbackFailToPartner(Context context, DataError dataError, DataFromPartner dataFromPartner, SaveLogController saveLogController) throws Exception{
        String urlCallback = dataFromPartner.getUrlCallBack();
        if (TextUtils.isEmpty(urlCallback)) {
//            showDialogErrorCallbackPartner(getString(R.string.txt_no_urlcallback_copy_tid_to_clipboard_pay_false));
            throw new Exception(context.getString(R.string.txt_no_urlcallback_copy_tid_to_clipboard_pay_false));
        }
        else {
            try {
                //dataFromPartner.getContent()
                JSONObject jRoot = new JSONObject();
                JSONObject jError = new JSONObject();
                jError.put("code", dataError.getErrorCode());
                jError.put("message", dataError.getMsg());

                jRoot.put("transDate", System.currentTimeMillis());
                jRoot.put("transAmount", dataFromPartner.getAmount());
                jRoot.put("orderId", dataFromPartner.getOrderId());
                jRoot.put("muid", PrefLibTV.getInstance(context).getUserId());
                jRoot.put("transDesc", dataFromPartner.getDescription());

                jRoot.put("error", jError);

                if (!TextUtils.isEmpty(dataFromPartner.getExtParam())) {
                    jRoot.put("extParam", dataFromPartner.getExtParam());
                }

                Utils.LOGD(tag, "callbackToPartner: "+ jRoot);

                if (saveLogController != null) {
                    saveLogController.appendLogAction("callback Fail data =" + jRoot);
                    saveLogController.saveLog();
                    saveLogController.pushLog();
                }

                StringBuilder secrectKey = new StringBuilder(DataStoreApp.getInstance().getIntergratSecrectKey());
                // if secrect_key = "" -> default: 16 character F
                for (int i = secrectKey.length(); i<16; i++) {
                    secrectKey.append("F");
                }
                Utils.LOGD(tag, "key: "+secrectKey);
                String content = EncodeDecode.doAESEncrypt(jRoot.toString(), secrectKey.toString());
//                String content = EncodeDecode.doAESEncrypt(jRoot.toString(), DataStoreApp.getInstance().getIntergratSecrectKey());
                Utils.LOGD(tag, "callbackToPartner: contentEncryptAES=" + content);

                content = Uri.encode(content);
                Utils.LOGD(tag, "callbackToPartner: content encode=" + content);

                context.startActivity(new Intent(Intent.ACTION_VIEW,Uri.parse(urlCallback+content)));
                ((Activity) context).setResult(RESULT_CANCELED);
                ((Activity) context).finish();
            } catch (Exception e) {
                Utils.LOGE(tag, "callBackToPartner: ", e);
                throw new Exception(String.format(context.getString(R.string.txt_copy_tid_to_clipboard),urlCallback));
//                showDialogErrorCallbackPartner(String.format(getString(R.string.txt_copy_tid_to_clipboard),urlCallback));
            }
        }
    }


    public String createUdidForIntegration(String orderId, String udid) {
        return String.format(Config.UDID_INTEGRATED_FORMAT, ConstantsPay.PREFIX_UDID_MERCHANT_INTEGRATED, orderId, udid);
    }

    /**
     * convert typeReader to floagDevice
     * @param typeReader : AR, PR01, PR02
     * @return
     */
    public int convertTypeReaderToFlagDevice(String typeReader) {
        switch (typeReader) {
            case "AR":
                return ConstantsPay.DEVICE_AUDIO;
            case "PR01":
                return ConstantsPay.DEVICE_PINPAD;
            case "PR02":
                return ConstantsPay.DEVICE_DSPREAD;
            case "SP01":
                return ConstantsPay.DEVICE_P20L;
            default:
                return 0;
        }
    }

    public static String getShortBankName(String bankName) {
        switch (bankName) {
            case ConstantsPay.VIETINBANK:
                return "VTB";
            case ConstantsPay.SACOMBANK:
                return "STB";
            case ConstantsPay.BIDV:
                return "BIDV";
            case ConstantsPay.VIETCOMBANK:
                return "VCB";
            case ConstantsPay.MPOS_MULTI_ACQUIRER:
                return "MA";
            default:
                return "NA";
        }
    }

    public void registerBrcInInsertCard(Context context, BroadcastReceiver broadcastReceiver) {
        Utils.LOGD(tag, "registerBrcInInsertCard: ------->>>");
//        context.registerReceiver(broadcastReceiver, new IntentFilter(NAME_BRC_INSERT_CARD), "vn.mpos.permission_brc",null);
        LocalBroadcastManager.getInstance(context).registerReceiver(broadcastReceiver, new IntentFilter(NAME_BRC_INSERT_CARD));
    }

    public void releaseBrcInInsertCard(Context context, BroadcastReceiver broadcastReceiver) {
        Utils.LOGD(tag, "releaseBrcInInsertCard: ------->>>");
//        context.unregisterReceiver(broadcastReceiver);
        LocalBroadcastManager.getInstance(context).unregisterReceiver(broadcastReceiver);
    }

    public void sendMessageFinishToScreenInsertCard(Context context) {
        Utils.LOGD(tag, "sendMessageFinishToScreenInsertCard: ----->>>");
        LocalBroadcastManager.getInstance(context).sendBroadcast(new Intent(NAME_BRC_INSERT_CARD));
//        context.sendBroadcast(new Intent(NAME_BRC_INSERT_CARD), "vn.mpos.permission_brc");
    }

    public boolean checkNeedLoginLevel2() {
        long lastTimeLoginLevel2 = DataStoreApp.getInstance().getDataByKey(DataStoreApp.lastTimeLoginLevel2, Long.class, 0L);
        Utils.LOGD(tag, "checkNeedLoginLevel2: lastTimeLoginLevel2=" + lastTimeLoginLevel2);
        // vtb:         About to create a new login token that expires in 3600 seconds
        // stb, bidv:   About to create a new login token that expires in 1800 seconds
        // < 25min => not need login level2
        //1500000 = 25min
        return System.currentTimeMillis() - lastTimeLoginLevel2 > 1500000;
    }

    public void handlerActionDisplayNavbar(Context context, boolean display) {
        if (DevicesUtil.isPax()) {
            MyApplication.self().getLibPax().handlerDisplayNavigationBar(display);
        } else if (DevicesUtil.isSP02()) {
            Utils.LOGD(tag, "switchDisableNavbar = " + display);
            Intent intent = new Intent();
            intent.setAction(Constants.ACTION_SET_NVBAR);
            intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
            intent.putExtra("navbar_display", display);
            context.sendBroadcast(intent);
        }
    }

    public void handlerActionDisableStatusBar(Context context, boolean b) {
        Utils.LOGD(tag, "switchDisableStatusBar = " + b);
        if (DevicesUtil.isPax()) {
            MyApplication.self().getLibPax().handlerDisplayStatusBar(!b);
        }else if (DevicesUtil.isSP02P8()) {
            int isDisable = 0;
            if (!b) {
                isDisable = 1;
            }
            try {
                Intent intent = new Intent(Constants.ACTION_SET_STATUSBAR_PULL_DOWN);
                intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
                intent.putExtra(Constants.EXTRA_INTENT_STATUS_BAR_STATE_KEY, isDisable);
                intent.putExtra("status_bar", isDisable == 1);  //true or false, default true, new version
                context.sendBroadcast(intent);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (DevicesUtil.isSP02()){
            MyApplication.self().getLibSP02().disableStatusBarP5(b);
        }
    }
}
