package com.mpos.utils;

public class Constants {

	public static final String ACTION_SET_STATUSBAR_PULL_DOWN   = "android.intent.action.CUSTOM_ACTION_SET_STATUSBAR_PULL_DOWN";
	public static final String ACTION_SET_NVBAR   				= "android.intent.action.CUSTOM_ACTION_SET_NVBAR";

	public static final String EXTRA_INTENT_STATUS_BAR_STATE_KEY = "status_bar_pull_down";//Intent extra, put 1 enable pull down, put 0 disable pull down

	// path fonts custom
	public static final String FONTS_PATH = "";
//	public static final String FONTS_PATH = "fonts/SourceSansPro.ttf";
//	public static final String FONTS_PATH = "fonts/SourceSansPro-Light.ttf";

	public static final String FROM_F_ORDER_CARD_TW     = "FragmentOrderCardTW";
	public static final String FROM_A_UTILITY_SERVICE   = "ActivtyUtilityService";

    public static final String STATUS_TRANS_APPROVED    = "APPROVED";
    public static final String STATUS_TRANS_VOIDED      = "VOIDED";
    public static final String STATUS_TRANS_PENDING     = "PENDING";

    // status: cashier reward
    public static final String STATUS_CR_PENDING        = "PENDING";
    public static final String STATUS_CR_DENIED         = "DENIED";
    public static final String STATUS_CR_PROCESSING     = "PROCESSING";
    public static final String STATUS_CR_SUCCESS        = "SUCCESS";

	public static final String FIELD_CASHBACK_INFO      = "cashbackInfo";
	public static final String FIELD_INSTALLMENT        = "listInstallment";

	public static final String STR_SERVICE_NAME        = "serviceName";

	public static final String TXT_VETC         = "vetc";

	public static final String VISA_LOCAL       = "VISA_LOCAL";
	public static final String MASTER_LOCAL     = "MASTER_LOCAL";
	public static final String JCB_LOCAL              = "JCB_LOCAL";
	public static final String CUP_LOCAL              = "CUP_LOCAL";

	public static final String SVALUE_2              = "2";
	public static final String SVALUE_1              = "1";
	public static final String SVALUE_0              = "0";

	public static final String SVALUE_TRUE           = "true";
	public static final String SVALUE_FALSE          = "false";

	public static final String VAYMUONQR              = "VAYMUONQR";
	public static final String LINKCARD              = "LINKCARD";

	public static final String NORMAL              = "NORMAL";
	public static final String INSTALLMENT         = "INSTALLMENT";

	/**
	 * installment
	 */
	public static final String SCREEN_ACTIVITY_INSTALLMENT = "ActivityInstallment";
	public static final String ID_CARD_NUM_DISPLAY   = "DISPLAY";
	public static final String ID_CARD_NUM_REQUIRED  = "REQUIRED";
	public static final String ID_CARD_NUM_NONE		 = "NONE";

	//----------------------Nhanlx------------------------------
	public static final String KEY_EXTRA_MVISA = "ActivityScanQRCode";
	public static final String KEY_MVISA_AMOUNT = "key_mvisa_amount";
	public static final String KEY_MVISA_NOTE = "key_mvisa_note";
	public static final String KEY_MVISA_EMAIL_CUSTOMER = "key_mvisa_email_customer";

	public static final String KEY_MVISA_QRID   = "key_mvisa_qrid";
	public static final String KEY_MVISA_QRCODE = "key_mvisa_qrCode";
	public static final String KEY_MVISA_TYPE   = "key_mvisa_type";
	public static final String KEY_MVISA_UDID   = "key_mvisa_udid";
	public static final String EXTRA_INTENT   = "EXTRA_INTENT";
	public static final String EXTRA_SERVICE_CODE   = "EXTRA_SERVICE_CODE";
	public static final String BROADCAST   = "BROADCAST";

//----------------------------------------------------------------
	public static final String CHAR_SPLIT_PHONE        		= ",";
	public static final String CHAR_SPLIT_DESC_UDID 		= "|";
	public static final String CHAR_REPLACE_SPACE_OF_UDID	= "_";

	public static final String TYPE_PHONE	= "phone";
	public static final String TYPE_EMAIL	= "email";

//    MVISA,
//    VIMOQR
//    WECHAT,
//    ALIPAY,
//    ZALOPAY,
	public static final String TYPE_QR_MVISA	= "MVISA";
	public static final String TYPE_QR_VIMO 	= "VIMOQR";
	public static final String TYPE_QR_VCB 		= "VCBQR";

	public static final int NUM_MAX_PHONE_SAVE        = 30;
	public static final int NUM_MAX_PHONE_SHOW        = 5;


	public static final int RESULT_NOT_HANDLER        	= 2;
	public static final int RESULT_FAILURE_PAY			= 3;

	/**
	 * error general
	 */
	public static final int ERROR_CODE_10001         = 10001;
	public static final String CARD_PAYMENT = "CARD_PAYMENT";
	public static final String QR_PAYMENT = "QR_PAYMENT";
	public static final String CARD_ENTER_PAYMENT = "CARD_ENTER_PAYMENT";
	public static final String NAPAS_LOCAL = "NAPAS_LOCAL";
	public static final String SAMSUNG_PAY_LOCAL = "SAMSUNG_PAY_LOCAL";

	/**
	 * guide installment of bank
	 */
	public static String USER_AGENT = "Mozilla/5.0 (Linux; U; Android 4.1.2; en-us; IM-A850L Build/JZO54K) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30 (Mobile; afma-sdk-a-v6.4.1)";
	public static final int TYPE_GUIDE_INSTALLMENT = 8;
	public static final int TYPE_ERROR_INSTALLMENT = 9;

	public static final int TRANS_TYPE_SUCCESS			= 100;
	public static final int TRANS_TYPE_REVERSAL			= 101;
	public static final int TRANS_TYPE_VOID				= 102;
	public static final int TRANS_TYPE_PENDING_SIGNATURE= 103;
	public static final int TRANS_TYPE_SETTLE			= 104;
	public static final int TRANS_TYPE_PENDING_TC		= 105;
	public static final int TRANS_TYPE_REFUND			= 99;
	public static final int TRANS_TYPE_PROCESSING		= 98;
	public static final int TRANS_TYPE_FAILED			= 97;

	public static final int TRANS_TYPE_MVISA			= 106;


	public static final int ERROR_CODE_FAIL_LOAD_DATA   = -1;

    public static final String TRANS_STATUS_REVERSAL            = "REVERSAL";
    public static final String TRANS_STATUS_VOIDED              = "VOIDED";
    public static final String TRANS_STATUS_APPROVED            = "APPROVED";
    public static final String TRANS_STATUS_SETTLE              = "SETTLE";
    public static final String TRANS_STATUS_SETTLED             = "SETTLED";
    public static final String TRANS_STATUS_PENDING_TC          = "PENDING_TC";
    public static final String TRANS_STATUS_PENDING_SIGNATURE   = "PENDING_SIGNATURE";


    // GIFT CARD EMART
	public static final String SUCCESS            				= "SUCCESS";
	public static final String UNKNOWN_ERROR              		= "UNKNOWN_ERROR";
	public static final String INVALID_MERCHANT_SITE_CODE       = "INVALID_MERCHANT_SITE_CODE";
	public static final String INVALID_CARD_NUMBER            	= "INVALID_CARD_NUMBER";
	public static final String INVALID_AMOUNT            		= "INVALID_AMOUNT";
	public static final String INSUFFICIENT_BALANCE            	= "INSUFFICIENT_BALANCE";
	public static final String INVALID_ORDER_CODE            	= "INVALID_ORDER_CODE";
	public static final String INVALID_CHECKSUM            		= "INVALID_CHECKSUM";

	// QRCODE emart
	public static final String AMOUNT_QR            				= "AMOUNT_QR";
	public static final String ORDER_CODE            				= "ORDER_CODE";

	public static final String TYPE_QR_NL            				= "TYPE_QR_NL";
	public static final String TYPE_PAY_QR_VNPAY    				= "QR_VNPAY";
	public static final String TYPE_PAY_QR_MOMO     				= "QR_MOMO";
	public static final String TYPE_PAY_QR_ZALOPAY_NL     			= "QR_ZALO";

	public static final String TYPE_SETTING            				= "TYPE_SETTING";
	public static final String CONFIG_BASE							= "CONFIG_BASE";
	public static final String UPDATE_APP							= "UPDATE_APP";

	public static final String SUCCESS_QR						= "0000";


	/**
	 * mvisa notification
	 */

	public static final String IntentFilterNotificationMVISA = "IAM_MVISA_NOTICATION";

	public static String QR_NAME="QR_NAME";

	public static String LINK_WEBVIEW="LINK_WEBVIEW";
	public static String TITLE_WEBVIEW="TITLE_WEBVIEW";
	public static String TITLE_WEBVIEW_OVERLAY="TITLE_WEBVIEW_OVERLAY";


    /**
     * receipt
     */
//    public static final String WIDTH_IMAGE_RECEIPT	= "760";
    public static final String WIDTH_IMAGE_RECEIPT	= "380";


    public static final String BROADCAST_MPOS   = "receiver_mpos";
    public static final String BROADCAST_SDK    = "receiver_sdk";

    public static final String HOME_SERVICE_CODE_CASHIER_PAY    = "CASHIER-PAYMENT";

	// function get banks qr ngan luong
//	public static final String BANK_CODE_QR_VIETQR              = "VIETQR";
	public static final String BANK_CODE_QR_NL                	= "VCB";
	public static final String BANK_CODE_QR_ZALO_NL             = "ZALO";
	public static final String BANK_CODE_QR_MOMO_NL             = "MOMO";

	public static final String FUNCTION_BANKS_QR                = "GetBanksPos";
	public static final String FUNCTION_CREATE_ORDER_QR         = "CreateOrder";
	public static final String METHOD_CODE_QR                   = "QRCODE";
	public static final String FUNCTION_CHECK_ORDER_QR          = "CheckOrder";
	public static final String FUNCTION_GET_LIST_ORDER_QR       = "GetListOrder";
	public static final String URL_CALLBACK_SUCCESSS_QR         = "https://mpos.vn/";
	public static final String URL_CALLBACK_ERR_QR         		= "https://wiki.nextpay.vn/vi/home";
	public static final String URL_CALLBACK_ERR_NOTIFY         	= "https://nextpay.vn/";


    public static final String HOME_SERVICE_CODE_MACQ_MOTO      = "MOTO_MACQ_PAYMENT";
    public static final String TRX_TYPE_PAY_DEPOSIT      		= "4";

}
