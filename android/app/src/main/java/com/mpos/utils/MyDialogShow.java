package com.mpos.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View.OnClickListener;

import com.mpos.customview.MposDialog;
import com.mpos.screen.login.LoginActivity;
import com.mpos.sdk.core.model.PrefLibTV;
import com.pps.core.ScreenUtils;

import vn.mpos.R;

public class MyDialogShow {

    private static MposDialog mposDialog;
    
	public static void showDialogError(String msg, Context context) {
		showDialogError(null, msg, context);
	}
	public static void showDialogError(String msg, Context context, boolean isFinish) {
		showDialogError(null, msg, context, isFinish);
	}

	public static void showDialogError(String title, String desc, Context context) {
		showDialogError(title, desc, context, false);
	}

	public static void showDialogErrorFinish(String msg, Context context) {
		showDialogError(null, msg, context, true);
	}

	public static void showDialogError(String title, String desc, final Context context, final boolean isFinish) {
		showDialogError(title, desc, context, isFinish, null);
	}

	public static void showDialogError(String title, String desc, final Context context, final boolean isFinish, final OnClickListener onClickListener) {
        showDialogError(title, desc, context, isFinish, null,  null, null, onClickListener);
    }
	public static void showDialogError(String title, String desc, final Context context, final boolean isFinish,
                                       String nameBtnOk, String nameBtnClose,
                                       final OnClickListener onClickOk, final OnClickListener onClickClose) {
        if(!ScreenUtils.canShowDialog(context)){
			return;
		}

		title 	= TextUtils.isEmpty(title) ? context.getString(R.string.dialog_error_title_default)    : title;
		desc	= TextUtils.isEmpty(desc)  ? context.getString(R.string.error_default) : desc;

        mposDialog = new MposDialog(context);
        mposDialog.setTitle(title);
        mposDialog.setType(MposDialog.TYPE_DIALOG_ERROR);
        mposDialog.setDesDialogErrorTop(desc);

        if (onClickOk != null && onClickClose != null) {
            mposDialog.setEnableTwoButtonBottom(true);
            if (!TextUtils.isEmpty(nameBtnOk)) {
                mposDialog.setLabelForButtonOk(nameBtnOk);
            }
            if (!TextUtils.isEmpty(nameBtnClose)) {
                mposDialog.setLabelForButtonCancel(nameBtnClose);
            }
            mposDialog.setOnClickListenerButtonOk(v -> {
                onClickOk.onClick(v);
                mposDialog.dismiss();
            });

            mposDialog.setOnClickListenerButtonCancel(v -> {
                onClickClose.onClick(v);


                if (isFinish && !PrefLibTV.getInstance(context).getPermitSocket()) {
                    ((Activity) context).finish();
                }
                mposDialog.dismiss();
            });
        }
        else {
            mposDialog.setEnableTwoButtonBottom(false);
            mposDialog.setOnClickListenerDialogClose(v -> {
                if (onClickClose != null) {
                    onClickClose.onClick(v);
                }

                if (isFinish && !PrefLibTV.getInstance(context).getPermitSocket()) {
                    ((Activity) context).finish();
                }

                mposDialog.dismiss();
            });
        }

        mposDialog.show();

	}
//
//
	public static void showDialogErrorReLogin(String desc, final Context context){
        showDialogError(null, desc, context, true, v -> gotoReLogin(context));
	}

	public static void gotoReLogin(Context context){
		Intent intent = new Intent(context, LoginActivity.class);
		intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
		context.startActivity(intent);
	}

	public static void showDialogWarning(Context context, String desc, boolean isFinish) {
	    showDialogWarning(context, desc, null, isFinish);
    }
	public static void showDialogWarning(Context context, String desc, OnClickListener onClickClose, boolean isFinish) {
        if (!ScreenUtils.canShowDialog(context)) {
            return;
        }

        mposDialog = new MposDialog(context);
        mposDialog.setType(MposDialog.TYPE_DIALOG_WARNING);
        mposDialog.setHandlerButtonOK(true);
        mposDialog.setDesDialogErrorTop(desc);

        mposDialog.setEnableTwoButtonBottom(false);
        mposDialog.setVisibleViaPhoneEmail(true);


        mposDialog.setOnClickListenerDialogClose(v -> {
            if (onClickClose != null) {
                onClickClose.onClick(v);
            }

            if (isFinish && !PrefLibTV.getInstance(context).getPermitSocket()) {
                ((Activity) context).finish();
            }
            mposDialog.dismiss();
        });
        mposDialog.show();
    }
	public static void showDialogRetry(String desc, Context c, OnClickListener onClickRetry) {
		showDialogRetryCancel(null, desc, c, onClickRetry, false);
	}
	public static void showDialogRetry(String title, String desc, Context c, OnClickListener onClickRetry) {
		showDialogRetryCancel(title, desc, c, onClickRetry, false);
	}
	public static void showDialogRetryCancel(String desc, Context c, final OnClickListener onClickRetry, boolean isShowCancel) {
		showDialogCancelAndClick(null, desc, c.getString(R.string.ALERT_BTN_RETRY), c, onClickRetry, isShowCancel, false);
	}
	public static void showDialogRetryCancel(String title, String desc, Context c, final OnClickListener onClickRetry, boolean isShowCancel) {
		showDialogCancelAndClick(title, desc, c.getString(R.string.ALERT_BTN_RETRY), c, onClickRetry, isShowCancel, false);
	}
    public static void showDialogRetryCancelFinish(String title, String desc, Context c, final OnClickListener onClickRetry, boolean isShowCancel) {
		showDialogCancelAndClick(title, desc, c.getString(R.string.ALERT_BTN_RETRY), c, onClickRetry, isShowCancel, true);
	}

    public static void showDialogContinueCancel(String desc, Context c, boolean isShowCancel, final OnClickListener onClickRetry) {
        showDialogCancelAndClick(null, desc, c.getString(R.string.LOGIN_BTN_CONTINUE), c, onClickRetry, isShowCancel, false);
    }

    public static void showDialogCancelAndClick(String title, String desc, String nameBtn, final Context c, final OnClickListener onClickRetry, boolean isShowCancel, final boolean isFinish) {
        showDialogCancelAndClick(title, desc, nameBtn, c, onClickRetry, null, isShowCancel, isFinish);
    }

	public static void showDialogRetryCancel(String title, String desc, Context c, OnClickListener onClickRetry, OnClickListener onClickCancel, boolean isShowCancel) {
		showDialogCancelAndClick(title, desc, c.getString(R.string.ALERT_BTN_RETRY), c, onClickRetry, onClickCancel, isShowCancel, false);
	}

	public static void showDialogCancelAndClick(String title, String desc, String nameBtn, final Context context, final OnClickListener onClickRetry, final OnClickListener onClickCancel, boolean isShowCancel, final boolean isFinish) {
        showDialogCancelAndClick(title, desc, nameBtn, context, onClickRetry, onClickCancel, isShowCancel, isFinish, false);
    }
	public static void showDialogCancelAndClick(String title, String desc, String nameBtn, final Context context, final OnClickListener onClickRetry, final OnClickListener onClickCancel, boolean isShowCancel, final boolean isFinish, boolean showSupport) {
        if (!ScreenUtils.canShowDialog(context)) {
            return;
        }

        mposDialog = new MposDialog(context);
		mposDialog.setType(MposDialog.TYPE_DIALOG_WARNING);
        mposDialog.setHandlerButtonOK(false);
        mposDialog.setDesDialogErrorTop(desc);

        if (TextUtils.isEmpty(nameBtn)) {
            nameBtn = context.getString(R.string.dialog_error_close);
        }
        mposDialog.setEnableTwoButtonBottom(true);
        mposDialog.setLabelForButtonOk(nameBtn);
        mposDialog.setEnableButtonCancel(isShowCancel);
        mposDialog.setVisibleViaPhoneEmail(showSupport);

        mposDialog.setOnClickListenerButtonOk(v -> {
            if (onClickRetry != null) {
                onClickRetry.onClick(v);
            }
            mposDialog.dismiss();
        });

        mposDialog.setOnClickListenerButtonCancel(v -> {
            if (onClickCancel != null) {
                onClickCancel.onClick(v);
            }

            if (isFinish && !PrefLibTV.getInstance(context).getPermitSocket()) {
                ((Activity) context).finish();
            }
            mposDialog.dismiss();
        });
		mposDialog.show();

	}

    /**
     * Dialog SUCCESS
     */
    public static void showDialogSuccess(final Context context, String desc, final boolean isFinish) {
        showDialogSuccess(context, desc, isFinish, null);
    }
    public static void showDialogSuccess(final Context context, String desc, final boolean isFinish, final OnClickListener onClickListener) {
        showDialogByType(MposDialog.TYPE_DIALOG_SUCCESS, context, desc, isFinish, onClickListener);
    }

    /**
     * Dialog INFO
     */
    public static void showDialogInfo(final Context context, String desc) {
        showDialogInfo(context, desc, false, null);
    }
    public static void showDialogInfo(final Context context, String desc, final boolean isFinish) {
        showDialogInfo(context, desc, isFinish, null);
    }
    public static void showDialogInfo(final Context context, String desc, final boolean isFinish, final OnClickListener onClickListener) {
        showDialogByType(MposDialog.TYPE_DIALOG_INFO, context, desc, isFinish, onClickListener);
    }

    /**
     * show dialog by type
     */
    public static void showDialogByType(int typeDialog, final Context context, String desc, final boolean isFinish, final OnClickListener onClickListener) {
        if (!ScreenUtils.canShowDialog(context)) {
            return;
        }

        desc	= TextUtils.isEmpty(desc)  ? "" : desc;

        mposDialog = new MposDialog(context);
        mposDialog.setType(typeDialog);
        mposDialog.setDesDialogErrorTop(desc);

        mposDialog.setEnableTwoButtonBottom(false);

        mposDialog.setOnClickListenerDialogClose(v -> {
            if (onClickListener != null) {
                onClickListener.onClick(v);
            }

            if (isFinish && !PrefLibTV.getInstance(context).getPermitSocket()) {
                ((Activity) context).finish();
            }

            mposDialog.dismiss();
        });
        mposDialog.show();
    }

    public static void dismissCurrDialog() {
        try {
            if (mposDialog != null && mposDialog.isShowing()) {
                mposDialog.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
/*
	public static void showDialogComingSoon(Context context) {
		final Dialog dialog = new Dialog(context, R.style.SpecialDialog);

//		LayoutInflater inflater = (LayoutInflater) context.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
//		View dialogLayout = inflater.inflate(R.layout.dialog_comming_soon, null);

        View dialogLayout = LayoutInflater.from(context).inflate(R.layout.dialog_comming_soon, null);

		ViewToolBar vToolBarDialog = new ViewToolBar(context, dialogLayout);
		vToolBarDialog.showTextTitle(context.getString(R.string.txt_service_pay));
		vToolBarDialog.showButtonBack(true, v -> dialog.dismiss());

		dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
		dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
		dialog.setCanceledOnTouchOutside(false);
		dialog.setContentView(dialogLayout);
		dialog.getWindow().setLayout(LayoutParams.FILL_PARENT, LayoutParams.FILL_PARENT);
		dialog.show();
	}*/
}
