package com.mpos.utils;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.util.Base64;
import android.widget.EditText;
import android.widget.TextView;

public class MyTextUtils {

    public static boolean isEmpty(String str) {
        return str == null || str.equals("");
    }

    private final char[] charA = {'à', 'á', 'ạ', 'ả', 'ã',// 0-&gt;16
            'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ'};// a,//
    // ă,//
    // â
    private final char[] charE = {'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',// 17-&gt;27
            'è', 'é', 'ẹ', 'ẻ', 'ẽ'};// e
    private final char[] charI = {'ì', 'í', 'ị', 'ỉ', 'ĩ'};// i 28-&gt;32
    private final char[] charO = {'ò', 'ó', 'ọ', 'ỏ', 'õ',// o 33-&gt;49
            'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ',// ô
            'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ'};// ơ
    private final char[] charU = {'ù', 'ú', 'ụ', 'ủ', 'ũ',// u 50-&gt;60
            'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ'};// ư
    private final char[] charY = {'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ'};// y 61-&gt;65
    private final char[] charD = {'đ', ' '}; // 66-67

    private final char[][] CH = {charA, charE, charI, charO, charU, charY, charD};

    private final String charact;

    public MyTextUtils() {
        // charact=String.valueOf(charA, 0, charA.length);
        charact = String.valueOf(charA, 0, charA.length) + String.valueOf(charE, 0, charE.length)
                + String.valueOf(charI, 0, charI.length) + String.valueOf(charO, 0, charO.length)
                + String.valueOf(charU, 0, charU.length) + String.valueOf(charY, 0, charY.length)
                + String.valueOf(charD, 0, charD.length);
        // LogUtils.d(charact + "/" + String.valueOf(charact.length()));
    }

    public String convertString(String pStr) {
        String convertString = pStr.toLowerCase();
        // Character[] returnString = new Character[convertString.length()];
        for (int i = 0, length = convertString.length(); i < length; i++) {
            char temp = convertString.charAt(i);
            if ((int) temp < 97 || temp > 122) {
                char tam1 = this.GetAlterChar(temp);
                if ((int) temp != 32)
                    convertString = convertString.replace(temp, tam1);
            }
        }
        return convertString;
    }

    private char GetAlterChar(char pC) {
        if ((int) pC == 32) {
            return ' ';
        }

        int i = 0;
        while (i < charact.length() && charact.charAt(i) != pC) {
            i++;
        }
        if (i < 0 || i > 67)
            return pC;

        if (i == 66) {
            return 'd';
        }
        if (i >= 0 && i <= 16) {
            return 'a';
        }
        if (i >= 17 && i <= 27) {
            return 'e';
        }
        if (i >= 28 && i <= 32) {
            return 'i';
        }
        if (i >= 33 && i <= 49) {
            return 'o';
        }
        if (i >= 50 && i <= 60) {
            return 'u';
        }
        if (i >= 61 && i <= 65) {
            return 'y';
        }
        return pC;
    }

    public void setAllCapsForEdt(EditText edt) {
        InputFilter[] editFilters = edt.getFilters();
        InputFilter[] newFilters = new InputFilter[editFilters.length + 1];
        System.arraycopy(editFilters, 0, newFilters, 0, editFilters.length);
        newFilters[editFilters.length] = new InputFilter.AllCaps();
        edt.setFilters(newFilters);
    }

    public static void makeLinks(TextView textView, String[] links, ClickableSpan[] clickableSpans) {
        SpannableString spannableString = new SpannableString(textView.getText());
        for (int i = 0; i < links.length; i++) {
            ClickableSpan clickableSpan = clickableSpans[i];
            String link = links[i];

            int startIndexOfLink = textView.getText().toString().indexOf(link);
            spannableString.setSpan(clickableSpan, startIndexOfLink, startIndexOfLink + link.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        textView.setMovementMethod(LinkMovementMethod.getInstance());
        textView.setText(spannableString, TextView.BufferType.SPANNABLE);
    }


    public Bitmap convertTextBase64ToBitmap(String content) {
        if (TextUtils.isEmpty(content)) {
            return null;
        }
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inScaled = false;

        byte[] decodedString = Base64.decode(content, Base64.DEFAULT);

//        Bitmap bitmap = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length, options);
//        Log.d(TAG, "convertTextBase64ToBitmap: w=" + bitmap.getWidth() + " h=" + bitmap.getHeight());

        return BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
//        return BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length, options);
    }

    public Bitmap addPaddingLeftForBitmap(Bitmap bitmap, int paddingLeft) {
        if (bitmap == null) {
            return null;
        }
        Bitmap outputBitmap = Bitmap.createBitmap(bitmap.getWidth() + paddingLeft, bitmap.getHeight(), Bitmap.Config.RGB_565);
        Canvas canvas = new Canvas(outputBitmap);
        canvas.drawColor(Color.WHITE);
        canvas.drawBitmap(bitmap, paddingLeft, 0, null);
        return outputBitmap;
    }

    public static void pushTextToClipboard(String text, ClipboardManager clipboard) {
        if (clipboard != null) {
            ClipData clip = ClipData.newPlainText("TransactionId", text);
            clipboard.setPrimaryClip(clip);
        }
    }

}
