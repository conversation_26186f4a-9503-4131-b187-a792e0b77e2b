package com.mpos.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.telephony.TelephonyManager;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.mpos.adapters.HistoryEmartAdapter;
import com.mpos.customview.MposDialog;
import com.mpos.customview.MposDialogInstallmentGuide;
import com.mpos.sdk.util.Utils;
import com.pps.core.ScreenUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import vn.mpos.R;

import static android.content.Context.TELEPHONY_SERVICE;


/**
 * Created by AnhNT on 5/6/16.
 */

public class MyUtils {

    public static final String TYPE_APP_EMART     = "TYPE_EMART";
    public static final String TYPE_APP_MPOS_PLUS  = "TYPE_MPOS_MPLUS";
    public static final String TYPE_APP_TAKASHIMAYA  = "TYPE_TAKASHIMAYA";

    public static boolean checkHaveStoragePermission(Context context) {
        return (ActivityCompat.checkSelfPermission(context, android.Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(context, android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED);
    }

    public static void requestStoragePermission(Activity activity, int requestCode) {
        // Request missing location permission.
        ActivityCompat.requestPermissions(activity,
                new String[]{Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE}, requestCode);
    }

    public static boolean checkHaveLocationPermission(Context context) {
        return (ActivityCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(context, android.Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED);
    }

    public static void requestLocationPermission(Activity activity, int requestCode) {
        // Request missing location permission.
        ActivityCompat.requestPermissions(activity,
                new String[]{Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION}, requestCode);
    }

    public static boolean checkHaveAudioPermission(Context context) {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
    }

    public static void requestAudioPermission(Activity activity, int requestCode) {
        // Request missing location permission.
        ActivityCompat.requestPermissions(activity,
                new String[]{Manifest.permission.RECORD_AUDIO}, requestCode);
    }

    public static void setRequestedOrientation(Context context, boolean isLandscape) {
        if (context == null) {
            return;
        }
        if (isLandscape) {
            ((Activity) context).setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        } else {
            ((Activity) context).setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    public void setViewStatusByStatus(Context context, String status, TextView tvStatus) {
        switch (status) {
            case Constants.TRANS_STATUS_APPROVED:
            case Constants.TRANS_STATUS_PENDING_TC:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_green, R.color.blue, context);
                tvStatus.setText(context.getString(R.string.txt_title_success));
                break;
            case Constants.TRANS_STATUS_REVERSAL:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.reversal));
                break;
            case Constants.TRANS_STATUS_VOIDED:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.canceled));
                break;
            case Constants.TRANS_STATUS_SETTLE:
            case Constants.TRANS_STATUS_SETTLED:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_blue, R.color.blue_1, context);
                tvStatus.setText(context.getString(R.string.settled));
                break;
            case Constants.TRANS_STATUS_PENDING_SIGNATURE:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_orange, R.color.orange, context);
                tvStatus.setText(context.getString(R.string.no_sign));
                break;

        }
    }

    public void setViewStatusByStatusGiftCard(Context context, String status, TextView tvStatus) {
        switch (status) {
            case Constants.SUCCESS:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_green, R.color.blue, context);
                tvStatus.setText(context.getString(R.string.txt_title_success));
                break;
            case Constants.INVALID_CARD_NUMBER:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.tv_card_not_exits));
                break;
            case Constants.INVALID_ORDER_CODE:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.tv_invalid_order_code));
                break;
            default:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.failed));
                break;
        }
    }

    public void setViewStatusByStatusQrNl(Context context, String status, TextView tvStatus) {
        switch (status) {
            case "1":
            case "2":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.not_paid));
                break;
            case "3":
            case "8":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_green, R.color.blue, context);
                tvStatus.setTextColor(context.getColor(R.color.blue));
                tvStatus.setText(context.getString(R.string.txt_title_success));
                break;
            case "4":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.canceled));
                break;
            case "5":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.processing)); // todo review
                break;
            case "6":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.refunding));
                break;
            case "7":
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.refunded));
                break;
            default:
//                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.failed));
                break;
        }
    }

    public void setViewStatusByType(Context context, int type, TextView tvStatus, TextView tvAmount, boolean isVaymuon) {
        tvStatus.setTextColor(ContextCompat.getColor(context, R.color.white));
        switch (type) {
            case Constants.TRANS_TYPE_SUCCESS:
            case Constants.TRANS_TYPE_PENDING_TC:
                tvStatus.setVisibility(View.VISIBLE);
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_green, R.color.blue, context);
                tvStatus.setText(context.getString(R.string.txt_title_success));
                if (isVaymuon && type == Constants.TRANS_TYPE_PENDING_TC) {
                    tvStatus.setText(context.getString(R.string.txt_title_wait_for_disbursement));
                    setupBtnStatus(tvStatus, R.drawable.bg_border_round_orange, R.color.orange, context);
                }
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.black));
                break;
//            case Constants.TRANS_TYPE_PENDING_TC:
//			case TYPE_SUCCESS :
//                tvStatus.setVisibility(View.GONE);
//                tvAmount.setTextColor(ContextCompat.getColor(context, R.color.black));
//                break;
            case Constants.TRANS_TYPE_REFUND:
                if (isVaymuon) {
                    tvStatus.setVisibility(View.VISIBLE);
                    setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                    tvStatus.setText(context.getString(R.string.canceled));
                } else {
                    tvStatus.setVisibility(View.VISIBLE);
                    setupBtnStatus(tvStatus, R.drawable.bg_border_round_gray, R.color.gray, context);
                    tvStatus.setText(context.getString(R.string.refunded));
                }
                break;
            case Constants.TRANS_TYPE_VOID:
//			case TYPE_VOID :
                tvStatus.setVisibility(View.VISIBLE);
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setText(context.getString(R.string.canceled));
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.orange));
                break;
            case Constants.TRANS_TYPE_PENDING_SIGNATURE:
//			case TYPE_PENDING_SIGNATURE :
                tvStatus.setVisibility(View.VISIBLE);
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_orange, R.color.orange, context);
                tvStatus.setText(context.getString(R.string.no_sign));
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.black));
                break;
            case Constants.TRANS_TYPE_REVERSAL:
//			case TYPE_REVERSAL :
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setVisibility(View.VISIBLE);
                tvStatus.setText(context.getString(R.string.reversal));
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.orange));
                break;
            case Constants.TRANS_TYPE_SETTLE:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_blue, R.color.blue_1, context);
                tvStatus.setVisibility(View.VISIBLE);
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.black));
                tvStatus.setText(context.getString(R.string.settled));
                break;
            case Constants.TRANS_TYPE_MVISA:
                tvStatus.setVisibility(View.VISIBLE);
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_blue, R.color.blue_1, context);
               // tvAmount.setTextColor(ContextCompat.getColor(context, R.color.black));
                tvStatus.setText(context.getString(R.string.settled));
                break;
            case Constants.TRANS_TYPE_FAILED:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_red, R.color.red_1, context);
                tvStatus.setVisibility(View.VISIBLE);
                tvStatus.setText(context.getString(R.string.failed));
                break;
            case Constants.TRANS_TYPE_PROCESSING:
                setupBtnStatus(tvStatus, R.drawable.bg_border_round_orange, R.color.orange, context);
                tvStatus.setVisibility(View.VISIBLE);
                tvStatus.setText(context.getString(R.string.processing));
                break;
            default:
                break;
        }
    }

    public static String getStatusNameType(Context context, int type, boolean isVaymuon) {
        switch (type) {
            case Constants.TRANS_TYPE_SUCCESS:
            case Constants.TRANS_TYPE_PENDING_TC:
                if (isVaymuon && type == Constants.TRANS_TYPE_PENDING_TC) {
                    return context.getString(R.string.txt_title_wait_for_disbursement);
                } else {
                    return context.getString(R.string.txt_title_success);
                }
            case Constants.TRANS_TYPE_REFUND:
                if (isVaymuon) {
                    return context.getString(R.string.canceled);
                } else {
                    return context.getString(R.string.refunded);
                }
            case Constants.TRANS_TYPE_VOID:
                return context.getString(R.string.canceled);
            case Constants.TRANS_TYPE_PENDING_SIGNATURE:
                return context.getString(R.string.no_sign);
            case Constants.TRANS_TYPE_REVERSAL:
                return context.getString(R.string.reversal);
            case Constants.TRANS_TYPE_SETTLE:
            case Constants.TRANS_TYPE_MVISA:
                return context.getString(R.string.settled);
            case Constants.TRANS_TYPE_FAILED:
                return context.getString(R.string.failed);
            case Constants.TRANS_TYPE_PROCESSING:
                return context.getString(R.string.processing);
            default:
                return "";
        }
    }

    public static Drawable getStatusBackgroundType(Context context, int type, boolean isVaymuon) {
        switch (type) {
            case Constants.TRANS_TYPE_SUCCESS:
            case Constants.TRANS_TYPE_PENDING_TC:
                if (isVaymuon && type == Constants.TRANS_TYPE_PENDING_TC) {
                    return context.getResources().getDrawable(R.drawable.bg_status_transaction_orange);
                } else {
                    return context.getResources().getDrawable(R.drawable.bg_status_transaction_green);
                }
            case Constants.TRANS_TYPE_REFUND:
                if (isVaymuon) {
                    return context.getResources().getDrawable(R.drawable.bg_status_transaction_red);
                } else {
                    return context.getResources().getDrawable(R.drawable.bg_status_transaction_gray);
                }
            case Constants.TRANS_TYPE_VOID:
            case Constants.TRANS_TYPE_REVERSAL:
            case Constants.TRANS_TYPE_FAILED:
                return context.getResources().getDrawable(R.drawable.bg_status_transaction_red);
            case Constants.TRANS_TYPE_PENDING_SIGNATURE:
            case Constants.TRANS_TYPE_PROCESSING:
                return context.getResources().getDrawable(R.drawable.bg_status_transaction_orange);
            case Constants.TRANS_TYPE_SETTLE:
            case Constants.TRANS_TYPE_MVISA:
                return context.getResources().getDrawable(R.drawable.bg_status_transaction_blue);
            default:
                return context.getResources().getDrawable(R.drawable.bg_status_transaction_black);
        }
    }

    public void setImgThumbByTypeCard(Context context, String label, ImageView iv) {
        setImgThumbByTypeCard(context, label, iv, null);
    }

    public static final String URL_ASSET_QR         = "file:///android_asset/mvisa.png";
    public static final String URL_ASSET_VISA       = "file:///android_asset/visa.png";
    public static final String URL_ASSET_MASTER     = "file:///android_asset/master.png";
    public static final String URL_ASSET_JCB        = "file:///android_asset/jcb.png";
    public static final String URL_ASSET_DOMESTIC   = "file:///android_asset/default.png";
    public static final String URL_ASSET_LINK       = "file:///android_asset/link.png";
    public static final String URL_ASSET_ENTER_CARD = "file:///android_asset/enter_card.png";

    public void setImgThumbByTypeCard(Context context, String label, ImageView iv, String accquirer) {
        String url = null;
        if (!TextUtils.isEmpty(accquirer) && accquirer.equalsIgnoreCase("MVISA")) {
            url = URL_ASSET_QR;
        }
        else if (!TextUtils.isEmpty(accquirer) && accquirer.toUpperCase().startsWith("MPQR")) {
            url = URL_ASSET_QR;
        }
        else if (!TextUtils.isEmpty(label)) {
            String labelUpperCase = label.toUpperCase();
            if (labelUpperCase.contains("VISA") || labelUpperCase.contains("PAYWAVE")) {
                url = URL_ASSET_VISA;
            }
            else if (labelUpperCase.contains("MASTERCARD") || labelUpperCase.contains("PAYPASS")) {
                url = URL_ASSET_MASTER;
            }
            else if (labelUpperCase.contains("JCB")) {
                url = URL_ASSET_JCB;
            }
        }
        if (TextUtils.isEmpty(url)) {
            url = URL_ASSET_DOMESTIC;
        }

        Glide.with(context).load(url).into(iv);
    }

    public void setImgQr(Context context, ImageView iv) {
        Glide.with(context).load(URL_ASSET_QR).into(iv);
    }

    public void setImgLink(Context context, ImageView iv) {
        Glide.with(context).load(URL_ASSET_LINK).into(iv);
    }

    public void setImgEnterCard(Context context, ImageView iv) {
        Glide.with(context).load(URL_ASSET_ENTER_CARD).into(iv);
    }

    private void setupBtnStatus(TextView tvStatus, int idResourceDraw, int idResourceColor, Context context) {
        tvStatus.setTextColor(context.getResources().getColor(idResourceColor));
        tvStatus.setBackgroundResource(idResourceDraw);
    }

    public static boolean checkMinAmount(String amount, String minAmount) {
        if (TextUtils.isEmpty(amount) || TextUtils.isEmpty(minAmount)) {
            return false;
        }
        amount = convertMoney(amount);
        return Integer.parseInt(amount) >= Integer.parseInt(minAmount);
    }

    public static boolean checkMinMaxAmount(String amount, String minAmount, String maxAmount) {
        if (TextUtils.isEmpty(amount) || TextUtils.isEmpty(minAmount) || TextUtils.isEmpty(maxAmount)) {
            return false;
        }
        amount = convertMoney(amount);
        return Integer.parseInt(minAmount) <= Integer.parseInt(amount) && Integer.parseInt(amount) <= Integer.parseInt(maxAmount);
    }

    public static String convertMoney(String amountMoney) {
        return amountMoney.replaceAll("[^\\d]", "").trim();
    }


    //-------------------------------- DIALOG GUIDE FOR INSTALLMENT OF BANK ------------------------

    /**
     * @param activity
     * @param bankLongName
     * @param bankName
     * @return
     */
    public static MposDialogInstallmentGuide initDialogInstallmentGuide(final Activity activity, String bankLongName, String bankName) {
        String urlGuideInstallment = String.format(Config.URL_GUIDE_INSTALLMENT, bankName);
//        String urlGuideInstallment = activity.getApplicationContext().getString(R.string.link_guide_installment_of_bank, bankName);
        final MposDialogInstallmentGuide myDialogGuide = new MposDialogInstallmentGuide(activity, urlGuideInstallment);
        myDialogGuide.setTitleDialogGuideInstallment(activity.getApplicationContext().getString(R.string.dialog_installment_guide_title, bankName/*bankLongName*/));

        myDialogGuide.setOnClickListenerDialogViaPhone(v -> {
            // ---- SUPPORT VIA PHONE ----
            callToSupport(myDialogGuide.getNumberHotline(), activity);
        });

        return myDialogGuide;
    }


    public static MposDialog initDialogError_GoToHistory(final Context context, String title, Spanned msg) {
        if (!ScreenUtils.canShowDialog(context)) {
            return null;
        }
        final MposDialog mposDialog = new MposDialog(context);
        mposDialog.setTitle(title);
        mposDialog.setType(MposDialog.TYPE_DIALOG_ERROR);
        if (msg != null) {
            mposDialog.setDesDialogErrorTop(msg);
        }

        mposDialog.setEnableTwoButtonBottom(true);
        mposDialog.setLabelForButtonOk(context.getString(R.string.check_history));
        mposDialog.setLabelForButtonCancel(context.getString(R.string.skip));
        mposDialog.getBtnOk().setBackgroundResource(R.drawable.btn_red_rounded);

        return mposDialog;
    }


    public static MposDialog initDialogGeneralError(final Context context, final int errorCode, final String desDetailErrorOnTop,
                                                    final String nameScene) {
        final MposDialog mposDialog = new MposDialog(context);
        /**
         * title message error
         */
        String title = context.getString(R.string.dialog_error_title_default);
        if (errorCode > 0) {
            title = context.getString(R.string.dialog_error_title, errorCode);
        }
        mposDialog.setTitle(title);
        mposDialog.setType(MposDialog.TYPE_DIALOG_ERROR);

        /**
         * content message error
         */
        if (TextUtils.isEmpty(desDetailErrorOnTop)) {
            mposDialog.setDesDialogErrorTop(context.getString(R.string.check_internet));
        } else {
            mposDialog.setDesDialogErrorTop(desDetailErrorOnTop);
        }
        return mposDialog;
    }

    public static void callToSupport(String numberPhoneNumber, @NonNull Context activity) {
        try {
            if (isTelephonyEnabled(activity)) {
                Uri call = Uri.parse("tel:" + numberPhoneNumber);
                Intent surf = new Intent(Intent.ACTION_DIAL, call);
                activity.startActivity(surf);
            }
            else {
                showToast(activity,"Device not support Telephony");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isTelephonyEnabled(@NonNull Context context){
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(TELEPHONY_SERVICE);
        return telephonyManager != null && telephonyManager.getSimState()== TelephonyManager.SIM_STATE_READY;
    }


    public static void shareViaEmail(Context activity, String contentError) {
        shareViaEmail(activity, null, null, contentError);
    }

    public static void shareViaEmail(Context activity, String email, String titleEmail, String contentError) {
        if (TextUtils.isEmpty(titleEmail)) {
            titleEmail = "Thông báo lỗi từ ứng dụng mPoS";
        }

        Intent emailIntent = new Intent(Intent.ACTION_SEND);
        emailIntent.setType("message/rfc822");
        final PackageManager pm = activity.getApplicationContext().getPackageManager();
        final List<ResolveInfo> matches = pm.queryIntentActivities(emailIntent, 0);
        String className = null;
        for (final ResolveInfo info : matches) {
            if (info.activityInfo.packageName.equals("com.google.android.gm")) {
                className = info.activityInfo.name;

                if (className != null && !className.isEmpty()) {
                    Utils.LOGD("error", "className is " + className);
                    break;
                }
            }
        }
        emailIntent.putExtra(Intent.EXTRA_TEXT, contentError);
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, titleEmail);
        emailIntent.putExtra(Intent.EXTRA_EMAIL, new String[]{TextUtils.isEmpty(email) ? Config.DEFAULT_EMAIL_SUPPORT : email});
//        emailIntent.setClassName("com.google.android.gm", className != null ? className : "");
//        activity.startActivity(emailIntent);
        if (TextUtils.isEmpty(className)) {
            showToast(activity,"Can not open email application");
        }
        else {
            emailIntent.setClassName("com.google.android.gm", className);
            activity.startActivity(emailIntent);
        }
    }

    public static void showToast(@NonNull Context context, @NonNull String msg) {
        Toast.makeText(context, msg, Toast.LENGTH_LONG).show();
    }

    public static Spanned setTextHtml(String content) {
        Spanned notice;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            notice = Html.fromHtml(content, Html.FROM_HTML_MODE_LEGACY);
        } else {
            notice = Html.fromHtml(content);
        }

        return notice;
    }


    public static String formatTimeTransaction(String time) {
        /**
         * date create format is
         * 09:50, 12-10-2017
         */
        long currentTime = Long.parseLong(time);
        String str_1 = convertTimestamp(currentTime, "HH:mm");
        String str_2 = convertTimestamp(currentTime, "dd-MM-yyyy");

        return str_1 + ", " + str_2;
    }


    public static String convertTimestamp(long timeStamp, String format) {
        Calendar mydate = Calendar.getInstance();
        mydate.setTimeInMillis(timeStamp);

        try {
            return new SimpleDateFormat(format).format(mydate.getTime());
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static String getLocalIpAddress() {
        java.util.Scanner s;
        String ip = "";
        try {
            s = new java.util.Scanner(new java.net.URL("https://api.ipify.org").openStream(), "UTF-8").useDelimiter("\\A");
//            System.out.println("My current IP address is " + s.next());
            ip = s.next();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ip;
    }

    public static String getPhoneByTime(String phoneInTime, String phoneOutTime) {
        if (checkInWorkingTime()) {
            return TextUtils.isEmpty(phoneInTime) ? Config.DEFAULT_PHONE_IN_WORKING : phoneInTime;
//            return TextUtils.isEmpty(dataStore.getPhoneInTime())  ?  Config.DEFAULT_PHONE_IN_WORKING : dataStore.getPhoneInTime();
        } else {
            return TextUtils.isEmpty(phoneOutTime) ? Config.DEFAULT_PHONE_OUT_WORKING : phoneOutTime;
        }
    }


    //Time in working from 08:00 to 17:30
    public static boolean checkInWorkingTime() {
        boolean isOK = false;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
            Date dateInWorking = dateFormat.parse("08:00");
            Date dateOutWorking = dateFormat.parse("17:30");
            Date dateCurrent = dateFormat.parse(dateFormat.format(new Date().getTime()));

            long currTime = dateCurrent.getTime();
            long inWorkingTime = dateInWorking.getTime();
            long outWorkingTime = dateOutWorking.getTime();

            isOK = inWorkingTime <= currTime && currTime <= outWorkingTime;
        } catch (Exception ex) {
            Utils.LOGD("SUPPORT", "ERROR = " + ex);
        }
        Utils.LOGD("MyUtils", "checkInWorkingTime: isok=" + isOK);
        return isOK;
    }

    public static boolean validPhone(String number) {
        if (TextUtils.isEmpty(number) || number.length() < 10 || number.length() > 12 || number.contains(" ")) {
            return false;
        } else {
            return android.util.Patterns.PHONE.matcher(number).matches();
        }
    }

    public static boolean isAppCertify(@NonNull Context context) {
        return context != null && context.getPackageName().equals("vn.mpos.certify");
    }

    public static boolean isTypeAppEmart() {
        return BuildConfig.TYPE_APP.equals(TYPE_APP_EMART);
    }

    public static boolean isTypeAppTakashimaya() {
        return BuildConfig.TYPE_APP.equals(TYPE_APP_TAKASHIMAYA);
    }
}
