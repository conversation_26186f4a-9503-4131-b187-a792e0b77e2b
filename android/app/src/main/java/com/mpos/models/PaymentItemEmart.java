package com.mpos.models;

import java.io.Serializable;
import java.util.ArrayList;

public class PaymentItemEmart implements Serializable {
    public ArrayList<TransItem> data;
    public Paging paging;
    public Summary summary;

    public ArrayList<TransItem> getData() {
        return data;
    }

    public void setData(ArrayList<TransItem> data) {
        this.data = data;
    }

    public Paging getPaging() {
        return paging;
    }

    public void setPaging(Paging paging) {
        this.paging = paging;
    }

    public Summary getSummary() {
        return summary;
    }

    public void setSummary(Summary summary) {
        this.summary = summary;
    }

    public class Paging{
        public String pageIndex;
        public String pageSize;
        public String pageCount;

        public String getPageIndex() {
            return pageIndex;
        }

        public void setPageIndex(String pageIndex) {
            this.pageIndex = pageIndex;
        }

        public String getPageSize() {
            return pageSize;
        }

        public void setPageSize(String pageSize) {
            this.pageSize = pageSize;
        }

        public String getPageCount() {
            return pageCount;
        }

        public void setPageCount(String pageCount) {
            this.pageCount = pageCount;
        }
    }

    public class Summary{
        public String totalAmount;

        public String getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(String totalAmount) {
            this.totalAmount = totalAmount;
        }
    }

    public class TransItem implements Serializable {
        public String udid;
        public String userID;
        public String readerSerialNo;
        public String versionNo;
        public String longitude;
        public String latitude;
        public String mposTID;
        public String mposMID;
        public String amount;
        public String orderCode;
        public String authCode;
        public String transID;
        public String pan;
        public String transactionStatus;
        public String transactionDate;

        public TransItem(String udid, String userID, String readerSerialNo, String versionNo, String longitude, String latitude, String mposTID, String mposMID, String amount, String orderCode, String authCode, String transID, String pan, String transactionStatus, String transactionDate) {
            this.udid = udid;
            this.userID = userID;
            this.readerSerialNo = readerSerialNo;
            this.versionNo = versionNo;
            this.longitude = longitude;
            this.latitude = latitude;
            this.mposTID = mposTID;
            this.mposMID = mposMID;
            this.amount = amount;
            this.orderCode = orderCode;
            this.authCode = authCode;
            this.transID = transID;
            this.pan = pan;
            this.transactionStatus = transactionStatus;
            this.transactionDate = transactionDate;
        }

        public String getUdid() {
            return udid;
        }

        public void setUdid(String udid) {
            this.udid = udid;
        }

        public String getUserID() {
            return userID;
        }

        public void setUserID(String userID) {
            this.userID = userID;
        }

        public String getReaderSerialNo() {
            return readerSerialNo;
        }

        public void setReaderSerialNo(String readerSerialNo) {
            this.readerSerialNo = readerSerialNo;
        }

        public String getVersionNo() {
            return versionNo;
        }

        public void setVersionNo(String versionNo) {
            this.versionNo = versionNo;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getMposTID() {
            return mposTID;
        }

        public void setMposTID(String mposTID) {
            this.mposTID = mposTID;
        }

        public String getMposMID() {
            return mposMID;
        }

        public void setMposMID(String mposMID) {
            this.mposMID = mposMID;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public String getAuthCode() {
            return authCode;
        }

        public void setAuthCode(String authCode) {
            this.authCode = authCode;
        }

        public String getTransID() {
            return transID;
        }

        public void setTransID(String transID) {
            this.transID = transID;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getTransactionStatus() {
            return transactionStatus;
        }

        public void setTransactionStatus(String transactionStatus) {
            this.transactionStatus = transactionStatus;
        }

        public String getTransactionDate() {
            return transactionDate;
        }

        public void setTransactionDate(String transactionDate) {
            this.transactionDate = transactionDate;
        }
    }
}
