package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
/**
 * Create by anhnguyen on 2019-12-19
 */
public class AmountLimit {

    @SerializedName("muid")
    @Expose
    private String muid;
    @SerializedName("error")
    @Expose
    private BaseObjJson error;
    @SerializedName("maxAmountPerTransation")
    @Expose
    private long maxAmountPerTransation;

    @SerializedName("maxAmountPerDay")
    @Expose
    private long maxAmountPerDay;
    @SerializedName("maxAmountPerMonth")
    @Expose
    private long maxAmountPerMonth;
    @SerializedName("totalAmountDay")
    @Expose
    private long totalAmountDay;
    @SerializedName("totalAmountMonth")
    @Expose
    private long totalAmountMonth;


    @SerializedName("midMaxAmountPerTransation")
    @Expose
    private long midMaxAmountPerTransation;
    @SerializedName("midMaxAmountPerDay")
    @Expose
    private long midMaxAmountPerDay;
    @SerializedName("midMaxAmountPerMonth")
    @Expose
    private long midMaxAmountPerMonth;
    @SerializedName("midTotalAmountDay")
    @Expose
    private long midTotalAmountDay;
    @SerializedName("midTotalAmountMonth")
    @Expose
    private long midTotalAmountMonth;
    @SerializedName("mid")
    @Expose
    private String mid;


    public String getMuid() {
        return muid;
    }

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public BaseObjJson getError() {
        return error;
    }

    public void setError(BaseObjJson error) {
        this.error = error;
    }

    public long getMaxAmountPerDay() {
        return maxAmountPerDay;
    }

    public void setMaxAmountPerDay(long maxAmountPerDay) {
        this.maxAmountPerDay = maxAmountPerDay;
    }

    public long getMaxAmountPerMonth() {
        return maxAmountPerMonth;
    }

    public void setMaxAmountPerMonth(long maxAmountPerMonth) {
        this.maxAmountPerMonth = maxAmountPerMonth;
    }

    public long getTotalAmountDay() {
        return totalAmountDay;
    }

    public void setTotalAmountDay(long totalAmountDay) {
        this.totalAmountDay = totalAmountDay;
    }

    public long getTotalAmountMonth() {
        return totalAmountMonth;
    }

    public void setTotalAmountMonth(long totalAmountMonth) {
        this.totalAmountMonth = totalAmountMonth;
    }

    public long getMaxAmountPerTransation() {
        return maxAmountPerTransation;
    }

    public void setMaxAmountPerTransation(long maxAmountPerTransation) {
        this.maxAmountPerTransation = maxAmountPerTransation;
    }


    public long getMidMaxAmountPerTransation() {
        return midMaxAmountPerTransation;
    }

    public void setMidMaxAmountPerTransation(long midMaxAmountPerTransation) {
        this.midMaxAmountPerTransation = midMaxAmountPerTransation;
    }

    public long getMidMaxAmountPerDay() {
        return midMaxAmountPerDay;
    }

    public void setMidMaxAmountPerDay(long midMaxAmountPerDay) {
        this.midMaxAmountPerDay = midMaxAmountPerDay;
    }

    public long getMidMaxAmountPerMonth() {
        return midMaxAmountPerMonth;
    }

    public void setMidMaxAmountPerMonth(long midMaxAmountPerMonth) {
        this.midMaxAmountPerMonth = midMaxAmountPerMonth;
    }

    public long getMidTotalAmountDay() {
        return midTotalAmountDay;
    }

    public void setMidTotalAmountDay(long midTotalAmountDay) {
        this.midTotalAmountDay = midTotalAmountDay;
    }

    public long getMidTotalAmountMonth() {
        return midTotalAmountMonth;
    }

    public void setMidTotalAmountMonth(long midTotalAmountMonth) {
        this.midTotalAmountMonth = midTotalAmountMonth;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }
}
