package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class DataOrder {
    @SerializedName("serviceName")
    @Expose
    public String serviceName;
    @SerializedName("typePayment")
    @Expose
    public String typePayment;
    @SerializedName("orderId")
    @Expose
    public String orderId;
    @SerializedName("totalAmount")
    @Expose
    public long totalAmount;
    @SerializedName("autoDismissDlgTimer")
    @Expose
    public int autoDismissDlgTimer = 0;
    @SerializedName("description")
    @Expose
    public String description;
    @SerializedName("billInfor")
    @Expose
    public List<BillInfor> billInfor;

    public DataOrder() {
    }

    public DataOrder(String serviceName, String orderId, long totalAmount, int autoDismissDlgTimer, String description, List<BillInfor> billInfor) {
        this.serviceName = serviceName;
        this.orderId = orderId;
        this.totalAmount = totalAmount;
        this.autoDismissDlgTimer = autoDismissDlgTimer;
        this.description = description;
        this.billInfor = billInfor;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<BillInfor> getBillInfor() {
        return billInfor;
    }

    public void setBillInfor(List<BillInfor> billInfor) {
        this.billInfor = billInfor;
    }

    public String getTypePayment() {
        return typePayment;
    }

    public void setTypePayment(String typePayment) {
        this.typePayment = typePayment;
    }

    public int getAutoDismissDlgTimer() {
        return autoDismissDlgTimer;
    }

    public void setAutoDismissDlgTimer(int autoDismissDlgTimer) {
        this.autoDismissDlgTimer = autoDismissDlgTimer;
    }

    public static class BillInfor{
        @SerializedName("item")
        @Expose
        String item;
        @SerializedName("quantity")
        @Expose
        String quantity;
        @SerializedName("amount")
        @Expose
        String amount;

        public BillInfor(String item1, String s, String s1) {
        }

        public String getItem() {
            return item;
        }

        public void setItem(String item) {
            this.item = item;
        }

        public String getQuantity() {
            return quantity;
        }

        public void setQuantity(String quantity) {
            this.quantity = quantity;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }
}
