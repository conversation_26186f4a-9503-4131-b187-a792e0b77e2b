package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class VetcAmount implements Serializable{

    @SerializedName("error")
    @Expose
    private BaseObjJson error;

    @SerializedName("amount")
    @Expose
    private String amount;
    @SerializedName("domesticCardFee")
    @Expose
    private String domesticCardFee;
    @SerializedName("internationalCardFee")
    @Expose
    private String internationalCardFee;
    @SerializedName("issuer")
    @Expose
    private String issuer;
    @SerializedName("token")
    @Expose
    private String token;
    @SerializedName("check")
    @Expose
    private Boolean check;

    private String feeAtm;
    private String feeGlobal;

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getDomesticCardFee() {
        return domesticCardFee;
    }

    public void setDomesticCardFee(String domesticCardFee) {
        this.domesticCardFee = domesticCardFee;
    }

    public String getInternationalCardFee() {
        return internationalCardFee;
    }

    public void setInternationalCardFee(String internationalCardFee) {
        this.internationalCardFee = internationalCardFee;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Boolean getCheck() {
        return check;
    }

    public void setCheck(Boolean check) {
        this.check = check;
    }

    public BaseObjJson getError() {
        return error;
    }

    public void setError(BaseObjJson error) {
        this.error = error;
    }

    public String getFeeAtm() {
        return feeAtm;
    }

    public void setFeeAtm(String feeAtm) {
        this.feeAtm = feeAtm;
    }

    public String getFeeGlobal() {
        return feeGlobal;
    }

    public void setFeeGlobal(String feeGlobal) {
        this.feeGlobal = feeGlobal;
    }
}
