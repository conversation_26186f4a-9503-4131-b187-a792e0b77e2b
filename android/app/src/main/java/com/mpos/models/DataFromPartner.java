package com.mpos.models;

import android.text.TextUtils;

import com.mpos.common.JsonParser;

import org.json.JSONObject;

import java.io.Serializable;

public class DataFromPartner implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8918974390735571307L;
	
	private String amount;
	private String description;
	private String orderId;
	private String urlCallBack;
	private String emailReceipt;
	private String content;
	private String orderType;
	private String extParam;
    private String typePay;
    private String showInfoOrder; // need call get info order in server
    private boolean isManual;
    private String pushbackError;
    private String stageHandler;

    private boolean haveError;
    private String  msgError;

    private String  qrCode;
    private String  qrType;

	private boolean flag;

	// user login
    private String mobileUser;
    private String mobilePass;
    private String readerType;
    private String readerSN;
    private String readerBluetoothAddress;

    public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public boolean isFlag() {
		return flag;
	}
	public void setFlag(boolean flag) {
		this.flag = flag;
	}

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getUrlCallBack() {
        return urlCallBack;
    }

    public void setUrlCallBack(String urlCallBack) {
        this.urlCallBack = urlCallBack;
    }

    public String getEmailReceipt() {
        return emailReceipt;
    }

    public void setEmailReceipt(String emailReceipt) {
        this.emailReceipt = emailReceipt;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isManual() {
        return isManual;
    }

    public void setManual(boolean manual) {
        isManual = manual;
    }

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getExtParam() {
		return extParam;
	}

	public void setExtParam(String extParam) {
		this.extParam = extParam;
	}

    public String getTypePay() {
        return typePay;
    }

    public void setTypePay(String typePay) {
        this.typePay = typePay;
    }

    public String getShowInfoOrder() {
        return showInfoOrder;
    }

    public void setShowInfoOrder(String showInfoOrder) {
        this.showInfoOrder = showInfoOrder;
    }

    public String isPushbackError() {
        return pushbackError;
    }

    public void setPushbackError(String pushbackError) {
        this.pushbackError = pushbackError;
    }

    public String getStageHandler() {
        return stageHandler;
    }

    public void setStageHandler(String stageHandler) {
        this.stageHandler = stageHandler;
    }

    public boolean isHaveError() {
        return haveError;
    }

    public void setHaveError(boolean haveError) {
        this.haveError = haveError;
    }

    public String getMsgError() {
        return msgError;
    }

    public void setMsgError(String msgError) {
	    this.haveError = true;
        this.msgError = msgError;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getPushbackError() {
        return pushbackError;
    }

    public String getQrType() {
        return qrType;
    }

    public void setQrType(String qrType) {
        this.qrType = qrType;
    }

    public void parseDataLogin(JSONObject jRoot) {
        if (jRoot != null) {
            mobileUser  = JsonParser.getDataJson(jRoot, "mobileUser");
            mobilePass  = JsonParser.getDataJson(jRoot, "mobileUserPass");
            readerType  = JsonParser.getDataJson(jRoot, "readerType");
            readerSN    = JsonParser.getDataJson(jRoot, "readerSerialNumber");
            readerBluetoothAddress = JsonParser.getDataJson(jRoot, "readerBluetoothAddress");
        }
    }
    public void parseOtherData(JSONObject jRoot) {
        if (jRoot != null) {
            setAmount(JsonParser.getDataJson(jRoot, "amount"));
            setDescription(JsonParser.getDataJson(jRoot, "description"));
            setOrderId(JsonParser.getDataJson(jRoot, "orderId"));
            setUrlCallBack(JsonParser.getDataJson(jRoot, "urlCallBack"));
            setEmailReceipt(JsonParser.getDataJson(jRoot, "emailReceipt"));
            setOrderType(JsonParser.getDataJson(jRoot, "orderType"));
            setTypePay(JsonParser.getDataJson(jRoot, "typePay"));
            setShowInfoOrder(JsonParser.getDataJson(jRoot, "showInfoOrder", "1"));
            setPushbackError(JsonParser.getDataJson(jRoot, "putbackError", "1"));
            setStageHandler(JsonParser.getDataJson(jRoot, "stageHandler", ""));

            setExtParam(JsonParser.getDataJson(jRoot, "extParam"));

//            mobileUser  = JsonParser.getDataJson(jRoot, "mobileUser");
//            mobilePass  = JsonParser.getDataJson(jRoot, "mobilePass");
//            readerType  = JsonParser.getDataJson(jRoot, "readerType");
//            readerSN    = JsonParser.getDataJson(jRoot, "readerSerialNumber");
//            readerBluetoothAddress = JsonParser.getDataJson(jRoot, "readerBluetoothAddress");
        }
    }

    public boolean checkCanAutoLogin() {
        return !TextUtils.isEmpty(mobileUser) && !TextUtils.isEmpty(mobilePass) && !TextUtils.isEmpty(readerType) && !TextUtils.isEmpty(readerSN);
    }

    public boolean checkMissDataLogin() {
        return !TextUtils.isEmpty(mobileUser) || !TextUtils.isEmpty(mobilePass) || !TextUtils.isEmpty(readerType) || !TextUtils.isEmpty(readerSN);
    }

    public String getMobileUser() {
        return mobileUser;
    }

    public String getMobilePass() {
        return mobilePass;
    }

    public String getReaderType() {
        return readerType;
    }

    public String getReaderSN() {
        return readerSN;
    }

    public String getReaderBluetoothAddress() {
        return readerBluetoothAddress;
    }
}
