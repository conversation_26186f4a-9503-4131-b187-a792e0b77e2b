package com.mpos.models;

import com.mpos.sdk.core.model.ResultPayWrapper;

public class DataStatusOrder {
    DataOrder dataOrder;
    ResultPayWrapper resultPayWrapper;

    public DataStatusOrder(DataOrder dataOrder, ResultPayWrapper resultPayWrapper) {
        this.dataOrder = dataOrder;
        this.resultPayWrapper = resultPayWrapper;
    }

    public DataOrder getDataOrder() {
        return dataOrder;
    }

    public void setDataOrder(DataOrder dataOrder) {
        this.dataOrder = dataOrder;
    }

    public ResultPayWrapper getResultPayWrapper() {
        return resultPayWrapper;
    }

    public void setResultPayWrapper(ResultPayWrapper resultPayWrapper) {
        this.resultPayWrapper = resultPayWrapper;
    }
}
