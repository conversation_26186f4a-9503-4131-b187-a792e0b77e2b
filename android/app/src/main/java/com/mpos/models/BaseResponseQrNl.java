package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class BaseResponseQrNl {
    @SerializedName("result_code")
    @Expose
    public String resultCode;
    @SerializedName("result_data")
    @Expose
    public ResultData resultData;
    @SerializedName("result_message")
    @Expose
    public String resultMessage;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public ResultData getResultData() {
        return resultData;
    }

    public void setResultData(ResultData resultData) {
        this.resultData = resultData;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public BaseResponseQrNl() {
    }

    public class ResultData implements Serializable {
        @SerializedName("checkout_url")
        @Expose
        public String checkoutUrl;
        @SerializedName("data")
        @Expose
        public String datQRr;
        @SerializedName("token_code")
        @Expose
        public String tokenCode;

        public ResultData() {
        }

        public String getCheckoutUrl() {
            return checkoutUrl;
        }

        public void setCheckoutUrl(String checkoutUrl) {
            this.checkoutUrl = checkoutUrl;
        }

        public String getTokenCode() {
            return tokenCode;
        }

        public void setTokenCode(String tokenCode) {
            this.tokenCode = tokenCode;
        }

        public String getDatQRr() {
            return datQRr;
        }

        public void setDatQRr(String datQRr) {
            this.datQRr = datQRr;
        }
    }
}
