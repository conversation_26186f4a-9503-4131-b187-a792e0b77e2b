package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DataTransStatusQrNl {
    @SerializedName("result_code")
    @Expose
    public String resultCode;
    @SerializedName("result_data")
    @Expose
    public ResultData resultData;
    @SerializedName("result_message")
    @Expose
    public String resultMessage;

    public DataTransStatusQrNl() {
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public ResultData getResultData() {
        return resultData;
    }

    public void setResultData(ResultData resultData) {
        this.resultData = resultData;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public class ResultData{
        @SerializedName("token_code")
        @Expose
        public String tokenCode;
        @SerializedName("version")
        @Expose
        public String version;
        @SerializedName("order_code")
        @Expose
        public String orderCode;
        @SerializedName("order_description")
        @Expose
        public String orderDescription;
        @SerializedName("amount")
        @Expose
        public String amount;
        @SerializedName("sender_fee")
        @Expose
        public int senderFee;
        @SerializedName("receiver_fee")
        @Expose
        public int receiverFee;
        @SerializedName("currency")
        @Expose
        public String currency;
        @SerializedName("return_url")
        @Expose
        public String returnUrl;
        @SerializedName("cancel_url")
        @Expose
        public String cancelUrl;
        @SerializedName("notify_url")
        @Expose
        public String notifyUrl;
        @SerializedName("status")
        @Expose
        public int status;
        @SerializedName("payment_method_code")
        @Expose
        public String paymentMethodCode;
        @SerializedName("payment_method_name")
        @Expose
        public String paymentMethodName;
        @SerializedName("message")
        @Expose
        public String message;
        @SerializedName("message_error")
        @Expose
        public String messageError;

        public ResultData() {
        }

        public String getTokenCode() {
            return tokenCode;
        }

        public void setTokenCode(String tokenCode) {
            this.tokenCode = tokenCode;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public String getOrderDescription() {
            return orderDescription;
        }

        public void setOrderDescription(String orderDescription) {
            this.orderDescription = orderDescription;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public int getSenderFee() {
            return senderFee;
        }

        public void setSenderFee(int senderFee) {
            this.senderFee = senderFee;
        }

        public int getReceiverFee() {
            return receiverFee;
        }

        public void setReceiverFee(int receiverFee) {
            this.receiverFee = receiverFee;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getReturnUrl() {
            return returnUrl;
        }

        public void setReturnUrl(String returnUrl) {
            this.returnUrl = returnUrl;
        }

        public String getCancelUrl() {
            return cancelUrl;
        }

        public void setCancelUrl(String cancelUrl) {
            this.cancelUrl = cancelUrl;
        }

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getPaymentMethodCode() {
            return paymentMethodCode;
        }

        public void setPaymentMethodCode(String paymentMethodCode) {
            this.paymentMethodCode = paymentMethodCode;
        }

        public String getPaymentMethodName() {
            return paymentMethodName;
        }

        public void setPaymentMethodName(String paymentMethodName) {
            this.paymentMethodName = paymentMethodName;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getMessageError() {
            return messageError;
        }

        public void setMessageError(String messageError) {
            this.messageError = messageError;
        }
    }
}
