package com.mpos.models;

import java.util.Map;

public class DataOrderTakashimaya {
    private String APP;
    private String TXN_TYPE;
    private String AMOUNT;
    private String BILL_ID;
    private String CURRENCY_CODE;
    private String PRINT_MSG;
    private String ADDL_DATA ;

    private String INVOICE ;

    private String SEND;
    // thêm các trường khác nếu có…

    // Constructor
    public DataOrderTakashimaya() {}

    // Getters & setters
    public String getAPP() { return APP; }
    public void setAPP(String APP) { this.APP = APP; }

    public String getBILL_ID() { return BILL_ID; }
    public void setBILL_ID(String BILL_ID) { this.BILL_ID = BILL_ID; }

    public String getAMOUNT() { return AMOUNT; }
    public void setAMOUNT(String AMOUNT) { this.AMOUNT = AMOUNT; }

    public String getTXN_TYPE() { return TXN_TYPE; }
    public void setTXN_TYPE(String TXN_TYPE) { this.TXN_TYPE = TXN_TYPE; }

    public String getSEND() { return SEND; }
    public void setSEND(String SEND) { this.SEND = SEND; }

    public String getPRINT_MSG() { return PRINT_MSG; }
    public void setPRINT_MSG(String PRINT_MSG) { this.PRINT_MSG = PRINT_MSG; }

    public String getCURRENCY_CODE() {return CURRENCY_CODE;}

    public void setCURRENCY_CODE(String CURRENCY_CODE) {this.CURRENCY_CODE = CURRENCY_CODE;}

    public String getADDL_DATA() {return ADDL_DATA;}

    public void setADDL_DATA(String ADDL_DATA) {this.ADDL_DATA = ADDL_DATA;}

    public String getINVOICE() {
        return INVOICE;
    }

    public void setINVOICE(String INVOICE) {
        this.INVOICE = INVOICE;
    }

    /**
     * Khởi tạo TransactionData từ Map<String,String>
     */
    public static DataOrderTakashimaya fromMap(Map<String, String> map) {
        DataOrderTakashimaya t = new DataOrderTakashimaya();
        if (map.containsKey("APP"))        t.setAPP(map.get("APP"));
        if (map.containsKey("TXN_TYPE"))   t.setTXN_TYPE(map.get("TXN_TYPE"));
        if (map.containsKey("AMOUNT"))     t.setAMOUNT(map.get("AMOUNT"));
        if (map.containsKey("BILL_ID"))    t.setBILL_ID(map.get("BILL_ID"));
        if (map.containsKey("CURRENCY_CODE"))    t.setCURRENCY_CODE(map.get("CURRENCY_CODE"));
        if (map.containsKey("PRINT_MSG"))  t.setPRINT_MSG(map.get("PRINT_MSG"));
        if (map.containsKey("ADDL_DATA"))  t.setADDL_DATA(map.get("ADDL_DATA"));
        if (map.containsKey("INVOICE"))  t.setINVOICE(map.get("INVOICE"));
        if (map.containsKey("SEND"))       t.setSEND(map.get("SEND"));
        // ... tương tự với các key khác nếu cần
        return t;
    }

    @Override
    public String toString() {
        return "TransactionData{" +
                "APP='" + APP + '\'' +
                ", BILL_ID='" + BILL_ID + '\'' +
                ", AMOUNT='" + AMOUNT + '\'' +
                ", CURRENCY_CODE='" + CURRENCY_CODE + '\'' +
                ", TXN_TYPE='" + TXN_TYPE + '\'' +
                ", PRINT_MSG='" + PRINT_MSG + '\'' +
                ", ADDL_DATA='" + ADDL_DATA + '\'' +
                ", INVOICE='" + INVOICE + '\'' +
                ", SEND='" + SEND + '\'' +
                '}';
    }
}
