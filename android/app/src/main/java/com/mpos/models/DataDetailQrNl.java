package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class DataDetailQrNl {
    @SerializedName("result_code")
    @Expose
    private String result_code;
    @SerializedName("result_data")
    @Expose
    private Data resultData;
    @SerializedName("result_message")
    @Expose
    private String result_message;

    public DataDetailQrNl() {
    }

    public String getResult_code() {
        return result_code;
    }

    public void setResult_code(String result_code) {
        this.result_code = result_code;
    }

    public Data getResultData() {
        return resultData;
    }

    public void setResultData(Data resultData) {
        this.resultData = resultData;
    }

    public String getResult_message() {
        return result_message;
    }

    public void setResult_message(String result_message) {
        this.result_message = result_message;
    }


    public class Data implements Serializable {
        @SerializedName("token_code")
        @Expose
        public String token_code;
        @SerializedName("version")
        @Expose
        public String version;
        @SerializedName("order_code")
        @Expose
        public String order_code;
        @SerializedName("order_description")
        @Expose
        public String order_description;
        @SerializedName("amount")
        @Expose
        public String amount;
        @SerializedName("sender_fee")
        @Expose
        public float sender_fee;
        @SerializedName("receiver_fee")
        @Expose
        public float receiver_fee;
        @SerializedName("currency")
        @Expose
        public String currency;
        @SerializedName("return_url")
        @Expose
        public String return_url;
        @SerializedName("cancel_url")
        @Expose
        public String cancel_url;
        @SerializedName("notify_url")
        @Expose
        public String notify_url;
        @SerializedName("status")
        @Expose
        public int status;
        @SerializedName("payment_method_code")
        @Expose
        public String payment_method_code;
        @SerializedName("payment_method_name")
        @Expose
        public String payment_method_name;
        @SerializedName("message")
        @Expose
        public String message;
        @SerializedName("message_error")
        @Expose
        public String message_error;
        public Data() {
        }

        public String getToken_code() {
            return token_code;
        }

        public void setToken_code(String token_code) {
            this.token_code = token_code;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getOrder_code() {
            return order_code;
        }

        public void setOrder_code(String order_code) {
            this.order_code = order_code;
        }

        public String getOrder_description() {
            return order_description;
        }

        public void setOrder_description(String order_description) {
            this.order_description = order_description;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public float getSender_fee() {
            return sender_fee;
        }

        public void setSender_fee(float sender_fee) {
            this.sender_fee = sender_fee;
        }

        public float getReceiver_fee() {
            return receiver_fee;
        }

        public void setReceiver_fee(float receiver_fee) {
            this.receiver_fee = receiver_fee;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getReturn_url() {
            return return_url;
        }

        public void setReturn_url(String return_url) {
            this.return_url = return_url;
        }

        public String getCancel_url() {
            return cancel_url;
        }

        public void setCancel_url(String cancel_url) {
            this.cancel_url = cancel_url;
        }

        public String getNotify_url() {
            return notify_url;
        }

        public void setNotify_url(String notify_url) {
            this.notify_url = notify_url;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getPayment_method_code() {
            return payment_method_code;
        }

        public void setPayment_method_code(String payment_method_code) {
            this.payment_method_code = payment_method_code;
        }

        public String getPayment_method_name() {
            return payment_method_name;
        }

        public void setPayment_method_name(String payment_method_name) {
            this.payment_method_name = payment_method_name;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getMessage_error() {
            return message_error;
        }

        public void setMessage_error(String message_error) {
            this.message_error = message_error;
        }
    }
}
