package com.mpos.models;

import java.io.Serializable;

public class DetailItemPaymentEmart implements Serializable {
    public Data data;

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public class Data{
        public String udid;
        public String userID;
        public String readerSerialNo;
        public String versionNo;
        public String longitude;
        public String latitude;
        public String mposTID;
        public String mposMID;
        public String amount;
        public String orderCode;
        public String authCode;
        public String transID;
        public String pan;
        public String transactionStatus;
        public String transactionDate;

        public String getUdid() {
            return udid;
        }

        public void setUdid(String udid) {
            this.udid = udid;
        }

        public String getUserID() {
            return userID;
        }

        public void setUserID(String userID) {
            this.userID = userID;
        }

        public String getReaderSerialNo() {
            return readerSerialNo;
        }

        public void setReaderSerialNo(String readerSerialNo) {
            this.readerSerialNo = readerSerialNo;
        }

        public String getVersionNo() {
            return versionNo;
        }

        public void setVersionNo(String versionNo) {
            this.versionNo = versionNo;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getMposTID() {
            return mposTID;
        }

        public void setMposTID(String mposTID) {
            this.mposTID = mposTID;
        }

        public String getMposMID() {
            return mposMID;
        }

        public void setMposMID(String mposMID) {
            this.mposMID = mposMID;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public String getAuthCode() {
            return authCode;
        }

        public void setAuthCode(String authCode) {
            this.authCode = authCode;
        }

        public String getTransID() {
            return transID;
        }

        public void setTransID(String transID) {
            this.transID = transID;
        }

        public String getPan() {
            return pan;
        }

        public void setPan(String pan) {
            this.pan = pan;
        }

        public String getTransactionStatus() {
            return transactionStatus;
        }

        public void setTransactionStatus(String transactionStatus) {
            this.transactionStatus = transactionStatus;
        }

        public String getTransactionDate() {
            return transactionDate;
        }

        public void setTransactionDate(String transactionDate) {
            this.transactionDate = transactionDate;
        }
    }
}
