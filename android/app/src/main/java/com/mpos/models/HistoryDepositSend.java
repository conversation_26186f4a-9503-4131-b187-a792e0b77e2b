package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class HistoryDepositSend {
    @SerializedName("muid")
    @Expose
    private String muid;
    @SerializedName("timeFrom")
    @Expose
    private String timeFrom;
    @SerializedName("timeTo")
    @Expose
    private String timeTo;
    @SerializedName("depositRefCode")
    @Expose
    private String depositRefCode;
    @SerializedName("status")
    @Expose
    private String status;
    @SerializedName("amount")
    @Expose
    private String amount;
    @SerializedName("paging")
    @Expose
    private Paging paging;
    @SerializedName("mobile")
    @Expose
    private String mobile;

    public HistoryDepositSend() {
    }

    // Getter Methods

    public String getMuid() {
        return muid;
    }

    public String getTimeFrom() {
        return timeFrom;
    }

    public String getTimeTo() {
        return timeTo;
    }

    public String getDepositRefCode() {
        return depositRefCode;
    }

    public String getStatus() {
        return status;
    }

    public String getAmount() {
        return amount;
    }

    public Paging getPaging() {
        return paging;
    }

    public String getMobile() {
        return mobile;
    }

    // Setter Methods

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public void setTimeFrom(String timeFrom) {
        this.timeFrom = timeFrom;
    }

    public void setTimeTo(String timeTo) {
        this.timeTo = timeTo;
    }

    public void setDepositRefCode(String depositRefCode) {
        this.depositRefCode = depositRefCode;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setPaging(Paging paging) {
        this.paging = paging;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }


    public static class Paging {
        @SerializedName("pageIndex")
        @Expose
        private int pageIndex;
        @SerializedName("pageSize")
        @Expose
        private int pageSize;
        @SerializedName("pageCount")
        @Expose
        private String pageCount;
        @SerializedName("dataCount")
        @Expose
        private String dataCount;

        public Paging() {
        }

        public int getPageIndex() {
            return this.pageIndex;
        }

        public void setPageIndex(int pageIndex) {
            this.pageIndex = pageIndex;
        }

        public int getPageSize() {
            return this.pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public String getPageCount() {
            return this.pageCount;
        }

        public void setPageCount(String pageCount) {
            this.pageCount = pageCount;
        }

        public String getDataCount() {
            return this.dataCount;
        }

        public void setDataCount(String dataCount) {
            this.dataCount = dataCount;
        }
    }
}
