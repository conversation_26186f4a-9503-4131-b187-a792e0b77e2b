package com.mpos.models;

import com.pax.baselink.listener.IBluetoothDevice;

public class BluetoothDeviceTcp implements IBluetoothDevice {
        String bM;
        boolean bN;
        String name;

        @Override
        public String getName() {
            return this.name;
        }

        @Override
        public String getIdentifier() {
            return this.bM;
        }

        @Override
        public boolean isBle() {
            return this.bN;
        }
    }