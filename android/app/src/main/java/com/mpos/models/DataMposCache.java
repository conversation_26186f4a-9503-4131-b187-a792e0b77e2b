package com.mpos.models;

import com.mpos.common.JsonParser;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by ldhd-lhll on 4/13/17.
 */

public class DataMposCache {

    String KEY_SERIAL= "serial";
    String KEY_BANK  = "bank";

    String serial;
    String bank;

    public DataMposCache() {
    }

    public DataMposCache(String serialNumber, String bankName) {
        this.serial = serialNumber;
        this.bank = bankName;
    }

    public String buildDataCache() {
        String result = "";

        try {
            JSONObject jRoot = new JSONObject();
            jRoot.put(KEY_SERIAL, serial);
            jRoot.put(KEY_BANK, bank);

            result = jRoot.toString();
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return result;
    }


    public void parseDataCache(String data) {
        try {
            JSONObject jRoot = new JSONObject(data);
            serial  = JsonParser.getDataJson(jRoot, KEY_SERIAL);
            bank    = JsonParser.getDataJson(jRoot, KEY_BANK);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }
}
