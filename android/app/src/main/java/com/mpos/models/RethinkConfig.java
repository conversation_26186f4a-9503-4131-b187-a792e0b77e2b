package com.mpos.models;

/**
 * Create by anhn<PERSON>yen on 7/13/20
 */
public class RethinkConfig {

    String dbName;
    String host;
    int port;
    String user;
    String muid;
    int merchantId;
    String password;
    boolean isShowHistory;
    String muPassword;
    boolean isAutoPay;
    int typeDevice ;

    public RethinkConfig(String dbName, String host, int port, String user, String muId, int merchantId, String password, boolean isAutoPay) {
        this.dbName = dbName;
        this.host = host;
        this.port = port;
        this.user = user;
        this.muid = muId;
        this.merchantId = merchantId;
        this.password = password;
        this.isAutoPay = isAutoPay;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMuid() {
        return muid;
    }

    public void setMuid(String muid) {
        this.muid = muid;
    }

    public int getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(int merchantId) {
        this.merchantId = merchantId;
    }

    public boolean isAutoPay() {
        return isAutoPay;
    }

    public void setAutoPay(boolean autoPay) {
        isAutoPay = autoPay;
    }

    public boolean getIsShowHistory() {
        return isShowHistory;
    }

    public void setIsShowHistory(boolean isShowHistory) {
        this.isShowHistory = isShowHistory;
    }

    public String getMuPassword() {
        return muPassword;
    }

    public void setMuPassword(String muPassword) {
        this.muPassword = muPassword;
    }

    public int getTypeDevice() {
        return typeDevice;
    }

    public void setTypeDevice(int typeDevice) {
        this.typeDevice = typeDevice;
    }
}
