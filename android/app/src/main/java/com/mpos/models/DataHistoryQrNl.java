package com.mpos.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;

public class DataHistoryQrNl  implements Serializable {
    @SerializedName("result_code")
    @Expose
    private String result_code;
    @SerializedName("result_data")
    @Expose
    private ResultData resultData;
    @SerializedName("result_message")
    @Expose
    private String result_message;

    public DataHistoryQrNl() {
    }

    public String getResult_code() {
        return result_code;
    }

    public void setResult_code(String result_code) {
        this.result_code = result_code;
    }

    public ResultData getResultData() {
        return resultData;
    }

    public void setResultData(ResultData resultData) {
        this.resultData = resultData;
    }

    public String getResult_message() {
        return result_message;
    }

    public void setResult_message(String result_message) {
        this.result_message = result_message;
    }

    public class ResultData {
        @SerializedName("total_record")
        @Expose
        public String totalRecord;
        @SerializedName("per_page")
        @Expose
        public String per_page;
        @SerializedName("page")
        @Expose
        public String page;

        @SerializedName("data")
        @Expose
        public ArrayList<Data> data;

        public ResultData() {
        }

        public String getTotalRecord() {
            return totalRecord;
        }

        public void setTotalRecord(String totalRecord) {
            this.totalRecord = totalRecord;
        }

        public String getPer_page() {
            return per_page;
        }

        public void setPer_page(String per_page) {
            this.per_page = per_page;
        }

        public String getPage() {
            return page;
        }

        public void setPage(String page) {
            this.page = page;
        }

        public ArrayList<Data> getData() {
            return data;
        }

        public void setData(ArrayList<Data> data) {
            this.data = data;
        }

        public class Data implements Serializable{
            @SerializedName("token_code")
            @Expose
            public String token_code;
            @SerializedName("order_code")
            @Expose
            public String order_code;
            @SerializedName("order_description")
            @Expose
            public String order_description;
            @SerializedName("amount")
            @Expose
            public String amount;
            @SerializedName("buyer_fullname")
            @Expose
            public String buyer_fullname;
            @SerializedName("buyer_email")
            @Expose
            public String buyer_email;
            @SerializedName("buyer_mobile")
            @Expose
            public String buyer_mobile;
            @SerializedName("buyer_address")
            @Expose
            public String buyer_address;
            @SerializedName("status")
            @Expose
            public String status;
            @SerializedName("time_created")
            @Expose
            public String time_created;
            @SerializedName("time_paid")
            @Expose
            public String time_paid;
            @SerializedName("time_refund")
            @Expose
            public String time_refund;
            @SerializedName("payment_method_name")
            @Expose
            public String payment_method_name;

            public Data() {
            }

            public String getToken_code() {
                return token_code;
            }

            public void setToken_code(String token_code) {
                this.token_code = token_code;
            }

            public String getOrder_code() {
                return order_code;
            }

            public void setOrder_code(String order_code) {
                this.order_code = order_code;
            }

            public String getOrder_description() {
                return order_description;
            }

            public void setOrder_description(String order_description) {
                this.order_description = order_description;
            }

            public String getAmount() {
                return amount;
            }

            public void setAmount(String amount) {
                this.amount = amount;
            }

            public String getBuyer_fullname() {
                return buyer_fullname;
            }

            public void setBuyer_fullname(String buyer_fullname) {
                this.buyer_fullname = buyer_fullname;
            }

            public String getBuyer_email() {
                return buyer_email;
            }

            public void setBuyer_email(String buyer_email) {
                this.buyer_email = buyer_email;
            }

            public String getBuyer_mobile() {
                return buyer_mobile;
            }

            public void setBuyer_mobile(String buyer_mobile) {
                this.buyer_mobile = buyer_mobile;
            }

            public String getBuyer_address() {
                return buyer_address;
            }

            public void setBuyer_address(String buyer_address) {
                this.buyer_address = buyer_address;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getTime_created() {
                return time_created;
            }

            public void setTime_created(String time_created) {
                this.time_created = time_created;
            }

            public String getTime_paid() {
                return time_paid;
            }

            public void setTime_paid(String time_paid) {
                this.time_paid = time_paid;
            }

            public String getTime_refund() {
                return time_refund;
            }

            public void setTime_refund(String time_refund) {
                this.time_refund = time_refund;
            }

            public String getPayment_method_name() {
                return payment_method_name;
            }

            public void setPayment_method_name(String payment_method_name) {
                this.payment_method_name = payment_method_name;
            }
        }

    }
}
