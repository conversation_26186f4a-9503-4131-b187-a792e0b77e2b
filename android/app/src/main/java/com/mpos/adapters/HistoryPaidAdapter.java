package com.mpos.adapters;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.colormoon.readmoretextview.ReadMoreTextView;
import com.emilsjolander.components.stickylistheaders.StickyListHeadersAdapter;
import com.mpos.models.ItemFailHistory;
import com.mpos.models.PaymentItem;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;
import com.mpos.utils.MyUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import vn.mpos.R;

import static com.mpos.screen.FragmentPaymentHistory.TYPE_BANK;

/**
 * Create by anhnguyen on 11/30/20
 */
public class HistoryPaidAdapter<T>  extends BaseAdapter implements StickyListHeadersAdapter {

    private static final String TAG = "HistoryPaidAdapter";

    private final List<T> mData;
    private final LayoutInflater mInflater;

    public static final int TYPE_MAX_COUNT = 3;
    public static final int TYPE_ADAPTER_NORMAL    = 0;
    public static final int TYPE_ADAPTER_FAIL_HIS  = 1;

    private int typeAdapter = TYPE_ADAPTER_NORMAL;
    MyUtils myUtils;
    private final Context context;
    private final String typeFragment;

    public HistoryPaidAdapter(Context c, String typeFragment, List<T> data) {
        mInflater = (LayoutInflater) c.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mData = data;
        myUtils = new MyUtils();
        this.context = c;
        this.typeFragment = typeFragment;
    }

    public void setTypeAdapter(int typeAdapter) {
        this.typeAdapter = typeAdapter;
    }

    @Override
    public int getViewTypeCount() {
        return TYPE_MAX_COUNT;
    }

    @Override
    public int getCount() {
        return mData == null ? 0 : mData.size();
    }

    public void addMore(ArrayList<T> arrNew) {
        if (arrNew == null || arrNew.size() == 0) {
            return;
        }
        ArrayList<T> arrTemp = new ArrayList<>();
        if (mData.size() > 0) {
            arrTemp.addAll(mData);
            mData.clear();
        }
        arrTemp.addAll(arrNew);
        mData.addAll(arrTemp);
        Utils.LOGD(TAG, "swap: size_new=" + mData.size());
        notifyDataSetChanged();
    }

    @Override
    public T getItem(int position) {
        return mData.get(position);
    }

    public List<T> getData() {
        return mData;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (typeAdapter == TYPE_ADAPTER_NORMAL) {
            convertView = getViewNormal(position, convertView, parent);
        }
        else {
            convertView = getViewFailTrans(position, convertView, parent);
        }
        return convertView;
    }

    private View getViewFailTrans(int position, View convertView, ViewGroup parent) {
        FailTransViewHolder viewHolder;
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.item_fail_payment, parent, false);
            viewHolder = new FailTransViewHolder(convertView);
            convertView.setTag(viewHolder);
        }
        else {
            viewHolder = (FailTransViewHolder) convertView.getTag();
        }
        ItemFailHistory item = (ItemFailHistory) mData.get(position);
        viewHolder.tvAmount.setText(String.format("%s%s", Utils.zenMoney(item.getAmount()), ConstantsPay.CURRENCY_SPACE_PRE));
        viewHolder.tvErrorCode.setText(String.format("%s%s", getString(R.string.code_break_line), item.getStatus()));
        try {
            viewHolder.tvDesError.setText(LibError.getErrorMsg(Integer.parseInt(item.getStatus()), context));
        } catch (NumberFormatException e) {
            viewHolder.tvDesError.setText("");
        }
        viewHolder.tvPan.setText(String.format(" %s", TextUtils.isEmpty(item.getPan()) ? getString(R.string.not_available) : CardUtils.getMaskedPan(item.getPan())));
        viewHolder.tvTime.setText(Utils.convertTimestamp(item.getCreatedDate(), 1));
        return convertView;
    }


    private View getViewNormal(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = mInflater.inflate(R.layout.item_payment_ctl, parent, false);

            holder.time = convertView.findViewById(R.id.time);
            holder.thumb = convertView.findViewById(R.id.thumb);
            holder.amount = convertView.findViewById(R.id.tv_amount);
            holder.number = convertView.findViewById(R.id.number);
            holder.status = convertView.findViewById(R.id.tv_status);
            holder.approval_code = convertView.findViewById(R.id.approval_code);
            holder.invoice_no = convertView.findViewById(R.id.invoice_no);
            holder.trxtype = convertView.findViewById(R.id.tv_trx_type);
            holder.tvFeedbackStatus = convertView.findViewById(R.id.tv_feedback_status);
            convertView.setTag(holder);
        }
        else {
            holder = (ViewHolder) convertView.getTag();
        }

        PaymentItem item = (PaymentItem) mData.get(position);

        holder.time.setText(item.getTime());
        String builderAmount = item.getAmount() + " " + item.getCurence();
        holder.amount.setText(builderAmount);
        if (Constants.VAYMUONQR.equals(item.getTransactionPushType())) {
            holder.number.setText(Constants.VAYMUONQR);
        }
        else if (Constants.LINKCARD.equals(item.getTransactionPushType())) {
            if (Constants.INSTALLMENT.equals(item.getTransactionType())) {
                holder.number.setText(R.string.type_vimolink);
            }
            else if (Constants.NORMAL.equals(item.getTransactionType())) {
                holder.number.setText(!TextUtils.isEmpty(item.getNumber()) ? item.getNumber().replaceAll("X", "*") : "");
            }
        }
        else {
            holder.number.setText(!TextUtils.isEmpty(item.getNumber()) ? CardUtils.getMaskedPan(item.getNumber()) : "");
        }
        String builderCode = getString(R.string.approval_code) + " " + item.getApprovalCode();
        holder.approval_code.setText(builderCode);
        String builderVoide = getString(R.string.refer_number) + " " + item.getInvoiceNo();
        holder.invoice_no.setText(builderVoide);

        if (TextUtils.isEmpty(item.getTransactionPushType())) {
            myUtils.setImgThumbByTypeCard(context, item.getName(), holder.thumb, item.getAccquirer());
        }
        else {
            if (Constants.LINKCARD.equals(item.getTransactionPushType())) {
                if (Constants.NORMAL.equals(item.getTransactionType())) {
                    myUtils.setImgEnterCard(context, holder.thumb);
                }
                else {
                    myUtils.setImgLink(context, holder.thumb);
                }
            }
            else {
                myUtils.setImgQr(context, holder.thumb);
            }
        }


        if (item.getAccquirer().equals("MVISA")) {
            holder.approval_code.setVisibility(View.GONE);
            holder.invoice_no.setVisibility(View.GONE);
            myUtils.setViewStatusByType(context, Constants.TRANS_TYPE_MVISA, holder.status, holder.amount, Constants.VAYMUONQR.equals(item.getTransactionPushType()));
        }
        else {
            holder.approval_code.setVisibility(View.VISIBLE);
            holder.invoice_no.setVisibility(View.VISIBLE);
            myUtils.setViewStatusByType(context, item.getStatus(), holder.status, holder.amount, Constants.VAYMUONQR.equals(item.getTransactionPushType()));
        }


        if (ConstantsPay.TRX_TYPE_SERVICE.equals(item.getTrxType())) {
            holder.trxtype.setVisibility(View.VISIBLE);
        }
        else {
            holder.trxtype.setVisibility(View.GONE);
        }

        if (typeFragment.equals(TYPE_BANK) || TextUtils.isEmpty(item.getFeedbackStatus())) {
            holder.tvFeedbackStatus.setVisibility(View.GONE);
        }
        else {
            holder.tvFeedbackStatus.setVisibility(View.VISIBLE);
            switch (item.getFeedbackStatus()) {
                case Constants.STATUS_CR_SUCCESS:
                    holder.tvFeedbackStatus.setText(getString(R.string.cashier_rw_success));
                    holder.tvFeedbackStatus.setTextColor(ContextCompat.getColor(context, R.color.green));
                    break;
                case Constants.STATUS_CR_PENDING:
                    holder.tvFeedbackStatus.setText(getString(R.string.cashier_rw_pending));
                    holder.tvFeedbackStatus.setTextColor(ContextCompat.getColor(context, R.color.orange));
                    break;
                case Constants.STATUS_CR_PROCESSING:
                    holder.tvFeedbackStatus.setText(getString(R.string.cashier_rw_processing));
                    holder.tvFeedbackStatus.setTextColor(ContextCompat.getColor(context, R.color.orange));
                    break;
                case Constants.STATUS_CR_DENIED:
                    holder.tvFeedbackStatus.setText(getString(R.string.cashier_rw_denied));
                    holder.tvFeedbackStatus.setTextColor(ContextCompat.getColor(context, R.color.red_1));
                    break;
                default:
                    holder.tvFeedbackStatus.setVisibility(View.GONE);
                    break;
            }
        }
        return convertView;
    }

    private String getString(int resId) {
        return context.getString(resId);
    }

    public class ViewHolder {
        public TextView date;
        public TextView time;
        public TextView status;
        public ImageView thumb;
        public TextView amount;
        public TextView number;
        public TextView approval_code;
        public TextView invoice_no;
        public TextView trxtype;
        public TextView tvFeedbackStatus;
    }
    public class FailTransViewHolder {
        public @BindView(R.id.tv_error_code)    TextView tvErrorCode;
        public @BindView(R.id.tv_amount)        TextView tvAmount;
        public @BindView(R.id.time)             TextView tvTime;
        public @BindView(R.id.number)           TextView tvPan;
        public @BindView(R.id.tv_error_desc) ReadMoreTextView tvDesError;

        public FailTransViewHolder(View view) {
            ButterKnife.bind( this, view);
        }
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = mInflater.inflate(R.layout.item_header, parent, false);
            holder.date = convertView.findViewById(R.id.date);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (typeAdapter == TYPE_ADAPTER_NORMAL) {
            holder.date.setText(((PaymentItem) mData.get(position)).getDate());
        }
        else {
            holder.date.setText(Utils.convertTimestamp(((ItemFailHistory) mData.get(position)).getCreatedDate(), 2));
        }

        return convertView;
    }

    @Override
    public long getHeaderId(int position) {
        if (typeAdapter == TYPE_ADAPTER_NORMAL) {
            return ((PaymentItem) mData.get(position)).getType();
        }
        else {
            return Utils.returnDate(((ItemFailHistory) mData.get(position)).getCreatedDate());
        }
    }
}