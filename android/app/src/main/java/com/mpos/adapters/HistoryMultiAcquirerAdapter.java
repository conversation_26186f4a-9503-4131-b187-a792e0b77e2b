package com.mpos.adapters;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.emilsjolander.components.stickylistheaders.StickyListHeadersAdapter;
import com.mpos.listeners.ItemListeners;
import com.mpos.sdk.core.modelma.TransItemMacq;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.MyUtils;

import java.util.ArrayList;
import java.util.List;

import vn.mpos.R;


public class HistoryMultiAcquirerAdapter extends BaseAdapter implements StickyListHeadersAdapter {

    private static final String TAG = "HistoryMacqAdapter";
    private final List<TransItemMacq> mData;
    private final Context context;
    private final ItemListeners<TransItemMacq> iClickItemChildVov;
    private final LayoutInflater mInflater;

    private final MyUtils myUtils;

    public HistoryMultiAcquirerAdapter(Context context, List<TransItemMacq> arr, ItemListeners<TransItemMacq> iClickItemChildVov){
        this.mData          = arr;
        this.context            = context;
        this.iClickItemChildVov = iClickItemChildVov;

        mInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        myUtils = new MyUtils();
    }


    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView == null) {
            holder = new ViewHolder();
            convertView = mInflater.inflate(R.layout.item_header, parent, false);
            holder.date = convertView.findViewById(R.id.date);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        String date = Utils.convertTimestamp(Long.parseLong(mData.get(position).getCreatedTimestamp()), 2);
        holder.date.setText(date);

        return convertView;
    }

    @Override
    public long getHeaderId(int position) {
        try {
            return Utils.returnDate(Long.parseLong(mData.get(position).getCreatedTimestamp()));
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    public void addMore(List<TransItemMacq> arrNew) {
        if (arrNew == null || arrNew.size() == 0) {
            return;
        }
        ArrayList<TransItemMacq> arrTemp = new ArrayList<>();
        if (mData.size() > 0) {
            arrTemp.addAll(mData);
            mData.clear();
        }
        arrTemp.addAll(arrNew);
        mData.addAll(arrTemp);
        Utils.LOGD(TAG, "swap: size_new=" + mData.size());
        notifyDataSetChanged();
    }

    public List<TransItemMacq> getData() {
        return mData;
    }

    @Override
    public Object getItem(int i) {
        return mData.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        ViewHolder holder;
        TransItemMacq item = mData.get(position);


        if (convertView == null) {
            holder = new ViewHolder();
            convertView = mInflater.inflate(R.layout.item_payment_ctl, viewGroup, false);

            holder.time = convertView.findViewById(R.id.time);
            holder.thumb = convertView.findViewById(R.id.thumb);
            holder.amount = convertView.findViewById(R.id.tv_amount);
            holder.number = convertView.findViewById(R.id.number);
            holder.status = convertView.findViewById(R.id.tv_status);
            holder.approval_code = convertView.findViewById(R.id.approval_code);
            holder.invoice_no = convertView.findViewById(R.id.invoice_no);
            holder.trxtype = convertView.findViewById(R.id.tv_trx_type);
            holder.tvFeedbackStatus = convertView.findViewById(R.id.tv_feedback_status);
            convertView.setTag(holder);
//            convertView.setOnClickListener(view -> iClickItemChildVov.onClick(position, item));
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        try {
            holder.time.setText(Utils.convertTimestamp(Long.parseLong(item.getCreatedTimestamp()), 1));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            holder.time.setText("");
        }

        String builderAmount = Utils.zenMoney(item.getAmount())+ ConstantsPay.CURRENCY_SPACE_PRE;
        holder.amount.setText(builderAmount);

        holder.number.setText(item.getPan());

        String builderCode = context.getString(R.string.approval_code) + " " + (TextUtils.isEmpty(item.getAuthCode())?"":item.getAuthCode());
        holder.approval_code.setText(builderCode);
        String builderVoide = context.getString(R.string.refer_number) + " " + (TextUtils.isEmpty(item.getRrn())?"":item.getRrn());
        holder.invoice_no.setText(builderVoide);

//        myUtils.setImgThumbByTypeCard(context, "", holder.thumb, null);
        myUtils.setImgThumbByTypeCard(context, item.getIssuerCode(), holder.thumb, "");

        myUtils.setViewStatusByStatus(context, item.getStatus(), holder.status);

        if (ConstantsPay.TRX_TYPE_SERVICE.equals(item.getTrxType())) {
            holder.trxtype.setVisibility(View.VISIBLE);
        }
        else {
            holder.trxtype.setVisibility(View.GONE);
        }

        return convertView;
    }

    public class ViewHolder {
        public TextView date;
        public TextView time;
        public TextView status;
        public ImageView thumb;
        public TextView amount;
        public TextView number;
        public TextView approval_code;
        public TextView invoice_no;
        public TextView trxtype;
        public TextView tvFeedbackStatus;
    }

}
