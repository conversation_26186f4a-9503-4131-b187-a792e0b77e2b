package com.mpos.adapters;

import static com.mpos.utils.MyUtils.URL_ASSET_DOMESTIC;

import android.content.Context;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.emilsjolander.components.stickylistheaders.StickyListHeadersAdapter;
import com.mpos.listeners.ItemListeners;
import com.mpos.models.DataHistoryQrNl;
import com.mpos.models.PaymentItemEmart;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;

import java.util.ArrayList;
import java.util.List;

import vn.mpos.R;

public class HistoryQrNlAdapter extends BaseAdapter implements StickyListHeadersAdapter {

    private final String TAG = HistoryQrNlAdapter.class.getSimpleName();

    private final List<DataHistoryQrNl.ResultData.Data> mData;
    private final Context context;
    private final LayoutInflater mInflater;

    public HistoryQrNlAdapter(List<DataHistoryQrNl.ResultData.Data> mData, Context context) {
        this.mData = mData;
        this.context = context;

        mInflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup viewGroup) {
        HistoryQrNlAdapter.ViewHolder holder;
        if (convertView == null) {
            holder = new HistoryQrNlAdapter.ViewHolder();
            convertView = mInflater.inflate(R.layout.item_header, viewGroup, false);
            holder.date = convertView.findViewById(R.id.date);
            convertView.setTag(holder);
        } else {
            holder = (HistoryQrNlAdapter.ViewHolder) convertView.getTag();
        }

        Utils.LOGD(TAG, "mData.get(position).getTime= " + mData.get(position).getTime_created());
        String transDate = DateFormat.format("dd-MM-yyyy", Long.parseLong(mData.get(position).getTime_created()) * 1000).toString();
        holder.date.setText(transDate);

        return convertView;
    }

    @Override
    public long getHeaderId(int i) {
        return 0;
    }

    @Override
    public int getCount() {
        return mData.size();
    }

    @Override
    public Object getItem(int i) {
        return mData.get(i);
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        HistoryEmartAdapter.ViewHolder holder;
        DataHistoryQrNl.ResultData.Data item = mData.get(position);

        if (convertView == null) {
            holder = new HistoryEmartAdapter.ViewHolder();
            convertView = mInflater.inflate(R.layout.item_payment_ctl, viewGroup, false);

            holder.time = convertView.findViewById(R.id.time);
            holder.thumb = convertView.findViewById(R.id.thumb);
            holder.amount = convertView.findViewById(R.id.tv_amount);
            holder.number = convertView.findViewById(R.id.number);
            holder.status = convertView.findViewById(R.id.tv_status);
            holder.approval_code = convertView.findViewById(R.id.approval_code);
            holder.invoice_no = convertView.findViewById(R.id.invoice_no);
            holder.trxtype = convertView.findViewById(R.id.tv_trx_type);
            holder.tvFeedbackStatus = convertView.findViewById(R.id.tv_feedback_status);
            convertView.setTag(holder);
//            convertView.setOnClickListener(view -> iClickItemChildVov.onClick(position, item));
        } else {
            holder = (HistoryEmartAdapter.ViewHolder) convertView.getTag();
        }

        try {
            String transTime = DateFormat.format("HH:mm:ss", Long.parseLong(mData.get(position).getTime_created()) * 1000).toString();
            holder.time.setText(transTime);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            holder.time.setText("");
        }

        String builderAmount = Utils.zenMoney(item.getAmount())+ ConstantsPay.CURRENCY_SPACE_PRE;
        holder.amount.setText(builderAmount);


        holder.approval_code.setText("" + item.getPayment_method_name());

//        String builderCode = context.getString(R.string.approval_code) + " " + (TextUtils.isEmpty(item.getAuthCode())?"":item.getAuthCode());
//        holder.approval_code.setText(builderCode);
//        String builderVoide = context.getString(R.string.refer_number) + " " + (TextUtils.isEmpty(item.getRrn())?"":item.getRrn());
//        holder.invoice_no.setText(builderVoide);

//        myUtils.setImgThumbByTypeCard(context, "", holder.thumb, null);
//        myUtils.setImgThumbByTypeCard(context, item.getIssuerCode(), holder.thumb, "");
        Glide.with(context).load(context.getDrawable(R.drawable.qr_code)).into(holder.thumb);
        handlerTranstatus(holder, mData.get(position).getStatus());
        return convertView;
    }

    private void handlerTranstatus(HistoryEmartAdapter.ViewHolder holder, String status) {
        Utils.LOGD(TAG, "handlerTranstatus " + status);
        switch (status) {
            case "1":
            case "2":
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.not_paid));
                break;
            case "3":
            case "8":
                holder.status.setVisibility(View.VISIBLE);
                holder.trxtype.setVisibility(View.GONE);
                holder.status.setText(context.getString(R.string.txt_title_success));
                break;
            case "4":
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.canceled));
                break;
            case "5":
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.processing));
                break;
            case "6":
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.refunding));
                break;
            case "7":
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.refunded));
                break;
            default:
                holder.trxtype.setVisibility(View.VISIBLE);
                holder.status.setVisibility(View.GONE);
                holder.trxtype.setText(context.getString(R.string.failed));
                break;
        }
    }

    public void addMore(ArrayList<DataHistoryQrNl.ResultData.Data> arrNew) {
        if (arrNew == null || arrNew.size() == 0) {
            return;
        }
        ArrayList<DataHistoryQrNl.ResultData.Data> arrTemp = new ArrayList<>();
        if (mData.size() > 0) {
            arrTemp.addAll(mData);
            mData.clear();
        }
        arrTemp.addAll(arrNew);
        mData.addAll(arrTemp);
        Utils.LOGD(TAG, "swap: size_new=" + mData.size());
        notifyDataSetChanged();
    }

    public class ViewHolder {
        public TextView date;
        public TextView time;
        public TextView status;
        public ImageView thumb;
        public TextView amount;
        public TextView number;
        public TextView approval_code;
        public TextView invoice_no;
        public TextView trxtype;
        public TextView tvFeedbackStatus;
    }
}
