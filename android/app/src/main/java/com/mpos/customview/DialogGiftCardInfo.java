package com.mpos.customview;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.mpos.sdk.core.model.GiftCardInfor;
import com.mpos.sdk.util.Utils;

import vn.mpos.R;

public class DialogGiftCardInfo {

    OnMyClickListener clickListener;
    GiftCardInfor cardInfor;
    Context context;

    public static final String CREATED = "CREATED";
    public static final String ACTIVATED = "ACTIVATED";
    public static final String BLOCKED = "BLOCKED";
    public static final String EXPIRED = "EXPIRED";


    public DialogGiftCardInfo(Context context) {
        this.context = context;
    }

    public void setContentView(@NonNull View view) {
        setContentCustom(view);
    }

    private void setContentCustom(View v) {
        String pan ="";
        if ((cardInfor.getCardData() == null) || TextUtils.isEmpty(cardInfor.getCardData().getPan())) {
            //todo thẻ ko tồn tại trên hệ thống
            pan = context.getString(R.string.tv_card_not_exits);
        } else {
            pan = cardInfor.getCardData().getPan();
        }

        ((TextView) v.findViewById(R.id.tv_gift_card_pan)).setText(context.getString(R.string.tv_giftcard_pan, pan));
        ((TextView) v.findViewById(R.id.tv_gift_card_balance)).setText(context.getString(R.string.tv_giftcard_balance, Utils.zenMoney(cardInfor.getCardData().getBalance()) + " VND"));

        String statusCard = "";
        if (!TextUtils.isEmpty(cardInfor.getCardData().getStatusCode())) {
            statusCard = getStatusCard(cardInfor.getCardData().getStatusCode());
        }
        ((TextView) v.findViewById(R.id.tv_gift_card_statusCode)).setText(context.getString(R.string.tv_giftcard_status, statusCard));

        String timeActiveCard = "";
        if (!TextUtils.isEmpty(cardInfor.getCardData().getTimeActiveCard()) && !cardInfor.getCardData().getTimeActiveCard().equals("0")) {
            timeActiveCard = Utils.convertTimestamp(Long.parseLong(cardInfor.getCardData().getTimeActiveCard()) * 1000, 2);
            ((TextView) v.findViewById(R.id.tv_gift_card_timeActiveCard)).setText(context.getString(R.string.tv_giftcard_timeActiveCard, timeActiveCard));
        } else {
            ((TextView) v.findViewById(R.id.tv_gift_card_timeActiveCard)).setVisibility(View.GONE);
        }

        String timeExpiredCard = "";
        if (!TextUtils.isEmpty(cardInfor.getCardData().getTimeExpiredCard())) {
            timeExpiredCard = Utils.convertTimestamp(Long.parseLong(cardInfor.getCardData().getTimeExpiredCard()) * 1000, 2);
            ((TextView) v.findViewById(R.id.tv_gift_card_timeExpiredCard)).setText(context.getString(R.string.tv_giftcard_timeExpiredCard, timeExpiredCard));
        } else {
            ((TextView) v.findViewById(R.id.tv_gift_card_timeExpiredCard)).setVisibility(View.GONE);
        }

        v.findViewById(R.id.btn_retry_gift_card).setOnClickListener(v1 -> {
            if (clickListener != null) {
                clickListener.clickRetry();
            }
        });
    }

    public void initVariable(GiftCardInfor cardInfor) {
        this.cardInfor = cardInfor;
    }

    public void setClickListener(OnMyClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnMyClickListener{
        void clickRetry();
    }

    private String getStatusCard(String statusCard) {
        String status = "";
        switch (statusCard) {
            case ACTIVATED:
                status = "ĐÃ KÍCH HOẠT";
                break;
            case CREATED:
                status = "ĐÃ TẠO";
                break;
                case BLOCKED:
                    status = "BỊ KHÓA";
                break;
                case EXPIRED:
                    status = "HẾT HẠN";
                break;
            default:
                status = statusCard;
        }
        return status;
    }
}
