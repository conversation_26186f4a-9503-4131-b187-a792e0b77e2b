package com.mpos.customview;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.adapters.SupportAdapter;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.models.ConfigSupport;
import com.mpos.models.SupportObj;
import com.mpos.screen.BaseDialogFragment;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;
import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import vn.mpos.R;

public class DialogSupportHotline extends BaseDialogFragment implements SupportAdapter.IClickItemChildSupport {

    String tag = "DialogSupportHotline";

    @BindView(R.id.rv_after_sale)   RecyclerView rvAfterSale;
    @BindView(R.id.rv_info)         RecyclerView rvGeneral;

    SupportAdapter adapterSale;
    SupportAdapter adapterGeneral;

    MyProgressDialog mPgdl;

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
        setColorStatusBar(R.color.red_1);
		setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
	}
	
	@Override
	public Dialog onCreateDialog(Bundle savedInstanceState) {

		Dialog dialog = super.onCreateDialog(savedInstanceState);
		dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
//		dialog.getWindow().getAttributes().windowAnimations = R.style.updownDialog;
		dialog.getWindow().setLayout(LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT);
		
		return dialog;
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container,
			Bundle savedInstanceState) {

		View view = inflater.inflate(R.layout.dialog_support_hotline, container);
		ViewToolBar vToolBar = new ViewToolBar(context, view);
		vToolBar.showTextTitle(getString(R.string.SUPPORT_SUBHEADER_HOTLINE));
		vToolBar.showButtonBack(true, v -> dismisDialog());

        ButterKnife.bind(this, view);
        /*view.findViewById(R.id.call).setOnClickListener(new View.OnClickListener() {
            @Override
			public void onClick(View v) {
				String number = "tel:" + getString(R.string.SUPPORT_HOTLINE_PHONE_NUMBER);
				Intent callIntent = new Intent(Intent.ACTION_DIAL, Uri.parse(number));
				startActivity(callIntent);
			}
		});*/

		return view;
	}
	
	private void dismisDialog() {
		this.dismiss();
	}


    public void loadConfig(Context context) {
        mPgdl = new MyProgressDialog(context);
	    mPgdl.showLoading();
        Utils.LOGD(tag, "run load file config");
        MposRestClient.getInstance(context).get(Config.URL_CONFIG, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int i, Header[] headers, byte[] bytes) {
                mPgdl.hideLoading();
                String content = new String(bytes);
                Utils.LOGD(tag, "onSuccess: content=" + content);

//                GsonBuilder gsonBuilder = new GsonBuilder();
//                Gson gson = gsonBuilder.create();

                try {
                    JSONObject jRoot = new JSONObject(content);
                    String config = JsonParser.getDataJson(jRoot, "contact");

                    ConfigSupport configSupport = MyGson.parseJson(config, ConfigSupport.class);
                    DataStoreApp.getInstance().savePhoneInTime(configSupport.getIntime());
                    DataStoreApp.getInstance().savePhoneOutTime(configSupport.getOuttime());

                    showData(configSupport);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(int i, Header[] headers, byte[] bytes, Throwable throwable) {
                mPgdl.hideLoading();
                Utils.LOGD(tag, "onFailure: loadconfig----");
                MyDialogShow.showDialogRetryCancel("", getString(R.string.error_timeout_content), context, view -> loadConfig(context), view -> dismiss(), true);
            }
        });
    }

    private void showData(ConfigSupport configSupport) {
        if (configSupport.getSupport() != null) {
            adapterGeneral = new SupportAdapter(context, configSupport.getSupport(), this);
            rvGeneral.setAdapter(adapterGeneral);
            rvGeneral.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        }
        if (configSupport.getAfterSale() != null) {
            adapterSale = new SupportAdapter(context, configSupport.getAfterSale(), this);
            rvAfterSale.setAdapter(adapterSale);
            rvAfterSale.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        }
    }

    @Override
    public void clickItemSupport(SupportObj itemBank) {
        if (Constants.TYPE_EMAIL.equalsIgnoreCase(itemBank.getType())) {
            MyUtils.shareViaEmail(context, itemBank.getValue(), "Cần hỗ trợ từ MPOS",  "");
        }
        else if (Constants.TYPE_PHONE.equalsIgnoreCase(itemBank.getType())) {
            MyUtils.callToSupport(itemBank.getValue(), context);
        }
    }

}
