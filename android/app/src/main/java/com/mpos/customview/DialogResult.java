package com.mpos.customview;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.ApiManager;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.common.MyApplication;
import com.mpos.models.BankInstallmentObj;
import com.mpos.models.BankPeriodObject;
import com.mpos.models.BaseResponse;
import com.mpos.models.DataQrPay;
import com.mpos.models.Promotion;
import com.mpos.models.requests.ReqWarrantyCode;
import com.mpos.screen.ActivitySubLogin;
import com.mpos.screen.BaseDialogFragment;
import com.mpos.screen.mart.EMartPresenter;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataConfirmCard;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ScreenUtils;
import com.pps.core.ToastUtil;

import org.json.JSONObject;

import java.lang.ref.WeakReference;

import butterknife.BindView;
import butterknife.ButterKnife;
import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

import static com.mpos.utils.Constants.RESULT_FAILURE_PAY;

public class DialogResult extends BaseDialogFragment {
    String tag = "DialogResult";

    @BindView(R.id.tv_result)           TextView tvResult;
    @BindView(R.id.tvAmountMoney)       TextView tvAmount;
    @BindView(R.id.llInforApprovedCodeResult) LinearLayout layoutInforApprovedCodeResult;
    @BindView(R.id.layoutParentInfoTrans) LinearLayout layoutParentInfoTrans;
    @BindView(R.id.v_info_service)      LinearLayout vInfoService;
    @BindView(R.id.tv_refno_code_result)  TextView tvRefNoCodeResult;
    @BindView(R.id.tv_service)          TextView tvNameService;
    @BindView(R.id.tv_mobile)           TextView tvMobile;
    @BindView(R.id.tv_title_left)       TextView tvTitleLeft;
    @BindView(R.id.installment_bank)    TextView tvInstallmentBank;
    @BindView(R.id.tv_title_right)      TextView tvTitleRight;
    @BindView(R.id.installment_period)  TextView tvInstallmentPeriod;
    @BindView(R.id.tv_note_after_pay)   TextView tvNoteAfterPay;
    @BindView(R.id.layoutInfoInstallment) LinearLayout layoutParentInstallment;
    @BindView(R.id.view_warranty)       View vWarranty;

    @BindView(R.id.v_feedback)          ConstraintLayout vFeedback;


    @BindView(R.id.quickWithdrawal)     Button btnWithDraw;
    @BindView(R.id.print_receipt)       Button btnPrintReceipt;
    @BindView(R.id.comeback)            Button tvStatusBtn;
//    @BindView(R.id.btn_warranty_send)   Button btnSendWarranty;
//    @BindView(R.id.imb_warranty_qr)     Button btnScanWarranty;

    private String TAG = DialogResult.class.getSimpleName();

    ViewCashierReward vCashierReward;
    ViewWarranty viewWarranty;

	int type   = 0;

    String amount    = "";
    String txID      = "";
    String tranxType = "";
    String authCode = "";
    String refNo = "";

    String transCode = ""; // transaction code QR NL

    boolean isShowAuthCode = false;

	String msgInfo  = "";
	boolean reLogin = false;
    boolean handleClickFinish = false;
    boolean callFinishActivity = true;

    private DataQrPay dataQrPay;
	private DataPay dataPay;
	private DataConfirmCard dataServices;
	private ItfActionAfterSuccess callback;
    private MyProgressDialog mPgdl;
    private SaveLogController logUtil;
    private ToastUtil mToast;

    private BankInstallmentObj bankInstallmentObj;
    private BankPeriodObject bankPeriodObject;
    private Promotion promotion;

    public static final int CLICK_PRINT 			= 1;
    public static final int CLICK_FINISH 			= 2;

	public static final int RESULT_SUCCESS 			= 1;
	public static final int RESULT_FAIL				= 2;
//	public static final int RESULT_SUCCESS_DISMIS	= 3;

    HandlerGuideInstallment handlerGuideInstallment;

    boolean isEnterCard = false;
    String amountEnterCard;
    String txIDEnterCard;

    boolean isHomeEmart = false;
    public Handler handlerCountdownDismissDialog = new Handler();

    public void setHomeEmart(boolean homeEmart) {
        isHomeEmart = homeEmart;
    }
    public void setAmountEnterCard(String amountEnterCard) {
        this.amountEnterCard = amountEnterCard;
    }

    public void setTxIDEnterCard(String txIDEnterCard) {
        this.txIDEnterCard = txIDEnterCard;
    }

    public void setEnterCard(boolean enterCard) {
        isEnterCard = enterCard;
    }

    private static class HandlerGuideInstallment extends Handler {
        private final WeakReference<DialogResult> weakReference;

        public HandlerGuideInstallment(DialogResult mlMap) {
            weakReference = new WeakReference<>(mlMap);
        }

        public void handleMessage(Message msg) {
            switch (msg.what) {
                case Constants.TYPE_GUIDE_INSTALLMENT:
                    if (ScreenUtils.canShowDialog(weakReference.get().context)) {
                        weakReference.get().showGuideByBank();
                    }
                    break;
                case Constants.TYPE_ERROR_INSTALLMENT:
                    if (ScreenUtils.canShowDialog(weakReference.get().context)) {
                        weakReference.get().showDialogErrorInstallment((String) msg.obj);
                    }
                default:
                    break;
            }
        }
    }

    /**
     * for installment
     */
    public static DialogResult newInstance(int typeDialog, String msg, DataPay dataPay, DataConfirmCard data,
                                           BankInstallmentObj bankInstallmentObj, BankPeriodObject bankPeriodObject, Promotion promotion) {
        return newInstance(typeDialog, msg, dataPay, data, false, bankInstallmentObj, bankPeriodObject, promotion);
    }

    /**
     * for normal
     */
//    public static DialogResult newInstance(int typeDialog, String msg, DataPay dataPay, DataConfirmCard data, Promotion promotion) {
//        return newInstance(typeDialog, msg, dataPay, data, false, null, null, promotion);
//    }
    public static DialogResult newInstance(int typeDialog, String msg, DataPay dataPay, DataConfirmCard data) {
        return newInstance(typeDialog, msg, dataPay, data, false, null, null, null);
    }

    public static DialogResult newInstanceForMvisa(int typeDialog, String msg, DataPay dataPay, DataQrPay dataQrPay) {
        return newInstance(typeDialog, msg, dataPay, null, false, null, null, dataQrPay, null);
    }

    public static DialogResult newInstanceForQrNl(int typeDialog, DataQrPay dataQrPay) {
        return newInstance(typeDialog, null, null, null, false, null, null, dataQrPay, null);
    }

    /**
     * for installment
     */
    public static DialogResult newInstance(int typeDialog, String msg, DataPay dataPay, DataConfirmCard data,
                                           boolean reLogin, BankInstallmentObj bankInstallmentObj, BankPeriodObject bankPeriodObject, Promotion promotion) {
        return newInstance(typeDialog, msg, dataPay, data, reLogin, bankInstallmentObj, bankPeriodObject, null, promotion);
    }
    public static DialogResult newInstance(int typeDialog, String msg, DataPay dataPay, DataConfirmCard data,
                                           boolean reLogin, BankInstallmentObj bankInstallmentObj, BankPeriodObject bankPeriodObject, DataQrPay dataQrPay, Promotion promotion) {
        DialogResult dialogResult = new DialogResult();
        Bundle bundle = new Bundle();
        bundle.putInt("typeDialog", typeDialog);
        bundle.putString("msg",     msg);
        bundle.putBoolean("reLogin", reLogin);
        bundle.putSerializable("dataConfirm",data);
        bundle.putSerializable("dataPay",dataPay);
        bundle.putSerializable("dataQrPay",dataQrPay);
        bundle.putSerializable("promotion",promotion);

//        bundle.putString("txID",    txID);
//        bundle.putString("amount",  amount);
//        bundle.putString("tranxType",  tranxType);

        bundle.putSerializable("objInstallment",  bankInstallmentObj);
        bundle.putSerializable("objPeriod",  bankPeriodObject);

        dialogResult.setArguments(bundle);
        return dialogResult;
    }

    @Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
		setColorStatusBar(R.color.gray_white);
	}

	public void setCallback(ItfActionAfterSuccess callback) {
		this.callback = callback;
	}

    public void setHandleClickFinish(boolean handleClickFinish) {
        this.handleClickFinish = handleClickFinish;
    }

    public void setCallFinishActivity(boolean callFinishActivity) {
        this.callFinishActivity = callFinishActivity;
    }

    @Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            type    = bundle.getInt("typeDialog");
            msgInfo = bundle.getString("msg");
            dataPay         = (DataPay) bundle.getSerializable("dataPay");
            dataQrPay       = (DataQrPay) bundle.getSerializable("dataQrPay");
            promotion       = (Promotion) bundle.getSerializable("promotion");
            dataServices    = (DataConfirmCard) bundle.getSerializable("dataConfirm");
            reLogin = bundle.getBoolean("reLogin", false);

            if (dataPay != null) {
                txID        = dataPay.getTxId();
                amount      = dataPay.getAmount();
                tranxType   = dataPay.getTrxType();
                authCode   = dataPay.getAuthCode();
                if (PrefLibTV.getInstance(context).getPermitSocket()) {
                    if (dataPay.getLabel().equals(EMartPresenter.EMART_VOUCHER_CARD)) {
                        isShowAuthCode = true;
                    } else if ((dataPay.getWfDetailRes() != null) && !TextUtils.isEmpty(dataPay.getWfDetailRes().getRrn())){
                        isShowAuthCode = false;
                        refNo = dataPay.getWfDetailRes().getRrn();
                    }
                }
            }
            else if (dataQrPay != null) {
                amount      = dataQrPay.soTienThanhToan;
                if (!TextUtils.isEmpty(dataQrPay.getTxId())) {
                    txID        = dataQrPay.getTxId();
                }
                if (!TextUtils.isEmpty(dataQrPay.getMaGiaoDich())) {
                    transCode        = dataQrPay.getMaGiaoDich().split("-")[0];
                    isShowAuthCode = true;
                }
            } else if (!TextUtils.isEmpty(amountEnterCard)) {
                amount      = amountEnterCard;
                txID        = txIDEnterCard;
            }

            bankInstallmentObj = (BankInstallmentObj) bundle.getSerializable("objInstallment");
            bankPeriodObject   = (BankPeriodObject) bundle.getSerializable("objPeriod");
        }

//        logUtil = new SaveLogController(getActivity());
        logUtil = MyApplication.self().getSaveLogController();
        mPgdl   = new MyProgressDialog(getActivity());
        mToast  = new ToastUtil(getActivity());

        View view = inflater.inflate(R.layout.dialog_result_success, container);
//        View view = inflater.inflate(R.layout.dialog_result_new, container);
        initView(view);

        return view;
    }

    private void initView(View view) {
        ButterKnife.bind(this, view);

        btnWithDraw.setVisibility(View.GONE);

        if (type == RESULT_SUCCESS) {
            tvResult.setText(context.getString(R.string.Successful_transaction));
//            tvResult.setTextColor(ContextCompat.getColor(getActivity(), R.color.tv_color_pay_success));

            boolean typePayAcceptPrint = true;
            boolean canShowNoteAfterPay = false;
            
            // pay service
            if (dataServices != null) {
                canShowNoteAfterPay = true;
                vInfoService.setVisibility(View.VISIBLE);

                if (!TextUtils.isEmpty(dataServices.nameTypeService)) {
                    tvNameService.setText(dataServices.nameTypeService);
                    tvNameService.setVisibility(View.VISIBLE);
                } else {
                    tvNameService.setVisibility(View.GONE);
                }
                if (!TextUtils.isEmpty(dataServices.mobile)) {
                    tvMobile.setText(dataServices.mobile);
                    tvMobile.setVisibility(View.VISIBLE);
                } else {
                    tvMobile.setVisibility(View.GONE);
                }

                String amount = !TextUtils.isEmpty(dataServices.amountPay)?dataServices.amountPay: (!TextUtils.isEmpty(dataServices.amount)?dataServices.amount:"");
                if (!TextUtils.isEmpty(amount)) {
                    showAmountPay(amount);
                }
            }
            // pay installment
            else if (bankInstallmentObj != null) {

                layoutParentInstallment.setVisibility(View.VISIBLE);
                layoutParentInfoTrans.setVisibility(View.GONE);

                tvResult.setText(getString(R.string.dialog_installment_success));

                showAmountPay(amount);
                tvInstallmentBank.setText(bankInstallmentObj.bankName);

                String monthPeriod = "";
                try {
                    monthPeriod = Integer.parseInt(bankPeriodObject.period) > 1 ?
                            getString(R.string.dialog_installment_periods_prefix) : getString(R.string.dialog_installment_period_prefix);
                } catch (NumberFormatException e) {
                    Utils.LOGE(tag, "getCustomView: parse month--", e);
                }

                tvInstallmentPeriod.setText(String.format("%s%s", bankPeriodObject.period, monthPeriod));

                checkShowWarranty();

                /*
                 * show guide installment per bank base on {bankName}
                 */
                handlerGuideInstallment = new HandlerGuideInstallment(this);

                Message message = new Message();
                if (TextUtils.isEmpty(bankInstallmentObj.msgError)) {
                    message.what = Constants.TYPE_GUIDE_INSTALLMENT;
                }
                else {
                    message.what = Constants.TYPE_ERROR_INSTALLMENT;
                    message.obj = bankInstallmentObj.msgError;
                }
                handlerGuideInstallment.sendMessage(message);
            }
            // pay qr code
            else if (dataQrPay!=null){
                layoutParentInstallment.setVisibility(View.VISIBLE);
                layoutParentInfoTrans.setVisibility(View.GONE);

                showAmountPay(dataQrPay.soTienThanhToan);

                if (!TextUtils.isEmpty(dataQrPay.soThe)) {
                    tvTitleLeft.setText(getString(R.string.dialog_mvisa_number_card));
                    tvInstallmentBank.setText(dataQrPay.soThe);
                } else if (!TextUtils.isEmpty(dataQrPay.getPaymentUnit())) {
                    tvTitleLeft.setText(getString(R.string.dialog_qr_nl_type_card));
                    tvInstallmentBank.setText(dataQrPay.getPaymentUnit());
                }

                tvTitleRight.setText(getString(R.string.dialog_mvisa_payment_base_on));
                tvInstallmentPeriod.setText(getString(R.string.qr_code));

                typePayAcceptPrint = false;
            }
            // pay normal
            else {
                canShowNoteAfterPay = true;
                vInfoService.setVisibility(View.GONE);
                showAmountPay(amount);
                if (isEnterCard) {
                    showAmountPay(amountEnterCard);
                }

            }

            if ((DevicesUtil.isP20L() || DevicesUtil.isSP02() || DevicesUtil.isPax()) && typePayAcceptPrint) {
                initViewPrint();
                new Handler().postDelayed(this::processAutoPrint, 300);
            }

            if (canShowNoteAfterPay && dataPay.getWfDetailRes()!=null && dataPay.getWfDetailRes().isFlagNoSignature()) {
                tvNoteAfterPay.setVisibility(View.VISIBLE);
                tvNoteAfterPay.setText(String.format("%s: %s", getString(R.string.txt_notice), getString(R.string.warning_norequired_signature)));
            }
//            if (canShowNoteAfterPay && !DataStoreApp.getInstance().isReceiptFromServer()
//                    && dataPay.getWfDetailRes()!=null && dataPay.getWfDetailRes().isFlagNoSignature())
//            {
//                tvNoteAfterPay.setVisibility(View.VISIBLE);
//                tvNoteAfterPay.setText(String.format("%s: %s", getString(R.string.txt_notice), getString(R.string.warning_norequired_signature)));
//            }


            // check quick draw
            checkQuickDraw();

            initCashierReward();

            if (PrefLibTV.getInstance(context).getPermitSocket()) {
                initApprovedCode();
            }
        }
        // RESULT_FAIL
        else {
            ImageView imvRs = view.findViewById(R.id.ic_result);
            imvRs.setBackgroundResource(R.drawable.ic_fail_big);

            tvResult.setText(context.getString(R.string.invalid_transaction));

            tvResult.setTextColor(ContextCompat.getColor(context, R.color.tv_bg_red_want_money));

            layoutParentInfoTrans.setVisibility(View.INVISIBLE);

            if (reLogin) {
                tvStatusBtn.setText(context.getString(R.string.txt_go_login));
            } else {
                tvStatusBtn.setText(context.getString(R.string.retry_button));
            }
        }

        view.findViewById(R.id.comeback).setOnClickListener(v -> actionBack());
    }

    private void initApprovedCode() {
        if (isShowAuthCode) {
            if (PrefLibTV.getInstance(context).getPermitSocket() && !TextUtils.isEmpty(authCode)) {
                Utils.LOGD(TAG, "authCode=== " + authCode);
                layoutInforApprovedCodeResult.setVisibility(View.VISIBLE);
                tvRefNoCodeResult.setText(getString(R.string.msg_approved_code, authCode));
            } else if (PrefLibTV.getInstance(context).getPermitSocket() && !TextUtils.isEmpty(transCode)) {
                Utils.LOGD(TAG, "transCode=== " + transCode);
                layoutInforApprovedCodeResult.setVisibility(View.VISIBLE);
                tvRefNoCodeResult.setText(getString(R.string.msg_trans_code, transCode));
            } else {
                layoutInforApprovedCodeResult.setVisibility(View.GONE);
            }
        }else {
            if (PrefLibTV.getInstance(context).getPermitSocket() && convertRefNo(refNo)) {
                Utils.LOGD(TAG, "refNo=== " + refNo);
                layoutInforApprovedCodeResult.setVisibility(View.VISIBLE);
                tvRefNoCodeResult.setText(getString(R.string.msg_ref_no_code, refNo.substring(refNo.length()-6)));
            } else {
                layoutInforApprovedCodeResult.setVisibility(View.GONE);
            }
        }
    }

    private boolean convertRefNo(String refNo) {
        logUtil.appendLogAction("Ref No= " + refNo);
        return !TextUtils.isEmpty(refNo) && (refNo.length() >= 6);
    }

    private void showAmountPay(String amount) {
        tvAmount.setText(String.format("%s%s", Utils.zenMoney(amount), ConstantsPay.CURRENCY_SPACE_PRE));
        tvAmount.setVisibility(View.VISIBLE);
    }

    private void checkQuickDraw() {
        String amountMinQuick = DataStoreApp.getInstance().getAmountMinQuick();
        if ((ConstantsPay.TRX_TYPE_NORMAL.equals(tranxType) || dataQrPay != null || !TextUtils.isEmpty(amountEnterCard))
                && DataStoreApp.getInstance().isEnableQuickDrawal()
                && !isEnterCard
                && !ConstantsPay.TRX_TYPE_SERVICE.equals(tranxType)
                && MyUtils.checkMinMaxAmount(amount, amountMinQuick, DataStoreApp.getInstance().getAmountMaxQuick())) {

            btnWithDraw.setVisibility(View.VISIBLE);
            btnWithDraw.setOnClickListener(v -> {
                logUtil.appendLogAction("action: quick with draw");
                DialogQuickWithdrawMoney mdialog = new DialogQuickWithdrawMoney();
                mdialog.setCancelable(false);
                mdialog.initVariable(amount, txID, DataStoreApp.getInstance(), mPgdl, logUtil, mToast, DialogResult.this);
                mdialog.setClickListener(new DialogQuickWithdrawMoney.OnMyClickListener() {
                    @Override
                    public void clickCancel(DialogFragment dialogFragment) {
                        dialogFragment.dismiss();
                    }

                    @Override
                    public void backToMain() {
                    }
                });
                mdialog.show(getChildFragmentManager(), DialogQuickWithdrawMoney.class.getName());
            });
        }
    }

    private void checkShowWarranty() {
        if (promotion == null) {
            return;
        }
        dialog.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        viewWarranty = new ViewWarranty(context, vWarranty, this::sendWarrantyCode);
        vWarranty.setVisibility(View.VISIBLE);
    }

    private void sendWarrantyCode(String code) {
        mPgdl.showLoading();
        ReqWarrantyCode reqWarrantyCode = new ReqWarrantyCode();
        reqWarrantyCode.setWarrantyCode(code);
        reqWarrantyCode.setIsPromotionKarofi(1);
        reqWarrantyCode.setMerchantId(Integer.parseInt(PrefLibTV.getInstance(context).getMerchantsId()));
        reqWarrantyCode.setServiceName(Config.UPDATE_WARRANTY_CODE_TRANSACTION_PROMOTION);
        reqWarrantyCode.setTransactionPromoCodeId(Integer.parseInt(promotion.getTransactionPromoCodeId()));

        String dataSend = MyGson.getGson().toJson(reqWarrantyCode);

        ApiManager apiManager = new ApiManager(context);
        apiManager.sendWarrantyCode(dataSend, new ApiManager.ItfResponseApi<BaseResponse>() {
            @Override
            public void onFailureApi(@NonNull DataError dataError) {
                MyDialogShow.showDialogError(getString(R.string.error_onfaild_request), context);
                mPgdl.hideLoading();
            }

            @Override
            public void onSuccessApi(BaseResponse obj) {
                mPgdl.hideLoading();
                if (obj.getErrObj().code.equals(Config.CODE_REQUEST_SUCCESS)) {
                    MyDialogShow.showDialogInfo(context, getString(R.string.success_send_warranty_code));
                    viewWarranty.disableView();
                }
                else {

                    String msgError = TextUtils.isEmpty(obj.getErrObj().message) ? getString(R.string.error_warranty_code) : obj.getErrObj().message;
                    MyDialogShow.showDialogError(msgError, context);
                }
            }
        });
    }

    private void initCashierReward() {
        vCashierReward = new ViewCashierReward(context, vFeedback, null);
        vCashierReward.checkFeedback(DataStoreApp.getInstance(), amount);
        vCashierReward.setShowBtnConfirm(false);
    }

    private boolean checkHaveInputCashierReward() {
        if (vCashierReward != null && vCashierReward.isShowInputMobile()) {
            String mobileInput = vCashierReward.getMobileInput();
            if (!TextUtils.isEmpty(mobileInput)) {
                if (vCashierReward.validateMobileInput(mobileInput)) {
                    registerFeedbackTrans(vCashierReward.getMobileInput());
                }
                return true;
            }
        }
        return false;
    }

    private void initViewPrint() {
        btnPrintReceipt.setVisibility(View.VISIBLE);
        btnPrintReceipt.setOnClickListener(view -> printerReceipt());
    }

    private void processAutoPrint() {
        if (DataStoreApp.getInstance().isAutoPrint(context)) {
            int numPrintReceipt = DataStoreApp.getInstance().getPrintMoreReceipt();
            Utils.LOGD(TAG, "auto print " + (numPrintReceipt));
            logUtil.appendLogAction("auto print " + (numPrintReceipt));
//            printerReceipt();

            if (!DataStoreApp.getInstance().getIsAutoPrintReceipt()) {
                return;
            }

            if (numPrintReceipt == 0) {
                printerReceipt();
                return;
            }

            for (int i = 1; i <= numPrintReceipt; i++ ){
                new Handler().postDelayed(() -> printerReceipt(), 500);
            }
//            if (numPrintReceipt > 0) {
//                new Handler().postDelayed(() -> printerReceipt(), 500);
//            }
//            if (numPrintReceipt == 2) {
//                new Handler().postDelayed(() -> printerReceipt(), 500);
//            }
        }
    }

    public void registerFeedbackTrans(final String mobileFeedback) {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.FEEDBACK_TRANSACTION);

            // long bao bo 05/12/18
//            jo.put("paymentMethod", txID);
//            jo.put("qrType", txID);

            jo.put("description", dataPay != null ? dataPay.getDesc() : "");
            jo.put("udid", dataPay != null ? dataPay.getUdid() : (dataQrPay != null ? dataQrPay.udid : ""));
            jo.put("deviceIdentifier", DataStoreApp.getInstance().getRegisterId());
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
            jo.put("amount", amount);
            jo.put("mobileUserPhone", mobileFeedback);
            Utils.LOGD(tag, "REQ FEEDBACK_TRANSACTION: "+ jo);
            entity = new StringEntity(jo.toString());

            logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION+" a:"+amount+" m:"+mobileFeedback);
        } catch (Exception ex) {
            Utils.LOGE(tag, "Exception: "+ex.getMessage());
        }

        MposRestClient.getInstance(getActivity()).post(getActivity(), Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading("");
                super.onStart();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                String msgError = null;
                try {
                    mPgdl.hideLoading();
                    JSONObject response = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "RES FEEDBACK_TRANSACTION: " + response);

                    JSONObject jsonObjectErr = new JSONObject(JsonParser.getDataJson(response, "error"));
                    String responseCode = JsonParser.getDataJson(jsonObjectErr, "code");
                    String responseMsg  = JsonParser.getDataJson(jsonObjectErr, "message");

                    Utils.LOGD(tag, "responseCode:"+responseCode);
                    Utils.LOGD(tag, "responseMsg:"+responseMsg);

                    if (!responseCode.equals(Config.CODE_REQUEST_SUCCESS)) {
//                        try {
                            String msg  = getString(R.string.error) + " " + responseCode + ": " + responseMsg;
                            Utils.LOGD(tag, "msg:"+msg);
                            logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION+" Error on Server: "+msg);
                            if (responseCode.equals("8003")) {
                                MyDialogShow.showDialogInfo(getActivity(), msg, true);
                            }
                            else {
                                msgError = msg;
                            }
                    } else {
                        DataStoreApp.getInstance().saveMobileUserPhone(mobileFeedback);
                        logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION+" onSuccessfully!!!");
                        MyDialogShow.showDialogSuccess(context, responseMsg,true);
                    }
                } catch (Exception e) {
                    logUtil.appendLogRequestApi(Config.FEEDBACK_TRANSACTION+" Error on parse: "+e.getMessage());
                    msgError = getString(R.string.error_default_contact_hotline);
                    Utils.LOGE(tag, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    MyDialogShow.showDialogError(null, msgError, getActivity(), false,
                            getString(R.string.txt_go_home), getString(R.string.dialog_error_close),
                            v -> callbackClickAndFinish(), v -> {});
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(tag, "onFailure");
                logUtil.appendLogRequestApiFail(Config.FEEDBACK_TRANSACTION+" onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), getActivity(), v -> registerFeedbackTrans(mobileFeedback), true);
            }
        });
    }

    public void actionBack(){
        logUtil.appendLogAction("action: back");
        if (type == RESULT_SUCCESS) {
            if (!checkHaveInputCashierReward()) {
                Utils.LOGD(tag, "actionBack: -- finish activity");
                dismissDialog();
                stopCountDownDismissDlg();
                MyDialogShow.dismissCurrDialog();
                if (!isHomeEmart) {
                    Utils.LOGD(TAG, "callbackClickAndFinish");
                    callbackClickAndFinish();
                }

            }
        } else if (type == RESULT_FAIL) {
            if (reLogin) {
                MyDialogShow.gotoReLogin(context);
            } else {
                ((Activity) context).setResult(RESULT_FAILURE_PAY);
            }
            finishActivity();
        } else {
            dismissDialog();
        }
    }

    public void dismissDialogFormMart() {
        Utils.LOGD(tag, "dismissDialogFormMart: ");
        if ((dialog != null) && dialog.isShowing()) {
            Utils.LOGD(tag, "dialog isShowing: ");
            logUtil.appendLogAction("dismiss dialog");
            dialog.dismiss();
            stopCountDownDismissDlg();
            if (!isHomeEmart) {
                Utils.LOGD(TAG, "dismissDialogFormMart callbackClickAndFinish");
                callbackClickAndFinish();
            }
        }
    }

    public void stopCountDownDismissDlg() {
        if (handlerCountdownDismissDialog != null) {
            Utils.LOGD(TAG, "stopCountDownDismissDlg");
            handlerCountdownDismissDialog.removeCallbacksAndMessages(null);
            handlerCountdownDismissDialog.removeCallbacks(runnable);
        }
    }

    public void startCoundDismissDialog(int time) {
        if (time == 0) {
            time = 10000;
        }
        handlerCountdownDismissDialog.postDelayed(runnable, time);
    }

    Runnable runnable = () -> {
        if (getShowsDialog()) {
            try {
                Utils.LOGD(TAG, "dismissDialogAtTime dismissCurrDialog");
                actionBack();
            } catch (Exception e) {
                Utils.LOGE(TAG, "dismissDialog err " + e.getMessage());
                if (logUtil != null) {
                    logUtil.appendLogAction("can not dismiss dialog resultPay: " + e.getMessage());
                }
            }
        }
    };

    private void  callbackClickAndFinish() {
        if (handleClickFinish && callback != null) {
            callback.actionClickByType(CLICK_FINISH);
        }
        if (callFinishActivity) {
            finishAndReturnResult();
        }
    }

    private void finishAndReturnResult() {
        if (isEnterCard) {
            Intent returnIntent = new Intent();
            getActivity().setResult(Activity.RESULT_OK, returnIntent);
        }
        finishActivity();
    }

    private void finishActivity() {
        ((Activity) context).finish();
    }

    private void dismissDialog() {
		this.dismiss();
	}


    private void printerReceipt() {
        Utils.LOGD(TAG, "action: print receipt");
        logUtil.appendLogAction("action: print receipt");
        if (callback != null) {
            callback.actionClickByType(CLICK_PRINT);
        }
        else {
            logUtil.appendLogAction("not found callback");
        }
    }

	public interface ItfActionAfterSuccess{
//		void actionClickOkSuccess();
        void actionClickByType(int type);
	}

    /**
     * NOTE: added by noe
     * SHOW WEBVIEW GUIDE PROCESS STEP IN STEP OF INSTALLMENT per BANK
     * url base on >>> http://mpos.vn/public/installment-guide/{bankName}.html
     */
    private void showGuideByBank(){
        final MposDialogInstallmentGuide mposDialogInstallmentGuide = MyUtils.initDialogInstallmentGuide(getActivity(), bankInstallmentObj.bankLongName, bankInstallmentObj.bankName);
        mposDialogInstallmentGuide.setOnClickListenerDialogUnderstand(v -> mposDialogInstallmentGuide.dismiss());
        mposDialogInstallmentGuide.show();
    }

    private void showDialogErrorInstallment(String msg) {

        final MposDialog dialogError = MyUtils.initDialogError_GoToHistory(context,
                getString(R.string.title_error_installment), null);
        dialogError.setDesDialogErrorTop(msg);
        dialogError.setLabelForButtonOk(getString(R.string.dialog_error_close));

        dialogError.setOnClickListenerButtonCancel(v -> {
            dialogError.dismiss();
//            ((Activity) context).setResult(Activity.RESULT_OK);
//            finishActivity();
        });
        dialogError.setOnClickListenerButtonOk(v -> {
            dialogError.dismiss();
            Intent i = new Intent(context, ActivitySubLogin.class);
            startActivity(i);
            ((Activity) context).setResult(Activity.RESULT_OK);
            finishActivity();

        });
        dialogError.show();
    }
}
