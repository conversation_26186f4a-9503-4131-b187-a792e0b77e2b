package com.mpos.customview;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mpos.common.DataStoreApp;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.utils.MyUtils;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import vn.mpos.R;

/**
 * Created by noe on 5/13/16
 */
public class MposDialog extends Dialog {

    private boolean setContentCustom = false;
    private final Context context;

    public final static int TYPE_DIALOG_INFO = 1;
    public final static int TYPE_DIALOG_SUCCESS = 2;
    public final static int TYPE_DIALOG_WARNING = 3;
    public final static int TYPE_DIALOG_ERROR = 4;

    View.OnClickListener onClickCancelListener;
    View.OnClickListener onClickOKListener;
    private boolean isHandlerButtonOK = false;
    public Handler handlerCountdownDismissDialog = new Handler();

    @BindView(R.id.bg_top)
    View bgTop;
    @BindView(R.id.v_space_button)
    View vSpaceButton;
    @BindView(R.id.ic_status)
    ImageView icStatus;
    @BindView(R.id.tv_title_dialog_code_error)
    TextView tvTitleDialogCodeError;

    @BindView(R.id.tv_des_dialog_error)
    TextView tvDesDialogError;
    @BindView(R.id.tv_merchant_info)
    TextView tvMerchantInfo;

    @BindView(R.id.btn_close)
    Button btnClose;
    @BindView(R.id.btn_via_phone)
    Button btnViaPhone;
    @BindView(R.id.btn_via_email)
    Button btnViaEmail;

    @BindView(R.id.btn_cancel)
    Button btnCancel;
    @BindView(R.id.btn_ok)
    Button btnOk;

    @BindView(R.id.layout_contain_two_btn_support)
    LinearLayout vBtnSupport;
    @BindView(R.id.layout_contain_two_button)
    LinearLayout vTwoButton;
    @BindView(R.id.layout_parent_bottom_dialog_contact)
    LinearLayout vButtonAndSupport;

    Unbinder unbinder;

    private String title = "";
    private String desc;
    private int type = 0;

    public MposDialog(@NonNull Context context) {
        super(context);
        this.context = context;
    }

    public MposDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
        this.context = context;
    }

    protected MposDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
        this.context = context;
    }

//    public static MposDialog newInstance(int type, String desc) {
//        return newInstance(type, null, desc);
//    }
//    public static MposDialog newInstance(int type, String title, String desc) {
//
//        Bundle args = new Bundle();
//        args.putInt("type", type);
//        args.putString("title", title);
//        args.putString("desc", desc);
//
//        MposDialog fragment = new MposDialog();
//        fragment.setArguments(args);
//        return fragment;
//    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setType(int type) {
        this.type = type;
        initView();
    }

    /*@Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.dialog_layout_general, container);

        getData();

        initView(view);
        return view;
    }*/

    /*private void getData() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            type    = bundle.getInt("type");
            title   = bundle.getString("title");
            desc    = bundle.getString("desc");
        }

    }*/

    private void initView() {
        if (setContentCustom)
            return;
        setContentCustom = true;
        requestWindowFeature(Window.FEATURE_NO_TITLE);

        setContentView(R.layout.dialog_layout_general);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        setCanceledOnTouchOutside(false);
        setCancelable(false);

        unbinder = ButterKnife.bind(this);

        btnViaPhone.setText(MyUtils.setTextHtml(context.getString(R.string.dialog_error_via_contact)));
        btnViaEmail.setText(MyUtils.setTextHtml(context.getString(R.string.dialog_error_via_email)));

        switch (type) {
            case TYPE_DIALOG_INFO:
                tvTitleDialogCodeError.setText(context.getString(R.string.txt_title_notice));
                bgTop.setBackgroundResource(R.drawable.bg_corner_top_blue);
                icStatus.setBackgroundResource(R.drawable.ic_info_white);
                setVisibleViaPhoneEmail(true);
                break;
            case TYPE_DIALOG_SUCCESS:
                tvTitleDialogCodeError.setText(context.getString(R.string.txt_title_success));
                bgTop.setBackgroundResource(R.drawable.bg_corner_top_green);
                icStatus.setBackgroundResource(R.drawable.ic_success_white);
                setVisibleViaPhoneEmail(false);
                break;
            case TYPE_DIALOG_WARNING:
                tvTitleDialogCodeError.setText(title.isEmpty() ? context.getString(R.string.txt_notice) : title);
                bgTop.setBackgroundResource(R.drawable.bg_corner_top_orange);
                icStatus.setBackgroundResource(R.drawable.ic_notice_white);
                tvDesDialogError.setText(desc);
                setVisibleViaPhoneEmail(true);
                break;
            case TYPE_DIALOG_ERROR:
                tvTitleDialogCodeError.setText(title);
                bgTop.setBackgroundResource(R.drawable.bg_corner_top_red);
                icStatus.setBackgroundResource(R.drawable.ic_fail_red);
                setVisibleViaPhoneEmail(true);
                break;
        }
        showMerchantInfo();
    }

    private void showMerchantInfo() {
        String merchantInfo = DataStoreApp.getInstance().getMerchantInfo();
        if (TextUtils.isEmpty(merchantInfo)) {
            tvMerchantInfo.setVisibility(View.GONE);
        }
        else {
            tvMerchantInfo.setText(merchantInfo);
        }
    }

    @OnClick({R.id.btn_via_email, R.id.btn_via_phone})
    protected void onClickView(View v) {
        if (v.getId() == R.id.btn_via_email) {
            MyUtils.shareViaEmail(context, "User: " + PrefLibTV.getInstance(context).getUserId() + "  SN: " + PrefLibTV.getInstance(context).getSerialNumber() + "  ErrorCode: " + title + "  Content: " + desc);
        } else if (v.getId() == R.id.btn_via_phone) {
            MyUtils.callToSupport(context.getString(R.string.SUPPORT_HOTLINE_PHONE_NUMBER), context);
        }
    }

    public void setVisibleViaPhoneEmail(boolean show) {
        vBtnSupport.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    /**
     * listener when user click button CLOSE
     */
    public void setOnClickListenerDialogClose(View.OnClickListener onClickListener) {
        this.onClickCancelListener = onClickListener;

        btnClose.setOnClickListener(onClickListener);
    }


    public void setButtonCloseLabel(String labelButtonClose) {
        btnClose.setText(labelButtonClose);
    }

    /**
     * set title dialog error
     */
    public void setTitleDialogErrorCode(String title) {
        tvTitleDialogCodeError.setText(title);
    }

    public TextView getTitleDialogErrorCode() {
        return tvTitleDialogCodeError;
    }

    /**
     * set description dialog error on top
     *
     * @param des spanned
     */
    public void setDesDialogErrorTop(Spanned des) {
        tvDesDialogError.setText(des);
    }

    public void setDesDialogErrorTop(String des) {
        tvDesDialogError.setText(des);
    }

    public TextView getDesDialogError() {
        return tvDesDialogError;
    }

    /*public void isError10001(boolean isSwipeCard) {
        if (isSwipeCard) {
            TextView txtDesDetailFix = findViewById(R.id.tv_des_detail_dialog_error);
            Spanned howToFix;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                howToFix = Html.fromHtml(context.getString(R.string.dialog_error_guide_fix), Html.FROM_HTML_MODE_LEGACY, null, new UlTagHandler());
            }
            else {
                howToFix = Html.fromHtml(context.getString(R.string.dialog_error_guide_fix), null, new UlTagHandler());
            }
            txtDesDetailFix.setText(howToFix);
            txtDesDetailFix.setVisibility(View.VISIBLE);

        }
        else {
            findViewById(R.id.tv_des_detail_dialog_error).setVisibility(View.INVISIBLE);
            ((TextView) findViewById(R.id.tv_des_detail_dialog_error)).setText("");

            findViewById(R.id.tv_des_detail_dialog_error).setMinimumHeight(10);

        }
    }*/


    public void setEnableTwoButtonBottom(boolean isShow) {
        if (isShow) {
            btnClose.setVisibility(View.GONE);
            vTwoButton.setVisibility(View.VISIBLE);
        }
    }

    // BUTTON OK

    public Button getBtnOk() {
        return btnOk;
    }

    public void setLabelForButtonOk(String label) {

        btnOk.setText(label);
    }

    public void setOnClickListenerButtonOk(View.OnClickListener onClickListener) {
        this.onClickOKListener = onClickListener;

        btnOk.setOnClickListener(onClickListener);
    }

    // BUTTON CANCEL
    public void setLabelForButtonCancel(String label) {

        btnCancel.setText(label);
    }

    public void setEnableButtonCancel(boolean isShow) {

        if (isShow) {
            btnCancel.setVisibility(View.VISIBLE);
            vSpaceButton.setVisibility(View.VISIBLE);
        } else {
            btnCancel.setVisibility(View.GONE);
            vSpaceButton.setVisibility(View.GONE);
        }
    }

    public void setOnClickListenerButtonCancel(View.OnClickListener onClickListener) {
        this.onClickCancelListener = onClickListener;
        btnCancel.setOnClickListener(onClickListener);
    }

    public void setHandlerButtonOK(boolean handlerButtonOK) {
        isHandlerButtonOK = handlerButtonOK;
    }

    @Override
    public void show() {
        if (PrefLibTV.getInstance(context).get(PrefLibTV.isAutoCloseDialog, Boolean.class)) {
            handlerCountdownDismissDialog.postDelayed(runnable, 10000);
        }
        super.show();
    }

    @Override
    public void dismiss() {
        if (handlerCountdownDismissDialog != null) {
            handlerCountdownDismissDialog.removeCallbacks(runnable);
            handlerCountdownDismissDialog.removeCallbacksAndMessages(null);
        }
        super.dismiss();
    }

    Runnable runnable = () -> {
        if (isShowing()) {
            try {
                if (type == TYPE_DIALOG_ERROR) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                    return;
                }

                if (type == TYPE_DIALOG_INFO) {
                    if (onClickOKListener != null) {
                        onClickOKListener.onClick(null);
                    }
                    return;
                }

                if (onClickOKListener == null) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                    return;
                }

                if (isHandlerButtonOK) {
                    if (onClickCancelListener != null) {
                        onClickCancelListener.onClick(null);
                    }
                }else {
                    if (onClickOKListener != null) {
                        onClickOKListener.onClick(null);
                    }
                }
            } catch (Exception e) {

            }
        }
    };
}
