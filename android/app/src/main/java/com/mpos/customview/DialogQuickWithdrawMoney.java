package com.mpos.customview;

import android.app.Dialog;
import android.graphics.Point;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.DialogFragment;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.common.DataStoreApp;
import com.mpos.common.JsonParser;
import com.mpos.screen.BaseDialogFragment;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Config;
import com.mpos.utils.Constants;
import com.mpos.utils.MyDialogShow;
import com.mpos.utils.MyUtils;
import com.pps.core.MyProgressDialog;
import com.pps.core.ToastUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;
import vn.mpos.R;

/**
 * Created by noe on 4/13/17
 */

public class DialogQuickWithdrawMoney extends BaseDialogFragment {
    String tag = "DialogQuickWithdrawMoney";

    Button buttonConfirm;
    Button buttonCancel;

    TextView tvDesQuickWithdrawal;
    TextView tvCostForService;
    TextView tvDesNote;

    OnMyClickListener clickListener;
    String amount, txID;

    MyProgressDialog mPgdl;
    SaveLogController logUtil;
    ToastUtil mToast;
    DialogResult dialogResult;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // setStyle(STYLE_NO_FRAME, android.R.style.Theme_Translucent);
    }

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
        dialog.getWindow().setLayout(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        return dialog;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_quick_withdrawal, container);
        setContentCustom(view);
        return view;
    }

    @Override
    public void onResume() {
        Window window = getDialog().getWindow();
        if (window != null) {
            Point size = new Point();
            Display display = window.getWindowManager().getDefaultDisplay();
            display.getSize(size);
            window.setLayout((int) (size.x * 0.95), WindowManager.LayoutParams.WRAP_CONTENT);
            window.setGravity(Gravity.CENTER);
        }
        super.onResume();
    }

    public void initVariable(String amount, String txID, DataStoreApp dataStoreApp, MyProgressDialog mPgdl, SaveLogController logUtil, ToastUtil mToast, DialogResult dialogResult) {
        this.amount = amount;
        this.txID = txID;

        this.mPgdl = mPgdl;
        this.logUtil = logUtil;
        this.mToast = mToast;
        this.dialogResult = dialogResult;
    }

    private void setContentCustom(View v) {
        buttonCancel = v.findViewById(R.id.btn_cancel_quick_money);
        buttonConfirm = v.findViewById(R.id.btn_confirm_quick_money);

        tvCostForService = v.findViewById(R.id.tv_des_value_cost_service);
        String amountFee = checkAmountInRange(amount);
        if (!TextUtils.isEmpty(amountFee)) {
            tvCostForService.setText("~" + Utils.zenMoney(amountFee) + ConstantsPay.CURRENCY_SPACE_PRE);
//        tvCostForService.setText("~"+Utils.zenMoney(calculatorMoneyForService(amount)) + ConstantsPay.CURRENCY_SPACE_PRE);
        }

        Spanned desWithdrawal, desNote;
        desWithdrawal = Html.fromHtml(context.getString(R.string.dialog_des));
        desNote = Html.fromHtml(context.getString(R.string.dialog_note));

        tvDesQuickWithdrawal = v.findViewById(R.id.tv_des_dialog_withdrawal);
        tvDesNote = v.findViewById(R.id.tv_des_note);

        tvDesQuickWithdrawal.setText(desWithdrawal);
        tvDesNote.setText(desNote);


        buttonConfirm.setOnClickListener(v12 -> markQuickWithdrawal());

        buttonCancel.setOnClickListener(v1 -> {
            if (clickListener != null) {
                clickListener.clickCancel(DialogQuickWithdrawMoney.this);
            }
        });
    }


    public String checkAmountInRange(String amount) {

        try {
            String amountConvert = MyUtils.convertMoney(amount);
            JSONArray jsonArray = new JSONArray(DataStoreApp.getInstance().getQuickWithdrawList());
            long amountOrigin = Long.parseLong(amountConvert);
            long amountMin;
            long amountMax;
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    amountMin = Long.parseLong(JsonParser.getDataJson(jsonArray.getJSONObject(i), "amountMin", "0"));
                    amountMax = Long.parseLong(JsonParser.getDataJson(jsonArray.getJSONObject(i), "amountMax", "0"));
                    if (amountMin <= amountOrigin && amountOrigin <= amountMax) {
                        String flatFee = JsonParser.getDataJson(jsonArray.getJSONObject(i), "flatFee", "0");
                        String percentFee = JsonParser.getDataJson(jsonArray.getJSONObject(i), "percentageFee", "0");
                        return calculatorMoneyForService(amount, flatFee, percentFee);
                    }
                } catch (NumberFormatException | JSONException e) {
                    e.printStackTrace();
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return "";

    }

    public String calculatorMoneyForService(String amount, String flatFee, String precentFee) {
        /**
         *  PHI_DICH_VU = PHI_CO_DINH (Ex: 35000) + PHAN_TRAM_TREN_TONG_TIEN (Ex: 0.5%)
         */

//        long fixed_money = Long.valueOf(dataStoreApp.getVatQuickWithdrawal());
        long fixed_money = Long.parseLong(flatFee);
//        long amount_a = (long) (Float.valueOf(dataStoreApp.getPercentQuickWithdrawal()) * Long.valueOf(MyUtils.convertMoney(amount)));
        long amount_a = (long) (Float.parseFloat(precentFee) * Long.parseLong(MyUtils.convertMoney(amount)));
        long amount_b = Math.round(1.0 * amount_a / 100);
        amount_b = amount_b + fixed_money;
        return String.valueOf(amount_b);
    }


    public void markQuickWithdrawal() {
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, Config.MARK_QUICK_WITHDRAWAL);
            jo.put("txId", txID);
            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            jo.put("merchantId", PrefLibTV.getInstance(context).getMerchantsId());
            Utils.LOGD(tag, "REQ MARK_QUICK_WITHDRAWAL: " + jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception ex) {
            Utils.LOGE(tag, "Exception: " + ex.getMessage());
        }

        MposRestClient.getInstance(getActivity()).post(getActivity(), Config.URL_GATEWAY_API, entity, Config.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                mPgdl.showLoading("");
                super.onStart();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    mPgdl.hideLoading();
                    JSONObject response = new JSONObject(new String(arg2));
                    Utils.LOGD(tag, "RES MARK_QUICK_WITHDRAWAL: " + response);

                    JSONObject jsonObjectErr = new JSONObject(JsonParser.getDataJson(response, "error"));
                    String responseCode = JsonParser.getDataJson(jsonObjectErr, "code");
                    String responseMsg = JsonParser.getDataJson(jsonObjectErr, "message");

                    Utils.LOGD(tag, "responseCode:" + responseCode);
                    Utils.LOGD(tag, "responseMsg:" + responseMsg);

                    if (!responseCode.equals(Config.CODE_REQUEST_SUCCESS)) {
                        try {
                            if ("0".equals(responseCode)) {
                                Utils.LOGD(tag, "msg:" + responseMsg);
                                logUtil.appendLogRequestApi(Config.MARK_QUICK_WITHDRAWAL + " Error on Server: " + responseMsg);
                                MyDialogShow.showDialogInfo(getActivity(), responseMsg, false, v -> {
                                    if (dialogResult != null) {
                                        dialogResult.actionBack();
                                    } else {
                                        if (clickListener != null) {
                                            clickListener.backToMain();
                                        }
                                    }
                                });
                            } else {
                                String msg = getString(R.string.error) + " " + responseCode + ": " + responseMsg;
                                Utils.LOGD(tag, "msg:" + msg);
                                logUtil.appendLogRequestApi(Config.MARK_QUICK_WITHDRAWAL + " Error on Server: " + msg);
                                MyDialogShow.showDialogError(msg, getActivity());
                            }
                        } catch (Exception e) {
                            Utils.LOGD(tag, "ex:" + e);
                            e.printStackTrace();
                            MyDialogShow.showDialogError("", getActivity());
                        }
                    } else {
                        logUtil.appendLogRequestApi(Config.MARK_QUICK_WITHDRAWAL + " onSuccessfully!!!");
                        mToast.showToast(getString(R.string.tv_quick_money_mark_success));
                        if (dialogResult != null) {
                            dialogResult.actionBack();
                        } else {
                            if (clickListener != null) {
                                clickListener.backToMain();
                            }
                        }
                    }
                } catch (Exception e) {
                    Utils.LOGE(tag, "Exception", e);
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGD(tag, "onFailure");
                logUtil.appendLogRequestApiFail(Config.MARK_QUICK_WITHDRAWAL + " onFailure", arg2);
                mPgdl.hideLoading();
                MyDialogShow.showDialogRetryCancel("", getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), getActivity(), v -> markQuickWithdrawal(), true);
            }
        });
    }


    public void setClickListener(OnMyClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnMyClickListener {
        void clickCancel(DialogFragment dialogFragment);

        void backToMain();
    }
}
