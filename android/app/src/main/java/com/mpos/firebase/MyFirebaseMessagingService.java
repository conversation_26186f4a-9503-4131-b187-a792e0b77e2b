package com.mpos.firebase;

/*
 * Created by anhnguyen on 5/17/18.
 */

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

//import com.facebook.react.bridge.Arguments;
//import com.facebook.react.bridge.WritableMap;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.mpos.common.DataStoreApp;
//import com.mpos.rnmodules.MyReactModule;
import com.mpos.screen.login.LoginActivity;
import com.mpos.sdk.util.Utils;
import com.mpos.utils.Constants;

import vn.mpos.R;


public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "MyFirebaseMsgService";

    @Override
    public void onNewToken(@NonNull String token) {
        super.onNewToken(token);
        Utils.LOGD(TAG, "onNewToken: -->"+token);
        DataStoreApp.getInstance().createRegisterId(token);
    }

    //    public static final int notifyID = 9001;
    /**
     * Called when message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    // [START receive_message]
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        // [START_EXCLUDE]
        // There are two types of messages data messages and notification messages. Data messages are handled
        // here in onMessageReceived whether the app is in the foreground or background. Data messages are the type
        // traditionally used with GCM. Notification messages are only received here in onMessageReceived when the app
        // is in the foreground. When the app is in the background an automatically generated notification is displayed.
        // When the user taps on the notification they are returned to the app. Messages containing both notification
        // and data payloads are treated as notification messages. The Firebase console always sends notification
        // messages. For more see: https://firebase.google.com/docs/cloud-messaging/concept-options
        // [END_EXCLUDE]

        // Not getting messages here? See why this may be: https://goo.gl/39bRNJ
        Log.d(TAG, "From: " + remoteMessage.getFrom());

        // Check if message contains a data payload.
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());

//            if (/* Check if data needs to be processed by long running job */ true) {
                // For long-running tasks (10 seconds or more) use Firebase Job Dispatcher.
//                scheduleJob();
//            } else {
                // Handle message within 10 seconds
//                handleNow();
//            }
//
        }

        // Check if message contains a notification payload.
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
            sendNotification(remoteMessage.getNotification().getBody());
        }

        if (remoteMessage.getData() != null) {
//            sendNotification(remoteMessage.getData().get("message"));

            String msg      =  remoteMessage.getData().get("message");
            String category =  remoteMessage.getData().get("category");
            String udid     =  remoteMessage.getData().get("udid");
            Utils.LOGD(TAG, ">>> category: "+category);
            Utils.LOGD(TAG, ">>> content: "+msg);

            // Send data to RN
//            WritableMap mapNotificationRN = Arguments.createMap();
//            mapNotificationRN.putString("message", msg);
//            mapNotificationRN.putString("category", category);
//            mapNotificationRN.putString("udid", udid);
//            mapNotificationRN.putString("transactionStatus", remoteMessage.getData().get("transactionStatus"));
//            mapNotificationRN.putString("orderCode", remoteMessage.getData().get("orderCode"));
//            MyReactModule.sentEventRN("NOTIFICATION_EVENT", mapNotificationRN);
            //

            if (!TextUtils.isEmpty(category)) {
                //note: MVISA
                //note: category = "MVISA|729216719933";
                //note: Ex: Giao dịch MVISA 500 VND đã được thanh toán thành công MVISA|729216719933
                Utils.LOGD(TAG, "goto ActivityScanQRCode with msg: "+msg+" cat: "+category);

                handlerNotificationMVisa(msg, category, udid);
            } else {
                sendNotification(msg);
            }
        }
    }
    // [END receive_message]


    private void handlerNotificationMVisa(String msg, String category, String udid) {
        Intent intent = new Intent(Constants.IntentFilterNotificationMVISA);
        Utils.LOGD(TAG, "--------------handlerNotificationMVisa   ---------------");
        intent.putExtra("message", msg);
        intent.putExtra("category", category);
        intent.putExtra("udid", udid);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Create and show a simple notification containing the received FCM message.
     *
     * @param messageBody FCM message body received.
     */
    private void sendNotification(String messageBody) {
        Intent intent = new Intent(this, LoginActivity.class);
//        Intent intent = new Intent(this, ActivityLogin.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.putExtra("msg", messageBody);
        PendingIntent pendingIntent;
        pendingIntent = PendingIntent.getActivity(this, 0 /* Request code */, intent,
                PendingIntent.FLAG_IMMUTABLE);

        int defaults = 0;
        defaults = defaults | Notification.DEFAULT_LIGHTS;
        defaults = defaults | Notification.DEFAULT_VIBRATE;
        defaults = defaults | Notification.DEFAULT_SOUND;

        String channelId = getString(R.string.default_notification_channel_id);
        Uri defaultSoundUri= RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        NotificationCompat.Builder notificationBuilder =
                new NotificationCompat.Builder(this, channelId)
                        .setSmallIcon(R.drawable.ic_launcher_material)
                        .setContentTitle(getString(R.string.app_name))
//                        .setContentText(messageBody)
                        .setStyle(new NotificationCompat.BigTextStyle().bigText(messageBody))
                        .setAutoCancel(true)
                        .setSound(defaultSoundUri)
                        .setContentIntent(pendingIntent)
                        .setDefaults(defaults);

        NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Since android Oreo notification channel is needed.
        if (notificationManager!=null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId,
                    getString(R.string.app_name),
                    NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
        }

        if (notificationManager != null) {
            int notifyId = (int)(System.currentTimeMillis()/1000);
            notificationManager.notify(notifyId /* ID of notification */, notificationBuilder.build());
        }
    }
}