<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/home_enter_nestedScroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@id/ll_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <!-- layout for MERCHANT USE MVISA, default GONE -->
            <TextView
                android:id="@+id/home_enter_label_bill_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white_2"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/padding_item"
                android:paddingTop="@dimen/padding_medium"
                android:paddingBottom="@dimen/padding_item"
                android:text="@string/info_order"
                android:textSize="@dimen/font_normal"
                android:textStyle="bold" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_white_stroke_top_bottom"
                android:paddingTop="@dimen/padding_half_item"
                android:paddingBottom="@dimen/padding_half_item">

                <RelativeLayout
                    android:id="@+id/layout_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.mpos.customview.AmountEditText
                        android:id="@+id/edt_money"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:digits="0123456789.,"
                        android:hint="@string/input_amount"
                        android:imeOptions="actionNext"
                        android:inputType="numberDecimal"
                        android:maxLength="11"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/distance_10"
                        android:paddingEnd="@dimen/padding_item"
                        android:paddingBottom="@dimen/distance_10"
                        android:textColor="@color/red_1"
                        android:textSize="@dimen/font_xlarge"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/img_remove_amount"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_centerVertical="true"
                        android:layout_toStartOf="@+id/tv_currency"
                        android:padding="@dimen/distance_7"
                        android:src="@drawable/ic_remove"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_currency"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/padding_item"
                        android:layout_marginTop="@dimen/padding_item"
                        android:layout_marginEnd="@dimen/padding_item"
                        android:layout_marginBottom="@dimen/padding_item"
                        android:gravity="center_vertical"
                        android:text="@string/currency"
                        android:textColor="@color/black"
                        android:textSize="@dimen/font_large" />
                </RelativeLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/v_suggest_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/layout_money"
                    android:background="@color/white_2"
                    android:gravity="center_horizontal"
                    android:paddingStart="@dimen/padding_half_item"
                    android:paddingTop="@dimen/padding_item"
                    android:paddingEnd="@dimen/padding_half_item"
                    android:paddingBottom="@dimen/padding_item"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_amount_sg_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_border_gray"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/padding_half_item"
                        android:paddingEnd="@dimen/padding_item"
                        android:paddingBottom="@dimen/padding_half_item"
                        android:textSize="@dimen/font_normal"
                        android:visibility="gone"
                        tools:text="1,000,000đ"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_amount_sg_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding_item"
                        android:layout_marginEnd="@dimen/padding_item"
                        android:background="@drawable/bg_border_gray"
                        android:paddingStart="@dimen/padding_medium"
                        android:paddingTop="@dimen/padding_half_item"
                        android:paddingEnd="@dimen/padding_medium"
                        android:paddingBottom="@dimen/padding_half_item"
                        android:textSize="@dimen/font_normal"
                        android:visibility="gone"
                        tools:text="10,000,000đ"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_amount_sg_3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_border_gray"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/padding_half_item"
                        android:paddingEnd="@dimen/padding_item"
                        android:paddingBottom="@dimen/padding_half_item"
                        android:textSize="@dimen/font_normal"
                        android:visibility="gone"
                        tools:text="1,000,000,000đ"
                        tools:visibility="visible" />
                </androidx.appcompat.widget.LinearLayoutCompat>

                <View
                    android:id="@+id/tv_fake_line"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_below="@+id/v_suggest_amount"
                    android:background="@color/gray_2" />

                <!--desc-->
                <RelativeLayout
                    android:id="@+id/layout_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_fake_line">

                    <TextView
                        android:id="@+id/title_desc"
                        style="@style/tv_left_constraints"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:text="@string/description"
                        android:textSize="@dimen/font_normal" />

                    <EditText
                        android:id="@+id/edt_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@+id/title_desc"
                        android:background="@null"
                        android:ems="10"
                        android:hint="@string/enter_trans_decs_option"
                        android:imeOptions="actionNext"
                        android:inputType="text"
                        android:maxLength="255"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/padding_item"
                        android:paddingEnd="40dp"
                        android:paddingBottom="@dimen/padding_item"
                        android:textSize="@dimen/font_normal" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/img_remove_description"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/distance_5"
                        android:padding="@dimen/distance_7"
                        android:src="@drawable/ic_remove"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>


            <TextView
                android:id="@+id/home_enter_label_customer_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white_2"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/padding_item"
                android:paddingTop="@dimen/padding_medium"
                android:paddingEnd="@dimen/padding_item"
                android:paddingBottom="@dimen/padding_item"
                android:text="@string/acc_info"
                android:textSize="@dimen/font_normal"
                android:textStyle="bold" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_white_stroke_top_bottom"
                android:paddingTop="@dimen/padding_half_item"
                android:paddingBottom="@dimen/padding_half_item">

                <!--desc-->
                <TextView
                    android:id="@id/textView7"
                    style="@style/tv_left_constraints"
                    android:text="@string/phone"
                    android:textSize="@dimen/font_normal"
                    app:layout_constraintBottom_toBottomOf="@id/layout_phone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/layout_phone" />

                <LinearLayout
                    android:id="@+id/layout_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/textView7"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.mpos.customview.PhoneAutocompleteTextView
                        android:id="@+id/edt_phone"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:completionThreshold="1"
                        android:ems="10"
                        android:hint="@string/phone_input"
                        android:imeOptions="actionNext"
                        android:inputType="phone"
                        android:maxLength="12"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/padding_item"
                        android:paddingEnd="40dp"
                        android:paddingBottom="@dimen/padding_item"
                        android:textSize="@dimen/font_normal" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/icon_remove_phone"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginEnd="@dimen/distance_5"
                        android:padding="@dimen/distance_7"
                        android:src="@drawable/ic_remove"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/iv_cashback_button"
                        android:layout_width="70dp"
                        android:layout_height="38dp"
                        android:layout_marginEnd="@dimen/distance_10"
                        android:scaleType="centerInside"
                        android:src="@drawable/ic_cashback_button"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@color/gray_2"
                    app:layout_constraintLeft_toLeftOf="@id/textView7"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_phone" />

                <!--email-->
                <TextView
                    android:id="@+id/textView4"
                    style="@style/tv_left_constraints"
                    android:text="@string/email"
                    android:textSize="@dimen/font_normal"
                    app:layout_constraintBottom_toBottomOf="@id/layout_email"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/layout_email" />

                <RelativeLayout
                    android:id="@+id/layout_email"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/layout_phone"
                    app:layout_constraintStart_toStartOf="@id/layout_phone"
                    app:layout_constraintTop_toBottomOf="@id/layout_phone">

                    <EditText
                        android:id="@+id/edt_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@null"
                        android:ems="10"
                        android:hint="@string/hint_email"
                        android:imeOptions="actionDone"
                        android:inputType="textEmailAddress"
                        android:maxLength="50"
                        android:paddingStart="@dimen/padding_item"
                        android:paddingTop="@dimen/padding_item"
                        android:paddingEnd="40dp"
                        android:paddingBottom="@dimen/padding_item"
                        android:textSize="@dimen/font_normal" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/img_remove_email"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/distance_5"
                        android:padding="@dimen/distance_7"
                        android:src="@drawable/ic_remove"
                        android:visibility="gone" />
                </RelativeLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_fee_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="35dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="35dp"
                android:gravity="center"
                android:text="@string/content_link_payment_fee"
                android:textColor="@color/red_2"
                android:textSize="13sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_warn_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/distance_15"
                android:layout_marginTop="@dimen/distance_10"
                android:layout_marginEnd="@dimen/distance_15"
                android:text="@string/scan_card_warn_amount"
                android:textColor="@color/gray_4"
                android:textSize="14sp"
                android:textStyle="italic"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/v_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/home_enter_nestedScroll"
        app:layout_constraintVertical_bias="0">


        <LinearLayout
            android:layout_margin="10dp"
            android:id="@+id/container_btn_pay_type"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content">


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btn_link"
                    android:layout_weight="1"
                    style="@style/label_normal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_button_red"
                    android:gravity="center"
                    android:padding="@dimen/distance_12"
                    android:text="@string/button_create_link"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btn_next"
                    style="@style/label_normal"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_button_red"
                    android:gravity="center"
                    android:padding="@dimen/distance_12"
                    android:text="@string/next"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="gone" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_weight="1"
                    android:id="@+id/btn_create_qr_nl"
                    style="@style/label_normal"
                    android:layout_width="0dp"
                    android:layout_margin="5dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/padding_half_item"
                    android:layout_marginEnd="@dimen/padding_half_item"
                    android:background="@drawable/bg_btn_resend_bill"
                    android:gravity="center"
                    android:padding="@dimen/distance_12"
                    android:text="QR CODE"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btn_create_momo_qr_nl"
                    style="@style/label_normal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_button_blue"
                    android:gravity="center"
                    android:padding="@dimen/distance_12"
                    android:text="QR MOMO PAY"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_weight="1"
                    android:id="@+id/btn_create_qr_zalo_nl"
                    style="@style/label_normal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:background="@drawable/bg_btn_void_trans_round_green"
                    android:gravity="center"
                    android:padding="@dimen/distance_12"
                    android:text="QR ZALO PAY"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_link_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/distance_15"
            android:layout_marginEnd="@dimen/distance_15"
            android:layout_marginBottom="@dimen/distance_15"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="gone">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="10dp"
                android:src="@drawable/ic_question" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/content_link_payment"
                android:textColor="@color/gray_4"
                android:textSize="14sp"
                android:textStyle="italic" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btn_pay_now"
            style="@style/ButtonApp"
            android:layout_marginBottom="@dimen/padding_item"
            android:background="@drawable/button_style_orange"
            android:text="@string/PAYMENT_BTN_PAY_NOW"
            android:textAllCaps="true"
            tools:visibility="gone" />

    </LinearLayout>

    <include
        android:id="@+id/v_splash"
        layout="@layout/view_splash"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>