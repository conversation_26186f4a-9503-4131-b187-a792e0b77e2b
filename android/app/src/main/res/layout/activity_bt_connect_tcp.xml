<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_grey_rounded_normal"
    android:orientation="vertical"
    tools:context="com.mpos.screen.mart.ActivityBtConnectTcp">

    <LinearLayout
        android:id="@+id/ll_head_connect_tcp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_guide"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/red_1"
            android:gravity="center_horizontal"
            android:padding="@dimen/mp_padding_medium"
            android:text="@string/txt_select_device"
            android:textColor="@android:color/white"
            android:textSize="@dimen/text_size_normal" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/ll_bt_device_emart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_head_connect_tcp">

        <ListView
            android:id="@+id/paired_devices"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_centerVertical="true"
            android:id="@+id/tv_not_devices_bt"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:padding="@dimen/mp_padding_medium"
            android:text="@string/txt_connect_base"
            android:textColor="@android:color/black"
            android:textSize="@dimen/text_size_normal"
            android:visibility="visible" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/v_button"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:background="@color/bg_title_select_on_act_device"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/mp_padding_half_item"
        android:paddingTop="@dimen/mp_padding_half_item"
        android:paddingRight="@dimen/mp_padding_half_item"
        android:paddingBottom="@dimen/mp_padding_half_item"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_bt_device_emart">

        <Button
            android:id="@+id/btn_scan_printer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/mp_padding_half_item"
            android:layout_marginTop="@dimen/mp_padding_half_item"
            android:layout_marginEnd="@dimen/mp_padding_half_item"
            android:layout_marginBottom="@dimen/mp_padding_half_item"
            android:layout_weight="1"
            android:background="@drawable/btn_orange_rounded"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:minWidth="100dp"
            android:padding="@dimen/mp_padding_half_item"
            android:text="@string/txt_find_base"
            android:textAllCaps="false"
            android:textColor="@drawable/bg_for_text_right"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            android:visibility="visible"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>