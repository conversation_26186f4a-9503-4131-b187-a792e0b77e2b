<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.mpos.screen.mart.ActivitySettingSP04">

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_config_base_pax"
        app:layout_constraintTop_toBottomOf="@+id/ll_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="visible"
        tools:visibility="gone">

        <LinearLayout
            android:id="@+id/ll_info_base"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/padding_item"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <Button
                android:id="@+id/btn_connect_base_sp04"
                android:background="@drawable/btn_red_rounded"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_half_item"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:text="@string/tv_config_wifi_base"
                style="?android:attr/buttonBarButtonStyle" />

            <Button
                android:id="@+id/btn_infor_base_sp04"
                android:background="@drawable/bg_btn_resend_bill"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_half_item"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:text="@string/tv_base_infor"
                style="?android:attr/buttonBarButtonStyle" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_infor_base"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/ll_info_base"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_base_connect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_xhigh"
            android:layout_marginTop="@dimen/padding_normal"
            android:gravity="center"
            android:paddingEnd="@dimen/padding_xhigh"
            android:textSize="@dimen/text_size_larger"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry"
            tools:text="Đã kết nối với Dock: P920UfoThuan" />

        <TextView
            android:id="@+id/tv_ssid_base"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/text_size_larger"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="SSID: 456789" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_update_sp04"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_toolbar"
        tools:visibility="visible">

        <LinearLayout
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_set_auto_print"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/settings_auto_print"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_auto_print"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <net.cachapa.expandablelayout.ExpandableLayout
            android:id="@+id/expandable_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_set_auto_print"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:el_duration="300"
            app:el_expanded="false"
            app:el_parallax="0.5">

            <RadioGroup
                android:id="@+id/radio_gr_print_receipt"
                android:padding="@dimen/padding_item"
                android:layout_marginStart="@dimen/padding_item"
                android:layout_marginEnd="@dimen/padding_item"
                android:gravity="center"
                android:checkedButton="@id/radio_1_receipt"
                android:layout_gravity="center"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content">

                <RadioButton
                    android:gravity="center"
                    android:button="@null"
                    android:padding="@dimen/padding_item"
                    android:id="@+id/radio_1_receipt"
                    android:layout_gravity="center"
                    android:layout_width="0dp"
                    android:text="@string/tv_auto_print_one_receipt"
                    android:background="@drawable/radio_button_custom"
                    android:layout_weight="0.7"
                    android:textColor="@color/text_black_white_radio_btn"
                    android:layout_height="wrap_content"/>

                <View
                    android:layout_weight="0.1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"/>

                <RadioButton
                    android:gravity="center"
                    android:button="@null"
                    android:padding="@dimen/padding_item"
                    android:id="@+id/radio_2_receipt"
                    android:layout_gravity="center"
                    android:layout_width="0dp"
                    android:text="@string/tv_auto_print_two_receipt"
                    android:background="@drawable/radio_button_custom"
                    android:layout_weight="0.7"
                    android:textColor="@color/text_black_white_radio_btn"
                    android:layout_height="wrap_content"/>
                <View
                    android:layout_weight="0.1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"/>

                <RadioButton
                    android:gravity="center"
                    android:button="@null"
                    android:padding="@dimen/padding_item"
                    android:id="@+id/radio_3_receipt"
                    android:layout_gravity="center"
                    android:layout_width="0dp"
                    android:text="@string/tv_auto_print_three_receipt"
                    android:background="@drawable/radio_button_custom"
                    android:layout_weight="0.7"
                    android:textColor="@color/text_black_white_radio_btn"
                    android:layout_height="wrap_content"/>

                <View
                    android:layout_weight="0.1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"/>
            </RadioGroup>
        </net.cachapa.expandablelayout.ExpandableLayout>

        <LinearLayout
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_confirmVoid_tcp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/expandable_layout"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/tv_confirmVoid_tcp"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_confirmVoid_tcp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_hide_statusbbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_confirmVoid_tcp"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/tv_lock_statusbar"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_disable_statusbar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_hide_navbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_hide_statusbbar"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/tv_hide_navbar"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_disable_navbar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <LinearLayout
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_auto_hide_dlg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_hide_navbar"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/tv_auto_close_dlg"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_auto_dissmis_dlg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_margin="@dimen/padding_item"
            android:id="@+id/ll_set_show_full_ui"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_auto_hide_dlg"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:padding="@dimen/distance_12"
                android:textStyle="bold"
                android:text="@string/tv_only_show_ui_wait_order"
                android:layout_width="wrap_content"
                android:textSize="@dimen/text_size_normal"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_only_show_ui_wait_order"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:switchPadding="@dimen/padding_hight"
                android:padding="@dimen/distance_12"
                android:thumb="@drawable/thumb"
                app:track="@drawable/track" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_margin="@dimen/padding_item"
            android:textSize="@dimen/text_size_normal"
            android:id="@+id/btn_check_update"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:padding="@dimen/distance_12"
            style="@style/label_normal"
            android:layout_width="0dp"
            android:gravity="center"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_red_rounded"
            android:text="@string/tv_check_update"
            app:textAllCaps="false"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:visibility="gone"
            tools:visibility="visible"
            android:textColor="@color/white" />

        <LinearLayout
            android:id="@+id/ll_progress_update"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/tv_version_app"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_auto_hide_dlg"
            tools:visibility="visible">

            <ProgressBar
                android:id="@+id/pgb_process_update_app"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="280dp"
                android:layout_height="15dp"
                android:layout_marginStart="@dimen/padding_hight"
                android:layout_marginTop="@dimen/mp_padding_high"
                android:layout_marginEnd="@dimen/padding_hight"
                android:max="100"
                android:progress="0"
                android:progressDrawable="@drawable/progress_bar_horizontal"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_percent_update_app"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mp_padding_medium"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge"
                tools:text="36%"
                tools:visibility="visible" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/mp_padding_medium"
                android:layout_marginTop="@dimen/padding_hight"
                android:layout_marginRight="@dimen/mp_padding_medium"
                android:layout_marginBottom="@dimen/mp_padding_xhigh"
                android:gravity="center"
                android:text="@string/warning_close_app"
                android:textColor="@color/orange_1"
                android:textSize="@dimen/text_size_small" />

        </LinearLayout>

        <TextView
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/btn_check_update"
            android:layout_marginBottom="@dimen/padding_item"
            android:id="@+id/tv_version_app"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_item"
            android:layout_marginEnd="@dimen/padding_item"
            android:gravity="center"
            android:textSize="@dimen/font_large"
            android:textStyle="bold"
            tools:text="Version: 185" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>