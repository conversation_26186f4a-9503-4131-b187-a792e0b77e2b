<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/v_root"
    android:layout_width="380dp"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/white"
    >

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_logo"
        android:layout_width="220dp"
        android:layout_height="54dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/logo_mpos_vcb_printer"
        android:layout_marginStart="@dimen/padding_xxhight"
        android:layout_marginEnd="@dimen/padding_xxhight"
        />

    <TextView
        android:id="@+id/tv_sale"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imv_logo"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textAllCaps="true"
        android:textStyle="bold"
        android:textSize="@dimen/pts_20"
        android:textColor="@android:color/black"
        android:text="sale - thanh toan"
        tools:text="sale - thanh toan"
        />


    <ImageView
        android:id="@+id/v_line1"
        android:layout_width="0dp"
        android:layout_height="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_sale"
        android:background="@drawable/line_dashed"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        />

    <!--date time-->
    <TextView
        android:id="@+id/tv_title_time"
        style="@style/layoutPrintTitle"
        android:text="Date/Time"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line1" />

    <TextView
        android:id="@+id/tv_time"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_time"
        app:layout_constraintTop_toTopOf="@id/tv_title_time"
        android:maxLines="2"
        tools:text="06/12/2022 11:26:28" />

    <!--AppCode-->
    <TextView
        android:id="@+id/tv_title_appcode"
        style="@style/layoutPrintTitle"
        android:text="AppCode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

    <TextView
        android:id="@+id/tv_appcode"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_appcode"
        app:layout_constraintTop_toTopOf="@id/tv_title_appcode"
        tools:text="000000" />

    <!--Card/So The-->
    <TextView
        android:id="@+id/tv_title_card"
        style="@style/layoutPrintTitle"
        android:text="Card/So The"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_appcode" />

    <TextView
        android:id="@+id/tv_pan"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_card"
        app:layout_constraintTop_toTopOf="@id/tv_title_card"
        tools:text="44509300****7837" />

    <!--Type/Loai-->
    <TextView
        android:id="@+id/tv_title_type"
        style="@style/layoutPrintTitle"
        android:text="Type/Loai"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_card" />

    <TextView
        android:id="@+id/tv_type"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_type"
        app:layout_constraintTop_toTopOf="@id/tv_title_type"
        tools:text="VISA" />

    <!--Desc/Mo Ta-->
    <TextView
        android:id="@+id/tv_title_desc"
        style="@style/layoutPrintTitle"
        android:text="Desc/Mo Ta"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_type" />

    <TextView
        android:id="@+id/tv_desc"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_desc"
        app:layout_constraintTop_toTopOf="@id/tv_title_desc"
        tools:text="mo con nha ba ta mo con nha ba ta mo con nha ba ta mo con nha ba ta " />

    <!--TOTAL-->
    <TextView
        android:id="@+id/tv_title_total"
        style="@style/layoutPrintTitle"
        android:text="TOTAL"
        android:textStyle="bold"
        android:textSize="@dimen/pts_20"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/layoutPrintValue"
        android:textStyle="bold"
        android:textSize="@dimen/pts_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_total"
        app:layout_constraintTop_toTopOf="@id/tv_title_total"
        tools:text="1,500,500,500 VND" />

    <!--no required signature-->
    <TextView
        android:id="@+id/tv_no_required_signature"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_total"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center"
        android:padding="@dimen/padding_half_item"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_18"
        android:textAllCaps="true"
        android:text="No signature required /\nKhong yeu cau khach hang ky"
        android:textStyle="bold"
        />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/brr_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_no_required_signature"
        />

    <!--    NO REFUND / KHONG HOAN TRA-->
    <TextView
        android:id="@+id/tv_no_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/brr_bottom"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:text="NO REFUND / KHONG HOAN TRA"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        />


</androidx.constraintlayout.widget.ConstraintLayout>