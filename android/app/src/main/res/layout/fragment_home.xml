<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f2f2f2"
    tools:context="com.mpos.screen.ActivityMainNew">

    <RelativeLayout
        android:id="@+id/main_new_layout_header"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:background="@drawable/ic_header_home">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_top"
            android:layout_width="90dp"
            android:layout_height="30dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/distance_10"
            android:scaleType="centerInside"
            android:src="@drawable/ic_logo"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/imgQRCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_margin="@dimen/padding_item"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/imgQRCode"
            android:layout_marginLeft="@dimen/padding_xhigh"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_xhigh"
            android:layout_marginBottom="@dimen/padding_item"
            android:gravity="center"
            android:text="@string/guilde_user_scan_qrcode"
            android:textColor="@color/white"
            android:textSize="@dimen/font_normal"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgQRCode" />

        <RelativeLayout
            android:id="@+id/main_layout_notify"
            android:layout_width="31dp"
            android:layout_height="27dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginRight="15dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/main_home_icon_notify"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentBottom="true"
                android:src="@drawable/ic_notify" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/main_home_tv_notify"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_alignParentRight="true"
                android:background="@drawable/bg_notify_red"
                android:gravity="center"
                android:text="1"
                android:textColor="@drawable/selector_textview_white"
                android:textSize="@dimen/distance_10"
                android:textStyle="bold" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/ic_setting"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:src="@drawable/ic_settings"
            android:layout_alignParentTop="true"
            android:layout_toStartOf="@id/main_layout_notify"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginEnd="@dimen/padding_medium"
            android:visibility="gone"
            tools:visibility="visible"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/main_layout_account"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_account"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_user" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_account"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/account"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/main_layout_payment_history"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_payment_history"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_time" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_payment_history"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/history"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/main_layout_affiliate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_affiliate"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_affiliate" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_affiliate"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/earn_money"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/main_layout_statistic_shift"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="visible"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_nextlend"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_statistic_shift" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_nextlend"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/statistic_shift"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/main_layout_report"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="40dp"
            android:layout_marginBottom="20dp"
            android:visibility="gone"
            >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/main_icon_report"
                android:layout_width="@dimen/distance_48"
                android:layout_height="@dimen/distance_48"
                android:layout_centerHorizontal="true"
                android:background="@drawable/btn_circel_white"
                android:padding="@dimen/distance_12"
                android:src="@drawable/ic_growth" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/label_normal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/main_icon_report"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/distance_7"
                android:text="@string/report"
                android:textColor="@drawable/selector_textview_white" />
        </RelativeLayout>
    </RelativeLayout>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/scv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/main_new_layout_header"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:focusableInTouchMode="true"
            >

            <include
                android:id="@+id/v_update_config"
                layout="@layout/view_item_update_config"
                android:layout_marginStart="@dimen/distance_12"
                android:layout_marginEnd="@dimen/distance_12"
                android:layout_marginTop="@dimen/distance_5"
                android:layout_marginBottom="@dimen/padding_item"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <include
                android:id="@+id/v_cashier"
                layout="@layout/view_item_cashier"
                android:layout_marginStart="@dimen/distance_12"
                android:layout_marginEnd="@dimen/distance_12"
                android:layout_marginTop="@dimen/distance_5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <LinearLayout
                android:id="@+id/ll_banner"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginTop="@dimen/distance_10">

<!--                <com.synnapps.carouselview.CarouselView-->
<!--                    android:id="@+id/carousel_banner"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    app:indicatorVisibility="gone"-->
<!--                    app:fillColor="#FFFFFFFF"-->
<!--                    app:pageColor="#00000000"-->
<!--                    app:radius="6dp"-->
<!--                    app:slideInterval="7000"-->
<!--                    app:strokeColor="#FF777777"-->
<!--                    app:strokeWidth="1dp" />-->

            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rcv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:padding="@dimen/padding_half_item"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:itemCount="6"
                tools:listitem="@layout/item_grid_home"
                tools:spanCount="3" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vp_banner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tl_banner"
                android:layout_width="wrap_content"
                android:layout_height="10dp"
                android:layout_gravity="center"
                app:tabBackground="@drawable/tab_selector"
                app:tabGravity="center"
                android:visibility="gone"
                app:tabPaddingStart="10dp"
                app:tabPaddingEnd="10dp"
                app:tabIndicatorGravity="center"
                app:tabIndicatorFullWidth="true"
                app:tabIndicatorHeight="10dp"
                app:tabIndicator="@drawable/tab_indicator"
                app:tabIndicatorColor="@color/red_5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginTop="@dimen/padding_half_item"
                />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/padding_item"
                android:layout_marginTop="@dimen/padding_item"
                >
                <LinearLayout
                    android:id="@+id/main_layout_guide"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:paddingTop="@dimen/padding_half_item"
                    android:paddingBottom="@dimen/padding_half_item"
                    android:paddingEnd="@dimen/padding_item"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/main_icon_guide"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="@dimen/distance_5"
                        android:layout_marginEnd="@dimen/distance_5"
                        android:layout_marginBottom="@dimen/distance_5"
                        android:src="@drawable/ic_hdsd" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/main_tv_guide"
                        style="@style/label_small"
                         android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/guide_use"
                        android:textColor="@drawable/selector_textview" />
                </LinearLayout>
                <!--<TextView
                    android:id="@+id/tv_version"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintVertical_bias="0.5"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintStart_toEndOf="@id/main_layout_guide"
                    android:layout_marginEnd="60dp"
                    android:layout_marginStart="@dimen/padding_item"
                    tools:text="phien banphien banphien banphien ban"
                    android:textColor="@drawable/selector_textview"
                    />-->
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!--support-->
    <LinearLayout
        android:id="@+id/main_layout_hotline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:background="@android:color/transparent"
        android:gravity="end|center_vertical"
        >

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/main_icon_hotline"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:background="@drawable/ic_hotline_suport"
            android:gravity="center"
            android:paddingStart="@dimen/padding_item"
            android:paddingEnd="@dimen/padding_medium"
            android:text="@string/support_247"
            android:textColor="@color/red_2"
            android:textSize="11sp"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imv_phone"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:background="@drawable/ic_bg_hotline"
            android:padding="20dp"
            />
    </LinearLayout>

</RelativeLayout>

