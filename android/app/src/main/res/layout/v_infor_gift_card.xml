<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:id="@+id/ll_view_card_info"
    android:background="@color/white">


    <TextView
        style="@style/boldFont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white_light"
        android:gravity="center"
        android:paddingBottom="20dp"
        android:paddingTop="20dp"
        android:text="@string/tv_gift_card_info"
        android:textColor="@color/bg_native" />

    <TextView
        android:layout_marginTop="@dimen/mp_padding_xhigh"
        android:id="@+id/tv_gift_card_pan"
        style="@style/titleFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="pan"
        android:textColor="@color/black" />

    <TextView
        android:layout_marginTop="@dimen/mp_padding_xhigh"
        android:id="@+id/tv_gift_card_balance"
        style="@style/titleFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Balance: 1000"
        android:textColor="@color/black" />

    <TextView
        android:layout_marginTop="@dimen/mp_padding_xhigh"
        android:id="@+id/tv_gift_card_statusCode"
        style="@style/titleFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="ACTIVED"
        android:textColor="@color/black" />

    <TextView
        android:layout_marginTop="@dimen/mp_padding_xhigh"
        android:id="@+id/tv_gift_card_timeActiveCard"
        style="@style/titleFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="timeActiveCard"
        android:textColor="@color/black" />

    <TextView
        android:layout_marginTop="@dimen/mp_padding_xhigh"
        android:id="@+id/tv_gift_card_timeExpiredCard"
        style="@style/titleFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="timeExpiredCard"
        android:textColor="@color/black" />

    <Button
        android:id="@+id/btn_retry_gift_card"
        style="@style/boldFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_hight"
        android:background="@drawable/btn_orange_rounded"
        android:paddingLeft="@dimen/padding_medium"
        android:paddingRight="@dimen/padding_medium"
        android:padding="@dimen/padding_item"
        android:text="@string/btn_retry_gift_card_info" />

</LinearLayout>