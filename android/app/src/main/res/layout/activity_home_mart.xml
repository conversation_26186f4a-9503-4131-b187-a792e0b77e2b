<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="com.mpos.screen.mart.ActivityHomeMart">

    <RelativeLayout
        android:id="@+id/ll_bar_home_mart"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:background="@drawable/ic_header_home"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/ll_setting_home_mart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="right"
            android:gravity="center_vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/mp_padding_item"
                android:layout_toStartOf="@id/btn_logout"
                android:background="@drawable/btn_transparent_img"
                android:padding="@dimen/padding_item"
                android:src="@drawable/ic_baseline_settings_24"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_logout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/padding_medium"
                android:background="@drawable/btn_transparent_img"
                android:padding="@dimen/padding_item"
                android:src="@drawable/ic_baseline_logout" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/contianer_home_bar"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_below="@+id/ll_setting_home_mart"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ll_input_amount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_account"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/plus" />


                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_account"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/tv_new_trans"
                    android:textColor="@drawable/selector_textview_white"
                    android:textSize="@dimen/font_small" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/btn_go_history"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_payment_history"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_time" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_payment_history"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/history"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>


<!--            <Button-->
<!--                android:id="@+id/btn_goto_pushpayment"-->
<!--                style="@style/MyButtonRedFullWidth"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginStart="@dimen/padding_normal"-->
<!--                android:layout_marginTop="@dimen/padding_large"-->
<!--                android:layout_marginEnd="@dimen/padding_normal"-->
<!--                android:layout_marginBottom="@dimen/padding_normal"-->
<!--                android:padding="@dimen/padding_item"-->
<!--                android:text="Thanh toán điểm bán hàng"-->
<!--                android:visibility="gone"-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                app:layout_constraintEnd_toEndOf="parent"-->
<!--                app:layout_constraintStart_toStartOf="parent" />-->


            <RelativeLayout
                android:id="@+id/btn_goto_pushpayment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="gone"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_pushPayment"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_supermarket_new" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_pushPayment"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="TT Điểm bán hàng"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>




            <RelativeLayout
                android:id="@+id/btn_get_info_card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_affiliate"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_affiliate" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_affiliate"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/tv_gift_card"
                    android:textColor="@drawable/selector_textview_white"
                    android:textSize="@dimen/font_small" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/btn_go_deposit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="gone">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_deposit"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_nextlend" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_deposit"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/tv_deposit"
                    android:textColor="@drawable/selector_textview_white"
                    android:textSize="@dimen/font_small" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/main_layout_statistic_shift"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="gone">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/main_icon_nextlend"
                    android:layout_width="@dimen/distance_48"
                    android:layout_height="@dimen/distance_48"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/btn_circel_white"
                    android:padding="@dimen/distance_12"
                    android:src="@drawable/ic_statistic_shift" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/label_normal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/main_icon_nextlend"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/distance_7"
                    android:gravity="center"
                    android:text="@string/statistic_shift"
                    android:textSize="@dimen/font_small"
                    android:textColor="@drawable/selector_textview_white" />
            </RelativeLayout>
        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/container_home_infor_ip"
        android:gravity="center"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_bar_home_mart">

        <RelativeLayout
            android:layout_marginStart="@dimen/padding_item"
            android:layout_gravity="start"
            android:id="@+id/layout_status_deviceConnect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_hight"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_bar_home_mart">

            <ImageView
                android:id="@+id/img_client_connect"
                android:layout_width="@dimen/dot"
                android:layout_height="@dimen/dot"
                android:layerType="software"
                android:scaleType="fitXY"
                android:src="@drawable/dotted_success" />

            <ImageView
                android:id="@+id/img_client_connect_err"
                android:layout_width="@dimen/dot"
                android:layout_height="@dimen/dot"
                android:layerType="software"
                android:scaleType="fitXY"
                android:src="@drawable/dotted_err" />

            <TextView
                android:id="@+id/tv_status_server_accept"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/padding_item"
                android:layout_toEndOf="@+id/img_client_connect"
                android:textSize="@dimen/text_size_large"
                android:textStyle="bold"
                android:text="Không có thiết bị nào được kết nối"
                tools:text="Không có thiết bị nào được kết nối" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_detail_Ip_Tcp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_xhigh"
            android:layout_marginStart="@dimen/padding_large"
            android:layout_marginEnd="@dimen/padding_large"
            android:gravity="center"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_status_deviceConnect"
            tools:text="@string/tv_show_ip_socket" />

        <TextView
            android:id="@+id/tv_infor_device"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.50"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_Ip_GateWay"
            tools:text="sp4esala4 " />

<!--        <TextView-->
<!--            android:id="@+id/tv_Ip_GateWay"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginBottom="@dimen/padding_hight"-->
<!--            android:gravity="center"-->
<!--            android:textSize="@dimen/text_size_large"-->
<!--            android:textStyle="bold"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintHorizontal_bias="0.50"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/tv_detail_Ip_Tcp"-->
<!--            tools:text="Ip Gateway: " />-->
    </LinearLayout>

    <TextView
        android:id="@+id/tv_status_trans_emart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_hight"
        android:layout_marginEnd="@dimen/padding_item"
        android:gravity="center_horizontal"
        android:text="@string/tv_wait_order"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_larger"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/container_home_infor_ip"
        app:layout_constraintVertical_bias="0.501"
        tools:text="loading..." />

    <Button
        android:id="@+id/btn_reset_socket"
        style="@style/MyButtonRedFullWidth"
        android:background="@drawable/btn_red_rounded"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_large"
        android:layout_marginEnd="@dimen/padding_normal"
        android:layout_marginBottom="@dimen/padding_normal"
        android:padding="@dimen/padding_item"
        android:text="@string/tv_check_connect_socket"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <Button
        android:id="@+id/btn_load_ip_socket"
        style="@style/MyButtonRedFullWidth"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_normal"
        android:layout_marginTop="@dimen/padding_large"
        android:layout_marginEnd="@dimen/padding_normal"
        android:layout_marginBottom="@dimen/padding_normal"
        android:padding="@dimen/padding_item"
        android:text="@string/load_ip_socket"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/v_process_giftcard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clickable="true"
        android:elevation="10dp"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:visibility="gone">

        <TextView
            android:id="@+id/tv_guide_giftcard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/font_normal"
            android:text="@string/msg_swipe_gift_card"
            android:textSize="@dimen/font_big"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title_giftcard" />

        <TextView
            android:id="@+id/tv_title_giftcard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/red"
            android:gravity="center"
            android:padding="@dimen/padding_hight"
            android:text="@string/check_giftcard"
            android:textColor="@color/white"
            android:textSize="@dimen/font_normal"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/pgb_giftcard"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:layout_constraintEnd_toStartOf="@id/tv_wait_swipe_giftcard"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_guide_giftcard" />

        <TextView
            android:id="@+id/tv_wait_swipe_giftcard"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_item"
            android:text="@string/msg_wait_giftcard_info"
            android:textColor="@color/green"
            android:textSize="@dimen/font_big"
            app:layout_constraintBottom_toBottomOf="@id/pgb_giftcard"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/pgb_giftcard"
            app:layout_constraintTop_toTopOf="@id/pgb_giftcard" />

        <Button
            android:id="@+id/btn_retry_card"
            style="@style/boldFont"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_hight"
            android:background="@drawable/btn_orange_rounded"
            android:padding="@dimen/padding_item"
            android:paddingLeft="@dimen/padding_medium"
            android:paddingRight="@dimen/padding_medium"
            android:text="@string/retry_button"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_wait_swipe_giftcard" />

        <Button
            android:id="@+id/btn_giftcard_back"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_medium"
            android:background="@drawable/btn_gray_rounded"
            android:padding="@dimen/padding_item"
            android:text="@string/BTN_BACK_TO_HOME"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <include
            android:id="@+id/ll_infor_gift_card"
            layout="@layout/v_infor_gift_card"
            android:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>