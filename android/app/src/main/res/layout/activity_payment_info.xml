<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="fill_parent"
    android:layout_height="match_parent"
    >

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar" />

    <!--button-->
    <LinearLayout
        android:id="@+id/v_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <Button
            android:id="@+id/void_payment"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/item_news_padding"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_void_trans"
            android:gravity="center"
            android:paddingLeft="@dimen/padding_item"
            android:paddingRight="@dimen/padding_item"
            android:text="@string/NAV_BAR_TITLE_VOID_PAYMENT"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_send_mail"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/item_news_padding"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_resend_bill"
            android:gravity="center"
            android:paddingLeft="@dimen/padding_item"
            android:paddingRight="@dimen/padding_item"
            android:text="@string/NAV_BAR_TITLE_RESEND_RECEIPT"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal" />

        <Button
            android:id="@+id/btn_print_receipt"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/item_news_padding"
            android:layout_marginLeft="@dimen/padding_half_item"
            android:layout_weight="1"
            android:background="@drawable/btn_red_rounded"
            android:gravity="center"
            android:text="@string/print_receipt"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:visibility="gone"
            tools:visibility="visible" />

        <Button
            android:id="@+id/btn_continue"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/item_news_padding"
            android:layout_marginLeft="@dimen/padding_half_item"
            android:layout_weight="1"
            android:background="@drawable/btn_blue_rounded"
            android:gravity="center"
            android:text="@string/txt_continue"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <!--quick recei money-->
    <Button
        android:id="@+id/btn_quick_recei_money"
        android:layout_width="fill_parent"
        android:layout_height="55dp"
        android:layout_above="@id/v_button"
        android:layout_marginStart="@dimen/item_news_padding"
        android:layout_marginTop="@dimen/item_news_padding"
        android:layout_marginEnd="@dimen/item_news_padding"
        android:layout_marginBottom="@dimen/item_news_padding"
        android:background="@drawable/bg_quick_confirm"
        android:gravity="center"
        android:paddingLeft="@dimen/padding_item"
        android:paddingRight="@dimen/padding_item"
        android:text="@string/txt_want_money_my_acc"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_normal"
        android:visibility="gone"
        tools:visibility="visible" />

    <!--info-->
    <ScrollView
        android:id="@+id/v_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/ll_toolbar"
        android:layout_above="@id/ll_btn_link">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >
            <!--view info card-->
            <include
                android:id="@+id/v_top_info_card"
                layout="@layout/item_payment_ctl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/v_info_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:padding="@dimen/item_news_padding">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/item_news_padding">

                    <TextView
                        android:id="@+id/tv_card_holder_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_xlarge"
                        android:textStyle="bold"
                        tools:text="card holder name"
                        />

                    <ImageView
                        android:id="@+id/imv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/img_alert_success" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:id="@+id/textView3"
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_BATCH_NO"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_batch_no"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small"
                        tools:text="batchNo"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_TRANS_ID"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_trans_id"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_TRANS_TIME"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_trans_time"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_TRANS_DATE"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_trans_date"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_guide_search"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_margin="@dimen/item_news_padding"
                    android:background="@color/white" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_CARD_APPLICATION"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_card_type"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="card type"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_TID"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_tid"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/item_news_padding"
                    android:paddingRight="@dimen/item_news_padding">

                    <TextView
                        android:layout_width="@dimen/width_left_column"
                        android:layout_height="wrap_content"
                        android:text="@string/SALES_DETAIL_MID"
                        android:textSize="@dimen/text_size_small" />

                    <TextView
                        android:id="@+id/tv_mid"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="@dimen/text_size_small" />
                </LinearLayout>

                <TextView
                    android:id="@+id/description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/item_news_padding"
                    android:text="@string/SALES_DETAIL_NO_DESC"
                    android:textSize="@dimen/text_size_small"
                    android:textStyle="italic" />

            </LinearLayout>

            <!--view info QR NL-->
            <include
                android:id="@+id/v_detail_qr_nl"
                layout="@layout/include_payment_info_qr_nl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <!--view info QR-->
            <include
                layout="@layout/include_payment_info_mvisa_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <!--view info Vaymuon-->
            <include
                layout="@layout/include_payment_info_vaymuon_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="gone"
                />

            <!--view info vimolink-->
            <include
                layout="@layout/include_payment_info_vimolink_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="gone"
                />

            <!--cashier reward-->
            <include layout="@layout/view_cashier_reward"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_item"
                />

        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/ll_btn_link"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_above="@+id/ll_btn_vaymuon"
        android:visibility="gone"
        tools:visibility="gone">

        <Button
            android:id="@+id/btn_link_quick_recei_money"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/bg_btn_void_trans_round_red"
            android:gravity="center"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:text="@string/txt_want_money_my_acc"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:layout_marginBottom="20dp"
            />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_btn_vaymuon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_above="@+id/btn_quick_recei_money"
        android:visibility="gone"
        tools:visibility="visible">

        <Button
            android:id="@+id/btn_vm_check_status"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/bg_btn_void_trans_round_green"
            android:gravity="center"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:text="@string/txt_check_trans"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:layout_marginBottom="10dp"
            />

        <Button
            android:id="@+id/btn_vm_quick_recei_money"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/bg_btn_void_trans_round_red"
            android:gravity="center"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:text="@string/txt_want_money_my_acc"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:layout_marginBottom="10dp"
            />

        <Button
            android:id="@+id/btn_void_vaymuon"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/bg_btn_void_trans_round_border"
            android:gravity="center"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:text="@string/cancel_transaction"
            android:textAllCaps="false"
            android:textColor="@color/gray_4"
            android:textSize="@dimen/text_size_normal"
            android:layout_marginBottom="20dp"
            />

    </LinearLayout>


    <!--<Button
        android:id="@+id/reload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerInParent="true"
        android:layout_centerVertical="true"
        android:text="@string/reload"
        android:visibility="gone" />-->

</RelativeLayout>