<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="8dp"
    android:paddingTop="10dp"
    android:paddingRight="8dp"
    android:paddingBottom="10dp">


    <ImageView
        android:id="@+id/thumb"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:padding="@dimen/corner_item"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_credit_card"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/log_out"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_marginBottom="@dimen/padding_item"
        android:src="@drawable/ic_arrow_gray_right"
        app:layout_constraintBottom_toBottomOf="@id/invoice_no"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginLeft="@dimen/padding_item"
        android:textSize="@dimen/text_size_normal"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@+id/thumb"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="VND 1.000.000 " />

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/gray_black_dark"
        android:textSize="@dimen/font_small"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="11/11/1111" />


    <TextView
        android:id="@+id/number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_weight="1"
        android:textSize="@dimen/text_size_normal"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@+id/tv_amount"
        app:layout_constraintTop_toBottomOf="@+id/tv_amount"
        tools:text="card number" />

    <TextView
        android:id="@+id/approval_code"
        android:layout_width="match_parent"
        android:layout_marginRight="120dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintTop_toBottomOf="@+id/number"
        tools:text="approval code" />

    <TextView
        android:id="@+id/invoice_no"
        android:layout_width="match_parent"
        android:layout_marginRight="120dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintTop_toBottomOf="@+id/approval_code"
        tools:text="invoice no" />

    <TextView
        android:id="@+id/tv_feedback_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintTop_toBottomOf="@+id/invoice_no"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="feedback status"
        android:visibility="gone"
        tools:visibility="visible"
        />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/distance_5"
        android:background="@drawable/bg_border_round_blue"
        android:padding="@dimen/distance_7"
        android:textColor="@color/blue_1"
        android:textSize="@dimen/font_tiny"
        app:layout_constraintTop_toBottomOf="@id/log_out"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_trx_type"
        tools:text="@string/txt_title_success"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_trx_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:background="@drawable/bg_border_round_orange"
        android:padding="@dimen/distance_7"
        android:text="@string/service"
        android:textColor="@color/black"
        android:textSize="@dimen/font_tiny"
        android:visibility="gone"
        app:layout_constraintRight_toLeftOf="@id/tv_status"
        app:layout_constraintTop_toTopOf="@id/tv_status"
        tools:visibility="visible" />


</androidx.constraintlayout.widget.ConstraintLayout>