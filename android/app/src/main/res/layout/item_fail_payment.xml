<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/padding_item"
    android:paddingTop="@dimen/padding_item"
    android:paddingRight="@dimen/padding_item"
    android:paddingBottom="@dimen/padding_item">


    <TextView
        android:id="@+id/tv_error_code"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:background="@drawable/bg_border_round_red"
        android:gravity="center"
        android:padding="@dimen/corner_item"
        android:textSize="@dimen/font_normal"
        android:textStyle="bold"
        android:textColor="@color/red"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Mã\n10007" />

    <TextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginLeft="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_item"
        android:textSize="@dimen/text_size_normal"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@+id/tv_error_code"
        app:layout_constraintTop_toBottomOf="@id/tv_title_pan"
        app:layout_constraintTop_toTopOf="@id/tv_error_code"
        tools:text="VND 1.000.000 " />

    <TextView
        android:id="@+id/time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/gray_black_dark"
        android:textSize="@dimen/font_small"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_amount"
        tools:text="11/11/1111" />


    <TextView
        android:id="@+id/tv_title_pan"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/pan_colon"
        android:textSize="@dimen/text_size_normal"
        app:layout_constraintBottom_toTopOf="@+id/tv_error_desc"
        app:layout_constraintLeft_toLeftOf="@+id/tv_amount"
        app:layout_constraintTop_toBottomOf="@+id/tv_amount" />

    <TextView
        android:id="@+id/number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_half_item"
        android:layout_weight="1"
        android:textSize="@dimen/text_size_normal"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_title_pan"
        app:layout_constraintStart_toEndOf="@id/tv_title_pan"
        app:layout_constraintTop_toTopOf="@id/tv_title_pan"
        tools:text="card number" />

    <com.colormoon.readmoretextview.ReadMoreTextView
        android:id="@+id/tv_error_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_error_code"
        app:colorClickableText="@color/gray"
        app:trimLength="60"
        app:trimExpandedText="@string/read_less"
        app:trimCollapsedText="@string/read_more"
        app:trimMode="trimModeLength"
        tools:text="Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi Chi tiết lỗi"
        />


</androidx.constraintlayout.widget.ConstraintLayout>