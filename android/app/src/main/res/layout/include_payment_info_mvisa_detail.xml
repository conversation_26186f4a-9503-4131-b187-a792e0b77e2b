<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llParentDetailInfoMVisa"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/activity_vertical_margin_haft">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_money"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_money_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="600.000.000 VND" />

    </RelativeLayout>

    <!-- space -->
    <TextView
        style="@style/MyLineSpace"
        android:layout_marginBottom="@dimen/activity_vertical_margin_haft"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="@dimen/activity_vertical_margin_haft" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_cardholdername_transaction"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_cardholdername_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="Nguyen Thanh Trung" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_type_card"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_type_card_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="mVISA" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_number_card"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_number_card_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="4082 **** **** 3736" />

    </RelativeLayout>

    <!-- space -->
    <TextView
        style="@style/MyLineSpace"
        android:layout_marginBottom="8dp"
        android:layout_marginLeft="@dimen/activity_vertical_margin"
        android:layout_marginRight="@dimen/activity_vertical_margin"
        android:layout_marginTop="8dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_code_transaction"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_magiaodich_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="273608026" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_time_create_qrcode"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_time_create_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="09:50, 18-10-2017" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:paddingBottom="@dimen/activity_vertical_margin_haft"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_payment_base_on"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_payment_baseon_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:text="@string/dialog_mvisa_payment_base_on_qr_international"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold" />

    </RelativeLayout>

    <!-- space -->
    <TextView
        style="@style/MyLineSpace"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="8dp"
        android:visibility="gone" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="8dp"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_email"
            android:textColor="#7f7f7f"
            android:textSize="@dimen/text_size_small"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_mvisa_email_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textColor="#7f7f7f"
            android:textSize="@dimen/text_size_small"
            android:textStyle="bold"
            tools:text="<EMAIL>" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.3dp"
        android:layout_marginEnd="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginStart="20dp"
        android:background="#dcdde0"
        android:visibility="gone" />


    <TextView
        android:id="@+id/tv_note_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="4"
        android:paddingEnd="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingStart="20dp"
        android:paddingTop="8dp"
        android:text="@string/txt_note"
        android:textSize="@dimen/text_size_normal"
        android:visibility="gone" />

</LinearLayout>

