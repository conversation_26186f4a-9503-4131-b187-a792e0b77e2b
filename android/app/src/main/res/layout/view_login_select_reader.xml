<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_connect"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_2"
    android:clickable="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/padding_item">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/padding_item"
            android:text="@string/txt_hello"
            android:textColor="@color/black"
            android:textSize="@dimen/font_big"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:paddingTop="@dimen/padding_half_item"
            android:text="@string/or_select_type_pay"
            android:textColor="@android:color/black"
            android:textSize="@dimen/font_small" />

        <TextView
            android:id="@+id/tv_register"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:paddingTop="@dimen/padding_half_item"
            android:text="@string/please_register"
            android:textColor="@android:color/black"
            android:textSize="@dimen/font_small" />
    </LinearLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="false"
            android:orientation="vertical"
            android:padding="@dimen/padding_item">

            <!-- PR - 02 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/v_dspread"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:background="@drawable/bg_select_devices">

                <ImageView
                    android:id="@+id/imv_pr02"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:layout_marginTop="@dimen/padding_item"
                    android:src="@drawable/img_pr02"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_pr02"
                    style="@style/tv_reader_name"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:text="@string/txt_qpos_reader"
                    app:layout_constraintBottom_toTopOf="@+id/tv_2"
                    app:layout_constraintLeft_toRightOf="@id/imv_pr02"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_2"
                    style="@style/tv_reader_name_short"
                    android:layout_marginTop="@dimen/padding_item"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:layout_marginBottom="@dimen/padding_item"
                    android:text="@string/connect_viva_bluetooth"
                    app:layout_constraintBottom_toTopOf="@+id/textView6"
                    app:layout_constraintLeft_toLeftOf="@id/tv_pr02"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_pr02" />

                <TextView
                    android:id="@+id/textView6"
                    style="@style/tv_reader_name_short"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:text="@string/login_by_acc_mpos"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/tv_pr02"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_2" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_guide_pr02"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_red_corner_bottom"
                android:padding="@dimen/padding_item"
                android:text="@string/guide_connect_pr02"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:visibility="gone" />

            <!-- PR - 01 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/v_pinpad"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/padding_item"
                android:background="@drawable/bg_select_devices">

                <ImageView
                    android:id="@+id/imv_pr01"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:layout_marginTop="@dimen/padding_item"
                    android:src="@drawable/img_pr01"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_pr01"
                    style="@style/tv_reader_name"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:text="@string/txt_bluepad_reader"
                    app:layout_constraintBottom_toTopOf="@+id/tv_1"
                    app:layout_constraintLeft_toRightOf="@id/imv_pr01"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_1"
                    style="@style/tv_reader_name_short"
                    android:layout_marginTop="@dimen/padding_item"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:layout_marginBottom="@dimen/padding_item"
                    android:text="@string/connect_viva_bluetooth"
                    app:layout_constraintBottom_toTopOf="@+id/textView5"
                    app:layout_constraintLeft_toLeftOf="@id/tv_pr01"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_pr01" />

                <TextView
                    android:id="@+id/textView5"
                    style="@style/tv_reader_name_short"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:text="@string/login_by_acc_mpos"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/tv_pr01"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_1" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_guide_pr01"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_red_corner_bottom"
                android:padding="@dimen/padding_item"
                android:text="@string/guide_connect_pr01"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:visibility="gone" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rltNotNeedDevice"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/padding_item"
                android:background="@drawable/bg_select_devices">

                <ImageView
                    android:id="@+id/imv_qr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/padding_item"
                    android:src="@drawable/img_qr_mpos"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_qr"
                    style="@style/tv_reader_name"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:text="@string/txt_pay_with_qr"
                    app:layout_constraintBottom_toTopOf="@+id/tv_3"
                    app:layout_constraintLeft_toRightOf="@id/imv_qr"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_3"
                    style="@style/tv_reader_name_short"
                    android:layout_marginTop="@dimen/padding_item"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:layout_marginBottom="@dimen/padding_item"
                    android:text="@string/no_need_connect_viva_bluetooth"
                    app:layout_constraintBottom_toTopOf="@+id/textView7"
                    app:layout_constraintLeft_toLeftOf="@id/tv_qr"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_qr" />

                <TextView
                    android:id="@+id/textView7"
                    style="@style/tv_reader_name_short"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:text="@string/login_by_email"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/tv_qr"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_3" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- AR -->

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/v_audio"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/padding_item"
                android:background="@drawable/bg_select_devices">

                <ImageView
                    android:id="@+id/imv_ar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/padding_item"
                    android:src="@drawable/img_ar01"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_ar"
                    style="@style/tv_reader_name"
                    android:layout_marginStart="@dimen/padding_item"
                    android:layout_marginLeft="@dimen/padding_item"
                    android:text="@string/txt_audio_reader"
                    app:layout_constraintBottom_toTopOf="@+id/tv_4"
                    app:layout_constraintLeft_toRightOf="@id/imv_ar"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed" />

                <TextView
                    android:id="@+id/tv_4"
                    style="@style/tv_reader_name_short"
                    android:layout_marginTop="@dimen/padding_item"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:layout_marginBottom="@dimen/padding_item"
                    android:text="@string/connect_viva_audio"
                    app:layout_constraintBottom_toTopOf="@+id/textView8"
                    app:layout_constraintLeft_toLeftOf="@id/tv_ar"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_ar" />

                <TextView
                    android:id="@+id/textView8"
                    style="@style/tv_reader_name_short"
                    android:layout_marginEnd="@dimen/padding_item"
                    android:layout_marginRight="@dimen/padding_item"
                    android:text="@string/login_by_acc_mpos"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/tv_ar"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_4" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_guide_ar01"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_red_corner_bottom"
                android:padding="@dimen/padding_item"
                android:text="@string/guide_connect_ar01"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>


    <TextView style="@style/MyLineSpace" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/orange"
        android:padding="@dimen/padding_item"
        android:text="@string/txt_guide_connect_devices"
        android:textColor="@color/white"
        android:textSize="@dimen/font_small"
        android:visibility="gone"
        tools:visibility="visible" />


</LinearLayout>
