<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content" >

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar" 
        />
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/ll_toolbar"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical" >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white_dark"
            android:gravity="center"
            android:paddingBottom="20dp"
            android:paddingTop="20dp"
            android:text="@string/SEND_RECEIPT_TITLE_RESEND"
            android:textColor="@color/bg_native"
            android:textSize="@dimen/text_size_normal" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="35dp"
            android:gravity="center"
            android:orientation="vertical" >

            <EditText
		        android:id="@+id/edt_email"
		        android:layout_width="match_parent"
		        android:layout_height="wrap_content"
		        android:background="@null"
		        android:drawableLeft="@drawable/ic_email"
		        android:drawablePadding="@dimen/padding_item"
		        android:ems="10"
		        android:hint="@string/email_receive_invoice"
		        android:imeOptions="actionNext"
		        android:inputType="textPersonName"
		        android:padding="@dimen/padding_item"
		        android:textColor="@color/black_light"
		        android:textCursorDrawable="@null"
		        android:gravity="center_vertical"
		        android:textSize="@dimen/text_size_normal" />

            <LinearLayout
		        android:id="@+id/line1"
		        style="@style/MyLineSpace"
		        android:orientation="vertical" />
            
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/padding_item"
                android:gravity="center" >

                <!-- <Button
                    android:id="@+id/btn_print"
                    android:layout_width="120dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/padding_item"
                    android:background="@drawable/custom_btn_red"
                    android:text="Print"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_normal"
                    android:visibility="gone" /> -->

                <Button
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_margin="@dimen/padding_item"
                    style="@style/MyBtnGray"
                    android:padding="@dimen/padding_medium"
                    android:text="@string/BTN_CANCEL"
                    android:textSize="@dimen/text_size_normal"
                    android:visibility="visible" />
                
                <Button
                    android:id="@+id/send_receipt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_margin="@dimen/padding_item"
                    android:background="@drawable/btn_orange_rounded"
                    android:text="@string/SEND_RECEIPT_BTN_SEND_RECEIPT"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_normal"
                    android:padding="@dimen/padding_medium"
                    android:gravity="center"
                    />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="#C0C0C0C0"
        android:clickable="true"
        android:gravity="bottom"
        android:visibility="gone" >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#c0b60808"
            android:gravity="center_horizontal"
            android:padding="15dp"
            android:textColor="#ffffff"
            android:textSize="32sp"
            android:textStyle="bold" />
    </LinearLayout>

</RelativeLayout>