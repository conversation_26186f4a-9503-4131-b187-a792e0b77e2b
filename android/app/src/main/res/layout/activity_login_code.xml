<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/white" >

    <include
        android:id="@+id/ll_toolbar"
        layout="@layout/tool_bar" />
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ll_toolbar"
        android:layout_marginTop="35dp"
        android:gravity="center"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:visibility="gone" />

        <EditText
            android:id="@+id/user_active_code"
            style="@style/EdtInputBase"
            android:drawableLeft="@drawable/ic_lock"
            android:hint="@string/activation_code"
            android:inputType="numberPassword"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_size_normal" />

        <LinearLayout
            style="@style/MyLineSpace"
            android:orientation="vertical" />

        <EditText
            android:id="@+id/user_pin"
            style="@style/EdtInputBase"
            android:drawableLeft="@drawable/ic_lock"
            android:hint="@string/LOGIN_LBL_STAFF_PIN_NEW"
            android:inputType="numberPassword"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_size_normal" />

        <LinearLayout
            style="@style/MyLineSpace"
            android:orientation="vertical" />

        <EditText
            android:id="@+id/user_pin_b"
            style="@style/EdtInputBase"
            android:drawableLeft="@drawable/ic_lock"
            android:hint="@string/LOGIN_LBL_STAFF_PIN_NEW_RETYPE"
            android:inputType="numberPassword"
            android:textCursorDrawable="@null"
            android:textSize="@dimen/text_size_normal" />

        <LinearLayout
            style="@style/MyLineSpace"
            android:orientation="vertical" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content" 
            android:layout_marginTop="@dimen/padding_large"
            android:padding="@dimen/padding_item"
            >
            <Button
                android:id="@+id/bcancel"
                style="@style/MyButton"
                android:background="@drawable/bg_view_gray"
                android:textColor="@color/gray_dark"
                android:layout_weight="1"
                android:text="@string/BTN_CANCEL" />
			<LinearLayout 
                android:id="@+id/v_space"
                android:layout_width="@dimen/padding_item"
                android:layout_height="match_parent"
                android:visibility="visible"
                android:orientation="vertical"/>
            <Button
                android:id="@+id/bcontinue"
                style="@style/MyButtonRedFullWidth"
                android:layout_weight="1"
                android:text="@string/LOGIN_BTN_CONTINUE" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="false"
        android:layout_alignParentTop="false"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/item_news_padding" >

        <!-- android:background="@drawable/btn_share" -->

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/NAV_BAR_TITLE_SUPPORT"
            android:textColor="@color/white" />
    </LinearLayout>

</RelativeLayout>