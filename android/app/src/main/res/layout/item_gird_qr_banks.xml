<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/padding_half_item"
    android:background="@drawable/btn_corner_border_white"
    android:gravity="center"
    android:orientation="vertical"
    android:elevation="@dimen/distance_4"
    android:padding="@dimen/padding_half_item">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_icon"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:src="@drawable/ic_bus"
        android:scaleType="centerInside"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_minimum"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:textStyle="bold"
        android:textColor="@color/color_text"
        android:textSize="@dimen/text_size_small"
        tools:text="VCB" />
</LinearLayout>
