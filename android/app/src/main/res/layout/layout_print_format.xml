<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root"
    android:layout_width="380dp"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_logo"
        android:layout_width="250dp"
        android:layout_height="60dp"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginEnd="@dimen/padding_item"
        android:background="@drawable/logo_mpos_vcb_printer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_mc_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imv_logo"
        tools:text="NGUU MA VUONG" />

    <TextView
        android:id="@+id/tv_mc_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mc_name"
        tools:text="Address cua mc nhe, thu 2 dong cai xem sao, mãi không xuống dòng nhỉ" />

    <TextView
        android:id="@+id/tv_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mc_address"
        tools:text="Username" />

    <TextView
        android:id="@+id/tv_title_bill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:text="sale - thanh toan"
        android:textAllCaps="true"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_username"
        tools:text="sale - thanh toan" />


    <ImageView
        android:id="@+id/v_line1"
        android:layout_width="0dp"
        android:layout_height="2dp"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        android:background="@drawable/line_dashed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_bill" />

    <!--mid-->
    <TextView
        android:id="@+id/tv_title_mid"
        style="@style/layoutPrintTitle"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        android:text="MID"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line1" />

    <TextView
        android:id="@+id/tv_mid"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_mid"
        app:layout_constraintTop_toTopOf="@id/tv_title_mid"
        tools:text="000000060104791" />

    <!--tid-->
    <TextView
        android:id="@+id/tv_title_tid"
        style="@style/layoutPrintTitle"
        android:text="TID"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mid" />

    <TextView
        android:id="@+id/tv_tid"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_tid"
        app:layout_constraintTop_toTopOf="@id/tv_title_tid"
        tools:text="10122233" />

    <!--date time-->
    <TextView
        android:id="@+id/tv_title_time"
        style="@style/layoutPrintTitle"
        android:text="Date/Time"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_tid" />

    <TextView
        android:id="@+id/tv_time"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_time"
        app:layout_constraintTop_toTopOf="@id/tv_title_time"
        tools:text="06/12/2022 11:26:28" />

    <!--Lat/Long-->
    <TextView
        android:id="@+id/tv_title_latlng"
        style="@style/layoutPrintTitle"
        android:text="Lat/Long"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

    <TextView
        android:id="@+id/tv_latlng"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_latlng"
        app:layout_constraintTop_toTopOf="@id/tv_title_latlng"
        tools:text="105.862012/105.862012" />

    <!--Batch No-->
    <TextView
        android:id="@+id/tv_title_batch"
        style="@style/layoutPrintTitle"
        android:text="Batch No"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_latlng" />

    <TextView
        android:id="@+id/tv_batch"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_batch"
        app:layout_constraintTop_toTopOf="@id/tv_title_batch"
        tools:text="000022" />

    <!--Invoice No-->
    <TextView
        android:id="@+id/tv_title_invoice"
        style="@style/layoutPrintTitle"
        android:text="Invoice No"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_batch" />

    <TextView
        android:id="@+id/tv_invoice"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_invoice"
        app:layout_constraintTop_toTopOf="@id/tv_title_invoice"
        tools:text="000217" />

    <!--Ref No-->
    <TextView
        android:id="@+id/tv_title_ref"
        style="@style/layoutPrintTitle"
        android:text="Ref No"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_invoice" />

    <TextView
        android:id="@+id/tv_ref"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_ref"
        app:layout_constraintTop_toTopOf="@id/tv_title_ref"
        tools:text="167029721662" />

    <!--AppCode-->
    <TextView
        android:id="@+id/tv_title_appcode"
        style="@style/layoutPrintTitle"
        android:text="AppCode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_ref" />

    <TextView
        android:id="@+id/tv_appcode"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_appcode"
        app:layout_constraintTop_toTopOf="@id/tv_title_appcode"
        tools:text="167029" />

    <!--Card/So The-->
    <TextView
        android:id="@+id/tv_title_card"
        style="@style/layoutPrintTitle"
        android:text="Card/So The"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_appcode" />

    <TextView
        android:id="@+id/tv_pan"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_card"
        app:layout_constraintTop_toTopOf="@id/tv_title_card"
        tools:text="44509300****7837" />

    <TextView
        android:id="@+id/tv_card_holdername"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_pan"
        tools:text="holdername holdername holdername holdername    /" />

    <!--Type/Loai-->
    <TextView
        android:id="@+id/tv_title_type"
        style="@style/layoutPrintTitle"
        android:text="Type/Loai"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_card_holdername" />

    <TextView
        android:id="@+id/tv_type"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_type"
        app:layout_constraintTop_toTopOf="@id/tv_title_type"
        tools:text="VISA" />


    <!--APPL-->
    <TextView
        android:id="@+id/tv_title_appl"
        style="@style/layoutPrintTitle"
        android:text="APPL"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_type" />

    <TextView
        android:id="@+id/tv_appl"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_appl"
        app:layout_constraintTop_toTopOf="@id/tv_title_appl"
        tools:text="HSBC PAYWAVE" />

    <!--AID-->
    <TextView
        android:id="@+id/tv_title_aid"
        style="@style/layoutPrintTitle"
        android:text="AID"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_appl" />

    <TextView
        android:id="@+id/tv_aid"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_aid"
        app:layout_constraintTop_toTopOf="@id/tv_title_aid"
        tools:text="A0000000031010" />

    <!--ARQC-->
    <TextView
        android:id="@+id/tv_title_arqc"
        style="@style/layoutPrintTitle"
        android:text="ARQC"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_aid" />

    <TextView
        android:id="@+id/tv_arqc"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_arqc"
        app:layout_constraintTop_toTopOf="@id/tv_title_arqc"
        tools:text="3DFF481647F9CDC8" />

    <!--ID Deposit-->
    <TextView
        android:visibility="gone"
        android:id="@+id/tv_title_id_deposit"
        style="@style/layoutPrintTitle"
        android:text="Ma dat coc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_arqc" />

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_id_deposit"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_id_deposit"
        app:layout_constraintTop_toTopOf="@id/tv_title_id_deposit"
        tools:text="3DFF481647F9CDC8" />

    <!--Amount Deposit-->
    <TextView
        android:visibility="gone"
        android:id="@+id/tv_title_amount_deposit"
        style="@style/layoutPrintTitle"
        android:text="Deposit/So tien"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_id_deposit" />

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_amount_deposit"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_amount_deposit"
        app:layout_constraintTop_toTopOf="@id/tv_title_amount_deposit"
        tools:text="20000" />

    <!--Desc/Mo Ta-->
    <TextView
        android:id="@+id/tv_title_desc"
        style="@style/layoutPrintTitle"
        android:text="Desc/Mo Ta"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_amount_deposit" />

    <TextView
        android:id="@+id/tv_desc"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_desc"
        app:layout_constraintTop_toTopOf="@id/tv_title_desc"
        tools:text="mo con nha ba ta mo con nha ba ta mo con nha ba ta mo con nha ba ta " />

    <!--TOTAL-->
    <TextView
        android:id="@+id/tv_title_total"
        style="@style/layoutPrintTitle"
        android:text="TOTAL"
        android:textSize="@dimen/pts_20"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_desc" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/layoutPrintValue"
        android:textSize="@dimen/pts_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_total"
        app:layout_constraintTop_toTopOf="@id/tv_title_total"
        tools:text="1,500,500,500 VND" />

    <!--Sign/Chu Ky-->
    <TextView
        android:id="@+id/tv_title_signature"
        style="@style/layoutPrintTitle"
        android:text="Sign/Chu Ky"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_amount"
        tools:visibility="gone" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_signature"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        android:minHeight="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title_signature"
        tools:background="@drawable/ic_mposvn"
        tools:visibility="gone" />

    <!--no required signature-->
    <TextView
        android:id="@+id/tv_no_required_signature"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_xxhigh"
        android:gravity="center"
        android:padding="@dimen/padding_half_item"
        android:text="No signature required /\nKhong yeu cau khach hang ky"
        android:textAllCaps="true"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_total"
        tools:visibility="visible" />

    <!--card holder name    -->
    <TextView
        android:id="@+id/tv_card_holdername_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imv_signature"
        tools:text="card holdername"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/brr_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_no_required_signature,tv_card_holdername_bottom" />

    <!--    NO REFUND / KHONG HOAN TRA-->
    <TextView
        android:id="@+id/tv_no_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:text="NO REFUND / KHONG HOAN TRA"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_18"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/brr_bottom" />


</androidx.constraintlayout.widget.ConstraintLayout>