<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:minWidth="300dp"
    android:orientation="vertical"
    android:background="@drawable/bg_white_corner"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:orientation="vertical"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:padding="@dimen/padding_item"
            android:background="@drawable/bg_corner_top_blue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_centerHorizontal="true"
                android:id="@+id/ic_status"
                android:layout_width="@dimen/item_ic_popup"
                android:layout_height="@dimen/item_ic_popup"
                android:layout_marginTop="@dimen/padding_item"
                android:background="@drawable/ic_info_white"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:gravity="center"
                android:layout_below="@+id/ic_status"
                android:text="@string/txt_title_notice"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </RelativeLayout>

        <TextView
            android:gravity="center"
            android:text="@string/msg_input_pass"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            android:padding="@dimen/padding_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <EditText
            android:hint="@string/LOGIN_LBL_STAFF_PASSWORD"
            android:background="@null"
            android:gravity="center"
            android:imeOptions="actionDone"
            android:inputType="numberPassword"
            android:fontFamily="@font/source_sans_pro"
            android:maxLength="30"
            android:id="@+id/edtxt_set_name"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_item"
            android:padding="@dimen/padding_item" />

        <LinearLayout
            android:layout_marginTop="@dimen/padding_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_item"
            android:orientation="horizontal">
            <Button
                android:id="@+id/btnDialogCancel"
                android:layout_width="fill_parent"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="@drawable/btn_grey_rounded"
                android:text="@string/ALERT_BTN_CANCEL"
                android:singleLine="true"
                android:textColor="#FF000000"
                android:textSize="@dimen/font_large"
                android:gravity="center"
                />
            <View
                android:layout_width="@dimen/padding_item"
                android:layout_height="1dp"
                android:orientation="horizontal"
                />
            <Button
                android:id="@+id/btnDialogOk"
                android:layout_width="fill_parent"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:background="@drawable/btn_orange_rounded"
                android:text="@string/BTN_OK"
                android:singleLine="true"
                android:textColor="#FFffffff"
                android:textSize="@dimen/font_large"
                android:gravity="center"
                />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>