<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:animateLayoutChanges="true"
    tools:context="com.mpos.screen.ActivityPaymentHistory">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/appbar_padding_top"
        android:background="@color/red"
        android:theme="@style/MyTheme.AppBarOverlay">

        <include
            android:id="@+id/ll_toolbar"
            layout="@layout/tool_bar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:popupTheme="@style/MyTheme.PopupOverlay"
            />

        <include
            android:id="@+id/search_view"
            layout="@layout/view_search_layout"
            android:visibility="gone"
            tools:visibility="visible"
            />

        <!--<android.support.v7.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:popupTheme="@style/MyTheme.PopupOverlay"/>-->

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:tabTextColor="@color/black"
            app:tabSelectedTextColor="@color/red"
            app:tabIndicatorColor="@color/red"
            app:tabTextAppearance="@style/TabLayoutTextStyle"
            app:tabMode="scrollable"
            app:tabGravity="start"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"/>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
