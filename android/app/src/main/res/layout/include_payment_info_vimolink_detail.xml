<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llParentDetailInfoVimoLink"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/activity_vertical_margin_haft">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_money"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_money_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="20dp"
            android:paddingRight="20dp"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="600.000.000 VND" />

    </RelativeLayout>

    <!-- space -->
    <TextView
        style="@style/MyLineSpace"
        android:layout_marginBottom="@dimen/activity_vertical_margin_haft"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="@dimen/activity_vertical_margin_haft" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/payment_method"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_payment_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="20dp"
            android:paddingRight="20dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="@string/type_vimolink" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/card_holder_name"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_cardholdername_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="20dp"
            android:paddingRight="20dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="Nguyen Thanh Trung" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/type_card"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_card_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="VISA" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_number_card"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_card_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal"
            tools:text="0000000000000" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/status_transaction"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal"
            android:layout_centerVertical="true"/>

        <TextView
            android:id="@+id/tv_link_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="15dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingStart="15dp"
            android:paddingBottom="3dp"
            android:paddingTop="3dp"
            android:layout_marginRight="20dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal"
            android:background="@drawable/bg_status_transaction_orange"
            tools:text="@string/txt_title_wait_for_disbursement"
            android:layout_alignParentRight="true"
            android:textColor="@color/white"/>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ll_link_pending_reason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="right|end"
        tools:visibility="visible"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_polygon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_polygon_black"
            android:layout_marginRight="50dp"
            android:tint="#F4ECE2"/>


        <TextView
            android:id="@+id/tv_link_pending_reason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginRight="20dp"
            android:layout_marginLeft="20dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingTop="3dp"
            android:paddingBottom="3dp"
            android:textColor="@color/orange_1"
            android:textSize="12sp"
            android:textStyle="italic"
            android:background="@drawable/bg_status_transaction_orange_overlay"
            tools:text="day la ly do"
            />

    </LinearLayout>

    <!-- space -->
    <TextView
        style="@style/MyLineSpace"
        android:layout_marginBottom="8dp"
        android:layout_marginLeft="@dimen/activity_vertical_margin"
        android:layout_marginRight="@dimen/activity_vertical_margin"
        android:layout_marginTop="8dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/dialog_mvisa_code_transaction"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_magiaodich_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="273608026" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:paddingEnd="0dp"
            android:paddingLeft="20dp"
            android:paddingRight="0dp"
            android:paddingStart="20dp"
            android:text="@string/time_transaction"
            android:textColor="#000000"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/tv_link_time_create_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end|center_vertical"
            android:paddingEnd="20dp"
            android:paddingLeft="0dp"
            android:paddingRight="20dp"
            android:paddingStart="0dp"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="bold"
            tools:text="09:50, 18-10-2017" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/distance_15"
        android:background="@drawable/bg_round_gray_overlay">

        <TextView
            android:id="@+id/tv_link_content_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/distance_10"
            android:textColor="@color/gray_1"
            android:textSize="@dimen/text_size_normal"
            android:textStyle="italic"
            tools:text="Mô tả thanh toán trả góp: VAYMUONQR Installment: 3 Months/VAYMUONQR Tra gop 3 Tháng - Noi dung mua hang" />

    </RelativeLayout>

</LinearLayout>

