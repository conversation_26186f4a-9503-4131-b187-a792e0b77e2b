<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="@color/white_dark"
    android:orientation="vertical">
    <!--start view bottom-->
    <Button
        android:id="@+id/settle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_item"
        android:background="@drawable/btn_orange_rounded"
        android:paddingLeft="@dimen/padding_item"
        android:paddingRight="@dimen/padding_item"
        android:text="@string/settle"
        android:textColor="@color/white"
        android:textSize="@dimen/font_normal"
        android:textAllCaps="false"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_item"
        android:layout_marginStart="@dimen/padding_item"
        android:text="@string/amount_need_settle"
        android:textSize="@dimen/font_small"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/total_amount" />

    <TextView
        android:id="@+id/total_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_item"
        android:layout_marginLeft="@dimen/padding_item"
        android:layout_marginStart="@dimen/padding_item"
        android:textSize="@dimen/font_large"
        android:textStyle="bold"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="10000.0000 vnd" />

    <LinearLayout
        tools:visibility="visible"
        android:visibility="gone"
        android:gravity="center"
        android:orientation="horizontal"
        android:id="@+id/layout_search_his_with_date"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Spinner
            android:background="@drawable/bg_white_corner_stroke"
            android:id="@+id/spn_type_time_qrnl"
            android:layout_width="0dp"
            android:layout_weight="4"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_item"
            android:padding="@dimen/padding_item" />

        <Button
            android:background="@drawable/btn_orange_rounded"
            android:textColor="@color/white"
            android:textSize="@dimen/font_normal"
            android:textAllCaps="false"
            android:id="@+id/btn_search_qr_nl"
            android:text="@string/tv_refresh"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_marginEnd="@dimen/padding_half_item"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <!--view empty + start pay-->
    <LinearLayout
        android:id="@+id/empty"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="0dp"
        android:layout_marginTop="0dp"
        android:background="@color/white"
        android:clickable="true"
        android:elevation="2dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_search_his_with_date"
        tools:visibility="gone">

        <TextView
            android:id="@+id/tv_status"
            style="@style/boldFont"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/padding_hight"
            android:text="@string/history_settle_empty"
            android:textColor="@color/txt_a"
            android:textSize="@dimen/text_size_normal" />

        <Button
            android:id="@+id/start_accepting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item"
            android:background="@drawable/btn_orange_rounded"
            android:paddingLeft="@dimen/padding_item"
            android:paddingRight="@dimen/padding_item"
            android:text="@string/HOME_BTN_START_NEW_PAYMENT"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:visibility="gone" />
    </LinearLayout>

    <com.emilsjolander.components.stickylistheaders.StickyListHeadersListView
        android:id="@+id/listView1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="8dp"
        android:layout_weight="1"
        android:background="@color/white"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/progressBar_more"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_search_his_with_date" />

    <ProgressBar
        android:id="@+id/progressBar1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginRight="8dp"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_search_his_with_date"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/progressBar_more"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/name"
        app:layout_constraintEnd_toStartOf="@+id/tv_loading_more"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        />

    <TextView
        android:id="@+id/tv_loading_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_half_item"
        android:text="@string/txt_loading"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/progressBar_more"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/progressBar_more"
        app:layout_constraintTop_toTopOf="@+id/progressBar_more"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
