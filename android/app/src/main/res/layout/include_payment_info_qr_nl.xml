<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llParentDetailInfoMVisa"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">


    <LinearLayout
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_hight"
            android:paddingTop="@dimen/activity_vertical_margin_haft">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/dialog_mvisa_money"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_mvisa_money_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:textColor="@android:color/holo_red_dark"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                tools:text="600.000.000 VND" />

        </RelativeLayout>

        <!-- space -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="@dimen/activity_vertical_margin_haft"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="@dimen/activity_vertical_margin_haft"
            android:background="@color/gray_light" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/status"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_status_qr_nl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:textColor="@color/red_1"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                tools:text="Thất bại" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/dialog_mvisa_code_transaction"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_mvisa_magiaodich_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                tools:text="273608026" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/dialog_mvisa_time_create_qrcode"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_mvisa_time_create_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                tools:text="09:50, 18-10-2017" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/time_paid_qrcode"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_time_paid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold"
                tools:text="09:50, 18-10-2017" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_item"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/activity_vertical_margin_haft">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="start|center_vertical"
                android:paddingStart="20dp"
                android:paddingLeft="20dp"
                android:paddingEnd="0dp"
                android:paddingRight="0dp"
                android:text="@string/method_support_qr"
                android:textColor="#000000"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/tv_mvisa_payment_baseon_detail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_weight="1"
                android:gravity="end|center_vertical"
                android:paddingStart="0dp"
                android:paddingLeft="0dp"
                android:paddingEnd="20dp"
                android:paddingRight="20dp"
                android:text="@string/dialog_mvisa_payment_base_on_qr_international"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_normal"
                android:textStyle="bold" />

        </LinearLayout>

        <Button
            android:id="@+id/btn_update_status_qr_nl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_hight"
            android:layout_marginTop="@dimen/padding_large"
            android:layout_marginEnd="@dimen/padding_hight"
            android:background="@drawable/bg_btn_void_trans_round_red"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/activity_vertical_margin_haft"
            android:text="@string/msg_update_status_qr_nl"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_normal"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/v_loading"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>