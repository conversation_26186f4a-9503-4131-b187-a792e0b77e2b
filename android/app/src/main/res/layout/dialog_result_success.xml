<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white_2">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white_2"
        android:orientation="vertical">


        <!--icon + text status pay-->
        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_item"
            android:layout_marginTop="@dimen/padding_item"
            android:background="@drawable/bg_green_corner_top"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingBottom="@dimen/padding_hight"
            android:paddingTop="@dimen/padding_hight">

            <ImageView
                android:id="@+id/ic_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/padding_half_item"
                android:layout_marginLeft="@dimen/padding_xhigh"
                android:layout_marginRight="@dimen/padding_xhigh"
                android:background="@drawable/img_success" />

            <TextView
                android:id="@+id/tv_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/padding_item"
                android:paddingRight="@dimen/padding_item"
                android:paddingTop="@dimen/padding_item"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_larger"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="@string/Successful_transaction" />

            <TextView
                android:id="@+id/tvAmountMoney"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_item"
                android:gravity="center"
                android:textAllCaps="true"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_larger"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="100.000.000 VND"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@color/red_1"
                android:textSize="@dimen/text_size_normal"
                tools:text="Lỗi 56: Số thẻ không tồn tại trên hệ ngân hàng, vui lòng đổi thẻ khác để giao dịch"
                tools:visibility="gone" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/llInforApprovedCodeResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_item"
            android:background="@drawable/bg_green_corner_bottom"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/padding_half_item"
            android:visibility="gone"
            android:baselineAligned="false"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tv_refno_code_result"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:maxLines="1"
                android:padding="@dimen/padding_item"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_larger"
                android:textStyle="bold"
                tools:text="Mã tham chiếu: 2345672345678" />
        </LinearLayout>

        <!--transaction id + info pay service (name service + phone)-->
        <LinearLayout
            android:id="@+id/layoutParentInfoTrans"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_item"
            android:background="@drawable/bg_green_corner_bottom"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/padding_half_item"
            android:visibility="visible"
            android:baselineAligned="false"
            tools:visibility="visible">

            <!--<LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="@dimen/padding_half_item"
                    android:text="@string/tv_transaction_id"
                    android:textAllCaps="true"
                    android:textColor="#000000"
                    android:textSize="@dimen/text_size_small"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="@dimen/padding_half_item"
                    android:textColor="#000000"
                    android:textSize="@dimen/text_size_tiny"
                    tools:text="600073451491807383432008954" />

            </LinearLayout>-->

            <!--info pay service-->
            <LinearLayout
                android:id="@+id/v_info_service"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_service"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:maxLines="1"
                    android:padding="@dimen/padding_half_item"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    android:textStyle="bold"
                    tools:text="name service" />

                <TextView
                    android:id="@+id/tv_mobile"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:maxLines="1"
                    android:padding="@dimen/padding_half_item"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_tiny"
                    tools:text="phone number customer" />
            </LinearLayout>
        </LinearLayout>

        <!--info installment-->
        <LinearLayout
            android:id="@+id/layoutInfoInstallment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginRight="@dimen/padding_item"
            android:background="@drawable/bg_green_corner_bottom"
            android:orientation="horizontal"
            android:padding="@dimen/padding_half_item"
            android:visibility="gone"
            android:weightSum="2"
            tools:visibility="visible"
            android:baselineAligned="false">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title_left"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/dialog_installment_bank"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/installment_bank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    tools:text="MaritimeBank" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title_right"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/dialog_installment_period"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/installment_period"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    tools:text="12 tháng" />

            </LinearLayout>
        </LinearLayout>

        <!--cashier reward-->
        <include layout="@layout/view_cashier_reward"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/padding_item"
            />

        <LinearLayout
            android:id="@+id/view_warranty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/bg_white_stroke"
            android:layout_marginTop="@dimen/padding_item"
            android:layout_marginBottom="@dimen/padding_item"
            android:padding="@dimen/padding_item"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/warranty_code"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/gray_1"
                />

            <EditText
                android:id="@+id/edt_warranty_code"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginLeft="@dimen/padding_item"
                android:layout_marginRight="@dimen/padding_item"
                android:maxLines="1"
                android:maxLength="24"
                android:textSize="@dimen/font_normal"
                android:paddingTop="@dimen/padding_item"
                android:paddingBottom="@dimen/padding_item"
                android:hint="@string/input_here"
                android:background="@null"
                tools:text="abc"
                />
            <ImageView
                android:id="@+id/imb_warranty_qr"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:background="@drawable/ic_barcode_scanner"
                android:visibility="gone"
                />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/gray_2"
                android:layout_marginLeft="@dimen/padding_item"
                android:layout_marginRight="@dimen/padding_item"
                android:visibility="gone"
                />

            <Button
                android:id="@+id/btn_warranty_send"
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:text="@string/BTN_SEND"
                android:textStyle="bold"
                android:textAllCaps="false"
                android:enabled="false"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/text_black_white"
                android:background="@drawable/bg_button_blue"
                />
        </LinearLayout>

        <!--button back + withdraw-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible">

            <Button
                android:id="@+id/quickWithdrawal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_item"
                android:background="@drawable/btn_red_rounded"
                android:gravity="center"
                android:padding="@dimen/padding_medium"
                android:text="@string/txt_want_money_my_acc"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <Button
                android:id="@+id/print_receipt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/print_receipt"
                android:background="@drawable/btn_blue_rounded"
                android:layout_marginStart="@dimen/padding_item"
                app:layout_constraintTop_toTopOf="@id/comeback"
                app:layout_constraintStart_toStartOf="parent"
                android:paddingTop="@dimen/padding_medium"
                android:paddingBottom="@dimen/padding_medium"
                android:paddingStart="@dimen/padding_xhigh"
                android:paddingEnd="@dimen/padding_xhigh"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <Button
                android:id="@+id/comeback"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_hight"
                android:layout_marginEnd="@dimen/padding_item"
                android:background="@drawable/btn_orange_rounded"
                android:gravity="center"
                android:padding="@dimen/padding_medium"
                android:text="@string/finish_transaction"
                android:textColor="@color/white"
                android:textSize="@dimen/font_normal"
                android:textStyle="normal"
                android:layout_marginStart="@dimen/padding_item"
                app:layout_constraintStart_toEndOf="@id/print_receipt"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/quickWithdrawal"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_note_after_pay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_margin="@dimen/padding_item"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/red"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="@string/note_min_amount_installment"
            />

    </LinearLayout>
</ScrollView>