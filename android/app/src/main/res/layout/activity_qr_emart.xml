<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   android:id="@+id/container"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   xmlns:tools="http://schemas.android.com/tools"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   tools:context="com.mpos.screen.mart.ActivityQrEmart">

    <include
            android:id="@+id/ll_toolbar"
            layout="@layout/tool_bar"/>

    <WebView
            android:id="@+id/webview_qr_emart"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_toolbar"/>

    <LinearLayout
            android:id="@+id/ll_qr_nl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_toolbar">

        <ImageView
                android:background="@drawable/bg_qr_border"
                android:id="@+id/img_qr"
                android:layout_width="300dp"
                android:padding="10dp"
                android:layout_height="300dp"
                android:layout_gravity="center_horizontal"/>
        <View
                android:layout_width="1dp"
                android:layout_height="20dp"/>

        <TextView
                android:textColor="@color/orange"
                android:textSize="@dimen/font_large"
                android:text="(Lưu ý: Chỉ 01 khách thanh toán 01 lần)"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        <View
                android:layout_width="1dp"
                android:layout_height="10dp"/>

        <TextView
                android:id="@+id/txtAmountQr"
                android:textColor="@color/blue_1"
                android:textSize="30sp"
                android:textStyle="bold"
                android:text="0 đ"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        <View
                android:layout_width="1dp"
                android:layout_height="20dp"/>

        <Button
            android:id="@+id/btn_check_status_trans"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/distance_10"
            android:background="@drawable/btn_red_rounded"
            android:gravity="center"
            android:padding="@dimen/distance_10"
            android:text="Kiểm tra trạng thái"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/font_normal"
            android:textStyle="bold"
            android:visibility="invisible"
                tools:visibility="visible"/>

    </LinearLayout>

    <ProgressBar
            android:id="@+id/progressBar1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/ll_toolbar"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_toolbar"/>

</androidx.constraintlayout.widget.ConstraintLayout>