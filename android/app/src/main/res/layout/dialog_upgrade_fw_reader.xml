<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/SpecialDialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:minWidth="@dimen/min_width_dialog_base"
    android:background="@drawable/border_radius"
    android:padding="@dimen/padding_medium"
    android:orientation="vertical">

    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/icon_upgrade_big"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_marginBottom="@dimen/padding_item"
        />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_upgrade_reader"
        android:textSize="@dimen/font_20"
        android:layout_marginStart="@dimen/padding_hight"
        android:layout_marginEnd="@dimen/padding_hight"
        android:layout_gravity="center_horizontal"
        />
    <com.mpos.customview.TextViewJustify
        android:id="@+id/tv_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/warning_have_upgrade_evm_config"
        android:textSize="@dimen/font_normal"
        android:layout_marginTop="@dimen/padding_item"
        android:paddingBottom="@dimen/padding_xhigh"
        />

    <Button
        android:id="@+id/btn_update_now"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/btn_update"
        android:textColor="@color/white"
        android:background="@drawable/btn_red_rounded"
        android:layout_marginTop="@dimen/padding_item"
        />

    <Button
        android:id="@+id/btn_close"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/btn_later"
        android:background="@drawable/btn_gray_rounded"
        android:layout_marginTop="@dimen/padding_medium"
        />

</LinearLayout>