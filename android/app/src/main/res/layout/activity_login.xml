<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/red_1"
    tools:context="com.mpos.screen.login.LoginActivity">

    <TextView
        android:id="@+id/tv_selected_device"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_medium"
        android:layout_marginTop="@dimen/padding_item"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:paddingBottom="@dimen/padding_half_item"
        android:paddingTop="@dimen/padding_half_item"
        android:textColor="@color/white"
        android:textSize="@dimen/font_small"
        tools:visibility="visible"
        tools:text="serialnumber"
        />

    <ImageView
        android:id="@+id/imv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_arrow_right"
        android:padding="@dimen/padding_item"
        android:layout_margin="@dimen/padding_item"
        tools:visibility="visible"
        />

    <TextView
        android:id="@+id/tv_more_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_selected_device"
        android:layout_marginStart="@dimen/padding_item"
        android:layout_marginTop="@dimen/padding_item"
        android:layout_marginEnd="@dimen/padding_item"
        android:layout_marginBottom="@dimen/padding_item"
        android:text="@string/guide_sub_login"
        android:textColor="@color/white"
        android:visibility="gone"
        tools:visibility="gone" />

    <!--input -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/v_input_acc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_item"
        android:background="@drawable/bg_white_corner"
        android:orientation="vertical"
        android:layout_below="@id/tv_more_info"
        android:padding="@dimen/padding_half_item">

        <ImageView
            android:id="@+id/imv_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/padding_item"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginStart="@dimen/padding_item"
            android:layout_marginTop="@dimen/padding_item"
            android:background="@drawable/ic_user_red"
            app:layout_constraintBottom_toBottomOf="@+id/user_id"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/user_id" />

        <!-- user name -->
        <EditText
            android:id="@+id/user_id"
            style="@style/EdtInputBase"
            android:layout_width="0dp"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginStart="@dimen/padding_item"
            android:hint="@string/LOGIN_LBL_STAFF_ID"
            android:imeOptions="actionNext"
            android:inputType="textPersonName"
            android:fontFamily="@font/source_sans_pro"
            android:maxLength="50"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imv_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/tv_fake_line"
            android:layout_width="0dp"
            android:layout_height="@dimen/stroke"
            android:layout_marginBottom="@dimen/padding_half_item"
            android:layout_marginTop="@dimen/padding_half_item"
            android:background="@color/gray_light"
            app:layout_constraintLeft_toLeftOf="@id/user_id"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_id" />

        <EditText
            android:id="@+id/user_pin"
            style="@style/EdtInputBase"
            android:layout_width="0dp"
            android:hint="@string/LOGIN_LBL_STAFF_PASSWORD"
            android:imeOptions="actionDone"
            android:inputType="numberPassword"
            android:fontFamily="@font/source_sans_pro"
            android:maxLength="30"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/tv_fake_line"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_fake_line"
            app:layout_constraintVertical_bias="0.0" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/padding_item"
            android:layout_marginLeft="@dimen/padding_item"
            android:layout_marginStart="@dimen/padding_item"
            android:layout_marginTop="@dimen/padding_item"
            android:background="@drawable/ic_protect"
            app:layout_constraintBottom_toBottomOf="@id/user_pin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/user_pin" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <!--login-->
    <LinearLayout
        android:id="@+id/v_login"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/v_input_acc"
        android:layout_margin="@dimen/padding_item"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/MyBtnGray"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_item"
            android:text="@string/BTN_CANCEL"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/v_space"
            android:layout_width="@dimen/padding_item"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone" />

        <Button
            android:id="@+id/log_in"
            style="@style/MyButtonRedFullWidth"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_item"
            android:text="@string/LOGIN_BTN_LOGIN" />
    </LinearLayout>

    <!--config base pax-->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/tv_call_support"
        android:layout_alignParentRight="true"
        android:layout_margin="@dimen/padding_item">

        <Button
            android:id="@+id/btn_config_base_pax"
            style="@style/MyButtonRedFullWidth"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_item"
            android:text="@string/txt_config_base"
            android:visibility="gone" />
    </LinearLayout>

    <!--register-->
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/v_login"
        android:layout_marginTop="@dimen/padding_hight"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/txt_no_account"
            android:textColor="@color/gray_dark"
            android:textSize="@dimen/text_size_small" />

        <TextView
            android:id="@+id/register_merchant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_half_item"
            android:text="@string/txt_register_now"
            android:textColor="@color/black_light"
            android:textSize="@dimen/text_size_small" />
    </LinearLayout>

    <!-- logo -->
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/v_input_acc"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/img_mpos_big" />

        <!--<TextView
            android:id="@+id/tv_selected_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/padding_medium"
            android:layout_marginTop="@dimen/padding_medium"
            android:background="@color/sea_green_medium_light"
            android:gravity="center"
            android:paddingBottom="@dimen/padding_half_item"
            android:paddingTop="@dimen/padding_half_item"
            android:text="@string/txt_select_device"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_small"
            tools:visibility="visible" />-->
    </LinearLayout>

    <!--<LinearLayout
        android:id="@+id/v_help"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_margin="@dimen/padding_item"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        >

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_ask_rounded" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/txt_help"
            android:textColor="@color/black"
            android:textSize="@dimen/font_small" />
    </LinearLayout>-->

    <!--select reader-->
    <include
        layout="@layout/view_login_select_reader"
        tools:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/tv_call_support"
        />

    <!--call support-->
    <TextView
        android:id="@+id/tv_call_support"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_alignParentBottom="true"
        android:maxLines="1"
        android:padding="@dimen/padding_item"
        android:textColor="@color/white_light"
        android:textSize="@dimen/font_small"
        android:gravity="center_vertical"
        android:drawablePadding="@dimen/padding_item"
        android:drawableLeft="@drawable/ic_call_support"
        android:background="@color/orange_1"
        android:text="@string/txt_support" />

    <!--splash-->
    <include
        layout="@layout/view_splash"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone"
        />

    <!--version-->
    <TextView
        android:id="@+id/version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:padding="@dimen/padding_half_item"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_small"
        android:textStyle="italic"
        tools:text="version" />
</RelativeLayout>