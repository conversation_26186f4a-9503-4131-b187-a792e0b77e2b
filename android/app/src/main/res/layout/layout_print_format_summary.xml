<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/v_root"
    android:layout_width="380dp"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <TextView
        android:id="@+id/tv_title_bill"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_column"
        android:gravity="center"
        android:text="TONG KET PHIEN GIAO DICH\nDAT COC"
        android:textAllCaps="true"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="TONG KET PHIEN GIAO DICH\nDAT COC" />

    <TextView
        android:id="@+id/tv_mc_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_item"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title_bill"
        tools:text="NGUU MA VUONG" />

    <TextView
        android:id="@+id/tv_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textColor="@android:color/black"
        android:textSize="@dimen/pts_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mc_name"
        tools:text="Username" />

    <!--Thoi gian bat dau -->
    <TextView
        android:id="@+id/tv_title_startTime"
        style="@style/layoutPrintTitleNoneBold"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        android:text="TG bat dau"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_username" />

    <TextView
        android:id="@+id/tv_startTime"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_startTime"
        app:layout_constraintTop_toTopOf="@id/tv_title_startTime"
        tools:text="11:10 04/07/1997" />

    <!--Thoi gian kết thúc -->
    <TextView
        android:id="@+id/tv_title_endTime"
        style="@style/layoutPrintTitleNoneBold"
        android:text="TG ket thuc"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_startTime" />

    <TextView
        android:id="@+id/tv_endTime"
        style="@style/layoutPrintValue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_endTime"
        app:layout_constraintTop_toTopOf="@id/tv_title_endTime"
        tools:text="11:10 11/10/1998" />

    <!-- container title !-->
    <LinearLayout
        android:layout_marginTop="@dimen/padding_half_item"
        android:id="@+id/ll_summary_print_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title_endTime"
        android:weightSum="5"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            style="@style/layoutPrintTitle"
            android:layout_weight="2.2"
            android:layout_width="0dp"
            android:text="Trang thai"
            android:layout_height="wrap_content"/>

        <TextView
            style="@style/layoutPrintTitle"
            android:layout_weight="1.1"
            android:layout_width="0dp"
            android:text="Tong GD"
            android:layout_height="wrap_content"/>

        <TextView
            style="@style/layoutPrintValue"
            android:layout_weight="1.7"
            android:layout_width="0dp"
            android:text="Tong tien"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <!-- container approve !-->
    <LinearLayout
        android:id="@+id/ll_summary_print_approve"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_summary_print_title"
        android:weightSum="5"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            style="@style/layoutPrintTitleNoneBold"
            android:layout_weight="2.2"
            android:layout_width="0dp"
            android:text="Thanh cong"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_summary_totalTrans_Approve"
            style="@style/layoutPrintTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:text="0" />

        <TextView
            android:id="@+id/tv_summary_totalAmount_Approve"
            style="@style/layoutPrintValue"
            android:layout_weight="1.7"
            android:layout_width="0dp"
            android:text="0 d"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <!-- container settle !-->
    <LinearLayout
        android:id="@+id/ll_summary_print_settle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_summary_print_approve"
        android:weightSum="5"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            style="@style/layoutPrintTitleNoneBold"
            android:layout_weight="2.2"
            android:layout_width="0dp"
            android:text="Ket toan"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_summary_totalTrans_Settle"
            style="@style/layoutPrintTitle"
            android:layout_weight="1.1"
            android:layout_width="0dp"
            android:text="0"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_summary_totalAmount_Settle"
            style="@style/layoutPrintValue"
            android:layout_weight="1.7"
            android:layout_width="0dp"
            android:text="0 d"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    <!-- container void !-->
    <LinearLayout
        android:id="@+id/ll_summary_print_void"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_summary_print_settle"
        android:weightSum="5"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <TextView
            style="@style/layoutPrintTitleNoneBold"
            android:layout_weight="2.2"
            android:layout_width="0dp"
            android:text="Hoan/Huy/Đao"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_summary_totalTrans_Void"
            style="@style/layoutPrintTitle"
            android:layout_weight="1.1"
            android:layout_width="0dp"
            android:text="0"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_summary_totalAmount_Void"
            style="@style/layoutPrintValue"
            android:layout_weight="1.7"
            android:layout_width="0dp"
            android:text="0 d"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/v_line1"
        android:layout_width="0dp"
        android:layout_height="2dp"
        android:layout_marginTop="@dimen/padding_item"
        android:background="@drawable/line_dashed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_summary_print_void" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/pts_18"
        android:textColor="@color/black"
        android:id="@+id/tv_info_summarry"
        android:gravity="center"
        android:paddingStart="@dimen/padding_item"
        android:paddingEnd="@dimen/padding_item"
        android:text="Ghi chu: So lieu tong ket ghi nhan theo\nlich su giao dich"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v_line1" />

</androidx.constraintlayout.widget.ConstraintLayout>