<resources xmlns:android="http://schemas.android.com/apk/res/android">


    <style name="MyTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- <item name="android:windowNoTitle">true</item> -->
        <!-- We will be using the toolbar so no need to show ActionBar -->
        <!-- <item name="android:windowActionBar">false</item> -->

        <!-- Set theme colors from http://www.google.com/design/spec/style/color.html#color-color-palette -->
        <!-- colorPrimary is used for the default action bar background -->
        <!--<item name="android:colorPrimary">@color/red_1</item>-->

        <!-- colorPrimaryDark is used for the status bar -->
        <!--<item name="android:colorPrimaryDark">@color/red_1</item>-->
        <!--         <item name="android:colorPrimaryDark">@color/red_dark</item> -->

        <!--
         colorAccent is used as the default value for colorControlActivated
         which is used to tint widgets
        -->
        <!--<item name="android:colorAccent">#FF4081</item>-->
        <!--
         You can also set colorControlNormal, colorControlActivated
         colorControlHighlight and colorSwitchThumbNormal.
        -->
        <!-- Toolbar + Overflow menu text color FFFFFF-->
        <!-- android:textColorPrimary is the  color of the title text
	       in the Toolbar, in the Theme.AppCompat theme:  -->
        <!-- <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item> -->

        <!-- android:textColorPrimaryInverse is the  color of the title
		       text in the Toolbar, in the Theme.AppCompat.Light theme:  -->
        <item name="android:textColorPrimaryInverse">#FFFFFF</item>
        <!-- Overflow menu button color -->
        <item name="android:textColorSecondary">#FFFFFF</item>
        <item name="android:textColor">#000000</item>
        <item name="android:typeface">serif</item>
        <!-- <item name="android:textAllCaps">true</item> -->
    </style>


    <!-- BUTTON -->
    <style name="MyButton">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">@dimen/font_normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="ButtonApp">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">@dimen/font_normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">@dimen/padding_medium</item>
    </style>

    <style name="MyButtonRedFullWidth" parent="MyButton">
        <item name="android:background">@drawable/btn_orange_rounded</item>
    </style>

    <style name="MyBtnGray">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:background">@drawable/bg_view_gray</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- LINE SPACE -->
    <style name="MyLineSpace">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/stroke_mini</item>
        <item name="android:background">@color/gray_light</item>
    </style>

    <!-- editext  -->
    <style name="EdtInputBase">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <!-- <item name="android:layout_marginTop">@dimen/padding_item</item> -->
        <item name="android:background">@null</item>
        <item name="android:drawablePadding">@dimen/padding_item</item>
        <item name="android:ems">10</item>
        <!--<item name="android:padding">@dimen/padding_item</item>-->
        <item name="android:paddingTop">@dimen/padding_item</item>
        <item name="android:paddingBottom">@dimen/padding_item</item>
        <item name="android:paddingRight">@dimen/padding_item</item>
        <item name="android:textColor">@color/black_light</item>
        <item name="android:textCursorDrawable">@null</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">@dimen/text_size_normal</item>

    </style>

    <style name="TextLabel">
        <item name="android:textColor">@color/black_light</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">@dimen/text_size_normal</item>

    </style>

    <!-- view input service -->
    <style name="BgItemService">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
        <item name="android:background">@drawable/bg_white_line</item>
        <!-- <item name="android:padding">@dimen/padding_item</item> -->
    </style>

    <style name="TvItemServiceBase">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_normal</item>
        <item name="android:textColor">@color/gray_black</item>
    </style>

    <style name="TvItemService" parent="TvItemServiceBase">
        <item name="android:layout_marginTop">@dimen/padding_item</item>
        <item name="android:layout_marginLeft">@dimen/padding_item</item>
        <item name="android:layout_marginRight">@dimen/padding_item</item>
    </style>

    <style name="EdtItemService">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:drawablePadding">@dimen/padding_item</item>
        <item name="android:background">@null</item>
        <item name="android:padding">@dimen/padding_item</item>
    </style>


    <!--screen home-->

    <!-- dialog -->

    <style name="MyTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="MyTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="DialogRadiusErrorGeneral" parent="@android:style/Theme.Dialog">
        <item name="android:windowFullscreen">false</item>
        <item name="android:background">@drawable/border_radius_dialog_error</item>
    </style>

    <style name="tv_reader_name">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">left</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">@dimen/font_normal</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_reader_name_short">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">left</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">@dimen/font_tiny</item>
        <item name="android:textStyle">normal</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">2</item>
    </style>

    <style name="DialogRadiusMVisa" parent="@android:style/Theme.Dialog">
        <item name="android:windowFullscreen">false</item>
        <item name="android:background">@drawable/border_radius_exchange</item>
    </style>

    <!-- dialog corner radius -->
    <style name="DialogRadius" parent="@android:style/Theme.Dialog">
        <item name="android:background">@drawable/border_radius</item>
    </style>


    <style name="tv_left_constraints">
        <item name="android:layout_width">@dimen/width_img_bank</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">@dimen/padding_item</item>
        <!--<item name="android:paddingBottom">@dimen/padding_item</item>-->
        <!--<item name="android:layout_marginTop">@dimen/padding_item</item>-->
    </style>

    <style name="animation_out_in_right">
         <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <!--<item name="android:windowEnterAnimation">@anim/slide_left_to_right_in</item>-->
        <item name="android:windowExitAnimation">@anim/slide_out_right</item>
    </style>


    <style name="BaseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorControlNormal">@android:color/white</item>
    </style>

    <style name="label_normal">
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">16dp</item>
    </style>

    <style name="label_small">
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">14dp</item>
        <item name="android:textColor">@color/color_text</item>
    </style>


    <style name="TabLayoutTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!--    printer layout-->
    <style name="layoutPrintValue">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">right</item>
        <item name="android:textSize">@dimen/pts_18</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:layout_marginStart">@dimen/printer_margin_column</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="layoutPrintTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
        <item name="android:textSize">@dimen/pts_18</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="layoutPrintTitleNoneBold">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:layout_marginTop">@dimen/printer_margin_line</item>
        <item name="android:textSize">@dimen/pts_18</item>
    </style>

    <!-- React Native -->
    <style name="RNTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowDisablePreview">true</item>
    </style>
    <style name="SplashScreenRNTheme" parent="SplashScreen_SplashTheme">
        <item name="colorPrimaryDark">@color/splash_rn_status_bar_color</item>
    </style>
</resources>
