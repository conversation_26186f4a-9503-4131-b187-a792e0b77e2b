<?xml version="1.0" encoding="utf-8"?>
<resources>

    <color name="transparent_white">#11ffffff</color>
    <color name="red">#ac272e</color>
    <color name="red_1">#cf3a3e</color>
    <color name="red_2">#d84d27</color>
    <color name="red_3">#c21a15</color>
    <color name="red_4">#ff0000</color>
    <color name="red_5">#D22E2A</color>
    <!--<color name="red_tool_bar">#ac272e</color>-->
    <color name="red_white">#e74c3c</color>
<!--     <color name="red">#d1232b</color> -->
    <color name="red_select_type_devices">#d1232b</color>
    <color name="red_button_white">#D37A7E</color>

    <color name="pink">#fcc6c6</color>

    <color name="green">#006600</color>

    <color name="yellow_light">#f1c40f</color>
    <color name="yellow_1">#cac08f</color>
    <color name="yellow_2">#fff7d6</color>
    <color name="transparent_color">#F2a7a7a7</color>
    <color name="orange">#f28300</color>
    <color name="orange_1">#ed9622</color>
    <color name="orange_dark">#eb9522</color>
    <color name="orange_light">#eb9621</color>
    <color name="orange_2">#D35822</color>
    <color name="orange_3">#955F16</color>



    <color name="white_gray">#f4f5f7</color>
    <color name="white_light">#f2f2f2</color>
    <color name="white_dark">#f3f3f3</color>
    <color name="white_1">#ced3d9</color>
    <color name="white_2">#f7f7f7</color>
    <color name="white_5">#F1F1F1</color>
    <color name="white_3">#f4f6f8</color>
    <color name="white_4">#e3e6e7</color>

    <color name="gray_white">#838383</color>
    <color name="gray_white_black">#6b6b6b</color>
    <color name="gray_light">#d1d1d1</color>
    <color name="gray_dark">#a7a7a7</color>
    <color name="color_text">#333333</color>
    <color name="gray_black">#575757</color>
    <color name="gray_black_dark">#5f5d5d</color>
    <color name="gray_1">#7a7a7a</color>
    <color name="gray_2">#e2e6e7</color>
    <color name="gray_3">#bdc3c7</color>
    <color name="gray_4">#959595</color>
    <color name="gray_5">#4A4A4A</color>
    <color name="gray_6">#747474</color>

    <color name="blue_light_50">#3498db</color>
    <color name="blue">#00a651</color>
    <color name="blue_dark">#025A2D</color>
    <color name="blue_1">#0a83d7</color>
    <color name="blue_2">#1464a9</color>
    <color name="blue_white">#C2E2FA</color>

    <color name="black_light">#1a1a1a</color>
    <color name="black_blue">#2c3e50</color>
    <color name="black_medium_light">#252424</color>
    <color name="black_gray_light">#373435</color>

    <color name="sea_green">#27ae60</color>
    <color name="sea_green_black">#1b8e4b</color>
    <color name="sea_green_medium">#2ecc71</color>

    <color name="azure">#ecf0f1</color>


    <!--percentages to hex values-->
    <color name="transparent_20">#33FFFFFF</color>


    <!-- quick withdrawal-->
    <color name="bg_gray_scene_quick">#34495e</color>
    <color name="tv_bg_red_want_money">#ac272e</color>
    <color name="tv_color_back_to_main">#34495e</color>
    <color name="tv_color_pay_success">#27ae61</color>

    <color name="line_view_note">#c9bf90</color>
    <color name="bg_note">#faf5de</color>

    <color name="bg_progress_connected">#2ecc71</color>
    <color name="bg_progress_connecting">#faf5df</color>
    <color name="tv_color_lost_connect">#e99433</color>

    <!-- guide installment -->
    <color name="colorAccentProgressInstallment">#fa0909</color>
    <color name="colorTintSelectInstallment">#d92830</color>

    <!-- error general dialog -->
    <color name="color_err_general_text">#2c2c2c</color>

    <!-- for mVISA -->
    <color name="color_bg_gray_normal">#f2f2f2</color>
    <color name="color_bg_gray_normal_border">#d9dbde</color>

    <color name="primary_dark">#000000</color>
    <color name="splash_rn_status_bar_color">#ac272e</color>

    <color name="gray">#848484</color>

    <color name="orange_overlay">#F4ECE2</color>
    <color name="red_overlay">#F7E0DF</color>
    <color name="green_dark">#007144</color>

</resources>
