<?xml version="1.0" encoding="utf-8"?>

<!DOCTYPE resources [
    <!ENTITY hot_line "0339 506 286">
    <!ENTITY hot_line_mpos "1900.6364.88">
    <!ENTITY consultant "0901 75 79 98">
    ]>
<resources>

    <string name="ALERT_LOCATION_SERVICE_ANDROID_MSG">Please Enable GPS and Other Location Services</string>
    <string name="need_allow_access_location">Please allow \"mPoS.vn\" to access this device\'s location.</string>
    <string name="need_allow_access_storage">Please allow \"mPoS.vn\" to access this device\'s storage.</string>
    <string name="need_allow_access_storage_and_location">Please allow \"mPoS.vn\" to access this device\'s storage and location.</string>
    <string name="alert_comming_soon_support_service">You can become a sale agent for more than 70 billing services!</string>
    <string name="alert_coming_soon">This function will be coming soon!</string>

    <string name="default_notification_channel_id" translatable="false">fcm_default_channel</string>

    <string name="app_name">mPoS.vn</string>
    <string name="txt_select_device">Please select reader type</string>
    <string name="please_register"><![CDATA[Or <b><font color=#cf3a3e>click here to sign up for using MPOS</font></b>]]></string>
    <string name="or_select_type_pay">Please select your payment method</string>
    <string name="txt_audio_reader">Audio Reader (AR-01)</string>
    <string name="txt_bluepad_reader">Bluetooth Reader (PR-01)</string>
    <string name="txt_qpos_reader">Bluetooth Reader (PR-02)</string>
    <string name="txt_pay_with_qr">Pay with QR code</string>
    <string name="account">Account</string>
    <string name="txt_no_account">Don\'t have account?</string>
    <string name="txt_register_now">Register Merchant</string>
    <string name="txt_support">Support: %s</string>
    <string name="txt_version">Current version: %s</string>
    <!--<string name="txt_support">Support: &hot_line;</string>-->

    <string name="warning_ar_battery_low">Battery low. Please Charge!</string>
    <string name="warning_canot_exit_while_pay">You can\'t exit while pay</string>
    <string name="warning_close_app_turn_off_reader">Please don\'t close application or turn off mPoS reader</string>
    <string name="warning_limit_amount_trans">You have exceeded total amount limit per transaction(%s). Please decrease amount to continue.</string>
    <string name="warning_limit_amount_day">You have exceeded your maximum daily total amount limit. Please wait until tomorrow to continue.</string>
    <string name="warning_limit_amount_month">You have exceeded your maximum monthly total amount limit. Please wait until next month to continue.</string>
    <string name="warning_integration_invaild_data">Invalid params(merchant integration).</string>

    <string name="msg_success_update_ca_public_key">Update success!</string>


    <string name="history">Transaction history</string>
    <string name="history_in_day">Not settled yet</string>
    <string name="history_macq">Card transaction</string>
    <string name="history_other">Other History</string>
    <string name="history_fail">Fail History</string>
    <string name="history_voucher_card">GIFT CARD</string>
    <string name="history_settle_empty">No transactions performed today.</string>
    <string name="settled">Settled</string>
    <string name="settle">Settle Credit</string>
    <string name="settle_success">Settle success</string>
    <string name="amount_need_settle">Total Unsettled Amount: </string>

    <string name="read_more">Read more</string>
    <string name="read_less">Collapse</string>
    <!-- Unlicensed dialog messages -->
    <string name="retry_button">Retry</string>

    <string name="txt_hello">Hello!</string>
    <string name="txt_help">Help</string>
    <string name="txt_notice">Notice</string>
    <string name="txt_continue">Continue</string>
    <string name="txt_go_home">Come back home</string>
    <string name="txt_check_status_trans">Check status transaction</string>
    <string name="txt_go_login">Relogin</string>
    <string name="txt_trans_detail">Transaction details</string>
    <string name="txt_agree">Agree</string>
    <string name="payment_info">Billing information</string>

    <string name="total_amount_pay">Total amount:</string>
    <string name="txt_amount">Invoice amount</string>
    <string name="txt_amount_discount">Cash back</string>
    <string name="txt_amount_pay">Total payment</string>
    <string name="txt_protect">Insurance</string>
    <string name="txt_car">Car</string>
    <string name="txt_moto">Moto</string>
    <string name="txt_protect_car">Car Insurance</string>
    <string name="txt_protect_moto">Moto Insurance</string>

    <string name="txt_title_payment">Payment</string>
    <string name="txt_title_payment_pr">Payment via QR code</string>

    <string name="hint_search">Search</string>

    <string name="notify_pay_success">Transaction successful </string>
    <string name="notify_pay_void">Cancel transaction successfully</string>
    <string name="notify_pay_void_fail">Transaction failed</string>

    <string name="alert_confirm_exit_app">Press back again to exit</string>
    <string name="alert_contact_soon">Thank you for using mPoS.vn, we will get in touch soon!</string>

    <string name="alert_have_reversal_trans">Bạn có giao dịch chưa hoàn thành!\nVui lòng hoàn thành giao dịch.</string>
    <string name="alert_have_reversal_trans_ma">Bạn có giao dịch chưa hoàn thành!\nVui lòng vào lịch sử và hoàn thành giao dịch.</string>

    <string name="alert_select_period">Please select period installment</string>
    <string name="alert_select_type_card">Please select card type</string>
    <!--    <string name="alert_update_emv_config_sp01_success">Update successful. Thank you for using our services</string>-->
    <string name="warning_network_sp01">Please turn off/on network connection.</string>

    <string name="txt_no_data">No data.</string>
    <string name="txt_copy_tid_to_clipboard">Can\'t open %s.\nPlease check with Transaction ID.(Transaction ID copied to clipboard)</string>
    <string name="txt_no_urlcallback_copy_tid_to_clipboard">Can\'t found urlCallBack.\nPlease check with Transaction ID.(Transaction ID copied to clipboard)</string>
    <string name="txt_no_urlcallback_copy_tid_to_clipboard_pay_false">Transaction failed. Can\'t found urlCallBack.\nPlease check again.</string>

    <string name="description">Description</string>
    <string name="amount">Amount</string>
    <string name="pan">PAN</string>
    <string name="card_holder_name">Card holder name</string>
    <string name="info_invoice">Order details</string>
    <string name="invoice_code_need_pay">Order code need pay</string>
    <string name="pay_invoice">Invoice Payment</string>
    <string name="pay_installment">Installment Payment</string>
    <string name="installment_by_credit_card">Installment by credit card</string>
    <string name="pay">Pay</string>
    <string name="pay_action_normal">Normal payments</string>
    <string name="with_invoice">Enter invoice</string>
    <string name="with_no_invoice">No enter invoice</string>
    <string name="pan_colon">PAN: </string>
    <string name="not_available">Not available</string>
    <string name="more">more</string>
    <string name="less">less</string>
    <string name="code_break_line">Code\n</string>
    <!-- installment -->
    <!--<string name="period_month">%s\nmonths</string>-->
    <!--<string name="period_3"><![CDATA[<h2><b>3</b></h2>months]]></string>-->
    <string name="wrong_min_amount_installment">%s bank requires minimum %s for each installment transaction</string>
    <string name="wrong_format_id_card_number">Invalid Identity card number(Format: length 7~19, does not contain special characters)</string>
    <string name="note_min_amount_installment"><![CDATA[* <b>%s</b> bank requires minimum <b>%s</b> for each installment transaction.]]></string>
    <string name="error_get_info_installment">Can\'t get info installment, please try again!</string>
    <string name="bank_installment_month">%s - Installment: %s months</string>

    <string name="title_error_installment" translatable="false">Trả góp không thành công</string>
    <string name="error_installment_not_support_card" translatable="false">Giao dịch thẻ %s không được ngân hàng phát hành hỗ trợ trả góp 0%%. Vui lòng vào lịch sử giao dịch để hủy giao dịch trả góp này sau đó thực hiện giao dịch thanh toán thường HOẶC liên hệ với hotline để chuyển đổi giao dịch trả góp này sang giao dịch thanh toán thường.</string>
    <string name="error_installment_invalid_bank" translatable="false">Thẻ %s không phải là thẻ của ngân hàng %s phát hành. Vui lòng vào lịch sử giao dịch để hủy giao dịch này sau đó thực hiện lại và chọn ngân hàng trả góp là ngân hàng %s (ngân hàng phát hành thẻ) HOẶC liên hệ với hotline để được trợ giúp.</string>

    <!-- cash back -->
    <string name="error_get_info_cash_back">Can\'t get info promotion, please try again!</string>

    <!--input amount-->
    <string name="enter_trans_decs_option">Enter transaction description</string>
    <!-- pti -->
    <string name="ma_chung_nhan_bao_hiem_tam_thoi">Order code:</string>
    <string name="so_tien_thanh_toan">Amount:</string>
    <string name="trang_thai_thanh_toan">Payment status:</string>
    <string name="more_info">More info</string>
    <string name="paid">Paid</string>
    <string name="order_cannot_pay">Order can not pay</string>
    <string name="not_paid">Not paid</string>
    <string name="can_paid">Can paid</string>
    <string name="not_found_order_select_continue">Not found order. Click Pay Now to continue this order?</string>
    <!-- error -->
    <string name="error_default">Failed</string>
    <string name="error_default_contact_hotline">Sorry, application have error. Please try again or call hotline to get support!</string>
    <string name="error_crash_send_log">Sorry! The application mpos has stopped unexpectedly. Please send loginfo to server!</string>
    <string name="error_7000">Not found merchant config!</string>
    <string name="error_7001">Merchant integrated status invalid!</string>
    <string name="error_7002">Not found PTI GCN!</string>
    <string name="error_7003">Merchant integrated is not active!</string>
    <string name="error_7004_order_id_not_found">Not found order: %s!</string>
    <string name="error_7006">Check order failed!</string>
    <string name="error_7007">Transaction is not found!</string>
    <string name="error_7008">Not permission!</string>
    <string name="error_14000">Can\'t connect to merchant system, please contact with technical to get support</string>
    <string name="error_4106">Card reader not found</string>
    <!--<string name="error_7005">Can not check this order. Please try another order or call hotline to get support</string>-->
    <string name="error_get_info_merchant">Get info merchant failed!</string>
    <string name="error_time_out_read_audio">Can not detect card reader</string>
    <string name="error_time_out_read_audio_please_reconnect">Can not detect card reader\n(Please reconnect card reader)</string>
    <string name="error_reversal_input_phone">Sorry, transaction aborted. This transaction will be refunded to card holder, please enter card holder\'s telephone number, mPoS.vn will get in touch soon.</string>
    <string name="error_onfaild_request">Request time out, please try again</string>
    <string name="error_field_required">This field is required</string>
    <string name="error_update_trans_mpos">Update transaction info failed.</string>
    <string name="error_load_merchant_info_mpos">Load merchant info failed%s.\nPlease \"Retry\" for reload, or \"Continue\" for normal payment.</string>
    <string name="error_timeout_session_12h">Please reLogin before continue transaction.</string>
    <string name="error_sp01_cant_get_SN">Device can not found SN, please reboot device and try again.</string>

    <string name="error_wrong_bien_so_xe">Invalid License plate</string>
    <string name="error_not_found_qr_transaction">Transaction is not found! Please contact hotline &hot_line;
        for assistance.</string>
    <string name="error_input_length_pin">Have error when enter pin, please try again!</string>

    <string name="hint_amount_incorrect_service_post_pay">Amount must be multiplied by 10.000 VND</string>
    <string name="msg_suggest_amount_service_post_pay">You will need to pay amount of %s. The excess amount %s will be added to your next monthly payment. Are you accept payment?</string>

    <string name="go_back_integration">Go back</string>
    <!--error cashback + installment-->
    <string name="error_8001">Can\'t not found installment program.</string>
    <string name="error_8002">Can\'t not found UDID.</string>
    <string name="error_8003">UDID already used.</string>
    <string name="error_8005">Cashback program is invalid.</string>

    <string name="error_110001">Reader ID in session and request dont match</string>
    <string name="error_110002">Reader ID does not exist in the concurrent map</string>
    <string name="error_12001">Connection between client and host expired, due to cancellation or timeout</string>
    <string name="error_12003">Thread interrupted in long poller, probably triggered by a forced destroy</string>
    <string name="error_50001">Connection error integrate code</string>
    <string name="error_50002">Connection error encrypt / decrypt</string>
    <string name="error_50003">Connection encrypt key expired</string>
    <string name="error_50004">Connection key fail</string>
    <string name="error_50005">Exchange not config</string>
    <string name="error_50006">Customer code / contract code fail</string>
    <string name="error_50007">Customer code / contract code is empty</string>
    <string name="error_50008">Phone number is empty</string>
    <string name="error_1004">Service is unavailable. Please call MPOS Customer Support hotline &hot_line;
        for further assistance.</string>
    <string name="error_4107">Not found config of integration. Please call MPOS Customer Support for further assistance.</string>

    <string name="error_10001_login">Can not login with your user. Please contact our support: hotline &hot_line;.</string>
    <string name="error_time_out_sale"><![CDATA[This transaction may have been successful. Please enter the <b>transaction history (history section in day)</b> to check. <br><br>If the transaction appears in the history list and the status is successful (the cardholder has been charged), you have completed the transaction. <br>If the transaction <b>does not appear in the list</b> or <b>the status is Reversal</b>, Please retry the transaction.]]></string>
    <string name="error_outage">The system is temporarily interrupted. Please try again later or Call &hot_line;
        to get help</string>
    <string name="error_get_amount_limit">Can\'t get a limit to the amount, please try again!</string>
    <!-- card/sim of TW -->

    <!-- utility service -->
    <string name="electric">Electric</string>
    <string name="internet">Internet</string>
    <string name="water">Water</string>
    <string name="supplier">Supplier</string>
    <string name="service_code">Service code</string>
    <string name="select_service">Select service</string>
    <string name="select_supplier">Select supplier</string>
    <string name="error_please_select_service">Please select service</string>
    <string name="error_please_select_supplier">Please select supplier</string>

    <!-- register -->

    <string name="notifications">Notifications</string>
    <string name="contact">Contact</string>
    <string name="guide">Guide</string>
    <string name="installment">Installment</string>
    <!-- menu -->
    <string name="txt_menu_news">News &amp; Notifications</string>
    <string name="txt_menu_privacy">Privacy policy</string>
    <string name="txt_menu_change_pass">Change password</string>
    <string name="txt_menu_logout">Logout</string>

    <string name="action_settings">Settings</string>

    <!-- user guide -->
    <string name="guide_connect">1. Select reader type you are using.\n2a. Audio reader: Plug reader into audio port.\n2b. Pinpad reader: Enable Bluetooth and connect Pinpad reader(Settings > Bluetooth > Scan and Pair with reader)</string>
    <string name="guide_login">1. Enter username and password to login.</string>
    <string name="guide_start_pay">1. Select \"START ACCEPTING PAYMENTS\" to start a transaction.\n2. Select \"Telco &amp; Game card or Utilities billing\" for other transaction.</string>
    <string name="guide_input_amount">1. Enter amount number, email receipt, transaction description.\n2. Click Pay now to start transaction.</string>
    <string name="guide_swipe_card">1. Swipe magnetic card (ATM card) or insert CHIP card (VISA, Master Card…) and wating to process payment.\n2. DO NOT remove card reader or CHIP card.</string>
    <string name="guide_input_pin">1. If ATM card, enter PIN to verify card holder.\n2. For Audio reader, enter PIN on the screen, otherwise enter PIN on device</string>
    <string name="guide_sign">1. Using finger to draw signature on screen to verify card holder.\n2. Compare the signature on the card and screen before continue.</string>
    <string name="guide_result">1. Display transaction result, if error please try again or contact our support.\n2. View transaction detail in Sale history.</string>

    <string name="guide_service_pre">* Using PREPAID.\n1. Select amount card and input your mobile number.\n2. Click Pay Now to continue</string>
    <string name="guide_service_pre_confirm">1. Confirm your data input if invalid click Back to modify.\n2. Click Confirm to continue this payment.</string>
    <string name="guide_service_post">* Using POSTPAID.\n1. Select amount card and input your mobile number.\n2. Click Pay Now to continue</string>
    <string name="guide_service_post_confirm">1. Confirm your data input if invalid click Back to modify.\n2. Click Confirm to continue this payment.</string>
    <string name="guide_service_card">* Using BUYCARD.\n1. Select amount card, Issuer and input your mobile number.\n2. Click Pay Now to continue.</string>
    <string name="guide_service_card_confirm">1. Confirm your data input if invalid click Back to modify.\n2. Click Confirm to continue this payment.\n* NOTE: Code will be send to your mobile when payment complete.</string>

    <string name="support_247">Hotline Support 24/7</string>
    <string name="SUPPORT_HOTLINE_PHONE_NUMBER">&hot_line;</string>
    <string name="title_activity_activity_utility_service">ActivityUtilityService</string>
    <string name="msg_request_relogin_for_update_capk">Please relogin for update firmware of Pinpad Reader</string>

    <string name="guide_sub_login">For security reasons for your transaction, please re-enter your login password to: \n1. Watch History transaction \n2. Settlement transaction \n3. Void transaction</string>
    <!-- quick withdrawal -->
    <string name="txt_want_money_my_acc">Quick withdrawal</string>
    <string name="dialog_cost_for_service">The cost you pay extra for this service:</string>
    <string name="dialog_des"><![CDATA[You will receive quick money on your bank account after click the button <b><font color=#ac272e>Confirm</b>.]]></string>
    <string name="dialog_note"><![CDATA[<font color=#ac272e>Note: All of your transactions will be  <b>Finalise</b> after you <b>Confirm</b>.]]></string>
    <string name="title_note_quick_withdraw">Schedule of quick withdrawal fees</string>
    <string name="content_note_quick_withdraw1">Transactions &lt; 4 million = 33,000 VND</string>
    <string name="content_note_quick_withdraw2">Transactions> = 4 million = 0.5% * withdrawal amount</string>
    <string name="tv_cancel">Cancel</string>
    <string name="tv_confirm">Confirm</string>
    <string name="tv_title_dialog_quick">Get quick money into your account</string>
    <string name="tv_quick_money_mark_success">Your transaction received your transaction was received to success system</string>

    <string name="tv_reader_serial_number_cdata"><![CDATA[<font color=#ffffff>Connected successfully: <b>%1$s</b></font><br>%2$s]]></string>
    <string name="des_chip">Insert the card into the slot for the chip card</string>
    <string name="des_magentic">Swipe card for magnetic card</string>
    <string name="tv_guide_payment_card">Please plug or swipe to pay</string>


    <!-- ERROR DIALOG GENERAL -->
    <string name="confirm">Confirm</string>

    <string name="dialog_error_title">Error %d</string>

    <string name="dialog_error_via_contact"><![CDATA[Contact support<br><strong>0339 506 286</strong>]]></string>
    <string name="dialog_error_via_email"><![CDATA[Report an error via email<br><strong><EMAIL></strong>]]></string>

    <!-- DIALOG WHEN INSTALLMENT SUCCESS -->
    <string name="dialog_installment_success">Transaction installment successfully</string>
    <string name="dialog_installment_period">Period</string>
    <string name="dialog_installment_bank">Bank</string>
    <string name="period_installment">Installment period</string>
    <string name="dialog_error_description">This error occurred during swipe card</string>
    <string name="dialog_error_guide_fix"><![CDATA[To fix this, please follow these instruction:<li>If it is a MAGNETIC CARD, swipe through the card reader slot; note: the word SAME strips side to the printed side of the card reader.</li><li>If it is a CHIP CARD, plug it into the card reader: note: magnetic strip from SAME with printed side of logo of card reader, do not pull the card out during the transaction.</li> ]]> </string>
    <!--<string name="dialog_error_guide_fix"><![CDATA[To fix this, please follow these instruction:<br> <ul><li>If it is a MAGNETIC CARD, swipe through the card reader slot; note: the word SAME strips side to the printed side of the card reader.</li><span style="display:block"></span> <li>If it is a CHIP CARD, plug it into the card reader: note: magnetic strip from SAME with printed side of logo of card reader, do not pull the card out during the transaction.</li> </ul>]]> </string>-->
    <string name="dialog_installment_period_prefix">&#160;month</string>
    <!--    <string name="link_guide_installment_of_bank">http://mpos.vn/public/installment-guide/%1$s.html</string>-->
    <string name="dialog_installment_periods_prefix">&#160;months</string>
    <string name="dialog_installment_guide_understand">OK</string>
    <string name="dialog_installment_guide_title">To complete the installment transaction of %1$s, you need to</string>
    <string name="dialog_error_close">Close</string>
    <string name="dialog_error_title_default">Error occurred</string>
    <string name="dialog_mvisa_code_transaction">Transaction code</string>
    <string name="dialog_mvisa_email">Email customer</string>
    <string name="dialog_mvisa_money">Amount</string>
    <string name="dialog_mvisa_number_card">Card number</string>
    <string name="dialog_mvisa_payment_base_on">Payment base on</string>
    <string name="dialog_mvisa_time_create_qrcode">Creation time</string>
    <string name="txt_swipe_card">Swipe card</string>
    <string name="txt_swipe_card_via_mpos">Swipe card via\nMPOS reader</string>
    <string name="txt_title_select_pay_type">Select a payment method</string>
    <string name="txt_guide_use_qrcode">Use your bank\'s application, scan this QR code for payment</string>

    <string name="txt_guide_connect_devices">Please pair your device on Settings> Bluetooth> Choose mPoS device. Please close the popup message if using another device. Note: If the accessory is not connected in Bluetooth settings, please forget this accessory and then pair it again.</string>

    <string name="txt_note"><![CDATA[<b> </b>%1$s]]></string>
    <string name="dialog_mvisa_not_success">Transaction failed</string>
    <string name="dialog_mvisa_success">Payment success</string>
    <string name="dialog_mvisa_view_history">View transaction details</string>
    <string name="dialog_mvisa_payment_base_on_qr_international">Scan QR code\n(International)</string>
    <string name="dialog_mvisa_payment_base_on_qr_international_only_line">QR code International Card</string>
    <string name="error_3300">Account has not been activated, please call &hot_line; to get help.</string>
    <string name="error_3301">Email or password incorrect.</string>
    <string name="error_3200">Account does not exist or email is incorrect, please call &hot_line;
        to get help.</string>
    <string name="dialog_mvisa_title_success">Transaction success</string>
    <string name="dialog_mvisa_btn_view_detail">View Detail</string>
    <string name="dialog_qr_nl_type_card">Payment unit code</string>
    <string name="txt_guide_step_swipe_card">You must connect to mPos device before making a transaction</string>

    <string name="txt_login_email_and_not_accept_mvisa">You have not registered for QR-Code payment service. Please call &hot_line;
        to get help.</string>
    <string name="txt_DVCNT_chuatichhop">Your account has not been activated for this payment method. Please use \"Start accept payments\" method.</string>
    <string name="txt_no">Cancel</string>
    <string name="txt_yes">OK</string>
    <string name="txt_title_notice">Notice</string>
    <string name="txt_title_success">Success</string>
    <string name="txt_title_wait_for_disbursement">Waiting for disbursement</string>
    <string name="txt_error_connect_bank">Lost connection payment bank</string>

    <string name="check_history">Go to History</string>
    <string name="skip">Skip</string>

    <string name="tv_login_baseon_email_enter_email">Enter email</string>
    <string name="dialog_mvisa_cardholdername_transaction">Card Holder Name</string>
    <string name="dialog_mvisa_type_card">Card Type</string>
    <string name="txt_create_square_code_mpos">Generate MPOS square code</string>

    <string name="input_amount">Enter amount</string>
    <string name="status">Status</string>
    <string name="type_card">Card type</string>


    <string name="connect_viva_bluetooth">Device connected via bluetooth</string>
    <string name="connect_viva_audio">Device connected via the phone\'s audio jack</string>
    <string name="no_need_connect_viva_bluetooth">No need to connect via MPOS card reader</string>


    <string name="login_by_acc_mpos">Login via MPOS account provided</string>
    <string name="login_by_email">Login via registered mail</string>

    <string name="select_type_card">Select the card type</string>
    <string name="acc_info">Customer\'s information</string>
    <string name="email" translatable="false">Email</string>
    <string name="phone">Phone number</string>

    <string name="cmt">Identity card number</string>
    <string name="hint_email" translatable="false"><EMAIL></string>
    <string name="hint_cmt_hochieu">Identity card number/Passport</string>


    <string name="guide_connect_pr01">Please go to Phone Settings > Bluetooth > Turn on bluetooth > Pair with MPOS reader.(MPOS device\'s name is "PP + SN number on the back of the device", touch the device name to connect.)</string>
    <string name="guide_connect_pr02">Please turn on the MPOS PR02 which you want to use. MPOS application will auto to connect to that device.\nNotice: if the application can not connect to MPOS PR02 which you want to use. Please follow these steps:\n1. Turn off the bluetooth on your device and then turn on it again.\n2. Turn off the MPOS PR02 and then turn on it again.\n3. Open mPoS.vn application again and select \"Bluetooth Reader (PR02)\" to reconnect.</string>
    <string name="guide_connect_ar01">Please plug MPOS reader to audio port of your phone.</string>

    <string name="guilde_user_scan_qrcode">Ask your customers using the applications to scan QR code</string>

    <string name="installment_bank">Installment Bank:</string>
    <string name="add_card_fee">Add card fee:</string>
    <string name="add_fee_installment">Add installment fee:</string>
    <string name="requirement_pay">Required payment:</string>
    <string name="monthly_pay">Monthly payment:</string>
    <string name="period">Period:</string>
    <string name="serial_number">Serial number: %s</string>

    <string name="selected_qr_pay">You selected: Pay with QR code</string>

    <string name="guide_contact_support">If you need advice, more information about products, services or error messages, please contact</string>
    <string name="after_sale">After Sales Service</string>
    <string name="support_general">Customer service - general information</string>
    <string name="integration_cant_pay">Order can not pay</string>
    <string name="integration_missing_filed_login">Can not auto login (missing some required filed)!</string>

    <string name="msg_error_load_data">Can not get data from server.(%s)</string>
    <string name="msg_error_load_data_no_param">Can not get data from server.</string>

    <string name="txt_guide_feedback">Please enter your mobile number to receive bonus for this transaction.</string>
    <string name="msg_not_eligible_feedback">Transaction is not eligible to receive the reward</string>
    <string name="txt_not_enough_for_feedback">The transaction amount is less than %1$s so you do not receive the reward.</string>
    <string name="finish_transaction">Finish transaction</string>
    <string name="input_here">Input here</string>
    <string name="confirm_new_pass">Confirm new password</string>
    <string name="new_pass">New Password</string>
    <string name="input_new_pass">Enter new password</string>
    <string name="input_current_pass">Enter current password</string>
    <string name="current_pass">Current password</string>
    <string name="change_pass">Change password</string>
    <string name="phone_input">Enter customer phone number</string>
    <string name="info_order">Order information</string>
    <string name="currency">VND</string>
    <string name="create_qr">Create QR code</string>
    <string name="selected_app_qr_pay">Select application QR payment</string>
    <string name="scan_qr_code">Scan QR code</string>
    <string name="reversal">Reversed</string>
    <string name="canceled">Voided</string>
    <string name="refunded">Refunded</string>
    <string name="failed">Failed</string>
    <string name="processing">Processing</string>
    <string name="not_qr_pay">There are no QR payment methods</string>
    <string name="cannot_qrtype">There are not QR code</string>
    <string name="qr_code" translatable="false">QR Code</string>
    <string name="refunding">Refunding</string>

    <string name="approval_code">Approval Code: </string>
    <string name="refer_number">Refer number: </string>

    <string name="cashier_rw_success">Reward received</string>
    <string name="cashier_rw_processing">Waiting for approval</string>
    <string name="cashier_rw_pending">Waiting for mobile number to be entered</string>
    <string name="cashier_rw_denied">Not in the reward policy</string>
    <string name="required_filed" translatable="false">%s <![CDATA[<font color=#cf3a3e><b>*</b>]]></string>
    <string name="error_55000">Generating QR codes failed due to lost connection, please try again later</string>
    <string name="message_card_unavailable">You have not registered payment service with mPos card reader, Please call &hot_line;
        to get help.</string>
    <string name="message_can_not_normal_payment">You have not permission payment, Please call &hot_line;
        to get help.</string>

    <string name="error_qrPay_unavailable">You have not registered for QR-Code payment service, please call &hot_line;
        to get help</string>
    <string name="not_qrpay_current">There is currently no method of QR payment</string>

    <string name="error_not_pending_qr_transaction">The transaction has still not paid or in processing. Please try again later, or call &hot_line;
        to get help.</string>
    <string name="error_qrCode_empty">Can not create the QR code. Please try again or choose another.</string>
    <string name="error_cashback_empty">Can not get cashback. Please try again or choose another.</string>
    <string name="report">Report</string>

    <string name="pay_by_card">Swipe Card Payment</string>
    <string name="pay_by_card_enter">Enter Card Payment</string>
    <string name="pay_by_qr">QR Code Payment</string>
    <string name="terms">Terms of use</string>
    <string name="guide_use">Help</string>
    <string name="next"> Continue</string>
    <string name="transaction_processing">Transaction is processing</string>

    <string name="disconnect_internet">Check your connection internet</string>
    <string name="error_son">Data json error</string>
    <string name="phone_recharge">Send Mobile Top-up</string>
    <string name="issuer">Card Value</string>
    <string name="recharge_telephone">Mobile Top-up</string>
    <string name="check">Check</string>
    <string name="expir_countdown">The transaction has expired. Please make another.</string>

    <string name="connected_device">Connecting with device</string>
    <string name="accept_payment">We accept</string>
    <string name="check_connect_device">Lost connection to device</string>
    <string name="retryconnect_device">Touch to reconnect</string>
    <string name="retry_connect">Connecting again…</string>
    <string name="swipe_magentic">Magnetic stripe card reader</string>
    <string name="des_chip_card">Insert the card</string>
    <string name="swipe_card">Swipe the card </string>
    <string name="cancel_transaction">Cancel transaction</string>
    <string name="cancel_transaction_ok">Ok</string>
    <string name="cancel_transaction_no">Cancel</string>
    <string name="open_device">Hãy chắc chắn thiết bị của bạn đang được bật nguồn.</string>
    <string name="title_guide_swipe_card">If you cannot connect card reader, please follow the steps below:</string>
    <string name="title_guide_swipe_card_1">1. Turn off Bluetooth on your mobile phone then turn it on again.</string>
    <string name="title_guide_swipe_card_2">2. Turn off and turn on card reader.</string>
    <string name="title_guide_swipe_card_3">3. Restart Mpos application and reselect the card reader name.</string>
    <string name="off">Turn off</string>
    <string name="on">Turn on</string>

    <string name="service">Services</string>
    <string name="status_unpaid">Unpaid</string>
    <string name="domestic_atm_card">Domestic ATM card</string>
    <string name="international_card">International card</string>
    <string name="vaymuon_installment">Installment by VayMuon</string>

    <string name="see_details_here">See details here</string>
    <string name="copied">Copied to Clipboard</string>


    <!--<string name="downloading_file_config">Downloading file config</string>-->
    <string name="btn_update">Update</string>
    <string name="btn_update_now">Update now</string>
    <string name="btn_later">Later</string>
    <string name="title_upgrade_reader">Cập nhật thiết bị đọc thẻ</string>
    <string name="desc_upgrade_reader">Cấu hình mới giúp thiết bị đọc được nhiều loại thẻ cùng các phương thức thanh toán khác. Ứng dụng sẽ bắt buộc việc cập nhật nếu bạn bỏ qua quá nhiều.</string>
    <string name="error_not_connect_reader">Can not connect to card reader\n(Please retry connect to card reader).</string>
    <string name="warning_have_upgrade_evm_config">Đã có phiên bản cấu hình mới cho thiết bị. Bạn cần tiến hành cập nhật để có thể chấp nhận nhiều loại thẻ của khách hàng hơn. Vui lòng click vào nút cập nhật và chờ trong 60 giây.</string>
    <string name="updating_config_reader">Upgrading...\nIt will take a few minutes(2)</string>
    <string name="update_config_reader_success">Update configuration card reader successful</string>
    <string name="update_config_reader_fail">Update configuration card reader fail. Please try again or call hotline to get support.</string>
    <!--    <string name="error_download_emv_config">Có lỗi xẩy ra trong quá trình tải xuống cấu hình mới. Vui lòng thử lại hoặc liên hệ với hotline để được trợ giúp!</string>-->
    <!--    <string name="error_no_permission_storage">Ứng dụng không được cấp quyền truy cập bộ nhớ, quá trình tải xuống bản cập nhật cấu hình mới không thành công.</string>-->
    <!--    <string name="warning_permission_storage">\n(Vui lòng cấp quyền truy cập bộ nhớ để tiến hành tải xuống bản cập nhật).</string>-->
    <!--    <string name="updating_config_reader_downloading">Upgrading...\nIt will take a few minutes(1)</string>-->
    <!--
    <string name="update_config_reader_fail">Card reader update configuration failed</string>-->

    <string name="not_url_callback" translatable="false">Giao dịch thanh toán thành công. Quay lại ứng dụng của bạn để tiếp tục thực hiện thanh toán đơn hàng tiếp theo.</string>
    <string name="not_url_callback_re_signature" translatable="false">Ký xác nhận giao dịch thành công. Quay lại ứng dụng của bạn để thực hiện thanh toán cho đơn hàng tiếp theo.</string>
    <plurals name="numberOfMonthAvailable">
        <!--
             As a developer, you should always supply "one" and "other"
             strings. Your translators will know which strings are actually
             needed for their language. Always include %d in "one" because
             translators will need to use %d for languages where "one"
             doesn't mean 1 (as explained above).
          -->
        <item quantity="one">%s\nmonth</item>
        <item quantity="other">%s\nmonths</item>
    </plurals>

    <!-- vay muon -->
    <string name="payment_method">Payment method</string>
    <string name="status_transaction">Status</string>
    <string name="time_transaction">Transaction time</string>
    <string name="payment_content">Payment content</string>
    <string name="status_wait_explain">Money has not been recorded for the seller, Please do not ship!</string>
    <string name="status_kt_explain">Note: this transaction supports CANCEL only 60 minutes after payment</string>
    <string name="cancel_transaction_content">Merchant is required to enter content to cancel this transaction.</string>
    <string name="cancel_transaction_content_label">Content canceled</string>
    <string name="cancel_transaction_content_placeholder">Enter the content to cancel the transaction</string>
    <string name="cancel_transaction_confirm">Confirm cancellation</string>
    <string name="cancel_transaction_confirm_empty">Please enter content to cancel</string>
    <string name="cancel_transaction_confirm_too_short">Canceled content is too short</string>
    <string name="cancel_transaction_confirm_success">Confirm successful cancellation</string>
    <string name="void_reason">Void reason</string>
    <string name="month">month(s)</string>
    <string name="day">day(s)</string>
    <string name="customer">Customer</string>
    <string name="payment_period">Payment period</string>
    <string name="txt_check_trans">Check transaction</string>

    <!-- link installment -->
    <string name="type_vimolink">Create payment link</string>
    <string name="type_vimonormal">Enter card payment</string>

    <!-- affiliate -->
    <string name="earn_money">Earn money</string>

    <!-- nextlend -->
    <string name="advance_now">Advance now</string>
    <string name="lend_money">Lend money</string>
    <string name="lend_money_title">Business capital advance</string>

    <!-- statistic shift-->
    <string name="statistic_shift">Statistic shift</string>

    <!-- settings-->
    <string name="title_settings">Settings</string>
    <string name="settings_auto_print">Auto print receipt</string>

    <!-- enter card -->
    <string name="error_min_amount">The payment amount must not be less than VND 1,000</string>
    <string name="error_min_amount2">The payment amount must not be less than VND %s</string>
    <string name="error_desc_length">Invalid description (The length of the description must be between 5 and 255)</string>
    <string name="error_empty_desc">Description cannot be empty</string>
    <string name="error_empty_email">Email cannot be empty</string>
    <string name="error_empty_phone">Phone number cannot be empty</string>
    <string name="title_enter_card">Enter payment card</string>
    <string name="button_enter_card">Enter card</string>
    <string name="button_create_link">Create link</string>
    <string name="error_code">Error code</string>
    <string name="content_link_payment">Create link: The system will create a link (Url). You copy or share to customers enter card information to pay.</string>
    <string name="content_link_payment_fee">Note: Payment for card entry by creating the mPos link to collect the seller fee is:</string>
    <string name="transaction">Transaction</string>
    <string name="scan_card_warn_amount">*Please enter correct phone number of cardholder</string>

    <!-- result pending -->
    <string name="pending_title">Transaction pending</string>
    <string name="pending_content">Currently your transaction needs to be verified. Please do not ship to the buyer.</string>
    <string name="button_back_home">GO BACK TO THE HOME</string>
    <string name="button_back_history">GO BACK TO THE HISTORY</string>
    <string name="button_call_hotline">CALL HOTLINE 1900 2079</string>
    <string name="text_hint_review">You can review the transaction in history</string>
    <string name="text_hint_support">Hotline 1900 2079 - Email: <EMAIL></string>

    <string name="active_vas_wallet_title">Activate service wallet ATM360</string>
    <string name="active_vas_wallet_content">Payment services ATM360 by service wallet with more benefit.</string>

    <!--    printer-->
    <string name="downloading_receipt">Receipt downloading…</string>
    <string name="receipt">Receipt</string>
    <!--    <string name="print_receipt">Print receipt</string>-->
    <!--    <string name="error_printer_default">Can not print. Please try again or call hotline to get support!</string>-->
    <!--    <string name="error_printer_out_of_paper">Out Of Paper, please add paper to the tray and try again!</string>-->
    <!--    <string name="error_printer_1_2">Please close and reopen app again.</string>-->
    <!--    <string name="error_printer_negative_1_2">Please reboot device and try again.</string>-->
    <!--    <string name="error_printer_negative_11">Battery is lower.</string>-->
    <!--    <string name="error_printer_negative_12">Please try again later.</string>-->
    <!--    <string name="error_printer_p8_overheat">Printer is overheat, please wait and retry later.</string>-->
    <string name="warning_battery_lower_cannot_print">Battery is low, device can not print.</string>
    <string name="msg_connect_printer_s85">You need to connect to the S85, XPrinter(XP-P210) printer before printing a receipt.</string>

    <string name="message_permission_write_ext">Please go to Settings and allow the app to access device storage to pick your photo.</string>
    <string name="button_permission_write_ext">Setting</string>

    <string name="xpp210_con_success">Printer connected successfully</string>
    <string name="xpp210_con_failed">Printer connected failed</string>
    <string name="xpp210_con_has_discon">Printer connection has disconnected</string>
    <string name="xpp210_notice_printer_after_5s">Printer will print the receipt after 5–7 seconds.</string>

    <!--    promotion-->
    <string name="warranty_code">Warranty code</string>
    <string name="success_send_warranty_code">Send Warranty code successfully</string>
    <string name="error_warranty_code">Can not update warranty code. Please try again or call hotline to get support.</string>
    <!--    <string name="error_warranty_code_length">Warranty code</string>-->

    <!--    cashier-->
    <string name="cashier_miss_user">Account info is invalid. Please close and reopen application to continue payment.</string>

    <!--    VPBank-->
    <string name="error_code_vp_50030">Cannot use credit card to deposit bank account of VPBank</string>

    <!--    Emart-->
    <string name="load_ip_socket">Load IP Network</string>
    <string name="tv_wait_order">Waiting for new Transaction...</string>
    <string name="order_void_trans">Confirm void transaction? This transaction amount will be refunded to the cardholder.\nOrderID: %s</string>
    <string name="noti_start_trans">Transaction in progress...</string>
    <string name="noti_void_trans">Void transaction in progress...</string>
    <string name="noti_socket_disconnect">Lost connection.\nPlease check the network connection</string>
    <string name="warning_invalid_data">Invalid data</string>
    <string name="error_start_server_socket">Không thể khởi tạo máy chủ nhận dữ liệu đơn hàng. Vui lòng kết nối lại hoặc đóng và mở lại ứng dụng.</string>
    <string name="server_online">Connect ready</string>
    <string name="server_offline">Connect not available</string>
    <string name="server_wating">Server Waiting</string>
    <string name="tv_show_ip_socket">IP Device:  <b>%s</b></string>
    <string name="tv_nothing_ip_socket">Unable to get IP address, please check the network connection.</string>
    <string name="order_hasbeen_done">The transaction with OrderID= %s has been done before. Do you want to receive transaction status?</string>
    <string name="check_internet">Request timed out. Please try again.</string>
    <string name="continue_trans">"Transaction with amount: %s has not been executed. Do you want to continue?"</string>
    <string name="txt_connect_base">"No device has been paired before, Please connect to the dock."</string>
    <string name="txt_config_base">"Config Base"</string>
    <string name="txt_find_base">"Find a Base"</string>
    <string name="tv_base_infor">"Base infor"</string>
    <string name="tv_config_wifi_base">"Config base wifi"</string>
    <string name="tv_ip_gateway">IP Gateway: <b>%s</b></string>
    <string name="tv_wait_client">No devices are connected</string>
    <string name="tv_client_connect">There is 1 connected device:\n %s</string>
    <string name="tv_base_connected">Connected to the Base: %s</string>
    <string name="tv_base_not_connected">No Dock is connect</string>
    <string name="msg_err_get_order">There is an error when got data:\n %s</string>
    <string name="msg_trans_had_payment">The order has been paid before, please check the OrderID.</string>
    <string name="msg_ref_no_code">Ref No: %s</string>
    <string name="msg_approved_code">Approve Code: %s</string>
    <string name="msg_permit_void_socket">There is no right to cancel the transaction!</string>
    <string name="tv_check_connect_socket">Check Connect</string>
    <string name="tv_confirm_reset_socket">Are you confirm reset connect?</string>
    <string name="check_giftcard">Check Gift Card</string>
    <string name="msg_gift_card_err">The card does not exist on the system. Please check again.</string>
    <string name="tv_gift_card_info">GiftCard Infor.</string>
    <string name="btn_retry_gift_card_info">Read other card</string>
    <string name="tv_giftcard_pan">Pan: %s</string>
    <string name="tv_giftcard_balance">Balance: %s</string>
    <string name="tv_giftcard_status">Status: %s</string>
    <string name="tv_giftcard_timeActiveCard">Time Active Card: %s</string>
    <string name="tv_giftcard_timeExpiredCard">Time Expired Card: %s</string>
    <string name="tv_card_not_exits">The card does not exist</string>
    <string name="tv_invalid_order_code">Invalid order code</string>
    <string name="msg_wait_giftcard_info">Waiting for card swipe.</string>
    <string name="msg_check_giftcard_info">Checking</string>
    <string name="msg_not_giftcard">This is not GiftCard! Please try again.</string>
    <string name="msg_error_emart_send_msg_to_client">Unable to send data to the vending machine, please check the network connection.</string>
    <string name="tv_ssid_base">SSID: %s</string>
    <string name="tv_gift_card">Gift Card</string>
    <string name="tv_new_trans">Input Amount</string>
    <string name="msg_amount">Amount: %s VND</string>
    <string name="msg_swipe_gift_card">Please swipe your Gift Card into the magnetic card reader slot</string>
    <string name="msg_void_trans_fail">VOID Transaction fail. Please check transaction status in history.</string>
    <string name="msg_void_trans_success">VOID Transaction success.</string>
    <string name="msg_service_not_ready">Please open wait order screen.</string>

    <!-- //QR emart -->
    <string name="msg_update_status_qr_nl">Update status</string>
    <string name="msg_alert_not_support_qr_nl">Your account is not registered to use QR. Please call hotline to get support.</string>
    <string name="msg_alert_invalid_amount_qr_nl">Invalid amount. Amount must be more than %sVND.</string>
    <string name="err_fetch_banks_qr_nl">There is an error when got QR bank list data. \nErrCode: %s</string>
    <string name="method_support_qr">QR payment unit</string>
    <string name="tittle_method_support_qr">QR payment unit</string>
    <string name="e_wallet">Wallet</string>
    <string name="bank_support_qr">Payment support bank</string>
    <string name="err_create_qr">There is an error when create QR code. Please try again or call Hotline &hot_line;
        to get support. \nErrCode: %s</string>

    <string name="msg_sign_fail">Signing confirmation of the transaction has not been successful. Please re-sign before making a new transaction!</string>
    <string name="history_qr">QR</string>
    <string name="title_scan_qr">Quét QR</string>
    <string name="title_processing_pay">Paying</string>
    <string name="msg_err_load_qr">Can not load QR history. Please contact with technical to get support</string>
    <string name="time_paid_qrcode">Time Paid</string>
    <string name="tv_refresh">Refresh</string>
    <string name="tv_today">Today</string>
    <string name="tv_day_ago">1 Day ago</string>
    <string name="tv_3_days_ago">3 Days ago</string>
    <string name="tv_week_ago">1 Week ago</string>
    <string name="tv_2_week_ago">2 Weeks ago</string>
    <string name="tv_months_ago">1 Months ago</string>
    <string name="msg_select_bank_qr">Please select a bank that supports QR payment</string>
    <string name="msg_err_fetch_his_qr">There is an error when got QR history data. \nErrCode: %s</string>
    <string name="msg_trans_code">Transaction code: %s</string>
    <string name="tv_qr_nl">QR Code VNPAY</string>
    <string name="tv_check_update">Check update</string>
    <string name="tv_progress_update_app">Updating... \n The application will automatically exit when the update process is successful.</string>
    <string name="msg_err_update_app">Version update failed. Please try again or contact the hotline for help. (Code: %s)</string>
    <string name="msg_err_complete_update">This is the latest version.</string>
    <string name="msg_warning_update_app">The update is in progress, closing the application may cause unexpected errors. Please wait for the update to finish.</string>
    <string name="msg_warning_check_update_app">The update process can take a long time. Do you want to continue?</string>
    <string name="msg_not_support_momo">Currently, QR Momo is not supported. Please come back later.</string>

    <string name="msg_warning_401_history">Transaction history download failed. Please try again!</string>
    <string name="msg_warning_logout_unauthorized">The session has expired. Please login again to continue using the service.</string>
    <string name="msg_name_vietqr">Bank QR</string>
    <string name="msg_warning_back_qr_screen">Are you sure you want to turn off this screen? You can track order status in transaction history.</string>

    <string name="tv_confirmVoid_tcp">Show notify popup VOID transaction</string>
    <string name="tv_lock_statusbar">Lock notification bar.</string>
    <string name="tv_hide_navbar">Hide navigation bar(Back, Home, Recent)</string>
    <string name="tv_auto_close_dlg">Auto close popup</string>
    <string name="tv_only_show_ui_wait_order">Only show the transaction waiting screen</string>
    <string name="msg_input_pass">Please input password</string>
    <string name="msg_pass_incorrect">Password incorrect</string>

    <string name="tv_auto_print_one_receipt">1 receipt</string>
    <string name="tv_auto_print_two_receipt">2 receipt</string>
    <string name="tv_auto_print_three_receipt">3 receipt</string>

    <string name="tv_deposit">Deposit</string>
    <string name="title_deposit_receipt" translatable="false">DEPOSIT - DAT COC</string>
    <string name="title_sale_receipt" translatable="false">SALE - THANH TOAN</string>
    <string name="title_deposit_void_receipt" translatable="false">VOID - HUY DAT COC</string>
    <string name="title_void_receipt" translatable="false">VOID - HUY</string>
    <string name="title_deposit_reversal_receipt" translatable="false">REVERSAL - DAO DAT COC</string>
    <string name="title_reversal_receipt" translatable="false">REVERSAL - DAO</string>

</resources>
