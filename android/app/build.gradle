apply plugin: 'com.android.application'

Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
def pathFile = properties.getProperty('signing.path')

project.ext.react = [
    entryFile: "index.js",
    enableHermes: false,  // clean and rebuild if changing
]

def jscFlavor = 'org.webkit:android-jsc:+'

def enableHermes = project.ext.react.get("enableHermes", false)

//def myBuildType = 'GOOGLE_PLAY'
//def myBuildType = 'SP01'
//def myBuildType = 'SP02'
def myBuildType = 'PAX'

android {
    signingConfigs {
        product {
//          @ntanh
            keyAlias 'mpos.vn'
            keyPassword 'mpos1234560'
            storePassword 'mpos1234560'
            storeFile file(pathFile)
        }
//        dev {
//            keyAlias 'androiddebugkey'
//            keyPassword 'android'
////            @ntanh
//            storeFile file('/Volumes/DataWork/ws_android_studio_ps/debug.keystore')
//            storePassword 'android'
//        }
    }
    compileSdkVersion 33
    buildToolsVersion '30.0.3'

    defaultConfig {
        applicationId "vn.mpos"
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 396
        versionName "4.0.241106-396"

//        versionName "3.0.20161128.1000-42_app_fix_7034"
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }
        if (myBuildType == 'SP01') {
            // todo note only arm64 for smartpos-sp01
            ndk {
                abiFilters 'arm64-v8a', 'arm64'
            }
        } else if ((myBuildType == 'SP02') || (myBuildType == 'PAX')){
            // todo note only arm64 for smartpos-sp02
            ndk {
                abiFilters 'armeabi', 'armeabi-v7a'
            }
        }

        resConfigs "en", "vi"   //limit language resources to just English and Vietnamese
        multiDexEnabled true
    }

    buildTypes {
        debug {
            resValue "string", "APPLICATION_NAME", "mPoS-test"
            versionNameSuffix '_debug'
            applicationIdSuffix ".test"
        }
        live {
            resValue "string", "APPLICATION_NAME", "mPoS-live"
            versionNameSuffix '_live'
            applicationIdSuffix ".live"
            minifyEnabled false
            debuggable true
            signingConfig signingConfigs.debug
            matchingFallbacks = ['release']
        }
        liveTest {
            resValue "string", "APPLICATION_NAME", "mPoS-test-live"
            versionNameSuffix '_p_test-live'
            applicationIdSuffix ".test"
            minifyEnabled false
            debuggable true
            signingConfig signingConfigs.debug
            matchingFallbacks = ['release']
        }
        certify {
            resValue "string", "APPLICATION_NAME", "mPoS-certify"
            versionNameSuffix '_p_certify'
            applicationIdSuffix ".test"
            minifyEnabled false
            debuggable true
            signingConfig signingConfigs.product
            matchingFallbacks = ['release']
        }
        release {
            resValue "string", "APPLICATION_NAME", "mPoS Plus"

            if (myBuildType == 'GOOGLE_PLAY') {
                shrinkResources true
                minifyEnabled true
                proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            } else {
                // sp02 can not enable + sp01 show log crash
                minifyEnabled false
                proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            }

            applicationIdSuffix ".plus"
            signingConfig signingConfigs.product
//            buildConfigField "String", "URL_SERVER_QR_NL", URL_SERVER_QR_NL
        }
        profile {
            initWith live
        }
        emart {
            resValue "string", "APPLICATION_NAME", "mPoS.vn"
            //debug
            minifyEnabled false
            debuggable true
            signingConfig signingConfigs.debug
            matchingFallbacks = ['release']
            //release
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            signingConfig signingConfigs.product
//            matchingFallbacks = ['release']
        }

        takashimaya {
            resValue "string", "APPLICATION_NAME", "mPoS.vn"
            //debug
            minifyEnabled false
            debuggable true
            signingConfig signingConfigs.product
            matchingFallbacks = ['release']
            applicationIdSuffix ".takashimaya"
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
//        lintOptions {
//            checkReleaseBuilds false
//            // Or, if you prefer, you can continue to check for errors in release builds,
//            // but continue the build even when errors are found:
//            abortOnError false
//        }
    }
    buildFeatures{
        viewBinding true
//        dataBinding = true
    }
    dexOptions {
        javaMaxHeapSize "4g"
        jumboMode true
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def formattedDate = new Date().format('yyyyMMdd')
//            def file = output.outputFile
            def fileName = "MPOS_${variant.name}_${variant.versionName}_${formattedDate}.apk"
            outputFileName = new File("./../build/", fileName)
        }
    }
}

repositories {
    mavenCentral() // lỗi lib react-native khi lấy từ repo này, tạm giữ lại jcenter, xử lý sau
    jcenter()
    maven {
//        url '/Volumes/DataWork/flutter/ws-flutter/f_cashier/cashier/cashier_module/build/host/outputs/repo'
        url '../cashier-1.0/repo'
        // This is relative to the location of the build.gradle file
        // if using a relative path.
    }
    maven {
        url 'http://download.flutter.io'
    }
}

def renameAPK(variant, defaultConfig, buildType) {
    variant.outputs.all { output ->
        def formattedDate = new Date().format('yyMMdd')
        def fileName = "mpos_vimo_V" + defaultConfig.versionCode + "_" + formattedDate + "_" + buildType
        outputFileName = new File(
                "../release",
                outputFileName.replace("app-release", "${fileName}"))
    }
}

dependencies {
    implementation 'androidx.databinding:viewbinding:7.1.2'

    compileOnly files('libs/kozen-sdk-compile-220208.jar')

//    implementation files('libs/printer_xp-p210-sdk.jar')

    implementation files('libs/VNTClientSDK-V0.3.3_20210808.aar')

    implementation project(':react-native-splash-screen')
//    implementation fileTree(include: ['*.jar'], dir: 'libs')

//    implementation project(':core_mpos-1.0.0')
    debugImplementation     files("lib-mpos-sdk/core_mpos-1.0.0-debug.aar")
//    profileImplementation   files("lib-mpos-sdk/core_mpos-1.0.1-debug.aar")
    liveImplementation      files("lib-mpos-sdk/core_mpos-1.0.0-live.aar")
//    liveTestImplementation  files("lib-mpos-sdk/core_mpos-1.0.0-testlive.aar")
//    certifyImplementation   files("lib-mpos-sdk/core_mpos-1.0.0-certify.aar")
    releaseImplementation   files("lib-mpos-sdk/core_mpos-1.0.0.aar")
//    emartImplementation     files("lib-mpos-sdk/core_mpos-1.0.0.aar")
    emartImplementation     files("lib-mpos-sdk/core_mpos-1.0.0-live.aar")
    takashimayaImplementation files("lib-mpos-sdk/core_mpos-1.0.0-debug.aar")

//    liveSp02Implementation  files("lib-mpos-sdk/core_mpos-1.0.0.aar")
//    liveSp02Implementation  files("lib-mpos-sdk/core_mpos-1.0.0-sp02.aar")

    implementation project(':lib_core-1.0.0')
//    implementation project(path: ':mpos-update-config-reader')

    // flutter cashier
    debugImplementation     'com.pps.cashiermodule:flutter_debug:1.0'
//    profileImplementation   'com.pps.cashiermodule:flutter_profile:1.0'
    liveImplementation      'com.pps.cashiermodule:flutter_release:1.0'
//    liveTestImplementation  'com.pps.cashiermodule:flutter_debug:1.0'
//    certifyImplementation   'com.pps.cashiermodule:flutter_profile:1.0'
    releaseImplementation   'com.pps.cashiermodule:flutter_release:1.0'
    emartImplementation   'com.pps.cashiermodule:flutter_release:1.0'
    takashimayaImplementation 'com.pps.cashiermodule:flutter_release:1.0'

//    liveSp02Implementation   'com.pps.cashiermodule:flutter_release:1.0'

    //https://github.com/journeyapps/zxing-android-embedded
    //    compile 'com.journeyapps:zxing-android-embedded:3.2.0@aar'
    //    compile 'com.google.zxing:core:3.2.1'
    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:2.1.0'

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.preference:preference:1.2.0'
    implementation 'androidx.security:security-crypto:1.0.0'

    implementation 'com.google.firebase:firebase-messaging:23.1.0'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.android.gms:play-services-location:21.0.0'
    // 2 lib has contain ads_id -> play store detect it and warning app maybe contains ads -> remove
//    implementation 'com.google.firebase:firebase-core:20.0.2'
//    implementation 'com.google.firebase:firebase-analytics:20.0.2'

//    implementation 'de.hdodenhof:circleimageview:2.2.0'
    implementation 'com.google.zxing:core:3.3.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    //    compile files('libs/mXparser-jdk17-4.1.1-.jar')
    //https://github.com/mariuszgromada/MathParser.org-mXparser
    // check leak memory
    //    debugCompile 'com.squareup.leakcanary:leakcanary-android:1.5.4'
    //    releaseCompile 'com.squareup.leakcanary:leakcanary-android-no-op:1.5.4'
    testImplementation 'junit:junit:4.12'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
//    implementation 'com.squareup.retrofit2:retrofit:2.2.0'
//    implementation 'com.squareup.retrofit2:converter-gson:2.2.0'
    implementation 'androidx.multidex:multidex:2.0.1'

    // download
//    implementation "com.tonyodev.fetch2:fetch2:3.0.10"
//    implementation "androidx.tonyodev.fetch2:xfetch2:3.1.6"

    // read more textview
    implementation 'com.github.colourmoon:readmore-textview:v1.0.2'
//    implementation 'com.borjabravo:readmoretextview:2.1.0'

    implementation 'com.google.crypto.tink:tink-android:1.7.0'

    // lottie: animation
    implementation   "com.airbnb.android:lottie:4.2.0"

//    implementation 'com.synnapps:carouselview:0.1.5'    //https://github.com/sayyam/carouselview
    implementation 'com.github.cachapa:ExpandableLayout:2.9.2'
}

apply plugin: 'com.google.gms.google-services'
